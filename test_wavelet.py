#!/usr/bin/env python3
"""
Test script for Wavelet Transform Scaling Analyzer
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__)))

from algorithms.Spectral_Methods.wavelet_analyzer import WaveletTransformScalingAnalyzer

def test_basic_functionality():
    """Test basic wavelet transform functionality"""
    print("=== Testing Basic Wavelet Transform Functionality ===")
    
    # Test DWT
    print("\n--- Testing DWT ---")
    analyzer_dwt = WaveletTransformScalingAnalyzer(
        wavelet='db4', 
        transform_type='dwt',
        output_dir="test_results/wavelet_dwt",
        enable_gpu_tracking=False
    )
    
    # Test small input sizes
    input_sizes = [100, 200, 500]
    try:
        results = analyzer_dwt.run_scaling_analysis(input_sizes)
        print(f"DWT test successful! Processed {len(results)} sizes")
        
        for result in results:
            custom_metrics = result.custom_metrics
            print(f"  Size {result.input_size}: {result.execution_time_ms:.2f}ms, "
                  f"Coeffs: {custom_metrics.get('total_coefficients', 0)}, "
                  f"Levels: {custom_metrics.get('decomposition_levels', 0)}")
    except Exception as e:
        print(f"DWT test failed: {e}")
    
    # Test CWT with appropriate wavelet
    print("\n--- Testing CWT ---")
    analyzer_cwt = WaveletTransformScalingAnalyzer(
        wavelet='morl', 
        transform_type='cwt',
        output_dir="test_results/wavelet_cwt",
        enable_gpu_tracking=False
    )
    
    # Test smaller input sizes for CWT (it's more expensive)
    input_sizes = [50, 100, 200]
    try:
        results = analyzer_cwt.run_scaling_analysis(input_sizes)
        print(f"CWT test successful! Processed {len(results)} sizes")
        
        for result in results:
            custom_metrics = result.custom_metrics
            print(f"  Size {result.input_size}: {result.execution_time_ms:.2f}ms, "
                  f"Coeffs: {custom_metrics.get('total_coefficients', 0)}, "
                  f"Levels: {custom_metrics.get('decomposition_levels', 0)}")
    except Exception as e:
        print(f"CWT test failed: {e}")
    
    # Test 2D DWT
    print("\n--- Testing 2D DWT ---")
    analyzer_2d = WaveletTransformScalingAnalyzer(
        wavelet='db4', 
        transform_type='dwt2d',
        output_dir="test_results/wavelet_2d",
        enable_gpu_tracking=False
    )
    
    # Test square sizes for 2D
    input_sizes = [100, 400, 900]  # 10x10, 20x20, 30x30
    try:
        results = analyzer_2d.run_scaling_analysis(input_sizes)
        print(f"2D DWT test successful! Processed {len(results)} sizes")
        
        for result in results:
            custom_metrics = result.custom_metrics
            print(f"  Size {result.input_size}: {result.execution_time_ms:.2f}ms, "
                  f"Coeffs: {custom_metrics.get('total_coefficients', 0)}, "
                  f"Levels: {custom_metrics.get('decomposition_levels', 0)}")
    except Exception as e:
        print(f"2D DWT test failed: {e}")

def test_different_wavelets():
    """Test different wavelet types"""
    print("\n=== Testing Different Wavelet Types ===")
    
    wavelets_to_test = ['haar', 'db4', 'db8', 'coif2', 'bior2.2']
    
    for wavelet in wavelets_to_test:
        print(f"\n--- Testing {wavelet} wavelet ---")
        try:
            analyzer = WaveletTransformScalingAnalyzer(
                wavelet=wavelet, 
                transform_type='dwt',
                output_dir=f"test_results/wavelet_{wavelet}",
                enable_gpu_tracking=False
            )
            
            # Test single size
            results = analyzer.run_scaling_analysis([500])
            result = results[0]
            custom_metrics = result.custom_metrics
            
            print(f"  {wavelet}: {result.execution_time_ms:.2f}ms, "
                  f"Coeffs: {custom_metrics.get('total_coefficients', 0)}, "
                  f"Levels: {custom_metrics.get('decomposition_levels', 0)}")
                  
        except Exception as e:
            print(f"  {wavelet} failed: {e}")

if __name__ == "__main__":
    test_basic_functionality()
    test_different_wavelets()
    print("\n=== Wavelet Transform Tests Completed ===")
