from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import torch
import json

# login()  # Uncomment if you need to authenticate

def analyze_llama_structure():
    print("Loading Llama2-7B model...")
    print("=" * 80)
    
    # Load tokenizer and model
    tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-7b-hf")
    model = AutoModelForCausalLM.from_pretrained("meta-llama/Llama-2-7b-hf")
    
    print("✓ Model loaded successfully!")
    print("=" * 80)
    
    # 1. Model Configuration
    print("\n1. MODEL CONFIGURATION:")
    print("-" * 40)
    config = model.config
    config_dict = config.to_dict()
    
    # Print key configuration parameters for Llama
    key_params = [
        'model_type', 'vocab_size', 'hidden_size', 'intermediate_size', 
        'num_hidden_layers', 'num_attention_heads', 'num_key_value_heads',
        'max_position_embeddings', 'rope_theta', 'attention_dropout',
        'hidden_act', 'initializer_range', 'rms_norm_eps', 'use_cache',
        'pad_token_id', 'bos_token_id', 'eos_token_id'
    ]
    
    for param in key_params:
        if param in config_dict:
            print(f"{param}: {config_dict[param]}")
    
    # 2. Model Structure Overview
    print("\n\n2. MODEL STRUCTURE OVERVIEW:")
    print("-" * 40)
    print(model)
    
    # 3. Layer Analysis
    print("\n\n3. LAYER-BY-LAYER ANALYSIS:")
    print("-" * 40)
    
    # Count different types of layers
    layer_types = {}
    
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if module_type not in layer_types:
            layer_types[module_type] = 0
        layer_types[module_type] += 1
    
    print("Layer types and counts:")
    for layer_type, count in sorted(layer_types.items()):
        print(f"  {layer_type}: {count}")
    
    # 4. Parameter Analysis
    print("\n\n4. PARAMETER ANALYSIS:")
    print("-" * 40)
    
    param_info = []
    total_params = 0
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        param_info.append({
            'name': name,
            'shape': list(param.shape),
            'params': param_count,
            'dtype': str(param.dtype)
        })
    
    print(f"Total parameters: {total_params:,}")
    print(f"Model size (approximate): {total_params * 4 / (1024**3):.2f} GB (float32)")
    print(f"Model size (approximate): {total_params * 2 / (1024**3):.2f} GB (float16)")
    
    # Group parameters by layer type
    print("\nParameter breakdown by component:")
    component_params = {}
    
    for info in param_info:
        name = info['name']
        if 'embed_tokens' in name:
            component = 'Embeddings'
        elif 'layers' in name:
            if 'self_attn' in name:
                component = 'Self-Attention'
            elif 'mlp' in name:
                component = 'MLP/Feed-Forward'
            elif 'input_layernorm' in name or 'post_attention_layernorm' in name:
                component = 'Layer Normalization'
            else:
                component = 'Other Transformer Layers'
        elif 'lm_head' in name:
            component = 'Language Model Head'
        elif 'norm' in name:
            component = 'Final Layer Normalization'
        else:
            component = 'Other'
        
        if component not in component_params:
            component_params[component] = 0
        component_params[component] += info['params']
    
    for component, params in sorted(component_params.items()):
        percentage = (params / total_params) * 100
        print(f"  {component}: {params:,} ({percentage:.1f}%)")
    
    # 5. Attention Mechanism Details
    print("\n\n5. ATTENTION MECHANISM DETAILS:")
    print("-" * 40)
    
    print(f"Number of attention layers: {config.num_hidden_layers}")
    print(f"Number of attention heads: {config.num_attention_heads}")
    if hasattr(config, 'num_key_value_heads'):
        print(f"Number of key-value heads: {config.num_key_value_heads}")
        attention_type = 'Grouped-Query Attention (GQA)' if config.num_key_value_heads < config.num_attention_heads else 'Multi-Head Attention'
    else:
        print(f"Number of key-value heads: {config.num_attention_heads} (same as attention heads)")
        attention_type = 'Multi-Head Attention'
    
    print(f"Head dimension: {config.hidden_size // config.num_attention_heads}")
    print(f"Attention mechanism: {attention_type}")
    if hasattr(config, 'rope_theta'):
        print(f"RoPE (Rotary Position Embedding) theta: {config.rope_theta}")
    
    # 6. MLP/Feed-Forward Details
    print("\n\n6. MLP/FEED-FORWARD DETAILS:")
    print("-" * 40)
    
    print(f"Hidden size: {config.hidden_size}")
    print(f"Intermediate size: {config.intermediate_size}")
    print(f"Activation function: {config.hidden_act}")
    print(f"Expansion ratio: {config.intermediate_size / config.hidden_size:.1f}x")
    print("MLP structure: SwiGLU (Swish-Gated Linear Unit)")
    
    # 7. Detailed Parameter Shapes (first 25 for brevity)
    print("\n\n7. DETAILED PARAMETER SHAPES (first 25):")
    print("-" * 40)
    
    for i, info in enumerate(param_info[:25]):
        print(f"{info['name']}: {info['shape']} ({info['params']:,} params, {info['dtype']})")
    
    if len(param_info) > 25:
        print(f"... and {len(param_info) - 25} more parameters")
    
    # 8. Model Operations Summary
    print("\n\n8. KEY OPERATIONS IN MODEL:")
    print("-" * 40)
    
    operations = set()
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if any(op in module_type.lower() for op in ['linear', 'conv', 'norm', 'embed', 'dropout', 'activation']):
            operations.add(module_type)
    
    print("Key operation types found:")
    for op in sorted(operations):
        print(f"  - {op}")
    
    # 9. Llama-Specific Architecture Details
    print("\n\n9. LLAMA-SPECIFIC ARCHITECTURE DETAILS:")
    print("-" * 40)
    
    print("Key architectural features of Llama2:")
    print("  - RMSNorm instead of LayerNorm")
    print("  - SwiGLU activation function in MLP")
    print("  - RoPE (Rotary Position Embedding)")
    if hasattr(config, 'num_key_value_heads') and config.num_key_value_heads < config.num_attention_heads:
        print("  - Grouped-Query Attention (GQA) for efficiency")
    print("  - Pre-normalization (norm before attention/MLP)")
    
    # 10. Memory and Computational Estimates
    print("\n\n10. COMPUTATIONAL ESTIMATES:")
    print("-" * 40)
    
    # Rough FLOP estimates for a forward pass
    sequence_length = 1024  # Example sequence length
    batch_size = 1
    
    # Attention FLOPs (approximate)
    attention_flops = (
        4 * config.num_hidden_layers * 
        config.num_attention_heads * 
        sequence_length * sequence_length * 
        (config.hidden_size // config.num_attention_heads)
    )
    
    # MLP FLOPs (SwiGLU has 3 linear layers: gate, up, down)
    mlp_flops = (
        3 * config.num_hidden_layers * 
        sequence_length * 
        config.hidden_size * 
        config.intermediate_size
    )
    
    total_flops = attention_flops + mlp_flops
    
    print(f"Estimated FLOPs for sequence length {sequence_length}:")
    print(f"  Attention: {attention_flops / 1e9:.2f} GFLOPs")
    print(f"  MLP (SwiGLU): {mlp_flops / 1e9:.2f} GFLOPs")
    print(f"  Total: {total_flops / 1e9:.2f} GFLOPs")
    
    # Memory estimates
    print(f"\nMemory estimates for sequence length {sequence_length}:")
    activation_memory = (
        batch_size * sequence_length * config.hidden_size * config.num_hidden_layers * 4
    ) / (1024**3)
    
    print(f"  Activations: ~{activation_memory:.2f} GB")
    print(f"  Model weights: ~{total_params * 4 / (1024**3):.2f} GB (float32)")
    
    print("\n" + "=" * 80)
    print("✓ Llama2-7B model structure analysis complete!")


if __name__ == "__main__":
    analyze_llama_structure() 