from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import time
import random
import csv

login()  # Uncomment if you need to authenticate

tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-2-7b-hf")
model = AutoModelForCausalLM.from_pretrained("meta-llama/Llama-2-7b-hf")

# Base words to generate random text
base_words = ["machine", "learning", "artificial", "intelligence", "neural", "network", "data", "science", 
              "algorithm", "model", "training", "deep", "computer", "vision", "natural", "language", "processing",
              "transformer", "attention", "embedding", "token", "sequence", "generation", "inference", "optimization"]

def generate_random_text(target_length):
    """Generate random text with approximately target_length tokens"""
    text = ""
    current_tokens = 0
    
    while current_tokens < target_length:
        # Add random words until we reach target length
        word = random.choice(base_words)
        test_text = text + " " + word if text else word
        
        # Check token count
        tokens = tokenizer(test_text, return_tensors="pt")
        token_count = tokens['input_ids'].shape[1]
        
        if token_count <= target_length:
            text = test_text
            current_tokens = token_count
        else:
            break
    
    return text

# Open CSV file for writing
def scaling_llama():
    csv_filename = "scaling_llama_latency.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['input_length', 'latency_ms', 'output_text'])
        
        # Llama-2 has a context length of 4096 tokens
        end_length = 4096   # max length is 4096 for Llama-2
        step_length = 50
        print(f"Testing various input lengths from 1 to {end_length} tokens...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        # Test various input lengths from 1 to 4096 with step 50
        for i, target_length in enumerate(range(1, end_length, step_length)):
            # Generate random text with target length
            input_text = generate_random_text(target_length)
            input_ids = tokenizer(input_text, return_tensors="pt")
            
            # Get actual input length
            actual_input_length = input_ids['input_ids'].shape[1]
            
            # Measure inference latency
            start_time = time.time()
            outputs = model.generate(**input_ids, max_new_tokens=20, do_sample=False)  # Limit output for faster testing
            end_time = time.time()
            
            # Calculate latency in milliseconds
            latency_ms = (end_time - start_time) * 1000
            
            # Get generated text (only the new tokens)
            generated_text = tokenizer.decode(outputs[0][actual_input_length:], skip_special_tokens=True)
            
            # Write to CSV file
            writer.writerow([actual_input_length, f"{latency_ms:.2f}", generated_text.strip()])
            
            # Print progress
            total_steps = len(range(1, end_length, step_length))
            print(f"Progress: {i+1}/{total_steps} - Input length: {actual_input_length}, Latency: {latency_ms:.2f} ms")
            
            # Flush both file and stdout
            csvfile.flush()

    print(f"\nCompleted! Results saved to {csv_filename}")

def scaling_llama_extended():
    """Extended scaling analysis with additional metrics"""
    csv_filename = "scaling_llama_extended.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header with additional metrics
        writer.writerow(['input_length', 'latency_ms', 'tokens_per_second', 'memory_mb', 'output_text'])
        
        end_length = 4096
        step_length = 100  # Larger steps for extended analysis
        print(f"Running extended scaling analysis from 1 to {end_length} tokens...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        for i, target_length in enumerate(range(1, end_length, step_length)):
            # Generate random text with target length
            input_text = generate_random_text(target_length)
            input_ids = tokenizer(input_text, return_tensors="pt")
            actual_input_length = input_ids['input_ids'].shape[1]
            
            # Clear GPU cache if available
            if hasattr(model, 'cuda') and model.device.type == 'cuda':
                import torch
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            
            # Measure inference latency
            start_time = time.time()
            outputs = model.generate(**input_ids, max_new_tokens=50, do_sample=False)
            end_time = time.time()
            
            latency_ms = (end_time - start_time) * 1000
            tokens_per_second = 50 / (latency_ms / 1000)  # 50 new tokens generated
            
            # Estimate memory usage (rough approximation)
            memory_mb = actual_input_length * 4 * 4096 / (1024 * 1024)  # Rough estimate
            
            # Get generated text
            generated_text = tokenizer.decode(outputs[0][actual_input_length:], skip_special_tokens=True)
            
            # Write to CSV
            writer.writerow([
                actual_input_length, 
                f"{latency_ms:.2f}", 
                f"{tokens_per_second:.2f}",
                f"{memory_mb:.2f}",
                generated_text.strip()
            ])
            
            total_steps = len(range(1, end_length, step_length))
            print(f"Progress: {i+1}/{total_steps} - Input: {actual_input_length}, "
                  f"Latency: {latency_ms:.2f} ms, TPS: {tokens_per_second:.2f}")
            
            csvfile.flush()
    
    print(f"\nExtended analysis completed! Results saved to {csv_filename}")

if __name__ == "__main__":
    print("Choose scaling analysis type:")
    print("1. Basic scaling analysis (similar to Gemma)")
    print("2. Extended scaling analysis (with additional metrics)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        scaling_llama_extended()
    else:
        scaling_llama() 