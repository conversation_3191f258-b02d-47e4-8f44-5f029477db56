import torch
import time
import csv
import numpy as np
import os
from PIL import Image
import torchvision.transforms as transforms
from stylegan2_pytorch import ModelLoader

def load_stylegan_model():
    """Load pre-trained StyleGAN2 model"""
    # Using a pre-trained model from stylegan2-pytorch
    # You can change this to your preferred StyleGAN implementation
    loader = ModelLoader(
        base_dir = './',
        name = 'default'
    )
    
    # Load model
    model = loader.load()
    model.eval()
    
    # Move to GPU if available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    return model, device

def generate_random_latent(batch_size, latent_dim=512, device='cpu'):
    """Generate random latent vectors for StyleGAN input"""
    return torch.randn(batch_size, latent_dim).to(device)

def scaling_stylegan():
    """Measure StyleGAN inference latency across different batch sizes"""
    
    print("Loading StyleGAN model...")
    model, device = load_stylegan_model()
    print(f"Model loaded on device: {device}")
    
    csv_filename = "scaling_stylegan_latency.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['batch_size', 'latency_ms', 'memory_usage_mb', 'throughput_images_per_sec'])
        
        max_batch_size = 64  # Adjust based on your GPU memory
        step_size = 5
        
        print(f"Testing various batch sizes from 1 to {max_batch_size}...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        # Test various batch sizes
        for i, batch_size in enumerate(range(1, max_batch_size + 1, step_size)):
            try:
                # Generate random latent vectors
                latent_vectors = generate_random_latent(batch_size, device=device)
                
                # Clear GPU cache before measurement
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                
                # Measure GPU memory before inference
                memory_before = 0
                if torch.cuda.is_available():
                    memory_before = torch.cuda.memory_allocated()
                
                # Measure inference latency
                start_time = time.time()
                
                with torch.no_grad():
                    generated_images = model(latent_vectors)
                
                # Save generated images
                save_generated_images(generated_images, batch_size, i, output_dir=f"generated_images_batch_{batch_size}")
                
                # Synchronize GPU operations
                if torch.cuda.is_available():
                    torch.cuda.synchronize()
                
                end_time = time.time()
                
                # Measure GPU memory after inference
                memory_after = 0
                if torch.cuda.is_available():
                    memory_after = torch.cuda.memory_allocated()
                
                # Calculate metrics
                latency_ms = (end_time - start_time) * 1000
                memory_usage_mb = (memory_after - memory_before) / (1024 * 1024)
                throughput = batch_size / (latency_ms / 1000)  # images per second
                
                # Write to CSV file
                writer.writerow([
                    batch_size, 
                    f"{latency_ms:.2f}", 
                    f"{memory_usage_mb:.2f}",
                    f"{throughput:.2f}"
                ])
                
                # Print progress
                print(f"Progress: {i+1}/{max_batch_size} - Batch size: {batch_size}, "
                      f"Latency: {latency_ms:.2f} ms, "
                      f"Memory: {memory_usage_mb:.2f} MB, "
                      f"Throughput: {throughput:.2f} img/s")
                
                # Flush both file and stdout
                csvfile.flush()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"Out of memory at batch size {batch_size}. Stopping here.")
                    break
                else:
                    raise e
            except Exception as e:
                print(f"Error at batch size {batch_size}: {str(e)}")
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")

# Alternative implementation using different StyleGAN library
def scaling_stylegan_alternative():
    """Alternative implementation using torch hub StyleGAN"""
    
    print("Loading StyleGAN model from torch hub...")
    
    # Load pre-trained StyleGAN2 from torch hub
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # You can replace this with other StyleGAN implementations
    # model = torch.hub.load('facebookresearch/pytorch_GAN_zoo:hub', 'PGAN', pretrained=True)
    # For now, we'll create a simple mock model for demonstration
    
    class MockStyleGAN(torch.nn.Module):
        def __init__(self):
            super().__init__()
            # Simplified StyleGAN-like architecture
            self.layers = torch.nn.Sequential(
                torch.nn.Linear(512, 1024),
                torch.nn.ReLU(),
                torch.nn.Linear(1024, 2048),
                torch.nn.ReLU(),
                torch.nn.Linear(2048, 4096),
                torch.nn.ReLU(),
                torch.nn.Linear(4096, 3 * 256 * 256),  # RGB 256x256 images
                torch.nn.Tanh()
            )
        
        def forward(self, z):
            batch_size = z.size(0)
            output = self.layers(z)
            return output.view(batch_size, 3, 256, 256)
    
    model = MockStyleGAN().to(device)
    model.eval()
    
    csv_filename = "scaling_stylegan_latency.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['batch_size', 'latency_ms', 'memory_usage_mb', 'throughput_images_per_sec'])
        
        max_batch_size = 3200  # Adjust based on your GPU memory
        step_size = 50
        
        print(f"Testing various batch sizes from 1 to {max_batch_size}...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        # Test various batch sizes
        for i, batch_size in enumerate(range(1, max_batch_size + 1, step_size)):
            try:
                # Generate random latent vectors
                latent_vectors = generate_random_latent(batch_size, device=device)
                
                # Clear GPU cache before measurement
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                
                # Measure GPU memory before inference
                memory_before = 0
                if torch.cuda.is_available():
                    memory_before = torch.cuda.memory_allocated()
                
                # Measure inference latency
                start_time = time.time()
                
                with torch.no_grad():
                    generated_images = model(latent_vectors)
                
                # Synchronize GPU operations
                if torch.cuda.is_available():
                    torch.cuda.synchronize()
                
                end_time = time.time()

                # Save generated images
                save_generated_images(generated_images, batch_size, i, output_dir=f"generated_images_batch_{batch_size}")
                
                # Measure GPU memory after inference
                memory_after = 0
                if torch.cuda.is_available():
                    memory_after = torch.cuda.memory_allocated()
                
                # Calculate metrics
                latency_ms = (end_time - start_time) * 1000
                memory_usage_mb = (memory_after - memory_before) / (1024 * 1024)
                throughput = batch_size / (latency_ms / 1000)  # images per second
                
                # Write to CSV file
                writer.writerow([
                    batch_size, 
                    f"{latency_ms:.2f}", 
                    f"{memory_usage_mb:.2f}",
                    f"{throughput:.2f}"
                ])
                
                # Print progress
                print(f"Progress: {i * step_size + 1}/{max_batch_size} - Batch size: {batch_size}, "
                      f"Latency: {latency_ms:.2f} ms, "
                      f"Memory: {memory_usage_mb:.2f} MB, "
                      f"Throughput: {throughput:.2f} img/s")
                
                # Flush both file and stdout
                csvfile.flush()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"Out of memory at batch size {batch_size}. Stopping here.")
                    break
                else:
                    raise e
            except Exception as e:
                print(f"Error at batch size {batch_size}: {str(e)}")
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")

def save_generated_images(generated_images, batch_size, iteration, output_dir="generated_images"):
    """Save generated images to disk"""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Convert from tensor to PIL images and save
    # Assuming generated_images is in range [-1, 1] or [0, 1]
    images = generated_images.cpu()
    
    # Normalize to [0, 1] if needed (assuming output is in [-1, 1])
    if images.min() < 0:
        images = (images + 1) / 2
    
    # Clamp to [0, 1]
    images = torch.clamp(images, 0, 1)
    
    # Convert to PIL and save each image
    to_pil = transforms.ToPILImage()
    
    for i in range(batch_size):
        img = to_pil(images[i])
        filename = f"batch_{batch_size}_iter_{iteration}_img_{i}.png"
        filepath = os.path.join(output_dir, filename)
        img.save(filepath)
    
    print(f"Saved {batch_size} images to {output_dir}/")

if __name__ == "__main__":
    # Use the alternative implementation with mock model
    # Replace with scaling_stylegan() if you have stylegan2_pytorch installed
    scaling_stylegan_alternative() 