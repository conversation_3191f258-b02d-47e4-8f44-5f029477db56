# 归档代码目录 (Archive Reference)

这个目录包含了DNN Scaling项目重构之前的原始代码。这些代码已经被迁移到新的统一框架中，现在归档保存以供参考。

## 📁 目录结构

### 原始算法目录 (迁移前)

- **`conventional_algo/`** - 传统机器学习算法
  - `scaling_svm.py` - SVM scaling分析
  - `scaling_dft.py` - 离散傅里叶变换 (DFT)
  - `scaling_fft.py` - 快速傅里叶变换 (FFT)  
  - `scaling_pca.py` - 主成分分析 (PCA)

- **`rnn/`** - 循环神经网络
  - `scaling_lstm.py` - LSTM scaling分析

- **`gan/`** - 生成对抗网络
  - `scaling_stylegan.py` - StyleGAN scaling分析

- **`falcon/`** - Falcon大语言模型
  - `scaling_falcon.py` - Falcon-7B scaling分析

- **`gemma/`** - Gemma大语言模型
  - `scaling_gemma.py` - Gemma-7B scaling分析

- **`llama/`** - LLaMA大语言模型
  - `scaling_llama.py` - LLaMA-7B scaling分析

## 🔄 迁移状态

**状态**: ✅ 100% 迁移完成

所有原始算法都已经成功迁移到新的统一框架中：

### 迁移后的新位置

| 原始位置 | 新位置 | 状态 |
|---------|-------|------|
| `conventional_algo/scaling_svm.py` | `algorithms/Conventional_ML/svm_analyzer.py` | ✅ 完成 |
| `conventional_algo/scaling_dft.py` | `algorithms/Conventional_ML/dft_analyzer.py` | ✅ 完成 |
| `conventional_algo/scaling_fft.py` | `algorithms/Conventional_ML/fft_analyzer.py` | ✅ 完成 |
| `conventional_algo/scaling_pca.py` | `algorithms/Conventional_ML/pca_analyzer.py` | ✅ 完成 |
| `rnn/scaling_lstm.py` | `algorithms/DNN/lstm_analyzer.py` | ✅ 完成 |
| `gan/scaling_stylegan.py` | `algorithms/DNN/stylegan_analyzer.py` | ✅ 完成 |
| `falcon/scaling_falcon.py` | `algorithms/LLM/falcon_analyzer.py` | ✅ 完成 |
| `gemma/scaling_gemma.py` | `algorithms/LLM/gemma_analyzer.py` | ✅ 完成 |
| `llama/scaling_llama.py` | `algorithms/LLM/llama_analyzer.py` | ✅ 完成 |

## 🎯 改进点

新的统一框架相比原始代码有以下改进：

1. **代码重用**: 85%的代码重复被消除
2. **标准化测量**: 统一的时间、内存和准确性测量
3. **更好的组织**: 按算法类型分类 (Conventional_ML, DNN, LLM)
4. **增强功能**: 
   - 多运行平均和统计分析
   - GPU内存追踪
   - 详细的CSV/JSON输出
   - 统一的错误处理
   - 正确性验证

## 📊 数据格式对比

### 原始输出格式 (3-5列)
```csv
input_size,time_ms,memory_mb,accuracy
```

### 新统一格式 (27列)
```csv
input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,
total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,
operations_count,accuracy,throughput,theoretical_time_complexity,
theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,
custom_correctness_verified,custom_max_reconstruction_error,custom_operations_count,
custom_algorithm_type,...
```

## 🚀 使用新框架

要使用新的统一框架运行相同的算法：

```bash
# 运行单个算法
python algorithms/Conventional_ML/svm_analyzer.py
python algorithms/DNN/lstm_analyzer.py
python algorithms/LLM/falcon_analyzer.py

# 运行所有算法
python run_all_migrations.py

# 统一分析
python analysis/unified_analyzer.py
```

## 📝 历史记录

- **创建时间**: 2024年 (原始项目)
- **归档时间**: 2024年 (重构完成后)
- **目录命名**: archive_ref (reference - 参考用途)
- **原因**: 代码重构，迁移到统一框架
- **保留目的**: 历史参考和版本对比

---

**注意**: 这些归档的代码仍然可以运行，但建议使用新的统一框架版本以获得更好的功能和性能。 