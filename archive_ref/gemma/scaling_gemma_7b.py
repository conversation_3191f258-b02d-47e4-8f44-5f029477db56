from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import time
import random
import csv

# login()

tokenizer = AutoTokenizer.from_pretrained("google/gemma-7b")
model = AutoModelForCausalLM.from_pretrained("google/gemma-7b")

# Base words to generate random text
base_words = ["machine", "learning", "artificial", "intelligence", "neural", "network", "data", "science", 
              "algorithm", "model", "training", "deep", "computer", "vision", "natural", "language", "processing"]

def generate_random_text(target_length):
    """Generate random text with approximately target_length tokens"""
    text = ""
    current_tokens = 0
    
    while current_tokens < target_length:
        # Add random words until we reach target length
        word = random.choice(base_words)
        test_text = text + " " + word if text else word
        
        # Check token count
        tokens = tokenizer(test_text, return_tensors="pt")
        token_count = tokens['input_ids'].shape[1]
        
        if token_count <= target_length:
            text = test_text
            current_tokens = token_count
        else:
            break
    
    return text

# Open CSV file for writing
def scaling_gemma():
    csv_filename = "scaling_gemma_latency.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['input_length', 'latency_ms', 'output_text'])
        

        end_length = 8192   # max length is 8192
        step_length = 50
        print(f"Testing various input lengths from 1 to {end_length} tokens...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        

        # Test various input lengths from 10 to 5000 with step 50
        for i, target_length in enumerate(range(1, end_length, step_length)):
            # Generate random text with target length
            input_text = generate_random_text(target_length)
            input_ids = tokenizer(input_text, return_tensors="pt")
            
            # Get actual input length
            actual_input_length = input_ids['input_ids'].shape[1]
            
            # Measure inference latency
            start_time = time.time()
            outputs = model.generate(**input_ids, max_new_tokens=20, do_sample=False)  # Limit output for faster testing
            end_time = time.time()
            
            # Calculate latency in milliseconds
            latency_ms = (end_time - start_time) * 1000
            
            # Get generated text (only the new tokens)
            generated_text = tokenizer.decode(outputs[0][actual_input_length:], skip_special_tokens=True)
            
            # Write to CSV file
            writer.writerow([actual_input_length, f"{latency_ms:.2f}", generated_text.strip()])
            
            # Print progress
            print(f"Progress: {i+1}/{(end_length-1)/step_length} - Input length: {actual_input_length}, Latency: {latency_ms:.2f} ms")
            
            # Flush both file and stdout
            csvfile.flush()

    print(f"\nCompleted! Results saved to {csv_filename}")


if __name__ == "__main__":
    scaling_gemma()