from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import torch
import json

# login()  # Uncomment if you need to authenticate

def analyze_gemma_structure():
    print("Loading Gemma model...")
    print("=" * 80)
    
    # Load tokenizer and model
    tokenizer = AutoTokenizer.from_pretrained("google/gemma-7b")
    model = AutoModelForCausalLM.from_pretrained("google/gemma-7b")
    
    print("✓ Model loaded successfully!")
    print("=" * 80)
    
    # 1. Model Configuration
    print("\n1. MODEL CONFIGURATION:")
    print("-" * 40)
    config = model.config
    config_dict = config.to_dict()
    
    # Print key configuration parameters
    key_params = [
        'model_type', 'vocab_size', 'hidden_size', 'intermediate_size', 
        'num_hidden_layers', 'num_attention_heads', 'num_key_value_heads',
        'max_position_embeddings', 'rope_theta', 'attention_dropout',
        'hidden_activation', 'initializer_range', 'rms_norm_eps'
    ]
    
    for param in key_params:
        if param in config_dict:
            print(f"{param}: {config_dict[param]}")
    
    # 2. Model Structure Overview
    print("\n\n2. MODEL STRUCTURE OVERVIEW:")
    print("-" * 40)
    print(model)
    
    # 3. Layer Analysis
    print("\n\n3. LAYER-BY-LAYER ANALYSIS:")
    print("-" * 40)
    
    # Count different types of layers
    layer_types = {}
    total_params = 0
    
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if module_type not in layer_types:
            layer_types[module_type] = 0
        layer_types[module_type] += 1
    
    print("Layer types and counts:")
    for layer_type, count in sorted(layer_types.items()):
        print(f"  {layer_type}: {count}")
    
    # 4. Parameter Analysis
    print("\n\n4. PARAMETER ANALYSIS:")
    print("-" * 40)
    
    param_info = []
    total_params = 0
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        param_info.append({
            'name': name,
            'shape': list(param.shape),
            'params': param_count,
            'dtype': str(param.dtype)
        })
    
    print(f"Total parameters: {total_params:,}")
    print(f"Model size (approximate): {total_params * 4 / (1024**3):.2f} GB (float32)")
    
    # Group parameters by layer type
    print("\nParameter breakdown by component:")
    component_params = {}
    
    for info in param_info:
        name = info['name']
        if 'embed' in name:
            component = 'Embeddings'
        elif 'layers' in name:
            if 'self_attn' in name:
                component = 'Self-Attention'
            elif 'mlp' in name:
                component = 'MLP/Feed-Forward'
            elif 'input_layernorm' in name or 'post_attention_layernorm' in name:
                component = 'Layer Normalization'
            else:
                component = 'Other Transformer Layers'
        elif 'lm_head' in name:
            component = 'Language Model Head'
        else:
            component = 'Other'
        
        if component not in component_params:
            component_params[component] = 0
        component_params[component] += info['params']
    
    for component, params in sorted(component_params.items()):
        percentage = (params / total_params) * 100
        print(f"  {component}: {params:,} ({percentage:.1f}%)")
    
    # 5. Attention Mechanism Details
    print("\n\n5. ATTENTION MECHANISM DETAILS:")
    print("-" * 40)
    
    print(f"Number of attention layers: {config.num_hidden_layers}")
    print(f"Number of attention heads: {config.num_attention_heads}")
    print(f"Number of key-value heads: {config.num_key_value_heads}")
    print(f"Head dimension: {config.hidden_size // config.num_attention_heads}")
    print(f"Attention mechanism: {'Multi-Query' if config.num_key_value_heads < config.num_attention_heads else 'Multi-Head'}")
    
    # 6. MLP/Feed-Forward Details
    print("\n\n6. MLP/FEED-FORWARD DETAILS:")
    print("-" * 40)
    
    print(f"Hidden size: {config.hidden_size}")
    print(f"Intermediate size: {config.intermediate_size}")
    print(f"Activation function: {config.hidden_activation}")
    print(f"Expansion ratio: {config.intermediate_size / config.hidden_size:.1f}x")
    
    # 7. Detailed Parameter Shapes (first 20 for brevity)
    print("\n\n7. DETAILED PARAMETER SHAPES (first 20):")
    print("-" * 40)
    
    for i, info in enumerate(param_info[:20]):
        print(f"{info['name']}: {info['shape']} ({info['params']:,} params, {info['dtype']})")
    
    if len(param_info) > 20:
        print(f"... and {len(param_info) - 20} more parameters")
    
    # 8. Model Operations Summary
    print("\n\n8. KEY OPERATIONS IN MODEL:")
    print("-" * 40)
    
    operations = set()
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if any(op in module_type.lower() for op in ['linear', 'conv', 'norm', 'embed', 'dropout', 'activation']):
            operations.add(module_type)
    
    print("Key operation types found:")
    for op in sorted(operations):
        print(f"  - {op}")
    
    # TODO: Need more careful analysis for the memory and computational estimates
    # 9. Memory and Computational Estimates
    print("\n\n9. COMPUTATIONAL ESTIMATES:")
    print("-" * 40)
    
    # Rough FLOP estimates for a forward pass
    sequence_length = 1024  # Example sequence length
    batch_size = 1
    
    # Attention FLOPs (approximate)
    attention_flops = (
        4 * config.num_hidden_layers * 
        config.num_attention_heads * 
        sequence_length * sequence_length * 
        config.hidden_size
    )
    
    # MLP FLOPs (approximate)  
    mlp_flops = (
        2 * config.num_hidden_layers * 
        sequence_length * 
        config.hidden_size * 
        config.intermediate_size
    )
    
    total_flops = attention_flops + mlp_flops
    
    print(f"Estimated FLOPs for sequence length {sequence_length}:")
    print(f"  Attention: {attention_flops / 1e9:.2f} GFLOPs")
    print(f"  MLP: {mlp_flops / 1e9:.2f} GFLOPs")
    print(f"  Total: {total_flops / 1e9:.2f} GFLOPs")
    
    print("\n" + "=" * 80)
    print("✓ Model structure analysis complete!")
    

if __name__ == "__main__":
    analyze_gemma_structure() 