import torch
import torch.nn as nn
import time
import random
import csv
import numpy as np
import gc

class SimpleLSTM(nn.Module):
    def __init__(self, input_size=128, hidden_size=256, num_layers=2):
        super(SimpleLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
    def forward(self, x):
        # Ensure input is on CPU
        x = x.cpu()
        
        # Initialize hidden state with zeros on CPU
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).cpu()
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).cpu()
        
        # Forward propagate LSTM only
        out, (hn, cn) = self.lstm(x, (h0, c0))
        
        return out, (hn, cn)

def generate_random_sequence(seq_length, input_size=128, batch_size=1):
    """Generate random input sequence with specified length on CPU"""
    # Generate random values between -1 and 1, explicitly on CPU
    sequence = torch.randn(batch_size, seq_length, input_size, device='cpu')
    return sequence

def measure_latency_stable(model, input_sequence, num_runs=10, warmup_runs=3):
    """Measure CPU latency with multiple runs and warmup for stable results"""
    
    # Ensure input sequence is on CPU
    input_sequence = input_sequence.cpu()
    
    # Warmup runs to eliminate cold start effects
    for _ in range(warmup_runs):
        with torch.no_grad():
            _ = model(input_sequence)
    
    # Clear any cached memory (CPU only)
    gc.collect()
    
    # Multiple measurement runs
    latencies = []
    for _ in range(num_runs):
        # Force garbage collection before each measurement
        gc.collect()
        
        start_time = time.perf_counter()  # More precise timer
        
        with torch.no_grad():
            lstm_output, (hidden_state, cell_state) = model(input_sequence)
        
        # CPU computation is synchronous, no need for synchronization
        end_time = time.perf_counter()
        
        latency_ms = (end_time - start_time) * 1000
        latencies.append(latency_ms)
    
    # Return statistics
    return {
        'mean': np.mean(latencies),
        'std': np.std(latencies),
        'min': np.min(latencies),
        'max': np.max(latencies),
        'median': np.median(latencies),
        'raw_measurements': latencies
    }

def scaling_lstm_stable():
    """Test LSTM inference latency with stable measurements on CPU"""
    
    # Force CPU-only execution
    torch.set_default_device('cpu')
    
    # Set deterministic behavior for reproducibility
    torch.manual_seed(42)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    # Initialize model on CPU
    print("Initializing LSTM model on CPU...")
    model = SimpleLSTM(input_size=128, hidden_size=256, num_layers=2)
    model = model.cpu()  # Explicitly move to CPU
    model.eval()  # Set to evaluation mode
    
    print("Device confirmation:")
    print(f"- Model device: CPU (forced)")
    print(f"- PyTorch default device: {torch.get_default_device()}")
    print(f"- CUDA available but not used: {torch.cuda.is_available()}")
    print()
    
    csv_filename = "scaling_lstm_stable_latency.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header with more detailed statistics
        writer.writerow(['sequence_length', 'mean_latency_ms', 'std_latency_ms', 
                        'min_latency_ms', 'max_latency_ms', 'median_latency_ms', 'cv'])
        
        end_length = 6000   # Reduced for faster testing
        step_length = 200   # Larger steps for fewer measurements
        num_runs = 10       # Multiple measurements per length
        
        print(f"Testing sequence lengths from 1 to {end_length} with {num_runs} runs each...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 80)
        
        # Test various sequence lengths
        sequence_lengths = list(range(1, end_length, step_length))
        
        for i, seq_length in enumerate(sequence_lengths):
            # Generate random input sequence
            input_sequence = generate_random_sequence(seq_length)
            
            # Measure latency with multiple runs
            stats = measure_latency_stable(model, input_sequence, num_runs=num_runs)
            
            # Calculate coefficient of variation
            cv = stats['std'] / stats['mean'] if stats['mean'] > 0 else 0
            
            # Write to CSV file
            writer.writerow([
                seq_length, 
                f"{stats['mean']:.3f}", 
                f"{stats['std']:.3f}",
                f"{stats['min']:.3f}",
                f"{stats['max']:.3f}", 
                f"{stats['median']:.3f}",
                f"{cv:.3f}"
            ])
            
            # Print progress with variance info
            print(f"Progress: {i+1:2d}/{len(sequence_lengths)} - "
                  f"Length: {seq_length:4d}, "
                  f"Mean: {stats['mean']:6.2f}ms, "
                  f"Std: {stats['std']:5.2f}ms, "
                  f"CV: {cv:.3f}")
            
            # Flush both file and stdout
            csvfile.flush()
    
    print(f"\nCompleted! CPU-only stable results saved to {csv_filename}")
    print("\nKey improvements for CPU time complexity measurement:")
    print("- CPU-only execution (no GPU interference)")
    print("- Multiple measurements per sequence length")
    print("- Warmup runs to eliminate cold start")
    print("- Garbage collection between measurements")
    print("- More precise timing with time.perf_counter()")
    print("- Coefficient of variation (CV) to measure stability")

if __name__ == "__main__":
    scaling_lstm_stable() 