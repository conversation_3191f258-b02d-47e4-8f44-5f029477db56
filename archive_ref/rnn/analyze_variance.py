import csv
import numpy as np

# Read the CSV data
sequence_lengths = []
latencies = []

with open('scaling_lstm_latency.csv', 'r') as f:
    reader = csv.reader(f)
    next(reader)  # Skip header
    for row in reader:
        sequence_lengths.append(int(row[0]))
        latencies.append(float(row[1]))

print("LSTM Latency Variance Analysis")
print("=" * 50)

# Print some examples of inconsistency
print("Examples showing inconsistent measurements:")
for i in range(0, min(15, len(sequence_lengths))):
    print(f"Length {sequence_lengths[i]:4d}: {latencies[i]:6.2f}ms")

print("\nStatistics:")
print(f"Mean latency: {np.mean(latencies):.2f}ms")
print(f"Std deviation: {np.std(latencies):.2f}ms")
print(f"Min latency: {np.min(latencies):.2f}ms")
print(f"Max latency: {np.max(latencies):.2f}ms")
print(f"Coefficient of variation: {np.std(latencies)/np.mean(latencies):.2f}")

# Check for cases where longer sequences are faster than shorter ones
inconsistent_cases = 0
for i in range(1, len(sequence_lengths)):
    if latencies[i] < latencies[i-1] and sequence_lengths[i] > sequence_lengths[i-1]:
        inconsistent_cases += 1

print(f"\nInconsistent cases (longer seq faster than shorter): {inconsistent_cases}")
print(f"Percentage of inconsistent cases: {inconsistent_cases/(len(sequence_lengths)-1)*100:.1f}%") 