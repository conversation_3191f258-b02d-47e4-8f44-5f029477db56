signal_length,fft_latency_ms,ifft_latency_ms,baseline_memory_mb,after_signal_gen_mb,signal_gen_increment_mb,after_fft_mb,fft_increment_mb,after_ifft_mb,ifft_increment_mb,peak_memory_mb,total_increment_mb,theoretical_input_mb,theoretical_fft_algorithm_mb,theoretical_ifft_algorithm_mb,theoretical_total_mb,memory_efficiency_ratio
16,1.32,0.04,25.98,32.42,6.44,32.92,0.50,32.92,0.00,32.92,6.94,0.00,0.00,0.00,0.00,0.00
32,0.11,0.09,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.00,0.00,0.00,0.00,0.00
64,0.08,0.07,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.00,0.00,0.00,0.00,0.00
128,0.06,0.06,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.00,0.00,0.00,0.01,0.00
256,0.10,0.09,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.00,0.01,0.01,0.02,0.00
512,0.12,0.12,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.01,0.01,0.01,0.03,0.00
1024,0.09,0.10,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.02,0.02,0.02,0.06,0.00
2048,0.19,0.24,32.92,32.92,0.00,32.92,0.00,32.92,0.00,32.92,0.00,0.03,0.05,0.05,0.12,0.00
4096,0.42,0.37,32.92,33.52,0.60,33.52,0.00,33.52,0.00,33.52,0.60,0.06,0.09,0.09,0.25,0.42
8192,0.62,0.76,33.52,33.78,0.26,33.78,0.00,33.78,0.00,33.78,0.26,0.12,0.19,0.19,0.50,1.94
16384,1.03,1.12,33.47,34.14,0.67,34.32,0.18,34.57,0.25,34.57,1.11,0.25,0.38,0.38,1.00,0.90
32768,2.69,2.37,33.82,34.33,0.50,34.83,0.50,35.33,0.50,35.33,1.51,0.50,0.75,0.75,2.00,1.33
65536,3.68,3.15,34.32,34.83,0.50,35.83,1.00,36.83,1.00,36.83,2.51,1.00,1.50,1.50,4.00,1.60
131072,7.28,6.38,34.82,35.83,1.00,37.83,2.00,39.83,2.00,39.83,5.01,2.00,3.00,3.00,8.00,1.60
262144,14.53,12.73,35.82,37.83,2.00,41.83,4.00,45.83,4.00,45.83,10.01,4.00,6.00,6.00,16.00,1.60
524288,32.68,28.37,37.82,41.83,4.00,49.83,8.00,57.83,8.00,57.83,20.01,8.00,12.00,12.00,32.00,1.60
1048576,49.53,40.29,41.82,49.83,8.00,65.83,16.00,81.83,16.00,81.83,40.01,16.00,24.00,24.00,64.00,1.60
