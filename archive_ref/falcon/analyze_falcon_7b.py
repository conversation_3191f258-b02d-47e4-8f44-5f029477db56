from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import torch
import json

# login()  # Uncomment if you need to authenticate

def analyze_falcon_structure():
    print("Loading Falcon-7B model...")
    print("=" * 80)
    
    # Load tokenizer and model (trust_remote_code required for <PERSON>)
    tokenizer = AutoTokenizer.from_pretrained("tiiuae/falcon-7b")
    model = AutoModelForCausalLM.from_pretrained("tiiuae/falcon-7b", trust_remote_code=True)
    
    print("✓ Model loaded successfully!")
    print("=" * 80)
    
    # 1. Model Configuration
    print("\n1. MODEL CONFIGURATION:")
    print("-" * 40)
    config = model.config
    config_dict = config.to_dict()
    
    # Print key configuration parameters for Falcon
    key_params = [
        'model_type', 'vocab_size', 'hidden_size', 'num_attention_heads', 'num_hidden_layers',
        'max_position_embeddings', 'attention_dropout', 'hidden_dropout',
        'activation_function', 'initializer_range', 'layer_norm_epsilon',
        'use_cache', 'bos_token_id', 'eos_token_id', 'alibi', 'new_decoder_architecture',
        'parallel_attn', 'bias', 'num_kv_heads'
    ]
    
    for param in key_params:
        if param in config_dict:
            print(f"{param}: {config_dict[param]}")
    
    # 2. Model Structure Overview
    print("\n\n2. MODEL STRUCTURE OVERVIEW:")
    print("-" * 40)
    print(model)
    
    # 3. Layer Analysis
    print("\n\n3. LAYER-BY-LAYER ANALYSIS:")
    print("-" * 40)
    
    # Count different types of layers
    layer_types = {}
    
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if module_type not in layer_types:
            layer_types[module_type] = 0
        layer_types[module_type] += 1
    
    print("Layer types and counts:")
    for layer_type, count in sorted(layer_types.items()):
        print(f"  {layer_type}: {count}")
    
    # 4. Parameter Analysis
    print("\n\n4. PARAMETER ANALYSIS:")
    print("-" * 40)
    
    param_info = []
    total_params = 0
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count
        param_info.append({
            'name': name,
            'shape': list(param.shape),
            'params': param_count,
            'dtype': str(param.dtype)
        })
    
    print(f"Total parameters: {total_params:,}")
    print(f"Model size (approximate): {total_params * 4 / (1024**3):.2f} GB (float32)")
    print(f"Model size (approximate): {total_params * 2 / (1024**3):.2f} GB (float16)")
    
    # Group parameters by layer type
    print("\nParameter breakdown by component:")
    component_params = {}
    
    for info in param_info:
        name = info['name']
        if 'word_embeddings' in name:
            component = 'Embeddings'
        elif 'h.' in name:  # Falcon uses 'h.' for transformer layers
            if 'self_attention' in name:
                component = 'Self-Attention'
            elif 'mlp' in name:
                component = 'MLP/Feed-Forward'
            elif 'ln' in name or 'input_layernorm' in name:
                component = 'Layer Normalization'
            else:
                component = 'Other Transformer Layers'
        elif 'lm_head' in name:
            component = 'Language Model Head'
        elif 'ln_f' in name:
            component = 'Final Layer Normalization'
        else:
            component = 'Other'
        
        if component not in component_params:
            component_params[component] = 0
        component_params[component] += info['params']
    
    for component, params in sorted(component_params.items()):
        percentage = (params / total_params) * 100
        print(f"  {component}: {params:,} ({percentage:.1f}%)")
    
    # 5. Attention Mechanism Details
    print("\n\n5. ATTENTION MECHANISM DETAILS:")
    print("-" * 40)
    
    print(f"Number of attention layers: {config.num_hidden_layers}")
    print(f"Number of attention heads: {config.num_attention_heads}")
    
    # Calculate head dimension
    head_dim = config.hidden_size // config.num_attention_heads
    print(f"Head dimension: {head_dim}")
    
    # Check for ALiBi (Attention with Linear Biases)
    if hasattr(config, 'alibi') and config.alibi:
        print("Position encoding: ALiBi (Attention with Linear Biases)")
    else:
        print("Position encoding: Learned position embeddings")
    
    # Check for parallel attention architecture
    if hasattr(config, 'parallel_attn') and config.parallel_attn:
        print("Architecture: Parallel attention (attention and MLP in parallel)")
    else:
        print("Architecture: Sequential attention (attention then MLP)")
    
    print(f"Attention mechanism: Multi-Head Attention")
    
    # 6. MLP/Feed-Forward Details
    print("\n\n6. MLP/FEED-FORWARD DETAILS:")
    print("-" * 40)
    
    print(f"Hidden size: {config.hidden_size}")
    
    # Falcon typically uses 4 * hidden_size for intermediate size
    intermediate_size = 4 * config.hidden_size
    print(f"Intermediate size: {intermediate_size} (estimated)")
    print(f"Activation function: {getattr(config, 'activation_function', 'gelu')}")
    print(f"Expansion ratio: 4.0x")
    
    # 7. Detailed Parameter Shapes (first 25 for brevity)
    print("\n\n7. DETAILED PARAMETER SHAPES (first 25):")
    print("-" * 40)
    
    for i, info in enumerate(param_info[:25]):
        print(f"{info['name']}: {info['shape']} ({info['params']:,} params, {info['dtype']})")
    
    if len(param_info) > 25:
        print(f"... and {len(param_info) - 25} more parameters")
    
    # 8. Model Operations Summary
    print("\n\n8. KEY OPERATIONS IN MODEL:")
    print("-" * 40)
    
    operations = set()
    for name, module in model.named_modules():
        module_type = type(module).__name__
        if any(op in module_type.lower() for op in ['linear', 'conv', 'norm', 'embed', 'dropout', 'activation']):
            operations.add(module_type)
    
    print("Key operation types found:")
    for op in sorted(operations):
        print(f"  - {op}")
    
    # 9. Falcon-Specific Architecture Details
    print("\n\n9. FALCON-SPECIFIC ARCHITECTURE DETAILS:")
    print("-" * 40)
    
    print("Key architectural features of Falcon:")
    print("  - LayerNorm (standard layer normalization)")
    if hasattr(config, 'alibi') and config.alibi:
        print("  - ALiBi (Attention with Linear Biases) for position encoding")
    else:
        print("  - Learned position embeddings")
    
    if hasattr(config, 'parallel_attn') and config.parallel_attn:
        print("  - Parallel attention architecture (attention and MLP computed in parallel)")
    
    if hasattr(config, 'new_decoder_architecture') and config.new_decoder_architecture:
        print("  - New decoder architecture")
    
    print("  - Multi-Query Attention in some variants")
    print("  - GELU activation function")
    
    # 10. Memory and Computational Estimates
    print("\n\n10. COMPUTATIONAL ESTIMATES:")
    print("-" * 40)
    
    # Rough FLOP estimates for a forward pass
    sequence_length = 1024  # Example sequence length
    batch_size = 1
    
    # Attention FLOPs (approximate)
    attention_flops = (
        4 * config.num_hidden_layers * 
        config.num_attention_heads * 
        sequence_length * sequence_length * 
        head_dim
    )
    
    # MLP FLOPs (2 linear layers in standard MLP)
    mlp_flops = (
        2 * config.num_hidden_layers * 
        sequence_length * 
        config.hidden_size * 
        intermediate_size
    )
    
    total_flops = attention_flops + mlp_flops
    
    print(f"Estimated FLOPs for sequence length {sequence_length}:")
    print(f"  Attention: {attention_flops / 1e9:.2f} GFLOPs")
    print(f"  MLP: {mlp_flops / 1e9:.2f} GFLOPs")
    print(f"  Total: {total_flops / 1e9:.2f} GFLOPs")
    
    # Memory estimates
    print(f"\nMemory estimates for sequence length {sequence_length}:")
    activation_memory = (
        batch_size * sequence_length * config.hidden_size * config.num_hidden_layers * 4
    ) / (1024**3)
    
    # KV cache memory (approximate)
    kv_cache_memory = (
        2 * batch_size * sequence_length * config.num_attention_heads * head_dim * config.num_hidden_layers * 4
    ) / (1024**3)
    
    print(f"  Activations: ~{activation_memory:.2f} GB")
    print(f"  KV Cache: ~{kv_cache_memory:.2f} GB")
    print(f"  Model weights: ~{total_params * 4 / (1024**3):.2f} GB (float32)")
    
    print("\n" + "=" * 80)
    print("✓ Falcon-7B model structure analysis complete!")


if __name__ == "__main__":
    analyze_falcon_structure() 