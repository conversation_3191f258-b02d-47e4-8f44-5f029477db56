from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import login
import time
import random
import csv
import torch

# login()  # Uncomment if you need to authenticate

tokenizer = AutoTokenizer.from_pretrained("tiiuae/falcon-7b")
model = AutoModelForCausalLM.from_pretrained("tiiuae/falcon-7b", trust_remote_code=True)

# Base words to generate random text
base_words = ["machine", "learning", "artificial", "intelligence", "neural", "network", "data", "science", 
              "algorithm", "model", "training", "deep", "computer", "vision", "natural", "language", "processing",
              "transformer", "attention", "embedding", "token", "sequence", "generation", "inference", "optimization",
              "falcon", "performance", "efficiency", "scalability", "computation", "memory", "throughput"]

def generate_random_text(target_length):
    """Generate random text with approximately target_length tokens"""
    text = ""
    current_tokens = 0
    
    while current_tokens < target_length:
        # Add random words until we reach target length
        word = random.choice(base_words)
        test_text = text + " " + word if text else word
        
        # Check token count
        tokens = tokenizer(test_text, return_tensors="pt")
        token_count = tokens['input_ids'].shape[1]
        
        if token_count <= target_length:
            text = test_text
            current_tokens = token_count
        else:
            break
    
    return text

def scaling_falcon():
    """Basic scaling analysis for Falcon model"""
    csv_filename = "scaling_falcon_latency.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['input_length', 'latency_ms', 'output_text'])
        
        # Falcon-7b has a context length of 2048 tokens
        end_length = 2048   # max length is 2048 for Falcon-7b
        step_length = 50
        print(f"Testing Falcon model with various input lengths from 1 to {end_length} tokens...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        # Test various input lengths from 1 to 2048 with step 50
        for i, target_length in enumerate(range(1, end_length, step_length)):
            try:
                # Generate random text with target length
                input_text = generate_random_text(target_length)
                input_ids = tokenizer(input_text, return_tensors="pt")
                
                # Get actual input length
                actual_input_length = input_ids['input_ids'].shape[1]
                
                # Move to GPU if available
                if torch.cuda.is_available():
                    input_ids = {k: v.cuda() for k, v in input_ids.items()}
                
                # Measure inference latency
                start_time = time.time()
                with torch.no_grad():
                    outputs = model.generate(**input_ids, max_new_tokens=20, do_sample=False, pad_token_id=tokenizer.eos_token_id)
                end_time = time.time()
                
                # Calculate latency in milliseconds
                latency_ms = (end_time - start_time) * 1000
                
                # Get generated text (only the new tokens)
                generated_text = tokenizer.decode(outputs[0][actual_input_length:], skip_special_tokens=True)
                
                # Write to CSV file
                writer.writerow([actual_input_length, f"{latency_ms:.2f}", generated_text.strip()])
                
                # Print progress
                total_steps = len(range(1, end_length, step_length))
                print(f"Progress: {i+1}/{total_steps} - Input length: {actual_input_length}, Latency: {latency_ms:.2f} ms")
                
                # Flush both file and stdout
                csvfile.flush()
                
            except Exception as e:
                print(f"Error at input length {target_length}: {str(e)}")
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")

def scaling_falcon_extended():
    """Extended scaling analysis with additional metrics for Falcon"""
    csv_filename = "scaling_falcon_extended.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header with additional metrics
        writer.writerow(['input_length', 'latency_ms', 'tokens_per_second', 'memory_mb', 'gpu_memory_mb', 'output_text'])
        
        end_length = 2048
        step_length = 100  # Larger steps for extended analysis
        print(f"Running extended Falcon scaling analysis from 1 to {end_length} tokens...")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        for i, target_length in enumerate(range(1, end_length, step_length)):
            try:
                # Generate random text with target length
                input_text = generate_random_text(target_length)
                input_ids = tokenizer(input_text, return_tensors="pt")
                actual_input_length = input_ids['input_ids'].shape[1]
                
                # Move to GPU if available and clear cache
                gpu_memory_mb = 0
                if torch.cuda.is_available():
                    input_ids = {k: v.cuda() for k, v in input_ids.items()}
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                    
                    # Measure GPU memory before inference
                    gpu_memory_before = torch.cuda.memory_allocated() / (1024 * 1024)
                
                # Measure inference latency
                start_time = time.time()
                with torch.no_grad():
                    outputs = model.generate(
                        **input_ids, 
                        max_new_tokens=50, 
                        do_sample=False, 
                        pad_token_id=tokenizer.eos_token_id,
                        temperature=1.0,
                        top_p=1.0
                    )
                end_time = time.time()
                
                latency_ms = (end_time - start_time) * 1000
                tokens_per_second = 50 / (latency_ms / 1000)  # 50 new tokens generated
                
                # Measure GPU memory after inference
                if torch.cuda.is_available():
                    gpu_memory_after = torch.cuda.memory_allocated() / (1024 * 1024)
                    gpu_memory_mb = gpu_memory_after - gpu_memory_before
                
                # Estimate CPU memory usage (rough approximation)
                memory_mb = actual_input_length * 4 * 4544 / (1024 * 1024)  # Falcon-7b hidden size is 4544
                
                # Get generated text
                generated_text = tokenizer.decode(outputs[0][actual_input_length:], skip_special_tokens=True)
                
                # Write to CSV
                writer.writerow([
                    actual_input_length, 
                    f"{latency_ms:.2f}", 
                    f"{tokens_per_second:.2f}",
                    f"{memory_mb:.2f}",
                    f"{gpu_memory_mb:.2f}",
                    generated_text.strip()
                ])
                
                total_steps = len(range(1, end_length, step_length))
                print(f"Progress: {i+1}/{total_steps} - Input: {actual_input_length}, "
                      f"Latency: {latency_ms:.2f} ms, TPS: {tokens_per_second:.2f}, "
                      f"GPU Mem: {gpu_memory_mb:.2f} MB")
                
                csvfile.flush()
                
            except Exception as e:
                print(f"Error at input length {target_length}: {str(e)}")
                continue
    
    print(f"\nExtended analysis completed! Results saved to {csv_filename}")

def scaling_falcon_batch():
    """Batch scaling analysis to test throughput with multiple sequences"""
    csv_filename = "scaling_falcon_batch.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['batch_size', 'input_length', 'total_latency_ms', 'latency_per_sequence_ms', 'throughput_sequences_per_sec'])
        
        input_length = 512  # Fixed input length for batch testing
        max_batch_size = 8
        
        print(f"Running batch scaling analysis with input length {input_length} tokens...")
        print(f"Testing batch sizes from 1 to {max_batch_size}")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 60)
        
        for batch_size in range(1, max_batch_size + 1):
            try:
                # Generate batch of random texts
                batch_texts = []
                for _ in range(batch_size):
                    text = generate_random_text(input_length)
                    batch_texts.append(text)
                
                # Tokenize batch
                inputs = tokenizer(batch_texts, return_tensors="pt", padding=True, truncation=True)
                
                if torch.cuda.is_available():
                    inputs = {k: v.cuda() for k, v in inputs.items()}
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                
                # Measure batch inference latency
                start_time = time.time()
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs, 
                        max_new_tokens=20, 
                        do_sample=False, 
                        pad_token_id=tokenizer.eos_token_id
                    )
                end_time = time.time()
                
                total_latency_ms = (end_time - start_time) * 1000
                latency_per_sequence_ms = total_latency_ms / batch_size
                throughput_sequences_per_sec = batch_size / (total_latency_ms / 1000)
                
                # Write to CSV
                writer.writerow([
                    batch_size,
                    input_length,
                    f"{total_latency_ms:.2f}",
                    f"{latency_per_sequence_ms:.2f}",
                    f"{throughput_sequences_per_sec:.2f}"
                ])
                
                print(f"Batch size: {batch_size}, Total latency: {total_latency_ms:.2f} ms, "
                      f"Per sequence: {latency_per_sequence_ms:.2f} ms, "
                      f"Throughput: {throughput_sequences_per_sec:.2f} seq/s")
                
                csvfile.flush()
                
            except Exception as e:
                print(f"Error at batch size {batch_size}: {str(e)}")
                continue
    
    print(f"\nBatch analysis completed! Results saved to {csv_filename}")

if __name__ == "__main__":
    print("Choose Falcon scaling analysis type:")
    print("1. Basic scaling analysis (latency vs input length)")
    print("2. Extended scaling analysis (with memory and performance metrics)")
    print("3. Batch scaling analysis (throughput with different batch sizes)")
    
    choice = input("Enter choice (1, 2, or 3): ").strip()
    
    if choice == "2":
        scaling_falcon_extended()
    elif choice == "3":
        scaling_falcon_batch()
    else:
        scaling_falcon() 