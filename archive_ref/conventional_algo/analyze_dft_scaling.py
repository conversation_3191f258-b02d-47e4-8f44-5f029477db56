#!/usr/bin/env python3
"""
Analysis script for DFT scaling results.
Visualizes performance comparison between naive DFT and FFT.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_scaling_results(csv_filename="scaling_dft_comparison.csv"):
    """Analyze DFT scaling results and create visualizations"""
    
    if not os.path.exists(csv_filename):
        print(f"Data file {csv_filename} not found")
        print("Run 'python demo_dft_scaling.py' or 'python scaling_dft.py' first")
        return
    
    # Load data
    data = pd.read_csv(csv_filename)
    print(f"Loaded {len(data)} data points from {csv_filename}")
    
    # Separate data by algorithm
    naive_data = data[data['algorithm'] == 'naive_dft']
    fft_data = data[data['algorithm'] == 'fft']
    
    print(f"Naive DFT: {len(naive_data)} points")
    print(f"FFT: {len(fft_data)} points")
    
    # Create comparison plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot 1: Performance comparison
    if not naive_data.empty:
        ax1.loglog(naive_data['signal_length'], naive_data['total_time_ms'], 
                  'ro-', label='Naive DFT (O(N²))', linewidth=2, markersize=6)
    
    if not fft_data.empty:
        ax1.loglog(fft_data['signal_length'], fft_data['total_time_ms'], 
                  'bo-', label='FFT (O(N log N))', linewidth=2, markersize=6)
    
    ax1.set_xlabel('Signal Length (samples)')
    ax1.set_ylabel('Total Time (ms)')
    ax1.set_title('DFT vs FFT Performance Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Speedup factor
    common_sizes = set(naive_data['signal_length']) & set(fft_data['signal_length'])
    if common_sizes:
        sizes = sorted(list(common_sizes))
        speedups = []
        
        for size in sizes:
            naive_time = naive_data[naive_data['signal_length'] == size]['total_time_ms'].iloc[0]
            fft_time = fft_data[fft_data['signal_length'] == size]['total_time_ms'].iloc[0]
            speedup = naive_time / fft_time
            speedups.append(speedup)
        
        ax2.semilogx(sizes, speedups, 'go-', linewidth=2, markersize=6)
        ax2.set_xlabel('Signal Length (samples)')
        ax2.set_ylabel('Speedup Factor (Naive DFT / FFT)')
        ax2.set_title('FFT Speedup over Naive DFT')
        ax2.grid(True, alpha=0.3)
        
        print(f"\nSpeedup analysis:")
        print(f"Size range: {min(sizes)} to {max(sizes)}")
        print(f"Speedup range: {min(speedups):.1f}x to {max(speedups):.1f}x")
        print(f"Average speedup: {np.mean(speedups):.1f}x")
    
    # Plot 3: Forward vs Inverse times
    if not naive_data.empty:
        ax3.loglog(naive_data['signal_length'], naive_data['forward_time_ms'], 
                  'ro-', label='Naive DFT Forward', markersize=4)
        ax3.loglog(naive_data['signal_length'], naive_data['inverse_time_ms'], 
                  'r^-', label='Naive IDFT', markersize=4)
    
    if not fft_data.empty:
        ax3.loglog(fft_data['signal_length'], fft_data['forward_time_ms'], 
                  'bo-', label='FFT Forward', markersize=4)
        ax3.loglog(fft_data['signal_length'], fft_data['inverse_time_ms'], 
                  'b^-', label='IFFT', markersize=4)
    
    ax3.set_xlabel('Signal Length (samples)')
    ax3.set_ylabel('Time (ms)')
    ax3.set_title('Forward vs Inverse Transform Times')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Scaling analysis
    def calc_scaling_factor(sizes, times):
        factors = []
        for i in range(1, len(sizes)):
            if sizes.iloc[i] > sizes.iloc[i-1]:
                time_ratio = times.iloc[i] / times.iloc[i-1]
                size_ratio = sizes.iloc[i] / sizes.iloc[i-1]
                factor = np.log(time_ratio) / np.log(size_ratio)
                factors.append((sizes.iloc[i], factor))
        return factors
    
    if not naive_data.empty and len(naive_data) > 1:
        naive_sorted = naive_data.sort_values('signal_length')
        naive_factors = calc_scaling_factor(naive_sorted['signal_length'], 
                                          naive_sorted['total_time_ms'])
        if naive_factors:
            sizes, factors = zip(*naive_factors)
            ax4.semilogx(sizes, factors, 'ro-', label='Naive DFT', markersize=4)
    
    if not fft_data.empty and len(fft_data) > 1:
        fft_sorted = fft_data.sort_values('signal_length')
        fft_factors = calc_scaling_factor(fft_sorted['signal_length'], 
                                        fft_sorted['total_time_ms'])
        if fft_factors:
            sizes, factors = zip(*fft_factors)
            ax4.semilogx(sizes, factors, 'bo-', label='FFT', markersize=4)
    
    ax4.axhline(y=2.0, color='red', linestyle='--', alpha=0.7, label='O(N²) theoretical')
    ax4.axhline(y=1.0, color='blue', linestyle='--', alpha=0.7, label='O(N log N) ideal')
    ax4.set_xlabel('Signal Length (samples)')
    ax4.set_ylabel('Empirical Scaling Factor')
    ax4.set_title('Scaling Factor Analysis')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 3)
    
    plt.tight_layout()
    
    # Save plot
    plot_filename = "dft_scaling_analysis.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\nAnalysis plot saved to: {plot_filename}")
    
    # Print summary report
    print("\n" + "="*50)
    print("SCALING ANALYSIS SUMMARY")
    print("="*50)
    
    for algo in ['naive_dft', 'fft']:
        algo_data = data[data['algorithm'] == algo]
        if not algo_data.empty:
            print(f"\n{algo.upper()}:")
            print(f"  Samples tested: {len(algo_data)}")
            print(f"  Size range: {algo_data['signal_length'].min():,} to {algo_data['signal_length'].max():,}")
            print(f"  Time range: {algo_data['total_time_ms'].min():.2f} to {algo_data['total_time_ms'].max():.2f} ms")
            
            # Check correctness
            all_correct = algo_data['correctness_verified'].all()
            print(f"  All tests correct: {'✓' if all_correct else '✗'}")
    
    print(f"\nTotal data points: {len(data)}")
    print(f"Results saved to: {csv_filename}")
    print(f"Visualization: {plot_filename}")

if __name__ == "__main__":
    analyze_scaling_results() 