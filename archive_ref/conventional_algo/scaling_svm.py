from sklearn.svm import SVC
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import time
import random
import csv
import numpy as np
import psutil
import os
import gc

def get_memory_usage_mb():
    """Get current memory usage of the process in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # Convert bytes to MB

def calculate_theoretical_memory_mb(n_train_samples, n_test_samples, n_features, n_support_vectors):
    """Calculate realistic memory usage for sklearn's libsvm SVM implementation"""
    # Core data structures (minimum required)
    train_data_mb = (n_train_samples * n_features * 8) / (1024 * 1024)  # Training data X_train
    test_data_mb = (n_test_samples * n_features * 8) / (1024 * 1024)    # Test data X_test
    labels_mb = ((n_train_samples + n_test_samples) * 8) / (1024 * 1024)  # Labels y_train, y_test
    
    # SVM-specific data structures (actually stored by sklearn)
    support_vectors_mb = (n_support_vectors * n_features * 8) / (1024 * 1024)  # Support vectors
    dual_coef_mb = (n_support_vectors * 8) / (1024 * 1024)  # Dual coefficients (alpha values)
    support_indices_mb = (n_support_vectors * 8) / (1024 * 1024)  # Support vector indices
    
    # Realistic kernel cache (libsvm uses limited cache, not full matrix)
    # Default cache size is typically 200MB or working_set_size^2, whichever is smaller
    cache_size_mb = min(200, (min(n_train_samples, 2000) ** 2 * 8) / (1024 * 1024))
    
    # Working memory for optimization algorithm
    # libsvm keeps working sets, gradients, and temporary arrays
    working_memory_mb = min(50, n_train_samples * 8 / (1024 * 1024))  # Capped at reasonable size
    
    # Prediction arrays
    predictions_mb = (n_test_samples * 8) / (1024 * 1024)  # Prediction results
    decision_function_mb = (n_test_samples * 8) / (1024 * 1024)  # Decision function values
    
    # sklearn/libsvm implementation overhead (more realistic)
    # Includes internal data structures, gradients, working sets
    sklearn_overhead_mb = min(20, train_data_mb * 0.1)  # Much more conservative
    
    return {
        # Input data structures
        'train_data_mb': train_data_mb,
        'test_data_mb': test_data_mb,
        'labels_mb': labels_mb,
        
        # SVM-specific structures (realistic)
        'support_vectors_mb': support_vectors_mb,
        'dual_coef_mb': dual_coef_mb,
        'support_indices_mb': support_indices_mb,
        'kernel_cache_mb': cache_size_mb,
        'working_memory_mb': working_memory_mb,
        
        # Prediction structures
        'predictions_mb': predictions_mb,
        'decision_function_mb': decision_function_mb,
        'sklearn_overhead_mb': sklearn_overhead_mb,
        
        # Totals
        'svm_algorithm_mb': support_vectors_mb + dual_coef_mb + support_indices_mb + cache_size_mb + working_memory_mb + sklearn_overhead_mb,
        'total_theoretical_mb': train_data_mb + test_data_mb + labels_mb + support_vectors_mb + dual_coef_mb + support_indices_mb + cache_size_mb + working_memory_mb + predictions_mb + decision_function_mb + sklearn_overhead_mb
    }

def generate_synthetic_dataset(n_samples, n_features=20, n_classes=2, random_state=42):
    """Generate synthetic classification dataset with specified number of samples.
    This function is called once to create the master dataset that gets subsampled."""
    X, y = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.7),
        n_redundant=int(n_features * 0.2),
        n_classes=n_classes,
        n_clusters_per_class=1,
        random_state=random_state + n_samples  # Vary random state for different sizes
    )
    
    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=random_state, stratify=y
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    return X_train_scaled, X_test_scaled, y_train, y_test

def scaling_svm():
    """Test SVM performance and memory scaling with comprehensive space complexity analysis"""
    csv_filename = "scaling_svm_space_time_analysis_fixed.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write comprehensive header with logical grouping
        writer.writerow([
            'dataset_size', 'train_size', 'test_size', 'n_features', 'n_support_vectors', 'support_vector_ratio',
            'training_latency_ms', 'prediction_latency_ms', 'train_accuracy', 'test_accuracy',
            # Memory progression: baseline → data_load → fit → predict
            'baseline_memory_mb', 'after_data_load_mb', 'data_load_increment_mb',
            'after_svm_fit_mb', 'svm_fit_increment_mb', 
            'after_prediction_mb', 'prediction_increment_mb',
            'peak_memory_mb', 'total_increment_mb',
            # Realistic theoretical analysis
            'realistic_train_data_mb', 'realistic_svm_algorithm_mb', 'realistic_prediction_mb', 'realistic_total_mb',
            'memory_efficiency_ratio'
        ])
        
        # Test parameters
        max_samples = 50000  # Maximum dataset size
        step_size = 250      # Step size for dataset sizes
        n_features = 20      # Number of features
        
        print(f"Testing SVM time and space complexity with comprehensive analysis...")
        print(f"Dataset sizes: {step_size} to {max_samples} (step: {step_size})")
        print(f"Features: {n_features} (fixed)")
        print(f"Kernel: RBF with C=1.0, gamma='scale'")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 80)
        
        # Measure baseline memory (without any large data structures)
        print("Measuring baseline memory usage...")
        gc.collect()
        time.sleep(1)  # Allow system to stabilize
        initial_baseline_mb = get_memory_usage_mb()
        print(f"Initial baseline memory: {initial_baseline_mb:.2f} MB")
        
        # Generate one large dataset that will be subsampled for each iteration
        print("Generating master dataset...")
        X_master_train, X_master_test, y_master_train, y_master_test = generate_synthetic_dataset(
            n_samples=max_samples, 
            n_features=n_features
        )
        print(f"Master dataset generated: {len(X_master_train)} training samples, {len(X_master_test)} test samples")
        print("=" * 80)
        
        # Test various dataset sizes
        dataset_sizes = list(range(step_size, max_samples + 1, step_size))
        
        for i, n_samples in enumerate(dataset_sizes):
            print(f"Progress: {i+1}/{len(dataset_sizes)} - Dataset size: {n_samples} samples...")
            
            try:
                # Clean memory and establish fresh baseline for this iteration
                gc.collect()
                time.sleep(0.5)  # Allow garbage collector to complete
                baseline_memory_mb = get_memory_usage_mb()
                
                # Use subset of master dataset to ensure same underlying data distribution
                train_size = int(n_samples * 0.8)  # 80% for training
                test_size = int(n_samples * 0.2)   # 20% for testing
                
                print(f"  Creating data subsets: {train_size} train, {test_size} test...")
                X_train = X_master_train[:train_size]
                X_test = X_master_test[:test_size]
                y_train = y_master_train[:train_size]
                y_test = y_master_test[:test_size]
                
                # Measure memory after data loading
                after_data_load_mb = get_memory_usage_mb()
                data_load_increment_mb = after_data_load_mb - baseline_memory_mb
                
                # Initialize SVM with RBF kernel (most common)
                svm_model = SVC(kernel='rbf', C=1.0, gamma='scale', random_state=42)
                
                # Measure SVM training latency and memory
                print(f"  Training SVM model...")
                start_time = time.time()
                svm_model.fit(X_train, y_train)
                training_end_time = time.time()
                training_latency_ms = (training_end_time - start_time) * 1000
                after_svm_fit_mb = get_memory_usage_mb()
                svm_fit_increment_mb = after_svm_fit_mb - after_data_load_mb
                
                # Get support vector information
                n_support_vectors = len(svm_model.support_)
                support_vector_ratio = n_support_vectors / train_size
                
                # Calculate training accuracy
                train_accuracy = svm_model.score(X_train, y_train)
                
                # Measure prediction latency and memory
                print(f"  Making predictions...")
                prediction_start_time = time.time()
                test_predictions = svm_model.predict(X_test)
                prediction_end_time = time.time()
                prediction_latency_ms = (prediction_end_time - prediction_start_time) * 1000
                after_prediction_mb = get_memory_usage_mb()
                prediction_increment_mb = after_prediction_mb - after_svm_fit_mb
                
                # Calculate test accuracy
                test_accuracy = svm_model.score(X_test, y_test)
                
                # Calculate overall memory metrics
                peak_memory_mb = max(baseline_memory_mb, after_data_load_mb, after_svm_fit_mb, after_prediction_mb)
                total_increment_mb = after_prediction_mb - baseline_memory_mb
                
                # Calculate theoretical memory usage
                theoretical_memory = calculate_theoretical_memory_mb(train_size, test_size, n_features, n_support_vectors)
                
                # Calculate memory efficiency ratio
                memory_efficiency_ratio = theoretical_memory['total_theoretical_mb'] / total_increment_mb if total_increment_mb > 0 else 0
                
                # Write comprehensive results to CSV
                writer.writerow([
                    n_samples,
                    train_size,
                    test_size,
                    n_features,
                    n_support_vectors,
                    f"{support_vector_ratio:.4f}",
                    f"{training_latency_ms:.2f}",
                    f"{prediction_latency_ms:.2f}",
                    f"{train_accuracy:.4f}",
                    f"{test_accuracy:.4f}",
                    # Memory progression
                    f"{baseline_memory_mb:.2f}",
                    f"{after_data_load_mb:.2f}",
                    f"{data_load_increment_mb:.2f}",
                    f"{after_svm_fit_mb:.2f}",
                    f"{svm_fit_increment_mb:.2f}",
                    f"{after_prediction_mb:.2f}",
                    f"{prediction_increment_mb:.2f}",
                    f"{peak_memory_mb:.2f}",
                    f"{total_increment_mb:.2f}",
                    # Realistic theoretical analysis
                    f"{theoretical_memory['train_data_mb']:.2f}",
                    f"{theoretical_memory['svm_algorithm_mb']:.2f}",
                    f"{theoretical_memory['predictions_mb']:.2f}",
                    f"{theoretical_memory['total_theoretical_mb']:.2f}",
                    f"{memory_efficiency_ratio:.2f}"
                ])
                
                # Print detailed progress with logical grouping
                print(f"  📊 Dataset: {train_size} train, {test_size} test, {n_features} features")
                print(f"  🎯 Support Vectors: {n_support_vectors} ({support_vector_ratio:.1%} of training data)")
                print(f"  ⏱️  Time Complexity:")
                print(f"    SVM fit: {training_latency_ms:.2f} ms, Prediction: {prediction_latency_ms:.2f} ms")
                print(f"  💾 Memory Progression:")
                print(f"    {baseline_memory_mb:.2f} → {after_data_load_mb:.2f} MB (+{data_load_increment_mb:.2f} data load)")
                print(f"    {after_data_load_mb:.2f} → {after_svm_fit_mb:.2f} MB (+{svm_fit_increment_mb:.2f} SVM fit)")
                print(f"    {after_svm_fit_mb:.2f} → {after_prediction_mb:.2f} MB (+{prediction_increment_mb:.2f} prediction)")
                print(f"    Total: {baseline_memory_mb:.2f} → {after_prediction_mb:.2f} MB (+{total_increment_mb:.2f} overall)")
                print(f"  📐 Realistic Theory vs Actual:")
                print(f"    Realistic Theory: {theoretical_memory['total_theoretical_mb']:.2f} MB, Actual: {total_increment_mb:.2f} MB")
                print(f"    Efficiency ratio: {memory_efficiency_ratio:.2f}")
                print(f"  ✨ Accuracy: Train={train_accuracy:.4f}, Test={test_accuracy:.4f}")
                print("-" * 50)
                
                # Flush file to ensure data is written
                csvfile.flush()
                
                # Comprehensive cleanup to prevent memory accumulation
                del X_train, X_test, y_train, y_test, svm_model, test_predictions
                gc.collect()
                
            except Exception as e:
                print(f"  Error with dataset size {n_samples}: {str(e)}")
                # Cleanup on error
                gc.collect()
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")
    print("\n" + "="*80)
    print("EXPERIMENT SUMMARY")
    print("="*80)
    print(f"✅ Tested dataset sizes: {step_size} to {max_samples} (step: {step_size})")
    print(f"✅ Fixed parameters: {n_features} features, RBF kernel SVM")
    print(f"✅ Consistent data distribution (subsampled master dataset)")
    print(f"✅ Comprehensive space and time complexity analysis")
    
    print(f"\n📈 TIME COMPLEXITY ANALYSIS:")
    print(f"  • SVM Training: O(n²) to O(n³) depending on data separability")
    print(f"  • SVM Prediction: O(n_test × n_support_vectors × {n_features})")
    print(f"  • Support vectors typically 10-50% of training data")
    print(f"  • Expected scaling: Quadratic to cubic growth for training")
    
    print(f"\n💾 SPACE COMPLEXITY ANALYSIS:")
    print(f"  • Training data: O(n_train × {n_features}) = O(n × {n_features})")
    print(f"  • Support vectors: O(n_support_vectors × {n_features}) - varies with data complexity")
    print(f"  • Kernel cache: O(cache_size) - limited cache (~200MB), not full matrix")
    print(f"  • Working memory: O(working_set_size) - optimization algorithm overhead")
    print(f"  • Total realistic: O(n × {n_features} + n_support_vectors × {n_features} + cache_size)")
    
    print(f"\n📊 OUTPUT CSV STRUCTURE (Logical Column Grouping):")
    print(f"  🔢 Basic: dataset_size, train_size, test_size, n_features")
    print(f"  🎯 SVM-specific: n_support_vectors, support_vector_ratio")
    print(f"  ⏱️  Performance: training_latency_ms, prediction_latency_ms, accuracies")
    print(f"  💾 Memory Progression:")
    print(f"      baseline_memory_mb → after_data_load_mb → data_load_increment_mb")
    print(f"      after_svm_fit_mb → svm_fit_increment_mb")
    print(f"      after_prediction_mb → prediction_increment_mb")
    print(f"      peak_memory_mb, total_increment_mb")
    print(f"  📐 Realistic Theoretical: train_data_mb, svm_algorithm_mb, prediction_mb, total_mb")
    print(f"  🎯 Efficiency: memory_efficiency_ratio")
    
    print(f"\n🔍 KEY INSIGHTS:")
    print(f"  • Incremental memory analysis eliminates baseline memory bias")
    print(f"  • Support vector ratio indicates model complexity and memory usage")
    print(f"  • SVM fit increment shows kernel matrix and algorithm overhead")
    print(f"  • Prediction complexity scales with number of support vectors")
    print(f"  • Efficiency ratio indicates actual vs realistic theoretical performance")
    print(f"  • Master dataset approach ensures fair complexity comparison")

if __name__ == "__main__":
    scaling_svm() 