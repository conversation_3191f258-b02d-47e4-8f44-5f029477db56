# Conventional ML Scaling Experiments

This directory contains scaling experiments for conventional machine learning algorithms.

## Available Experiments

### 1. SVM Scaling Experiment (`scaling_svm.py`)
### 2. FFT Scaling Experiment (`scaling_fft.py`)
### 3. PCA Scaling Experiment (`scaling_pca.py`)

---

## SVM Scaling Experiment

### Overview
The `scaling_svm.py` module tests the time complexity and performance of Support Vector Machines (SVM) across different dataset sizes, similar to the neural network scaling experiments in other directories.

### Features
- **Consistent Dataset Usage**: Uses same master dataset subsampled for each size to ensure fair scaling comparison
- **Dual Latency Measurement**: Measures both training time and prediction time
- **Performance Metrics**: Tracks accuracy on both training and test sets
- **CSV Output**: Saves results in a structured format for analysis

### Usage

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the scaling experiment:
```bash
python scaling_svm.py
```

### Output
The script generates `scaling_svm_latency.csv` with the following columns:
- `dataset_size`: Number of training samples
- `training_latency_ms`: Time taken to train the SVM model (milliseconds)
- `prediction_latency_ms`: Time taken to predict on test set (milliseconds)
- `train_accuracy`: Accuracy on training set
- `test_accuracy`: Accuracy on test set

### Configuration
Key parameters in the script:
- **Dataset sizes**: 250 to 50,000 samples (step size: 250)
- **Features**: 20-dimensional feature space
- **Kernel**: RBF (Radial Basis Function)
- **Test split**: 20% of data reserved for testing
- **Dataset consistency**: Single master dataset generated once, then subsampled for each test size

### Expected Scaling Behavior
SVM training complexity is approximately O(n²) to O(n³) depending on the implementation and kernel, where n is the number of training samples. This experiment helps visualize this scaling behavior empirically.

---

## FFT Scaling Experiment

### Overview
The `scaling_fft.py` module tests the time complexity of Fast Fourier Transform (FFT) and Inverse FFT operations across different signal sizes.

### Features
- **Complex Signal Generation**: Creates random complex signals with varying lengths
- **Dual Operation Measurement**: Measures both FFT and IFFT latencies
- **Power-of-2 Optimization**: Tests signal sizes that are powers of 2 for optimal FFT performance
- **CSV Output**: Saves results for performance analysis

### Usage
```bash
python scaling_fft.py
```

### Output
Generates `scaling_fft_latency.csv` with columns:
- `signal_length`: Number of samples in the signal
- `fft_latency_ms`: Time for forward FFT (milliseconds)
- `ifft_latency_ms`: Time for inverse FFT (milliseconds)

### Expected Scaling Behavior
FFT has O(N log N) time complexity, significantly better than the O(N²) complexity of naive DFT.

---

## PCA Scaling Experiment

### Overview
The `scaling_pca.py` module analyzes the time complexity of Principal Component Analysis (PCA) across different dataset dimensions, measuring both the fitting phase and transformation phase separately.

### Features
- **Multi-dimensional Testing**: Varies both number of samples and features
- **Dual Phase Measurement**: Measures both PCA fit and transform latencies
- **Synthetic Dataset Generation**: Creates high-dimensional classification datasets
- **Quality Metrics**: Tracks explained variance ratios
- **CSV Output**: Comprehensive results for scaling analysis

### Usage
```bash
python scaling_pca.py
```

### Output
Generates `scaling_pca_latency.csv` with columns:
- `n_samples`: Number of training samples
- `n_features`: Number of input features
- `n_components`: Number of principal components
- `fit_latency_ms`: Time to fit PCA model (milliseconds)
- `transform_latency_ms`: Time to transform data (milliseconds)
- `explained_variance_ratio`: Total explained variance by components

### Configuration
- **Sample sizes**: 1,000 to 20,000 (step: 1,000)
- **Feature dimensions**: 50 to 500 (step: 50)
- **Components**: Up to 95% of features or maximum 50
- **Preprocessing**: StandardScaler normalization

### Expected Scaling Behavior
- **Fit complexity**: O(min(n_samples, n_features)² × max(n_samples, n_features))
- **Transform complexity**: O(n_samples × n_features × n_components) 