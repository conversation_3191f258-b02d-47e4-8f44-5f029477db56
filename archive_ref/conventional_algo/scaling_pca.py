from sklearn.decomposition import PCA
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler
import time
import random
import csv
import numpy as np
import psutil
import os
import gc

def get_memory_usage_mb():
    """Get current memory usage of the process in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # Convert bytes to MB

def calculate_theoretical_memory_mb(n_samples, n_features, n_components):
    """Calculate theoretical memory usage for PCA data structures and operations"""
    # Core data structures (minimum required)
    input_data_mb = (n_samples * n_features * 8) / (1024 * 1024)  # Input X
    components_mb = (n_features * n_components * 8) / (1024 * 1024)  # Principal components
    transformed_data_mb = (n_samples * n_components * 8) / (1024 * 1024)  # Output X_transformed
    
    # Intermediate computations during PCA
    covariance_mb = (n_features * n_features * 8) / (1024 * 1024)  # Covariance matrix
    eigenvalues_mb = (n_features * 8) / (1024 * 1024)  # Eigenvalues array
    eigenvectors_mb = (n_features * n_features * 8) / (1024 * 1024)  # Full eigenvector matrix
    
    # sklearn implementation overhead (empirical estimation)
    # sklearn typically requires additional copies for numerical stability
    sklearn_overhead_mb = input_data_mb * 0.5  # Conservative estimate
    
    return {
        # Core data structures
        'input_data_mb': input_data_mb,
        'components_mb': components_mb, 
        'transformed_data_mb': transformed_data_mb,
        
        # Intermediate computations
        'covariance_mb': covariance_mb,
        'eigenvalues_mb': eigenvalues_mb,
        'eigenvectors_mb': eigenvectors_mb,
        'sklearn_overhead_mb': sklearn_overhead_mb,
        
        # Totals
        'pca_algorithm_mb': components_mb + covariance_mb + eigenvalues_mb + eigenvectors_mb + sklearn_overhead_mb,
        'total_theoretical_mb': input_data_mb + components_mb + transformed_data_mb + covariance_mb + eigenvalues_mb + eigenvectors_mb + sklearn_overhead_mb
    }

def generate_synthetic_dataset(n_samples, n_features, random_state=42):
    """Generate synthetic dataset with specified number of samples and features"""
    # Generate high-dimensional dataset for PCA analysis
    X, _ = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=int(n_features * 0.8),
        n_redundant=int(n_features * 0.15),
        n_clusters_per_class=2,
        random_state=random_state + n_samples  # Vary random state for different sizes
    )
    
    # Standardize features (important for PCA)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    return X_scaled

def scaling_pca():
    """Test PCA performance and memory scaling with incremental analysis (fixed features and components)"""
    csv_filename = "scaling_pca_incremental_analysis.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header with logical grouping: absolute_memory → increment → next_absolute_memory → increment
        writer.writerow([
            'n_samples', 'n_features', 'n_components', 
            'fit_latency_ms', 'transform_latency_ms', 'explained_variance_ratio',
            # Memory progression: baseline → data_load → fit → transform
            'baseline_memory_mb', 'after_data_load_mb', 'data_load_increment_mb',
            'after_fit_mb', 'pca_fit_increment_mb', 
            'after_transform_mb', 'pca_transform_increment_mb',
            'peak_memory_mb', 'total_increment_mb',
            # Theoretical analysis
            'theoretical_input_mb', 'theoretical_pca_algorithm_mb', 'theoretical_transformed_mb', 'theoretical_total_mb',
            'memory_efficiency_ratio'
        ])
        
        # Test parameters
        max_samples = 50000     # Maximum number of samples
        n_features = 150        # Fixed number of features
        n_components = 50       # Fixed number of components
        sample_step = 250      # Step size for samples
        
        print(f"Testing PCA time and space complexity with incremental memory analysis...")
        print(f"Sample sizes: {sample_step} to {max_samples} (step: {sample_step})")
        print(f"Features: {n_features} (fixed)")
        print(f"Components: {n_components} (fixed)")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 80)
        
        # Measure baseline memory (without any large data structures)
        print("Measuring baseline memory usage...")
        gc.collect()
        time.sleep(1)  # Allow system to stabilize
        initial_baseline_mb = get_memory_usage_mb()
        print(f"Initial baseline memory: {initial_baseline_mb:.2f} MB")
        print("-" * 50)
        
        # Test various sample sizes, generating fresh data for each test
        sample_sizes = list(range(sample_step, max_samples + 1, sample_step))
        
        for i, n_samples in enumerate(sample_sizes):
            print(f"Progress: {i+1}/{len(sample_sizes)} - Dataset: {n_samples} samples, {n_features} features, {n_components} components...")
            
            try:
                # Clean memory and establish fresh baseline for this iteration
                gc.collect()
                time.sleep(0.5)  # Allow garbage collector to complete
                baseline_memory_mb = get_memory_usage_mb()
                
                # Generate fresh dataset for this test (avoid pre-loading bias)
                print(f"  Generating fresh dataset with {n_samples} samples...")
                X = generate_synthetic_dataset(n_samples=n_samples, n_features=n_features)
                
                # Measure memory after data generation and loading
                after_data_load_mb = get_memory_usage_mb()
                data_load_increment_mb = after_data_load_mb - baseline_memory_mb
                
                # Initialize PCA model
                pca_model = PCA(n_components=n_components, random_state=42)
                
                # Measure PCA fit latency and memory
                print(f"  Fitting PCA model...")
                start_time = time.time()
                pca_model.fit(X)
                fit_end_time = time.time()
                fit_latency_ms = (fit_end_time - start_time) * 1000
                after_fit_mb = get_memory_usage_mb()
                pca_fit_increment_mb = after_fit_mb - after_data_load_mb
                
                # Measure PCA transform latency and memory
                print(f"  Transforming data...")
                transform_start_time = time.time()
                X_transformed = pca_model.transform(X)
                transform_end_time = time.time()
                transform_latency_ms = (transform_end_time - transform_start_time) * 1000
                after_transform_mb = get_memory_usage_mb()
                pca_transform_increment_mb = after_transform_mb - after_fit_mb
                
                # Calculate overall metrics
                peak_memory_mb = max(baseline_memory_mb, after_data_load_mb, after_fit_mb, after_transform_mb)
                total_increment_mb = after_transform_mb - baseline_memory_mb
                
                # Calculate theoretical memory usage
                theoretical_memory = calculate_theoretical_memory_mb(n_samples, n_features, n_components)
                
                # Calculate memory efficiency ratio
                memory_efficiency_ratio = theoretical_memory['total_theoretical_mb'] / total_increment_mb if total_increment_mb > 0 else 0
                
                # Calculate explained variance ratio
                explained_variance = np.sum(pca_model.explained_variance_ratio_)
                
                # Write comprehensive results to CSV with logical column grouping
                writer.writerow([
                    n_samples,
                    n_features,
                    n_components,
                    f"{fit_latency_ms:.2f}",
                    f"{transform_latency_ms:.2f}",
                    f"{explained_variance:.4f}",
                    # Memory progression: baseline → data_load → fit → transform
                    f"{baseline_memory_mb:.2f}",
                    f"{after_data_load_mb:.2f}",
                    f"{data_load_increment_mb:.2f}",
                    f"{after_fit_mb:.2f}",
                    f"{pca_fit_increment_mb:.2f}",
                    f"{after_transform_mb:.2f}",
                    f"{pca_transform_increment_mb:.2f}",
                    f"{peak_memory_mb:.2f}",
                    f"{total_increment_mb:.2f}",
                    # Theoretical analysis
                    f"{theoretical_memory['input_data_mb']:.2f}",
                    f"{theoretical_memory['pca_algorithm_mb']:.2f}",
                    f"{theoretical_memory['transformed_data_mb']:.2f}",
                    f"{theoretical_memory['total_theoretical_mb']:.2f}",
                    f"{memory_efficiency_ratio:.2f}"
                ])
                
                # Print detailed progress with logical memory progression
                print(f"  ⏱️  Time Complexity:")
                print(f"    PCA fit: {fit_latency_ms:.2f} ms, Transform: {transform_latency_ms:.2f} ms")
                print(f"  💾 Memory Progression:")
                print(f"    {baseline_memory_mb:.2f} → {after_data_load_mb:.2f} MB (+{data_load_increment_mb:.2f} data load)")
                print(f"    {after_data_load_mb:.2f} → {after_fit_mb:.2f} MB (+{pca_fit_increment_mb:.2f} PCA fit)")  
                print(f"    {after_fit_mb:.2f} → {after_transform_mb:.2f} MB (+{pca_transform_increment_mb:.2f} transform)")
                print(f"    Total: {baseline_memory_mb:.2f} → {after_transform_mb:.2f} MB (+{total_increment_mb:.2f} overall)")
                print(f"  📐 Theoretical vs Actual:")
                print(f"    Theory: {theoretical_memory['total_theoretical_mb']:.2f} MB, Actual: {total_increment_mb:.2f} MB")
                print(f"    Efficiency ratio: {memory_efficiency_ratio:.2f}")
                print(f"  ✨ Quality: Explained variance = {explained_variance:.4f}")
                print("-" * 50)
                
                # Flush file to ensure data is written
                csvfile.flush()
                
                # Comprehensive cleanup to prevent memory accumulation
                del X, X_transformed, pca_model
                gc.collect()
                
            except Exception as e:
                print(f"  Error with dataset ({n_samples}, {n_features}): {str(e)}")
                # Cleanup on error
                gc.collect()
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")
    print("\n" + "="*80)
    print("EXPERIMENT SUMMARY")
    print("="*80)
    print(f"✅ Tested sample sizes: {sample_step} to {max_samples} (step: {sample_step})")
    print(f"✅ Fixed parameters: {n_features} features, {n_components} components")
    print(f"✅ Fresh data generation per test (no pre-loading bias)")
    print(f"✅ Comprehensive incremental memory analysis")
    
    print(f"\n📈 TIME COMPLEXITY ANALYSIS:")
    print(f"  • PCA Fit: O(min(n_samples, n_features)² × max(n_samples, n_features))")
    print(f"  • PCA Transform: O(n_samples × n_features × n_components)")
    print(f"  • Expected scaling: O(n × {n_features}²) for n > {n_features}")
    
    print(f"\n💾 SPACE COMPLEXITY ANALYSIS:")
    print(f"  • Input data: O(n_samples × {n_features}) = O(n × {n_features})")
    print(f"  • PCA algorithm overhead: O({n_features}²) + sklearn overhead")
    print(f"  • Transformed data: O(n_samples × {n_components}) = O(n × {n_components})")
    print(f"  • Total theoretical: O(n × {n_features + n_components} + {n_features}²)")
    
    print(f"\n📊 OUTPUT CSV STRUCTURE (Logical Column Grouping):")
    print(f"  🔢 Basic: n_samples, n_features, n_components")
    print(f"  ⏱️  Performance: fit_latency_ms, transform_latency_ms, explained_variance_ratio")
    print(f"  💾 Memory Progression:")
    print(f"      baseline_memory_mb → after_data_load_mb → data_load_increment_mb")
    print(f"      after_fit_mb → pca_fit_increment_mb")
    print(f"      after_transform_mb → pca_transform_increment_mb") 
    print(f"      peak_memory_mb, total_increment_mb")
    print(f"  📐 Theoretical: input_mb, algorithm_mb, transformed_mb, total_mb")
    print(f"  🎯 Efficiency: memory_efficiency_ratio")
    
    print(f"\n🔍 KEY INSIGHTS:")
    print(f"  • Incremental analysis eliminates baseline memory bias")
    print(f"  • Data loading increment shows O(n) scaling")
    print(f"  • PCA fit increment shows algorithm overhead")
    print(f"  • Efficiency ratio indicates implementation vs theoretical performance")
    print(f"  • Fresh data generation ensures consistent complexity analysis")

if __name__ == "__main__":
    scaling_pca() 