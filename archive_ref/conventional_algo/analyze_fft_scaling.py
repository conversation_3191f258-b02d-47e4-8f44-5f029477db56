import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def nlogn_function(n, a, b):
    """
    n*log(n) function for curve fitting
    f(n) = a * n * log₂(n) + b
    """
    return a * n * np.log2(n) + b

def nlogn_function_no_offset(n, a):
    """
    n*log(n) function without offset for pure scaling analysis
    f(n) = a * n * log₂(n)
    """
    return a * n * np.log2(n)

def analyze_fft_scaling():
    """Analyze FFT scaling data and fit with n*log(n) curves"""
    
    # Load data
    df = pd.read_csv('scaling_fft_latency.csv')
    
    print("FFT Scaling Analysis with n*log(n) Curve Fitting")
    print("=" * 60)
    print(f"Total data points: {len(df)}")
    print(f"Number of Samples range: {df['signal_length'].min():,} to {df['signal_length'].max():,}")
    
    # Filter data to remove potentially noisy small values
    # FFT scaling behavior is more reliable for larger Number of Sampless
    min_size = 512  # Start analysis from this size
    df_filtered = df[df['signal_length'] >= min_size].copy()
    
    print(f"Filtered data points (n >= {min_size:,}): {len(df_filtered)}")
    print()
    
    # Prepare data for fitting
    n = df_filtered['signal_length'].values
    fft_latency = df_filtered['fft_latency_ms'].values
    
    # Fit FFT data
    print("FFT Latency Analysis:")
    print("-" * 30)
    
    # # Fit with offset
    # popt_fft, pcov_fft = curve_fit(nlogn_function, n, fft_latency, 
    #                                bounds=([0, -np.inf], [np.inf, np.inf]))
    # a_fft, b_fft = popt_fft
    # fft_fitted = nlogn_function(n, a_fft, b_fft)
    
    # Fit without offset (pure scaling)
    popt_fft_pure, _ = curve_fit(nlogn_function_no_offset, n, fft_latency, 
                                 bounds=([0], [np.inf]))
    a_fft_pure = popt_fft_pure[0]
    fft_fitted_pure = nlogn_function_no_offset(n, a_fft_pure)
    
    # Calculate goodness of fit
    # r2_fft = r2_score(fft_latency, fft_fitted)
    r2_fft_pure = r2_score(fft_latency, fft_fitted_pure)
    # rmse_fft = np.sqrt(np.mean((fft_latency - fft_fitted)**2))
    rmse_fft_pure = np.sqrt(np.mean((fft_latency - fft_fitted_pure)**2))
    
    # print(f"Fitted function: f(n) = {a_fft:.2e} * n * log₂(n) + {b_fft:.2f}")
    # print(f"R² score: {r2_fft:.6f}")
    # print(f"RMSE: {rmse_fft:.2f} ms")
    print(f"Pure scaling (no offset): f(n) = {a_fft_pure:.2e} * n * log₂(n)")
    print(f"R² score (pure): {r2_fft_pure:.6f}")
    print(f"RMSE (pure): {rmse_fft_pure:.2f} ms")
    print()
    
    # Create plots
    create_plots(df, df_filtered, n, fft_latency, 
                 fft_fitted_pure, a_fft_pure, r2_fft_pure)
    
    return {
        'fft_pure': a_fft_pure,
        'r2_score': r2_fft_pure
    }

def create_plots(df_full, df_filtered, n, fft_latency, 
                 fft_fitted_pure, a_fft_pure, r2_fft_pure):
    """Create comprehensive plots of the FFT data and fitted curves"""
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # # Plot 1: Log-log plot with fitted curves (with offset)
    # ax1.loglog(df_full['signal_length'], df_full['fft_latency_ms'], 'bo', 
    #            alpha=0.7, markersize=5, label='FFT Data')
    # ax1.loglog(df_full['signal_length'], df_full['ifft_latency_ms'], 'ro', 
    #            alpha=0.7, markersize=5, label='IFFT Data')
    # ax1.loglog(n, fft_fitted, 'b-', linewidth=2, 
    #            label=f'FFT Fit: {a_fft:.1e}n·log₂(n)+{b_fft:.1f} (R²={r2_fft:.4f})')
    # ax1.loglog(n, ifft_fitted, 'r-', linewidth=2, 
    #            label=f'IFFT Fit: {a_ifft:.1e}n·log₂(n)+{b_ifft:.1f} (R²={r2_ifft:.4f})')
    
    # ax1.set_xlabel('Number of Samples')
    # ax1.set_ylabel('Latency (ms)')
    # ax1.set_title('FFT/IFFT Scaling: Data vs n·log(n) Fits (with offset)')
    # ax1.grid(True, alpha=0.3)
    # ax1.legend()
    
    # Plot 1: Log-log plot with pure scaling fits (no offset)
    ax1.loglog(df_full['signal_length'], df_full['fft_latency_ms'], 'bo', 
               alpha=0.7, markersize=5, label='FFT Data')
    ax1.loglog(n, fft_fitted_pure, 'g-', linewidth=2, 
               label=f'FFT Fit: {a_fft_pure:.1e}n·log₂(n) (R²={r2_fft_pure:.4f})')
    
    ax1.set_xlabel('Number of Samples')
    ax1.set_ylabel('Latency (ms)')
    ax1.set_title('FFT Scaling: Pure n·log(n) Scaling')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Plot 2: Zoomed view for large Number of Sampless
    large_mask = df_full['signal_length'] >= 1024
    ax2.loglog(df_full.loc[large_mask, 'signal_length'], 
               df_full.loc[large_mask, 'fft_latency_ms'], 'bo', 
               alpha=0.7, markersize=6, label='FFT Data')
    ax2.loglog(n, fft_fitted_pure, 'g-', linewidth=2, 
               label=f'FFT Fit: {a_fft_pure:.1e}n·log₂(n)')
    
    ax2.set_xlabel('Number of Samples')
    ax2.set_ylabel('Latency (ms)')
    ax2.set_title('FFT Scaling: Large Number of Sampless (n ≥ 1024)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('fft_scaling_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('fft_scaling_analysis.pdf', bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    results = analyze_fft_scaling() 