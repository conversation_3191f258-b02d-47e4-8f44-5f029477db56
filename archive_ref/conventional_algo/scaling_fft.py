import numpy as np
import time
import csv
import random
import psutil
import os
import gc

def get_memory_usage_mb():
    """Get current memory usage of the process in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # Convert bytes to MB

def calculate_theoretical_memory_mb(signal_length):
    """Calculate theoretical memory usage for FFT data structures and operations"""
    # Each complex number requires 16 bytes (8 for real + 8 for imaginary parts)
    complex_size_bytes = 16
    
    # Core data structures
    input_signal_mb = (signal_length * complex_size_bytes) / (1024 * 1024)  # Input signal
    fft_result_mb = (signal_length * complex_size_bytes) / (1024 * 1024)    # FFT output
    ifft_result_mb = (signal_length * complex_size_bytes) / (1024 * 1024)   # IFFT output
    
    # NumPy FFT algorithm overhead (empirical estimation)
    # FFT typically requires additional working memory for intermediate computations
    # Cooley-Tukey algorithm needs temporary arrays during divide-and-conquer
    numpy_overhead_mb = input_signal_mb * 0.5  # Conservative estimate based on NumPy implementation
    
    return {
        # Core data structures
        'input_signal_mb': input_signal_mb,
        'fft_result_mb': fft_result_mb,
        'ifft_result_mb': ifft_result_mb,
        
        # Algorithm overhead
        'numpy_overhead_mb': numpy_overhead_mb,
        
        # Totals
        'fft_algorithm_mb': fft_result_mb + numpy_overhead_mb,
        'ifft_algorithm_mb': ifft_result_mb + numpy_overhead_mb,
        'total_theoretical_mb': input_signal_mb + fft_result_mb + ifft_result_mb + (numpy_overhead_mb * 2)  # overhead for both FFT and IFFT
    }

def generate_random_signal(signal_length):
    """Generate random complex signal with specified length"""
    # Generate random complex numbers
    real_part = np.random.randn(signal_length)
    imag_part = np.random.randn(signal_length)
    return real_part + 1j * imag_part

def scaling_fft():
    """Test FFT time and space complexity with incremental memory analysis"""
    csv_filename = "scaling_fft_analysis.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header with logical grouping: time → memory progression → theoretical
        writer.writerow([
            'signal_length', 
            'fft_latency_ms', 'ifft_latency_ms',
            # Memory progression: baseline → signal_gen → fft → ifft
            'baseline_memory_mb', 'after_signal_gen_mb', 'signal_gen_increment_mb',
            'after_fft_mb', 'fft_increment_mb', 
            'after_ifft_mb', 'ifft_increment_mb',
            'peak_memory_mb', 'total_increment_mb',
            # Theoretical analysis
            'theoretical_input_mb', 'theoretical_fft_algorithm_mb', 'theoretical_ifft_algorithm_mb', 'theoretical_total_mb',
            'memory_efficiency_ratio'
        ])
        
        # Test parameters - using powers of 2 for optimal FFT performance
        max_power = 20  # Reasonable limit for most systems (2^20 = ~1M samples)
        min_power = 4   # Start from 2^4 = 16 samples
        powers_of_2 = [2**i for i in range(min_power, max_power + 1)]  # 2^4 to 2^20
        
        print(f"Testing FFT time and space complexity with incremental memory analysis...")
        print(f"Signal sizes: 2^{min_power} to 2^{max_power} (powers of 2)")
        print(f"Results will be saved to {csv_filename}")
        print("=" * 80)
        
        # Measure baseline memory (without any large data structures)
        print("Measuring baseline memory usage...")
        gc.collect()
        time.sleep(1)  # Allow system to stabilize
        initial_baseline_mb = get_memory_usage_mb()
        print(f"Initial baseline memory: {initial_baseline_mb:.2f} MB")
        print("-" * 50)
        
        for i, signal_length in enumerate(powers_of_2):
            print(f"Progress: {i+1}/{len(powers_of_2)} - Signal length: {signal_length} samples (2^{min_power + i})...")
            
            try:
                # Clean memory and establish fresh baseline for this iteration
                gc.collect()
                time.sleep(0.5)  # Allow garbage collector to complete
                baseline_memory_mb = get_memory_usage_mb()
                
                # Generate random complex signal and measure memory
                print(f"  Generating signal with {signal_length} complex samples...")
                signal = generate_random_signal(signal_length)
                after_signal_gen_mb = get_memory_usage_mb()
                signal_gen_increment_mb = after_signal_gen_mb - baseline_memory_mb
                
                # Measure FFT latency and memory
                print(f"  Computing FFT...")
                start_time = time.time()
                fft_result = np.fft.fft(signal)
                fft_end_time = time.time()
                fft_latency_ms = (fft_end_time - start_time) * 1000
                after_fft_mb = get_memory_usage_mb()
                fft_increment_mb = after_fft_mb - after_signal_gen_mb
                
                # Measure IFFT latency and memory
                print(f"  Computing IFFT...")
                ifft_start_time = time.time()
                ifft_result = np.fft.ifft(fft_result)
                ifft_end_time = time.time()
                ifft_latency_ms = (ifft_end_time - ifft_start_time) * 1000
                after_ifft_mb = get_memory_usage_mb()
                ifft_increment_mb = after_ifft_mb - after_fft_mb
                
                # Calculate overall memory metrics
                peak_memory_mb = max(baseline_memory_mb, after_signal_gen_mb, after_fft_mb, after_ifft_mb)
                total_increment_mb = after_ifft_mb - baseline_memory_mb
                
                # Calculate theoretical memory usage
                theoretical_memory = calculate_theoretical_memory_mb(signal_length)
                
                # Calculate memory efficiency ratio
                memory_efficiency_ratio = theoretical_memory['total_theoretical_mb'] / total_increment_mb if total_increment_mb > 0 else 0
                
                # Write comprehensive results to CSV with logical column grouping
                writer.writerow([
                    signal_length,
                    f"{fft_latency_ms:.2f}",
                    f"{ifft_latency_ms:.2f}",
                    # Memory progression: baseline → signal_gen → fft → ifft
                    f"{baseline_memory_mb:.2f}",
                    f"{after_signal_gen_mb:.2f}",
                    f"{signal_gen_increment_mb:.2f}",
                    f"{after_fft_mb:.2f}",
                    f"{fft_increment_mb:.2f}",
                    f"{after_ifft_mb:.2f}",
                    f"{ifft_increment_mb:.2f}",
                    f"{peak_memory_mb:.2f}",
                    f"{total_increment_mb:.2f}",
                    # Theoretical analysis
                    f"{theoretical_memory['input_signal_mb']:.2f}",
                    f"{theoretical_memory['fft_algorithm_mb']:.2f}",
                    f"{theoretical_memory['ifft_algorithm_mb']:.2f}",
                    f"{theoretical_memory['total_theoretical_mb']:.2f}",
                    f"{memory_efficiency_ratio:.2f}"
                ])
                
                # Print detailed progress with logical memory progression
                print(f"  ⏱️  Time Complexity:")
                print(f"    FFT: {fft_latency_ms:.2f} ms, IFFT: {ifft_latency_ms:.2f} ms")
                print(f"  💾 Memory Progression:")
                print(f"    {baseline_memory_mb:.2f} → {after_signal_gen_mb:.2f} MB (+{signal_gen_increment_mb:.2f} signal gen)")
                print(f"    {after_signal_gen_mb:.2f} → {after_fft_mb:.2f} MB (+{fft_increment_mb:.2f} FFT)")
                print(f"    {after_fft_mb:.2f} → {after_ifft_mb:.2f} MB (+{ifft_increment_mb:.2f} IFFT)")
                print(f"    Total: {baseline_memory_mb:.2f} → {after_ifft_mb:.2f} MB (+{total_increment_mb:.2f} overall)")
                print(f"  📐 Theoretical vs Actual:")
                print(f"    Theory: {theoretical_memory['total_theoretical_mb']:.2f} MB, Actual: {total_increment_mb:.2f} MB")
                print(f"    Efficiency ratio: {memory_efficiency_ratio:.2f}")
                print("-" * 50)
                
                # Flush file to ensure data is written
                csvfile.flush()
                
                # Comprehensive cleanup to prevent memory accumulation
                del signal, fft_result, ifft_result
                gc.collect()
                
            except Exception as e:
                print(f"  Error with signal length {signal_length}: {str(e)}")
                # Cleanup on error
                gc.collect()
                continue

    print(f"\nCompleted! Results saved to {csv_filename}")
    print("\n" + "="*80)
    print("EXPERIMENT SUMMARY")
    print("="*80)
    print(f"✅ Tested signal sizes: 2^{min_power} to 2^{max_power} (powers of 2)")
    print(f"✅ Fresh signal generation per test (no pre-loading bias)")
    print(f"✅ Comprehensive incremental memory analysis")
    
    print(f"\n📈 TIME COMPLEXITY ANALYSIS:")
    print(f"  • FFT Algorithm: O(N log N) using Cooley-Tukey divide-and-conquer")
    print(f"  • IFFT Algorithm: O(N log N) (inverse of FFT)")
    print(f"  • Expected scaling: Both should show logarithmic growth relative to N")
    
    print(f"\n💾 SPACE COMPLEXITY ANALYSIS:")
    print(f"  • Input signal: O(N) complex numbers = O(N × 16 bytes)")
    print(f"  • FFT result: O(N) complex numbers = O(N × 16 bytes)")
    print(f"  • IFFT result: O(N) complex numbers = O(N × 16 bytes)")
    print(f"  • Algorithm overhead: O(N) for NumPy implementation")
    print(f"  • Total theoretical: O(N) linear scaling")
    
    print(f"\n📊 OUTPUT CSV STRUCTURE (Logical Column Grouping):")
    print(f"  🔢 Basic: signal_length")
    print(f"  ⏱️  Performance: fft_latency_ms, ifft_latency_ms")
    print(f"  💾 Memory Progression:")
    print(f"      baseline_memory_mb → after_signal_gen_mb → signal_gen_increment_mb")
    print(f"      after_fft_mb → fft_increment_mb")
    print(f"      after_ifft_mb → ifft_increment_mb")
    print(f"      peak_memory_mb, total_increment_mb")
    print(f"  📐 Theoretical: input_mb, fft_algorithm_mb, ifft_algorithm_mb, total_mb")
    print(f"  🎯 Efficiency: memory_efficiency_ratio")
    
    print(f"\n🔍 KEY INSIGHTS:")
    print(f"  • Powers of 2 ensure optimal FFT performance (Cooley-Tukey)")
    print(f"  • Incremental analysis shows memory allocation patterns")
    print(f"  • Signal generation increment shows O(N) scaling")
    print(f"  • FFT/IFFT increments show algorithm memory overhead")
    print(f"  • Efficiency ratio indicates NumPy vs theoretical performance")
    print(f"  • Time complexity should demonstrate O(N log N) scaling")

if __name__ == "__main__":
    scaling_fft()