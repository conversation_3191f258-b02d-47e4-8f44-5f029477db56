import numpy as np
import time
import csv
import cmath
import psutil
import os
import gc
from typing import <PERSON><PERSON>

def get_memory_usage_kb():
    """Get current memory usage of the process in KB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024  # Convert bytes to KB

def calculate_theoretical_memory_kb(signal_length):
    """Calculate theoretical memory usage for DFT data structures and operations"""
    # Each complex number requires 16 bytes (8 for real + 8 for imaginary parts)
    complex_size_bytes = 16
    
    # Core data structures
    input_signal_kb = (signal_length * complex_size_bytes) / 1024  # Input signal
    dft_result_kb = (signal_length * complex_size_bytes) / 1024    # DFT output
    idft_result_kb = (signal_length * complex_size_bytes) / 1024   # IDFT output
    
    # Naive DFT algorithm overhead
    # Unlike FFT, naive DFT doesn't need significant working memory beyond input/output
    # However, there might be small overhead for loop variables and intermediate calculations
    naive_dft_overhead_kb = input_signal_kb * 0.1  # Minimal overhead for O(N²) implementation
    
    return {
        # Core data structures
        'input_signal_kb': input_signal_kb,
        'dft_result_kb': dft_result_kb,
        'idft_result_kb': idft_result_kb,
        
        # Algorithm overhead
        'naive_dft_overhead_kb': naive_dft_overhead_kb,
        
        # Totals
        'dft_algorithm_kb': dft_result_kb + naive_dft_overhead_kb,
        'idft_algorithm_kb': idft_result_kb + naive_dft_overhead_kb,
        'total_theoretical_kb': input_signal_kb + dft_result_kb + idft_result_kb + (naive_dft_overhead_kb * 2)  # overhead for both DFT and IDFT
    }

class DFTScaler:
    """
    A module for scaling analysis of Discrete Fourier Transform implementation.
    Focuses on naive DFT (O(N²)) performance across different signal sizes.
    """
    
    def __init__(self):
        self.results = []
        
    def generate_random_signal(self, signal_length: int) -> np.ndarray:
        """Generate random complex signal with specified length"""
        real_part = np.random.randn(signal_length)
        imag_part = np.random.randn(signal_length)
        return real_part + 1j * imag_part
    
    def naive_dft(self, signal: np.ndarray) -> np.ndarray:
        """
        Naive implementation of Discrete Fourier Transform.
        Time complexity: O(N²)
        
        DFT formula: X[k] = Σ(n=0 to N-1) x[n] * e^(-2πi*k*n/N)
        """
        N = len(signal)
        dft_result = np.zeros(N, dtype=complex)
        
        for k in range(N):
            for n in range(N):
                # Calculate the complex exponential: e^(-2πi*k*n/N)
                angle = -2 * np.pi * k * n / N
                complex_exp = cmath.exp(1j * angle)
                dft_result[k] += signal[n] * complex_exp
                
        return dft_result
    
    def naive_idft(self, dft_signal: np.ndarray) -> np.ndarray:
        """
        Naive implementation of Inverse Discrete Fourier Transform.
        Time complexity: O(N²)
        
        IDFT formula: x[n] = (1/N) * Σ(k=0 to N-1) X[k] * e^(2πi*k*n/N)
        """
        N = len(dft_signal)
        idft_result = np.zeros(N, dtype=complex)
        
        for n in range(N):
            for k in range(N):
                # Calculate the complex exponential: e^(2πi*k*n/N)
                angle = 2 * np.pi * k * n / N
                complex_exp = cmath.exp(1j * angle)
                idft_result[n] += dft_signal[k] * complex_exp
                
        # Normalize by N
        return idft_result / N
    
    def measure_dft_performance_with_memory(self, signal: np.ndarray, baseline_memory_kb: float) -> Tuple[float, float, float, float, float, float, np.ndarray, np.ndarray]:
        """
        Measure performance and memory usage of DFT algorithm.
        Returns: (forward_time_ms, inverse_time_ms, after_dft_kb, dft_increment_kb, after_idft_kb, idft_increment_kb, forward_result, inverse_result)
        """
        # Measure DFT with memory tracking
        start_time = time.time()
        forward_result = self.naive_dft(signal)
        forward_time = (time.time() - start_time) * 1000
        after_dft_kb = get_memory_usage_kb()
        dft_increment_kb = after_dft_kb - baseline_memory_kb
        
        # Measure IDFT with memory tracking
        start_time = time.time()
        inverse_result = self.naive_idft(forward_result)
        inverse_time = (time.time() - start_time) * 1000
        after_idft_kb = get_memory_usage_kb()
        idft_increment_kb = after_idft_kb - after_dft_kb
        
        return forward_time, inverse_time, after_dft_kb, dft_increment_kb, after_idft_kb, idft_increment_kb, forward_result, inverse_result
    
    def verify_correctness(self, original: np.ndarray, reconstructed: np.ndarray, tolerance: float = 1e-10) -> bool:
        """Verify that the DFT->IDFT reconstruction is correct"""
        return np.allclose(original, reconstructed, atol=tolerance)
    
    def run_scaling_analysis(self, max_length: int = 2**16, step_size: int = 256) -> str:
        """
        Run DFT time and space complexity analysis across different signal sizes.
        
        Args:
            max_length: Maximum signal length (default: 2^16 = 65,536)
            step_size: Step size for each iteration (default: 256)
            
        Returns:
            CSV filename with results
        """
        csv_filename = "scaling_dft_analysis.csv"
        
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header with logical grouping: basic → time → memory progression → theoretical
            writer.writerow([
                'signal_length', 'operations_count', 'correctness_verified',
                'dft_latency_ms', 'idft_latency_ms', 'total_time_ms',
                # Memory progression: baseline → signal_gen → dft → idft
                'baseline_memory_kb', 'after_signal_gen_kb', 'signal_gen_increment_kb',
                'after_dft_kb', 'dft_increment_kb', 
                'after_idft_kb', 'idft_increment_kb',
                'peak_memory_kb', 'total_increment_kb',
                # Theoretical analysis
                'theoretical_input_kb', 'theoretical_dft_algorithm_kb', 'theoretical_idft_algorithm_kb', 'theoretical_total_kb',
                'memory_efficiency_ratio'
            ])
            
            print("DFT Time and Space Complexity Analysis")
            print("=" * 80)
            print(f"Testing DFT performance on signal sizes from {step_size} to {max_length}")
            print(f"Step size: {step_size} samples")
            print(f"Expected time complexity: O(N²)")
            print(f"Expected space complexity: O(N)")
            print(f"Results will be saved to {csv_filename}")
            print("-" * 80)
            
            # Measure baseline memory (without any large data structures)
            print("Measuring baseline memory usage...")
            gc.collect()
            time.sleep(1)  # Allow system to stabilize
            initial_baseline_kb = get_memory_usage_kb()
            print(f"Initial baseline memory: {initial_baseline_kb:.2f} KB")
            print("-" * 50)
            
            # Test DFT on range of sizes with step increments
            signal_lengths = range(step_size, max_length + 1, step_size)
            total_tests = len(list(signal_lengths))
            
            for i, signal_length in enumerate(signal_lengths, 1):
                operations_count = signal_length ** 2  # Theoretical O(N²) operations
                
                print(f"\nTesting DFT [{i}/{total_tests}] - Signal length: {signal_length} samples")
                print(f"Expected operations: {operations_count:,}")
                
                try:
                    # Clean memory and establish fresh baseline for this iteration
                    gc.collect()
                    time.sleep(0.5)  # Allow garbage collector to complete
                    baseline_memory_kb = get_memory_usage_kb()
                    
                    # Generate random complex signal and measure memory
                    print(f"  Generating signal with {signal_length} complex samples...")
                    signal = self.generate_random_signal(signal_length)
                    after_signal_gen_kb = get_memory_usage_kb()
                    signal_gen_increment_kb = after_signal_gen_kb - baseline_memory_kb
                    
                    # Measure DFT performance and memory
                    print(f"  Computing DFT and IDFT...")
                    forward_time, inverse_time, after_dft_kb, dft_increment_kb, after_idft_kb, idft_increment_kb, forward_result, reconstructed = \
                        self.measure_dft_performance_with_memory(signal, after_signal_gen_kb)
                    
                    total_time = forward_time + inverse_time
                    
                    # Calculate overall memory metrics
                    peak_memory_kb = max(baseline_memory_kb, after_signal_gen_kb, after_dft_kb, after_idft_kb)
                    total_increment_kb = after_idft_kb - baseline_memory_kb
                    
                    # Verify correctness
                    is_correct = self.verify_correctness(signal, reconstructed)
                    
                    # Calculate theoretical memory usage
                    theoretical_memory = calculate_theoretical_memory_kb(signal_length)
                    
                    # Calculate memory efficiency ratio
                    memory_efficiency_ratio = theoretical_memory['total_theoretical_kb'] / total_increment_kb if total_increment_kb > 0 else 0
                    
                    # Write comprehensive results to CSV with logical column grouping
                    writer.writerow([
                        signal_length, operations_count, is_correct,
                        f"{forward_time:.4f}", f"{inverse_time:.4f}", f"{total_time:.4f}",
                        # Memory progression: baseline → signal_gen → dft → idft
                        f"{baseline_memory_kb:.2f}",
                        f"{after_signal_gen_kb:.2f}",
                        f"{signal_gen_increment_kb:.2f}",
                        f"{after_dft_kb:.2f}",
                        f"{dft_increment_kb:.2f}",
                        f"{after_idft_kb:.2f}",
                        f"{idft_increment_kb:.2f}",
                        f"{peak_memory_kb:.2f}",
                        f"{total_increment_kb:.2f}",
                        # Theoretical analysis
                        f"{theoretical_memory['input_signal_kb']:.2f}",
                        f"{theoretical_memory['dft_algorithm_kb']:.2f}",
                        f"{theoretical_memory['idft_algorithm_kb']:.2f}",
                        f"{theoretical_memory['total_theoretical_kb']:.2f}",
                        f"{memory_efficiency_ratio:.2f}"
                    ])
                    
                    # Print detailed progress with logical memory progression
                    print(f"  ⏱️  Time Complexity:")
                    print(f"    DFT: {forward_time:.4f} ms, IDFT: {inverse_time:.4f} ms")
                    print(f"    Time per operation: {total_time/operations_count*1000:.6f} μs")
                    print(f"  💾 Memory Progression:")
                    print(f"    {baseline_memory_kb:.2f} → {after_signal_gen_kb:.2f} KB (+{signal_gen_increment_kb:.2f} signal gen)")
                    print(f"    {after_signal_gen_kb:.2f} → {after_dft_kb:.2f} KB (+{dft_increment_kb:.2f} DFT)")
                    print(f"    {after_dft_kb:.2f} → {after_idft_kb:.2f} KB (+{idft_increment_kb:.2f} IDFT)")
                    print(f"    Total: {baseline_memory_kb:.2f} → {after_idft_kb:.2f} KB (+{total_increment_kb:.2f} overall)")
                    print(f"  📐 Theoretical vs Actual:")
                    print(f"    Theory: {theoretical_memory['total_theoretical_kb']:.2f} KB, Actual: {total_increment_kb:.2f} KB")
                    print(f"    Efficiency ratio: {memory_efficiency_ratio:.2f}")
                    print(f"  ✓ Correctness: {'✓' if is_correct else '✗'}")
                    print("-" * 50)
                    
                    # Store for analysis
                    self.results.append({
                        'size': signal_length,
                        'time': total_time,
                        'operations': operations_count
                    })
                    
                    csvfile.flush()
                    
                    # Comprehensive cleanup to prevent memory accumulation
                    del signal, forward_result, reconstructed
                    gc.collect()
                    
                except Exception as e:
                    print(f"  Error with DFT size {signal_length}: {str(e)}")
                    # Cleanup on error
                    gc.collect()
                    continue
        
        print(f"\n{'='*80}")
        print(f"DFT Time and Space Complexity Analysis completed!")
        print(f"Results saved to {csv_filename}")
        
        # Print scaling analysis
        self._analyze_scaling_behavior()
        
        print(f"\n{'='*80}")
        print("EXPERIMENT SUMMARY")
        print("="*80)
        print(f"✅ Tested signal sizes: {step_size} to {max_length} (step size: {step_size})")
        print(f"✅ Fresh signal generation per test (no pre-loading bias)")
        print(f"✅ Comprehensive incremental memory analysis")
        
        print(f"\n📈 TIME COMPLEXITY ANALYSIS:")
        print(f"  • DFT Algorithm: O(N²) using naive double-loop implementation")
        print(f"  • IDFT Algorithm: O(N²) (inverse of DFT)")
        print(f"  • Expected scaling: Both should show quadratic growth relative to N")
        
        print(f"\n💾 SPACE COMPLEXITY ANALYSIS:")
        print(f"  • Input signal: O(N) complex numbers = O(N × 16 bytes)")
        print(f"  • DFT result: O(N) complex numbers = O(N × 16 bytes)")
        print(f"  • IDFT result: O(N) complex numbers = O(N × 16 bytes)")
        print(f"  • Algorithm overhead: O(N) minimal overhead for naive implementation")
        print(f"  • Total theoretical: O(N) linear scaling")
        
        print(f"\n📊 OUTPUT CSV STRUCTURE (Logical Column Grouping):")
        print(f"  🔢 Basic: signal_length, operations_count, correctness_verified")
        print(f"  ⏱️  Performance: dft_latency_ms, idft_latency_ms, total_time_ms")
        print(f"  💾 Memory Progression:")
        print(f"      baseline_memory_kb → after_signal_gen_kb → signal_gen_increment_kb")
        print(f"      after_dft_kb → dft_increment_kb")
        print(f"      after_idft_kb → idft_increment_kb")
        print(f"      peak_memory_kb, total_increment_kb")
        print(f"  📐 Theoretical: input_kb, dft_algorithm_kb, idft_algorithm_kb, total_kb")
        print(f"  🎯 Efficiency: memory_efficiency_ratio")
        
        print(f"\n🔍 KEY INSIGHTS:")
        print(f"  • Step-based progression provides fine-grained scaling analysis")
        print(f"  • Incremental analysis shows memory allocation patterns")
        print(f"  • Signal generation increment shows O(N) linear scaling")
        print(f"  • DFT/IDFT increments show algorithm memory overhead")
        print(f"  • Efficiency ratio indicates actual vs theoretical performance")
        print(f"  • Time complexity should demonstrate O(N²) quadratic scaling")
        print(f"  • Space complexity should demonstrate O(N) linear scaling")
        
        return csv_filename
    
    def _analyze_scaling_behavior(self):
        """Analyze the scaling behavior of the DFT implementation"""
        if len(self.results) < 2:
            print("Insufficient data for scaling analysis")
            return
        
        print(f"\nSCALING BEHAVIOR ANALYSIS:")
        print(f"Expected: O(N²) - time should scale quadratically with signal size")
        print("-" * 50)
        
        # Calculate empirical scaling factors
        scaling_factors = []
        for i in range(1, len(self.results)):
            prev_result = self.results[i-1]
            curr_result = self.results[i]
            
            size_ratio = curr_result['size'] / prev_result['size']
            time_ratio = curr_result['time'] / prev_result['time']
            
            if size_ratio > 1 and time_ratio > 0:
                scaling_factor = np.log(time_ratio) / np.log(size_ratio)
                scaling_factors.append(scaling_factor)
                
                print(f"Size {prev_result['size']:4d} → {curr_result['size']:4d}: "
                      f"Time ratio {time_ratio:6.2f}x, "
                      f"Scaling factor {scaling_factor:.2f}")
        
        if scaling_factors:
            avg_scaling = np.mean(scaling_factors)
            print(f"\nAverage empirical scaling factor: {avg_scaling:.2f}")
            print(f"Theoretical O(N²) scaling factor: 2.00")
            print(f"Deviation from theory: {abs(avg_scaling - 2.0):.2f}")
            
            if avg_scaling < 1.5:
                print("Note: Scaling factor may be lower due to overhead dominating at small sizes")
            elif avg_scaling > 2.5:
                print("Note: Scaling factor may be higher due to memory/cache effects at large sizes")
            else:
                print("✓ Scaling behavior consistent with O(N²) complexity")

def main():
    """Main function to run DFT scaling analysis"""
    scaler = DFTScaler()
    
    # Run scaling analysis with step-based progression
    csv_file = scaler.run_scaling_analysis(
        max_length=2**16,    # Up to 65,536 samples
        step_size=256        # Step increment of 256 samples
    )
    
    print(f"\nTo analyze results graphically, you can load {csv_file} in your preferred tool.")
    print("The CSV contains comprehensive time and space complexity data:")
    print("- Basic metrics: signal_length, operations_count, correctness_verified")
    print("- Time metrics: dft_latency_ms, idft_latency_ms, total_time_ms")  
    print("- Memory progression: baseline → signal_gen → dft → idft with increments (in KB)")
    print("- Theoretical analysis: theoretical memory usage and efficiency ratios (in KB)")

if __name__ == "__main__":
    main() 