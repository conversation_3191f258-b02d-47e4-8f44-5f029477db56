[{"input_size": 50, "algorithm_name": "DTW-trend", "timestamp": 1753706134.2252238, "execution_time_ms": 2.2665802389383316, "setup_time_ms": 0.07651792839169502, "cleanup_time_ms": 2.9018856585025787, "total_time_ms": 5.244983825832605, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 50, "sequence_length": 50, "dtw_distance": 7.838340584047208, "normalized_distance": 0.15676681168094414, "distance_calculations": 2500, "theoretical_operations": 2500, "operation_efficiency": 1.0, "matrix_accesses": 7500, "matrix_size": 2601, "theoretical_matrix_size": 2601, "optimal_path_length": 58, "seq1_std": 0.862, "seq2_std": 0.861, "cross_correlation": 0.944, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=50]", "theoretical_space_complexity": "O(n²) [n=50]", "theoretical_memory_mb": 0.01984405517578125, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "DTW-trend", "timestamp": 1753706134.2644808, "execution_time_ms": 8.984585292637348, "setup_time_ms": 0.08131610229611397, "cleanup_time_ms": 2.927308902144432, "total_time_ms": 11.993210297077894, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 100, "sequence_length": 100, "dtw_distance": 13.278388928105892, "normalized_distance": 0.13278388928105891, "distance_calculations": 10000, "theoretical_operations": 10000, "operation_efficiency": 1.0, "matrix_accesses": 30000, "matrix_size": 10201, "theoretical_matrix_size": 10201, "optimal_path_length": 128, "seq1_std": 0.875, "seq2_std": 0.894, "cross_correlation": 0.934, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=100]", "theoretical_space_complexity": "O(n²) [n=100]", "theoretical_memory_mb": 0.07782745361328125, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "DTW-trend", "timestamp": 1753706134.356506, "execution_time_ms": 19.91553120315075, "setup_time_ms": 0.08217617869377136, "cleanup_time_ms": 2.8894790448248386, "total_time_ms": 22.88718642666936, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 150, "sequence_length": 150, "dtw_distance": 22.224756961676164, "normalized_distance": 0.14816504641117442, "distance_calculations": 22500, "theoretical_operations": 22500, "operation_efficiency": 1.0, "matrix_accesses": 67500, "matrix_size": 22801, "theoretical_matrix_size": 22801, "optimal_path_length": 189, "seq1_std": 0.865, "seq2_std": 0.872, "cross_correlation": 0.918, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=150]", "theoretical_space_complexity": "O(n²) [n=150]", "theoretical_memory_mb": 0.17395782470703125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "DTW-trend", "timestamp": 1753706134.538518, "execution_time_ms": 35.171967558562756, "setup_time_ms": 0.08576828986406326, "cleanup_time_ms": 2.95183714479208, "total_time_ms": 38.2095729932189, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 200, "sequence_length": 200, "dtw_distance": 27.594512238967894, "normalized_distance": 0.13797256119483947, "distance_calculations": 40000, "theoretical_operations": 40000, "operation_efficiency": 1.0, "matrix_accesses": 120000, "matrix_size": 40401, "theoretical_matrix_size": 40401, "optimal_path_length": 263, "seq1_std": 0.876, "seq2_std": 0.882, "cross_correlation": 0.923, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=200]", "theoretical_space_complexity": "O(n²) [n=200]", "theoretical_memory_mb": 0.30823516845703125, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "DTW-trend", "timestamp": 1753706134.842814, "execution_time_ms": 57.174139842391014, "setup_time_ms": 0.09030196815729141, "cleanup_time_ms": 2.9656621627509594, "total_time_ms": 60.230103973299265, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 250, "sequence_length": 250, "dtw_distance": 32.1937282184096, "normalized_distance": 0.1287749128736384, "distance_calculations": 62500, "theoretical_operations": 62500, "operation_efficiency": 1.0, "matrix_accesses": 187500, "matrix_size": 63001, "theoretical_matrix_size": 63001, "optimal_path_length": 323, "seq1_std": 0.882, "seq2_std": 0.874, "cross_correlation": 0.923, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=250]", "theoretical_space_complexity": "O(n²) [n=250]", "theoretical_memory_mb": 0.48065948486328125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "DTW-trend", "timestamp": 1753706135.3174012, "execution_time_ms": 79.97715026140213, "setup_time_ms": 0.10363897308707237, "cleanup_time_ms": 2.9402230866253376, "total_time_ms": 83.02101232111454, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 300, "sequence_length": 300, "dtw_distance": 39.71164289143421, "normalized_distance": 0.13237214297144737, "distance_calculations": 90000, "theoretical_operations": 90000, "operation_efficiency": 1.0, "matrix_accesses": 270000, "matrix_size": 90601, "theoretical_matrix_size": 90601, "optimal_path_length": 396, "seq1_std": 0.873, "seq2_std": 0.874, "cross_correlation": 0.915, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=300]", "theoretical_space_complexity": "O(n²) [n=300]", "theoretical_memory_mb": 0.6912307739257812, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "DTW-trend", "timestamp": 1753706135.986045, "execution_time_ms": 109.64948898181319, "setup_time_ms": 0.10711001232266426, "cleanup_time_ms": 2.994332928210497, "total_time_ms": 112.75093192234635, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 350, "sequence_length": 350, "dtw_distance": 51.26674832397576, "normalized_distance": 0.14647642378278788, "distance_calculations": 122500, "theoretical_operations": 122500, "operation_efficiency": 1.0, "matrix_accesses": 367500, "matrix_size": 123201, "theoretical_matrix_size": 123201, "optimal_path_length": 458, "seq1_std": 0.87, "seq2_std": 0.897, "cross_correlation": 0.916, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=350]", "theoretical_space_complexity": "O(n²) [n=350]", "theoretical_memory_mb": 0.9399490356445312, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "DTW-trend", "timestamp": 1753706136.8828022, "execution_time_ms": 145.54050164297223, "setup_time_ms": 0.11166976764798164, "cleanup_time_ms": 3.0357297509908676, "total_time_ms": 148.68790116161108, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 400, "sequence_length": 400, "dtw_distance": 55.10191069923119, "normalized_distance": 0.13775477674807798, "distance_calculations": 160000, "theoretical_operations": 160000, "operation_efficiency": 1.0, "matrix_accesses": 480000, "matrix_size": 160801, "theoretical_matrix_size": 160801, "optimal_path_length": 529, "seq1_std": 0.873, "seq2_std": 0.886, "cross_correlation": 0.913, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=400]", "theoretical_space_complexity": "O(n²) [n=400]", "theoretical_memory_mb": 1.2268142700195312, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "DTW-trend", "timestamp": 1753706138.062389, "execution_time_ms": 182.21282633021474, "setup_time_ms": 0.11904817074537277, "cleanup_time_ms": 2.9970472678542137, "total_time_ms": 185.32892176881433, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 450, "sequence_length": 450, "dtw_distance": 61.43027770703332, "normalized_distance": 0.13651172823785182, "distance_calculations": 202500, "theoretical_operations": 202500, "operation_efficiency": 1.0, "matrix_accesses": 607500, "matrix_size": 203401, "theoretical_matrix_size": 203401, "optimal_path_length": 592, "seq1_std": 0.875, "seq2_std": 0.899, "cross_correlation": 0.922, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=450]", "theoretical_space_complexity": "O(n²) [n=450]", "theoretical_memory_mb": 1.5518264770507812, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "DTW-trend", "timestamp": 1753706139.5376651, "execution_time_ms": 226.0678119957447, "setup_time_ms": 0.1131831668317318, "cleanup_time_ms": 3.0400678515434265, "total_time_ms": 229.22106301411986, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "trend", "input_size": 500, "sequence_length": 500, "dtw_distance": 65.17790880360025, "normalized_distance": 0.1303558176072005, "distance_calculations": 250000, "theoretical_operations": 250000, "operation_efficiency": 1.0, "matrix_accesses": 750000, "matrix_size": 251001, "theoretical_matrix_size": 251001, "optimal_path_length": 662, "seq1_std": 0.872, "seq2_std": 0.9, "cross_correlation": 0.922, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=500]", "theoretical_space_complexity": "O(n²) [n=500]", "theoretical_memory_mb": 1.9149856567382812, "efficiency_ratio": 0.0}]