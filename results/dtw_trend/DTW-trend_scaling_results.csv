input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_sequence_type,custom_input_size,custom_sequence_length,custom_dtw_distance,custom_normalized_distance,custom_distance_calculations,custom_theoretical_operations,custom_operation_efficiency,custom_matrix_accesses,custom_matrix_size,custom_theoretical_matrix_size,custom_optimal_path_length,custom_seq1_std,custom_seq2_std,custom_cross_correlation,custom_algorithm_type
50,DTW-trend,1753706134.2252238,2.2666,0.0765,2.9019,5.2450,63.56,63.56,0.00,0.00,,,,O(n²) [n=50],O(n²) [n=50],0.02,0.0000,trend,50,50,7.838340584047208,0.15676681168094414,2500,2500,1.0,7500,2601,2601,58,0.862,0.861,0.944,dynamic_programming
100,DTW-trend,1753706134.2644808,8.9846,0.0813,2.9273,11.9932,63.56,63.56,0.00,0.00,,,,O(n²) [n=100],O(n²) [n=100],0.08,0.0000,trend,100,100,13.278388928105892,0.13278388928105891,10000,10000,1.0,30000,10201,10201,128,0.875,0.894,0.934,dynamic_programming
150,DTW-trend,1753706134.356506,19.9155,0.0822,2.8895,22.8872,63.56,63.56,0.00,0.00,,,,O(n²) [n=150],O(n²) [n=150],0.17,0.0000,trend,150,150,22.224756961676164,0.14816504641117442,22500,22500,1.0,67500,22801,22801,189,0.865,0.872,0.918,dynamic_programming
200,DTW-trend,1753706134.538518,35.1720,0.0858,2.9518,38.2096,63.56,63.56,0.00,0.00,,,,O(n²) [n=200],O(n²) [n=200],0.31,0.0000,trend,200,200,27.594512238967894,0.13797256119483947,40000,40000,1.0,120000,40401,40401,263,0.876,0.882,0.923,dynamic_programming
250,DTW-trend,1753706134.842814,57.1741,0.0903,2.9657,60.2301,63.56,63.56,0.00,0.00,,,,O(n²) [n=250],O(n²) [n=250],0.48,0.0000,trend,250,250,32.1937282184096,0.1287749128736384,62500,62500,1.0,187500,63001,63001,323,0.882,0.874,0.923,dynamic_programming
300,DTW-trend,1753706135.3174012,79.9772,0.1036,2.9402,83.0210,63.56,63.56,0.00,0.00,,,,O(n²) [n=300],O(n²) [n=300],0.69,0.0000,trend,300,300,39.71164289143421,0.13237214297144737,90000,90000,1.0,270000,90601,90601,396,0.873,0.874,0.915,dynamic_programming
350,DTW-trend,1753706135.986045,109.6495,0.1071,2.9943,112.7509,63.56,63.56,0.00,0.00,,,,O(n²) [n=350],O(n²) [n=350],0.94,0.0000,trend,350,350,51.26674832397576,0.14647642378278788,122500,122500,1.0,367500,123201,123201,458,0.87,0.897,0.916,dynamic_programming
400,DTW-trend,1753706136.8828022,145.5405,0.1117,3.0357,148.6879,63.56,63.56,0.00,0.00,,,,O(n²) [n=400],O(n²) [n=400],1.23,0.0000,trend,400,400,55.10191069923119,0.13775477674807798,160000,160000,1.0,480000,160801,160801,529,0.873,0.886,0.913,dynamic_programming
450,DTW-trend,1753706138.062389,182.2128,0.1190,2.9970,185.3289,63.56,63.56,0.00,0.00,,,,O(n²) [n=450],O(n²) [n=450],1.55,0.0000,trend,450,450,61.43027770703332,0.13651172823785182,202500,202500,1.0,607500,203401,203401,592,0.875,0.899,0.922,dynamic_programming
500,DTW-trend,1753706139.5376651,226.0678,0.1132,3.0401,229.2211,63.56,63.56,0.00,0.00,,,,O(n²) [n=500],O(n²) [n=500],1.91,0.0000,trend,500,500,65.17790880360025,0.1303558176072005,250000,250000,1.0,750000,251001,251001,662,0.872,0.9,0.922,dynamic_programming
