[{"input_size": 100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657379.2155373, "execution_time_ms": 0.15342021360993385, "setup_time_ms": 0.0765342265367508, "cleanup_time_ms": 90.36257397383451, "total_time_ms": 90.5925284139812, "baseline_memory_mb": 648.609375, "peak_memory_mb": 649.53125, "memory_increment_mb": 0.921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0016551906779661016}, {"input_size": 600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657379.9468203, "execution_time_ms": 0.1531730405986309, "setup_time_ms": 0.06792321801185608, "cleanup_time_ms": 99.35087012127042, "total_time_ms": 99.5719663798809, "baseline_memory_mb": 649.53125, "peak_memory_mb": 649.53125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 1200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657380.600435, "execution_time_ms": 0.25877486914396286, "setup_time_ms": 0.06761308759450912, "cleanup_time_ms": 101.87286091968417, "total_time_ms": 102.19924887642264, "baseline_memory_mb": 649.53125, "peak_memory_mb": 649.54296875, "memory_increment_mb": 0.01171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 2200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 1.4322916666666667}, {"input_size": 1600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657381.4810073, "execution_time_ms": 0.17551593482494354, "setup_time_ms": 0.07606996223330498, "cleanup_time_ms": 97.7012007497251, "total_time_ms": 97.95278664678335, "baseline_memory_mb": 649.54296875, "peak_memory_mb": 649.54296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 3200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 2100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657382.174235, "execution_time_ms": 0.1874081790447235, "setup_time_ms": 0.09674765169620514, "cleanup_time_ms": 105.93635402619839, "total_time_ms": 106.22050985693932, "baseline_memory_mb": 649.54296875, "peak_memory_mb": 649.5546875, "memory_increment_mb": 0.01171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 4200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.03204345703125, "efficiency_ratio": 2.734375}, {"input_size": 2600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657382.8761654, "execution_time_ms": 0.19683660939335823, "setup_time_ms": 0.11477712541818619, "cleanup_time_ms": 99.11859687417746, "total_time_ms": 99.430210608989, "baseline_memory_mb": 649.5546875, "peak_memory_mb": 649.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 5200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0396728515625, "efficiency_ratio": 0.0}, {"input_size": 3100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657383.6127508, "execution_time_ms": 0.21714521571993828, "setup_time_ms": 0.11825608089566231, "cleanup_time_ms": 98.06773392483592, "total_time_ms": 98.40313522145152, "baseline_memory_mb": 649.5546875, "peak_memory_mb": 649.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 6200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.04730224609375, "efficiency_ratio": 0.0}, {"input_size": 3600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657384.3070729, "execution_time_ms": 0.20457645878195763, "setup_time_ms": 0.14045601710677147, "cleanup_time_ms": 98.09416020289063, "total_time_ms": 98.43919267877936, "baseline_memory_mb": 649.5546875, "peak_memory_mb": 649.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 7200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 4100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657385.0005445, "execution_time_ms": 0.2315053716301918, "setup_time_ms": 0.12958189472556114, "cleanup_time_ms": 98.79689989611506, "total_time_ms": 99.15798716247082, "baseline_memory_mb": 649.5546875, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 8200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.06256103515625, "efficiency_ratio": 8.0078125}, {"input_size": 4600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657385.7462337, "execution_time_ms": 0.2195536158978939, "setup_time_ms": 0.16460195183753967, "cleanup_time_ms": 100.7889797911048, "total_time_ms": 101.17313535884023, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 9200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0701904296875, "efficiency_ratio": 0.0}, {"input_size": 5100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657386.4397798, "execution_time_ms": 0.2327345311641693, "setup_time_ms": 0.15547499060630798, "cleanup_time_ms": 97.9222790338099, "total_time_ms": 98.31048855558038, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 10200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.07781982421875, "efficiency_ratio": 0.0}, {"input_size": 5600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657387.1340747, "execution_time_ms": 0.23148907348513603, "setup_time_ms": 0.1757713034749031, "cleanup_time_ms": 98.63283019512892, "total_time_ms": 99.04009057208896, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 11200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.08544921875, "efficiency_ratio": 0.0}, {"input_size": 6100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657387.8251967, "execution_time_ms": 0.23528290912508965, "setup_time_ms": 0.17930008471012115, "cleanup_time_ms": 97.92891517281532, "total_time_ms": 98.34349816665053, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 12200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.09307861328125, "efficiency_ratio": 0.0}, {"input_size": 6600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657388.518895, "execution_time_ms": 0.26435861364006996, "setup_time_ms": 0.180710107088089, "cleanup_time_ms": 128.56321083381772, "total_time_ms": 129.00827955454588, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 13200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1007080078125, "efficiency_ratio": 0.0}, {"input_size": 7100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657389.2809033, "execution_time_ms": 0.2608747221529484, "setup_time_ms": 0.2402770332992077, "cleanup_time_ms": 114.95818896219134, "total_time_ms": 115.4593407176435, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 14200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.10833740234375, "efficiency_ratio": 0.0}, {"input_size": 7600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657390.0825124, "execution_time_ms": 0.2516435459256172, "setup_time_ms": 0.22073974832892418, "cleanup_time_ms": 97.89802506566048, "total_time_ms": 98.37040835991502, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 15200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.115966796875, "efficiency_ratio": 0.0}, {"input_size": 8100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657390.799813, "execution_time_ms": 0.26169028133153915, "setup_time_ms": 0.2140630967915058, "cleanup_time_ms": 99.34248588979244, "total_time_ms": 99.81823926791549, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 16200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.12359619140625, "efficiency_ratio": 0.0}, {"input_size": 8600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657391.5022886, "execution_time_ms": 0.2778945490717888, "setup_time_ms": 0.22470485419034958, "cleanup_time_ms": 100.405755918473, "total_time_ms": 100.90835532173514, "baseline_memory_mb": 649.5625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 17200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1312255859375, "efficiency_ratio": 33.59375}, {"input_size": 9100, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657392.1996756, "execution_time_ms": 0.26955418288707733, "setup_time_ms": 0.247919000685215, "cleanup_time_ms": 99.4571759365499, "total_time_ms": 99.9746491201222, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9100, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 18200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.13885498046875, "efficiency_ratio": 0.0}, {"input_size": 9600, "algorithm_name": "Wavelet-db4-dwt", "timestamp": 1753657392.8997118, "execution_time_ms": 0.2704302780330181, "setup_time_ms": 0.2459879033267498, "cleanup_time_ms": 98.92190899699926, "total_time_ms": 99.43832717835903, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9600, "wavelet_type": "db4", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 19200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.146484375, "efficiency_ratio": 0.0}]