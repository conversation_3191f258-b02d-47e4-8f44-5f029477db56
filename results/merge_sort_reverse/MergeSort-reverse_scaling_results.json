[{"input_size": 100, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.0935311, "execution_time_ms": 0.20425301045179367, "setup_time_ms": 0.008847098797559738, "cleanup_time_ms": 2.944808918982744, "total_time_ms": 3.1579090282320976, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 100, "actual_comparisons": 356, "theoretical_comparisons": 664, "comparison_efficiency": 1.866, "array_accesses": 1700, "max_recursion_depth": 7, "theoretical_depth": 7, "correctness_verified": true, "input_inversions": 4950, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=100]", "theoretical_space_complexity": "O(n) [n=100]", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.1198883, "execution_time_ms": 0.4029223695397377, "setup_time_ms": 0.004942994564771652, "cleanup_time_ms": 2.947792410850525, "total_time_ms": 3.3556577749550343, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 200, "actual_comparisons": 812, "theoretical_comparisons": 1528, "comparison_efficiency": 1.883, "array_accesses": 3900, "max_recursion_depth": 8, "theoretical_depth": 8, "correctness_verified": true, "input_inversions": 19900, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=200]", "theoretical_space_complexity": "O(n) [n=200]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.1446164, "execution_time_ms": 0.6176844239234924, "setup_time_ms": 0.004944857209920883, "cleanup_time_ms": 2.8955177403986454, "total_time_ms": 3.5181470215320587, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 300, "actual_comparisons": 1308, "theoretical_comparisons": 2468, "comparison_efficiency": 1.887, "array_accesses": 6284, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 44850, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=300]", "theoretical_space_complexity": "O(n) [n=300]", "theoretical_memory_mb": 0.00457763671875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.1721618, "execution_time_ms": 0.8586778305470943, "setup_time_ms": 0.005474314093589783, "cleanup_time_ms": 2.876431215554476, "total_time_ms": 3.74058336019516, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 400, "actual_comparisons": 1824, "theoretical_comparisons": 3457, "comparison_efficiency": 1.896, "array_accesses": 8800, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 79800, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=400]", "theoretical_space_complexity": "O(n) [n=400]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.203275, "execution_time_ms": 1.105472818017006, "setup_time_ms": 0.006224960088729858, "cleanup_time_ms": 3.062369767576456, "total_time_ms": 4.174067545682192, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 500, "actual_comparisons": 2272, "theoretical_comparisons": 4482, "comparison_efficiency": 1.973, "array_accesses": 11248, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 124750, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=500]", "theoretical_space_complexity": "O(n) [n=500]", "theoretical_memory_mb": 0.00762939453125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.238926, "execution_time_ms": 1.3053331524133682, "setup_time_ms": 0.009446870535612106, "cleanup_time_ms": 2.8676390647888184, "total_time_ms": 4.182419087737799, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 600, "actual_comparisons": 2916, "theoretical_comparisons": 5537, "comparison_efficiency": 1.899, "array_accesses": 14068, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 179700, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=600]", "theoretical_space_complexity": "O(n) [n=600]", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.2793126, "execution_time_ms": 1.5165109187364578, "setup_time_ms": 0.008147209882736206, "cleanup_time_ms": 2.841206733137369, "total_time_ms": 4.365864861756563, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 700, "actual_comparisons": 3504, "theoretical_comparisons": 6615, "comparison_efficiency": 1.888, "array_accesses": 16856, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 244650, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=700]", "theoretical_space_complexity": "O(n) [n=700]", "theoretical_memory_mb": 0.01068115234375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.323721, "execution_time_ms": 1.7669186927378178, "setup_time_ms": 0.011714175343513489, "cleanup_time_ms": 2.9479488730430603, "total_time_ms": 4.7265817411243916, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 800, "actual_comparisons": 4048, "theoretical_comparisons": 7715, "comparison_efficiency": 1.906, "array_accesses": 19600, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 319600, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=800]", "theoretical_space_complexity": "O(n) [n=800]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.3746345, "execution_time_ms": 1.9928171299397945, "setup_time_ms": 0.010943971574306488, "cleanup_time_ms": 2.8368202038109303, "total_time_ms": 4.840581305325031, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 900, "actual_comparisons": 4572, "theoretical_comparisons": 8832, "comparison_efficiency": 1.932, "array_accesses": 22324, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 404550, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=900]", "theoretical_space_complexity": "O(n) [n=900]", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.4298978, "execution_time_ms": 2.131449803709984, "setup_time_ms": 0.011040829122066498, "cleanup_time_ms": 2.7781310491263866, "total_time_ms": 4.920621681958437, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1000, "actual_comparisons": 5044, "theoretical_comparisons": 9965, "comparison_efficiency": 1.976, "array_accesses": 24996, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 499500, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1000]", "theoretical_space_complexity": "O(n) [n=1000]", "theoretical_memory_mb": 0.0152587890625, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.4909377, "execution_time_ms": 2.4527824483811855, "setup_time_ms": 0.012006144970655441, "cleanup_time_ms": 2.987559884786606, "total_time_ms": 5.452348478138447, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1100, "actual_comparisons": 5732, "theoretical_comparisons": 11113, "comparison_efficiency": 1.939, "array_accesses": 28036, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 604450, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1100]", "theoretical_space_complexity": "O(n) [n=1100]", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1200, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.560748, "execution_time_ms": 2.754616178572178, "setup_time_ms": 0.015519093722105026, "cleanup_time_ms": 2.8285114094614983, "total_time_ms": 5.598646681755781, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1200, "actual_comparisons": 6432, "theoretical_comparisons": 12274, "comparison_efficiency": 1.908, "array_accesses": 31136, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 719400, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1200]", "theoretical_space_complexity": "O(n) [n=1200]", "theoretical_memory_mb": 0.018310546875, "efficiency_ratio": 0.0}, {"input_size": 1300, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.6381025, "execution_time_ms": 2.9092978686094284, "setup_time_ms": 0.031348783522844315, "cleanup_time_ms": 2.898579929023981, "total_time_ms": 5.839226581156254, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1300, "actual_comparisons": 7072, "theoretical_comparisons": 13447, "comparison_efficiency": 1.902, "array_accesses": 34176, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 844350, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1300]", "theoretical_space_complexity": "O(n) [n=1300]", "theoretical_memory_mb": 0.01983642578125, "efficiency_ratio": 0.0}, {"input_size": 1400, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.7229369, "execution_time_ms": 3.2077182084321976, "setup_time_ms": 0.01581292599439621, "cleanup_time_ms": 2.8526061214506626, "total_time_ms": 6.076137255877256, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1400, "actual_comparisons": 7708, "theoretical_comparisons": 14631, "comparison_efficiency": 1.898, "array_accesses": 37212, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 979300, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1400]", "theoretical_space_complexity": "O(n) [n=1400]", "theoretical_memory_mb": 0.0213623046875, "efficiency_ratio": 0.0}, {"input_size": 1500, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.817166, "execution_time_ms": 3.337411768734455, "setup_time_ms": 0.014980323612689972, "cleanup_time_ms": 2.7800300158560276, "total_time_ms": 6.132422108203173, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1500, "actual_comparisons": 8288, "theoretical_comparisons": 15826, "comparison_efficiency": 1.91, "array_accesses": 40192, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1124250, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1500]", "theoretical_space_complexity": "O(n) [n=1500]", "theoretical_memory_mb": 0.02288818359375, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653400.918054, "execution_time_ms": 3.5761970095336437, "setup_time_ms": 0.0170278362929821, "cleanup_time_ms": 2.832626923918724, "total_time_ms": 6.42585176974535, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1600, "actual_comparisons": 8896, "theoretical_comparisons": 17030, "comparison_efficiency": 1.914, "array_accesses": 43200, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1279200, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1600]", "theoretical_space_complexity": "O(n) [n=1600]", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 1700, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653401.0258193, "execution_time_ms": 3.903191816061735, "setup_time_ms": 0.018658116459846497, "cleanup_time_ms": 3.590796608477831, "total_time_ms": 7.5126465409994125, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1700, "actual_comparisons": 9516, "theoretical_comparisons": 18243, "comparison_efficiency": 1.917, "array_accesses": 46220, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1444150, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1700]", "theoretical_space_complexity": "O(n) [n=1700]", "theoretical_memory_mb": 0.02593994140625, "efficiency_ratio": 0.0}, {"input_size": 1800, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653401.1482866, "execution_time_ms": 4.441847186535597, "setup_time_ms": 0.019079074263572693, "cleanup_time_ms": 3.634172026067972, "total_time_ms": 8.095098286867142, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1800, "actual_comparisons": 10044, "theoretical_comparisons": 19464, "comparison_efficiency": 1.938, "array_accesses": 49148, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1619100, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1800]", "theoretical_space_complexity": "O(n) [n=1800]", "theoretical_memory_mb": 0.0274658203125, "efficiency_ratio": 0.0}, {"input_size": 1900, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653401.285332, "execution_time_ms": 4.389696381986141, "setup_time_ms": 0.018899794667959213, "cleanup_time_ms": 2.7738818898797035, "total_time_ms": 7.182478066533804, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 1900, "actual_comparisons": 10608, "theoretical_comparisons": 20694, "comparison_efficiency": 1.951, "array_accesses": 52112, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1804050, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1900]", "theoretical_space_complexity": "O(n) [n=1900]", "theoretical_memory_mb": 0.02899169921875, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "MergeSort-reverse", "timestamp": 1753653401.4241107, "execution_time_ms": 4.457336291670799, "setup_time_ms": 0.020802952349185944, "cleanup_time_ms": 2.8045629151165485, "total_time_ms": 7.282702159136534, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "input_size": 2000, "actual_comparisons": 11088, "theoretical_comparisons": 21931, "comparison_efficiency": 1.978, "array_accesses": 54992, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 1999000, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=2000]", "theoretical_space_complexity": "O(n) [n=2000]", "theoretical_memory_mb": 0.030517578125, "efficiency_ratio": 0.0}]