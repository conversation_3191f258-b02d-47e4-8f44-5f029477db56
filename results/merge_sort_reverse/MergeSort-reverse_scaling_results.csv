input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_input_size,custom_actual_comparisons,custom_theoretical_comparisons,custom_comparison_efficiency,custom_array_accesses,custom_max_recursion_depth,custom_theoretical_depth,custom_correctness_verified,custom_input_inversions,custom_was_already_sorted,custom_algorithm_type
100,MergeSort-reverse,1753653400.0935311,0.2043,0.0088,2.9448,3.1579,57.32,57.32,0.00,0.00,,,,O(n log n) [n=100],O(n) [n=100],0.00,0.0000,reverse,100,356,664,1.866,1700,7,7,True,4950,False,divide_and_conquer
200,MergeSort-reverse,1753653400.1198883,0.4029,0.0049,2.9478,3.3557,57.32,57.32,0.00,0.00,,,,O(n log n) [n=200],O(n) [n=200],0.00,0.0000,reverse,200,812,1528,1.883,3900,8,8,True,19900,False,divide_and_conquer
300,MergeSort-reverse,1753653400.1446164,0.6177,0.0049,2.8955,3.5181,57.32,57.32,0.00,0.00,,,,O(n log n) [n=300],O(n) [n=300],0.00,0.0000,reverse,300,1308,2468,1.887,6284,9,9,True,44850,False,divide_and_conquer
400,MergeSort-reverse,1753653400.1721618,0.8587,0.0055,2.8764,3.7406,57.32,57.32,0.00,0.00,,,,O(n log n) [n=400],O(n) [n=400],0.01,0.0000,reverse,400,1824,3457,1.896,8800,9,9,True,79800,False,divide_and_conquer
500,MergeSort-reverse,1753653400.203275,1.1055,0.0062,3.0624,4.1741,57.32,57.32,0.00,0.00,,,,O(n log n) [n=500],O(n) [n=500],0.01,0.0000,reverse,500,2272,4482,1.973,11248,9,9,True,124750,False,divide_and_conquer
600,MergeSort-reverse,1753653400.238926,1.3053,0.0094,2.8676,4.1824,57.32,57.32,0.00,0.00,,,,O(n log n) [n=600],O(n) [n=600],0.01,0.0000,reverse,600,2916,5537,1.899,14068,10,10,True,179700,False,divide_and_conquer
700,MergeSort-reverse,1753653400.2793126,1.5165,0.0081,2.8412,4.3659,57.32,57.32,0.00,0.00,,,,O(n log n) [n=700],O(n) [n=700],0.01,0.0000,reverse,700,3504,6615,1.888,16856,10,10,True,244650,False,divide_and_conquer
800,MergeSort-reverse,1753653400.323721,1.7669,0.0117,2.9479,4.7266,57.32,57.32,0.00,0.00,,,,O(n log n) [n=800],O(n) [n=800],0.01,0.0000,reverse,800,4048,7715,1.906,19600,10,10,True,319600,False,divide_and_conquer
900,MergeSort-reverse,1753653400.3746345,1.9928,0.0109,2.8368,4.8406,57.32,57.32,0.00,0.00,,,,O(n log n) [n=900],O(n) [n=900],0.01,0.0000,reverse,900,4572,8832,1.932,22324,10,10,True,404550,False,divide_and_conquer
1000,MergeSort-reverse,1753653400.4298978,2.1314,0.0110,2.7781,4.9206,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1000],O(n) [n=1000],0.02,0.0000,reverse,1000,5044,9965,1.976,24996,10,10,True,499500,False,divide_and_conquer
1100,MergeSort-reverse,1753653400.4909377,2.4528,0.0120,2.9876,5.4523,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1100],O(n) [n=1100],0.02,0.0000,reverse,1100,5732,11113,1.939,28036,11,11,True,604450,False,divide_and_conquer
1200,MergeSort-reverse,1753653400.560748,2.7546,0.0155,2.8285,5.5986,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1200],O(n) [n=1200],0.02,0.0000,reverse,1200,6432,12274,1.908,31136,11,11,True,719400,False,divide_and_conquer
1300,MergeSort-reverse,1753653400.6381025,2.9093,0.0313,2.8986,5.8392,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1300],O(n) [n=1300],0.02,0.0000,reverse,1300,7072,13447,1.902,34176,11,11,True,844350,False,divide_and_conquer
1400,MergeSort-reverse,1753653400.7229369,3.2077,0.0158,2.8526,6.0761,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1400],O(n) [n=1400],0.02,0.0000,reverse,1400,7708,14631,1.898,37212,11,11,True,979300,False,divide_and_conquer
1500,MergeSort-reverse,1753653400.817166,3.3374,0.0150,2.7800,6.1324,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1500],O(n) [n=1500],0.02,0.0000,reverse,1500,8288,15826,1.91,40192,11,11,True,1124250,False,divide_and_conquer
1600,MergeSort-reverse,1753653400.918054,3.5762,0.0170,2.8326,6.4259,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1600],O(n) [n=1600],0.02,0.0000,reverse,1600,8896,17030,1.914,43200,11,11,True,1279200,False,divide_and_conquer
1700,MergeSort-reverse,1753653401.0258193,3.9032,0.0187,3.5908,7.5126,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1700],O(n) [n=1700],0.03,0.0000,reverse,1700,9516,18243,1.917,46220,11,11,True,1444150,False,divide_and_conquer
1800,MergeSort-reverse,1753653401.1482866,4.4418,0.0191,3.6342,8.0951,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1800],O(n) [n=1800],0.03,0.0000,reverse,1800,10044,19464,1.938,49148,11,11,True,1619100,False,divide_and_conquer
1900,MergeSort-reverse,1753653401.285332,4.3897,0.0189,2.7739,7.1825,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1900],O(n) [n=1900],0.03,0.0000,reverse,1900,10608,20694,1.951,52112,11,11,True,1804050,False,divide_and_conquer
2000,MergeSort-reverse,1753653401.4241107,4.4573,0.0208,2.8046,7.2827,57.32,57.32,0.00,0.00,,,,O(n log n) [n=2000],O(n) [n=2000],0.03,0.0000,reverse,2000,11088,21931,1.978,54992,11,11,True,1999000,False,divide_and_conquer
