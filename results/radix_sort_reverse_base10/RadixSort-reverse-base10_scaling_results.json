[{"input_size": 100, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654418.7686052, "execution_time_ms": 0.3692065365612507, "setup_time_ms": 0.008872244507074356, "cleanup_time_ms": 23.663241881877184, "total_time_ms": 24.04132066294551, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 100, "max_number": 100, "num_digits": 3, "actual_digit_extractions": 600, "theoretical_digit_extractions": 600, "digit_efficiency": 1.0, "array_accesses": 1000, "bucket_operations": 327, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=100, k=10]", "theoretical_space_complexity": "O(n+k) [n=100, k=10]", "theoretical_memory_mb": 0.0016021728515625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654418.951597, "execution_time_ms": 0.6504729390144348, "setup_time_ms": 0.009343959391117096, "cleanup_time_ms": 23.288161028176546, "total_time_ms": 23.947977926582098, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 200, "max_number": 200, "num_digits": 3, "actual_digit_extractions": 1200, "theoretical_digit_extractions": 1200, "digit_efficiency": 1.0, "array_accesses": 2000, "bucket_operations": 627, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 200, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=200, k=10]", "theoretical_space_complexity": "O(n+k) [n=200, k=10]", "theoretical_memory_mb": 0.0031280517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.1284912, "execution_time_ms": 0.9246337227523327, "setup_time_ms": 0.00981288030743599, "cleanup_time_ms": 24.094693828374147, "total_time_ms": 25.029140431433916, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 300, "max_number": 300, "num_digits": 3, "actual_digit_extractions": 1800, "theoretical_digit_extractions": 1800, "digit_efficiency": 1.0, "array_accesses": 3000, "bucket_operations": 927, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 300, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=300, k=10]", "theoretical_space_complexity": "O(n+k) [n=300, k=10]", "theoretical_memory_mb": 0.0046539306640625, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.3020854, "execution_time_ms": 1.2315290048718452, "setup_time_ms": 0.012890901416540146, "cleanup_time_ms": 22.84826198592782, "total_time_ms": 24.092681892216206, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 400, "max_number": 400, "num_digits": 3, "actual_digit_extractions": 2400, "theoretical_digit_extractions": 2400, "digit_efficiency": 1.0, "array_accesses": 4000, "bucket_operations": 1227, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 400, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=400, k=10]", "theoretical_space_complexity": "O(n+k) [n=400, k=10]", "theoretical_memory_mb": 0.0061798095703125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.4719033, "execution_time_ms": 1.4795697294175625, "setup_time_ms": 0.014038756489753723, "cleanup_time_ms": 22.728451061993837, "total_time_ms": 24.222059547901154, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 500, "max_number": 500, "num_digits": 3, "actual_digit_extractions": 3000, "theoretical_digit_extractions": 3000, "digit_efficiency": 1.0, "array_accesses": 5000, "bucket_operations": 1527, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 500, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=500, k=10]", "theoretical_space_complexity": "O(n+k) [n=500, k=10]", "theoretical_memory_mb": 0.0077056884765625, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.6429303, "execution_time_ms": 1.7989584244787693, "setup_time_ms": 0.01806020736694336, "cleanup_time_ms": 22.617849055677652, "total_time_ms": 24.434867687523365, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 600, "max_number": 600, "num_digits": 3, "actual_digit_extractions": 3600, "theoretical_digit_extractions": 3600, "digit_efficiency": 1.0, "array_accesses": 6000, "bucket_operations": 1827, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 600, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=600, k=10]", "theoretical_space_complexity": "O(n+k) [n=600, k=10]", "theoretical_memory_mb": 0.0092315673828125, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.8147929, "execution_time_ms": 2.131873182952404, "setup_time_ms": 0.02011191099882126, "cleanup_time_ms": 22.484830114990473, "total_time_ms": 24.636815208941698, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 700, "max_number": 700, "num_digits": 3, "actual_digit_extractions": 4200, "theoretical_digit_extractions": 4200, "digit_efficiency": 1.0, "array_accesses": 7000, "bucket_operations": 2127, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 700, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=700, k=10]", "theoretical_space_complexity": "O(n+k) [n=700, k=10]", "theoretical_memory_mb": 0.0107574462890625, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654419.9923787, "execution_time_ms": 2.3829367011785507, "setup_time_ms": 0.02201925963163376, "cleanup_time_ms": 22.410539910197258, "total_time_ms": 24.815495871007442, "baseline_memory_mb": 411.61328125, "peak_memory_mb": 411.61328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 800, "max_number": 800, "num_digits": 3, "actual_digit_extractions": 4800, "theoretical_digit_extractions": 4800, "digit_efficiency": 1.0, "array_accesses": 8000, "bucket_operations": 2427, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 800, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=800, k=10]", "theoretical_space_complexity": "O(n+k) [n=800, k=10]", "theoretical_memory_mb": 0.0122833251953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654420.1686745, "execution_time_ms": 2.684514969587326, "setup_time_ms": 0.031033065170049667, "cleanup_time_ms": 22.962354123592377, "total_time_ms": 25.677902158349752, "baseline_memory_mb": 411.6328125, "peak_memory_mb": 411.6328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 900, "max_number": 900, "num_digits": 3, "actual_digit_extractions": 5400, "theoretical_digit_extractions": 5400, "digit_efficiency": 1.0, "array_accesses": 9000, "bucket_operations": 2727, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 900, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=900, k=10]", "theoretical_space_complexity": "O(n+k) [n=900, k=10]", "theoretical_memory_mb": 0.0138092041015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-reverse-base10", "timestamp": 1753654420.3627613, "execution_time_ms": 2.8699837625026703, "setup_time_ms": 0.026592053472995758, "cleanup_time_ms": 22.94815005734563, "total_time_ms": 25.844725873321295, "baseline_memory_mb": 411.6328125, "peak_memory_mb": 411.6328125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "reverse", "radix_base": 10, "input_size": 1000, "max_number": 1000, "num_digits": 4, "actual_digit_extractions": 6000, "theoretical_digit_extractions": 8000, "digit_efficiency": 1.333, "array_accesses": 10000, "bucket_operations": 3027, "comparisons_used": 0, "correctness_verified": false, "unique_elements": 1000, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=5, n=1000, k=10]", "theoretical_space_complexity": "O(n+k) [n=1000, k=10]", "theoretical_memory_mb": 0.0153350830078125, "efficiency_ratio": 0.0}]