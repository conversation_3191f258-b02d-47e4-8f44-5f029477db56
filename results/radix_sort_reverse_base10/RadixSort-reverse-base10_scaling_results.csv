input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-reverse-base10,1753654418.7686052,0.3692,0.0089,23.6632,24.0413,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=100, k=10]","O(n+k) [n=100, k=10]",0.00,0.0000,reverse,10,100,100,3,600,600,1.0,1000,327,0,True,100,1.0,False,3.322,non_comparative
200,RadixSort-reverse-base10,1753654418.951597,0.6505,0.0093,23.2882,23.9480,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=200, k=10]","O(n+k) [n=200, k=10]",0.00,0.0000,reverse,10,200,200,3,1200,1200,1.0,2000,627,0,True,200,1.0,False,3.322,non_comparative
300,RadixSort-reverse-base10,1753654419.1284912,0.9246,0.0098,24.0947,25.0291,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=300, k=10]","O(n+k) [n=300, k=10]",0.00,0.0000,reverse,10,300,300,3,1800,1800,1.0,3000,927,0,True,300,1.0,False,3.322,non_comparative
400,RadixSort-reverse-base10,1753654419.3020854,1.2315,0.0129,22.8483,24.0927,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=400, k=10]","O(n+k) [n=400, k=10]",0.01,0.0000,reverse,10,400,400,3,2400,2400,1.0,4000,1227,0,True,400,1.0,False,3.322,non_comparative
500,RadixSort-reverse-base10,1753654419.4719033,1.4796,0.0140,22.7285,24.2221,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=500, k=10]","O(n+k) [n=500, k=10]",0.01,0.0000,reverse,10,500,500,3,3000,3000,1.0,5000,1527,0,True,500,1.0,False,3.322,non_comparative
600,RadixSort-reverse-base10,1753654419.6429303,1.7990,0.0181,22.6178,24.4349,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=600, k=10]","O(n+k) [n=600, k=10]",0.01,0.0000,reverse,10,600,600,3,3600,3600,1.0,6000,1827,0,True,600,1.0,False,3.322,non_comparative
700,RadixSort-reverse-base10,1753654419.8147929,2.1319,0.0201,22.4848,24.6368,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=700, k=10]","O(n+k) [n=700, k=10]",0.01,0.0000,reverse,10,700,700,3,4200,4200,1.0,7000,2127,0,True,700,1.0,False,3.322,non_comparative
800,RadixSort-reverse-base10,1753654419.9923787,2.3829,0.0220,22.4105,24.8155,411.61,411.61,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=800, k=10]","O(n+k) [n=800, k=10]",0.01,0.0000,reverse,10,800,800,3,4800,4800,1.0,8000,2427,0,True,800,1.0,False,3.322,non_comparative
900,RadixSort-reverse-base10,1753654420.1686745,2.6845,0.0310,22.9624,25.6779,411.63,411.63,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=900, k=10]","O(n+k) [n=900, k=10]",0.01,0.0000,reverse,10,900,900,3,5400,5400,1.0,9000,2727,0,True,900,1.0,False,3.322,non_comparative
1000,RadixSort-reverse-base10,1753654420.3627613,2.8700,0.0266,22.9482,25.8447,411.63,411.63,0.00,0.00,,,,"O(d*(n+k)) [d=5, n=1000, k=10]","O(n+k) [n=1000, k=10]",0.02,0.0000,reverse,10,1000,1000,4,6000,8000,1.333,10000,3027,0,False,1000,1.0,False,3.322,non_comparative
