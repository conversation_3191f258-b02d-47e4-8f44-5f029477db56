input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-random-base10,1753654414.837093,0.5982,0.1044,31.7873,32.4899,410.07,411.26,1.18,0.00,,,,"O(d*(n+k)) [d=4, n=100, k=10]","O(n+k) [n=100, k=10]",0.00,0.0014,random,10,100,9981,4,800,800,1.0,1300,436,0,True,100,1.0,False,3.245,non_comparative
200,RadixSort-random-base10,1753654415.197198,1.6217,0.2818,22.1242,24.0277,411.26,411.26,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=200, k=10]","O(n+k) [n=200, k=10]",0.00,0.0000,random,10,200,19961,5,2000,2000,1.0,3200,1045,0,True,198,0.99,False,3.296,non_comparative
300,RadixSort-random-base10,1753654415.4097347,1.4773,0.2336,22.6921,24.4030,411.26,411.26,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=300, k=10]","O(n+k) [n=300, k=10]",0.00,0.0000,random,10,300,29968,5,3000,3000,1.0,4800,1545,0,True,297,0.99,False,3.302,non_comparative
400,RadixSort-random-base10,1753654415.5834277,2.0299,0.3012,22.9479,25.2790,411.26,411.26,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=400, k=10]","O(n+k) [n=400, k=10]",0.01,0.0000,random,10,400,39921,5,4000,4000,1.0,6400,2045,0,True,395,0.988,False,3.304,non_comparative
500,RadixSort-random-base10,1753654415.757926,2.4775,0.3690,22.7701,25.6166,411.26,411.26,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=500, k=10]","O(n+k) [n=500, k=10]",0.01,0.0000,random,10,500,49972,5,5000,5000,1.0,8000,2545,0,True,493,0.986,False,3.309,non_comparative
600,RadixSort-random-base10,1753654415.9368167,2.9809,0.4071,22.6538,26.0418,411.47,411.48,0.01,0.00,,,,"O(d*(n+k)) [d=4, n=600, k=10]","O(n+k) [n=600, k=10]",0.01,1.1816,random,10,600,59936,5,6000,6000,1.0,9600,3045,0,True,593,0.988,False,3.314,non_comparative
700,RadixSort-random-base10,1753654416.1257944,3.4684,0.5397,22.9879,26.9960,411.48,411.48,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=700, k=10]","O(n+k) [n=700, k=10]",0.01,0.0000,random,10,700,69828,5,7000,7000,1.0,11200,3545,0,True,698,0.997,False,3.319,non_comparative
800,RadixSort-random-base10,1753654416.3131204,3.9221,0.5789,22.6860,27.1870,411.48,411.48,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=800, k=10]","O(n+k) [n=800, k=10]",0.01,0.0000,random,10,800,79998,5,8000,8000,1.0,12800,4045,0,True,796,0.995,False,3.319,non_comparative
900,RadixSort-random-base10,1753654416.5043566,4.3987,0.6109,22.4636,27.4732,411.48,411.48,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=900, k=10]","O(n+k) [n=900, k=10]",0.01,0.0000,random,10,900,89734,5,9000,9000,1.0,14400,4545,0,True,894,0.993,False,3.318,non_comparative
1000,RadixSort-random-base10,1753654416.698943,4.8523,0.6659,22.0930,27.6112,411.48,411.48,0.00,0.00,,,,"O(d*(n+k)) [d=5, n=1000, k=10]","O(n+k) [n=1000, k=10]",0.02,0.0000,random,10,1000,99944,5,10000,10000,1.0,16000,5045,0,True,994,0.994,False,3.319,non_comparative
