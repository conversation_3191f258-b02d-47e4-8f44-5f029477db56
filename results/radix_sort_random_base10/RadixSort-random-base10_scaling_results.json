[{"input_size": 100, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654414.837093, "execution_time_ms": 0.5981956608593464, "setup_time_ms": 0.10440219193696976, "cleanup_time_ms": 31.787320040166378, "total_time_ms": 32.489917892962694, "baseline_memory_mb": 410.07421875, "peak_memory_mb": 411.2578125, "memory_increment_mb": 1.18359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 100, "max_number": 9981, "num_digits": 4, "actual_digit_extractions": 800, "theoretical_digit_extractions": 800, "digit_efficiency": 1.0, "array_accesses": 1300, "bucket_operations": 436, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.245, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=100, k=10]", "theoretical_space_complexity": "O(n+k) [n=100, k=10]", "theoretical_memory_mb": 0.0016021728515625, "efficiency_ratio": 0.0013536509900990098}, {"input_size": 200, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654415.197198, "execution_time_ms": 1.6216720454394817, "setup_time_ms": 0.2818373031914234, "cleanup_time_ms": 22.124170791357756, "total_time_ms": 24.02768013998866, "baseline_memory_mb": 411.2578125, "peak_memory_mb": 411.2578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 200, "max_number": 19961, "num_digits": 5, "actual_digit_extractions": 2000, "theoretical_digit_extractions": 2000, "digit_efficiency": 1.0, "array_accesses": 3200, "bucket_operations": 1045, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 198, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 3.296, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=200, k=10]", "theoretical_space_complexity": "O(n+k) [n=200, k=10]", "theoretical_memory_mb": 0.0031280517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654415.4097347, "execution_time_ms": 1.4772934839129448, "setup_time_ms": 0.23359525948762894, "cleanup_time_ms": 22.692104801535606, "total_time_ms": 24.40299354493618, "baseline_memory_mb": 411.2578125, "peak_memory_mb": 411.2578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 300, "max_number": 29968, "num_digits": 5, "actual_digit_extractions": 3000, "theoretical_digit_extractions": 3000, "digit_efficiency": 1.0, "array_accesses": 4800, "bucket_operations": 1545, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 297, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 3.302, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=300, k=10]", "theoretical_space_complexity": "O(n+k) [n=300, k=10]", "theoretical_memory_mb": 0.0046539306640625, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654415.5834277, "execution_time_ms": 2.029895130544901, "setup_time_ms": 0.30123721808195114, "cleanup_time_ms": 22.947861347347498, "total_time_ms": 25.27899369597435, "baseline_memory_mb": 411.2578125, "peak_memory_mb": 411.2578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 400, "max_number": 39921, "num_digits": 5, "actual_digit_extractions": 4000, "theoretical_digit_extractions": 4000, "digit_efficiency": 1.0, "array_accesses": 6400, "bucket_operations": 2045, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 395, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 3.304, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=400, k=10]", "theoretical_space_complexity": "O(n+k) [n=400, k=10]", "theoretical_memory_mb": 0.0061798095703125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654415.757926, "execution_time_ms": 2.477480284869671, "setup_time_ms": 0.3689620643854141, "cleanup_time_ms": 22.770116571336985, "total_time_ms": 25.61655892059207, "baseline_memory_mb": 411.2578125, "peak_memory_mb": 411.2578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 500, "max_number": 49972, "num_digits": 5, "actual_digit_extractions": 5000, "theoretical_digit_extractions": 5000, "digit_efficiency": 1.0, "array_accesses": 8000, "bucket_operations": 2545, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 493, "uniqueness_ratio": 0.986, "was_already_sorted": false, "digit_distribution_entropy": 3.309, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=500, k=10]", "theoretical_space_complexity": "O(n+k) [n=500, k=10]", "theoretical_memory_mb": 0.0077056884765625, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654415.9368167, "execution_time_ms": 2.980917412787676, "setup_time_ms": 0.40705688297748566, "cleanup_time_ms": 22.65382604673505, "total_time_ms": 26.04180034250021, "baseline_memory_mb": 411.46875, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 600, "max_number": 59936, "num_digits": 5, "actual_digit_extractions": 6000, "theoretical_digit_extractions": 6000, "digit_efficiency": 1.0, "array_accesses": 9600, "bucket_operations": 3045, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 593, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 3.314, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=600, k=10]", "theoretical_space_complexity": "O(n+k) [n=600, k=10]", "theoretical_memory_mb": 0.0092315673828125, "efficiency_ratio": 1.181640625}, {"input_size": 700, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654416.1257944, "execution_time_ms": 3.468379471451044, "setup_time_ms": 0.5396651104092598, "cleanup_time_ms": 22.987948264926672, "total_time_ms": 26.995992846786976, "baseline_memory_mb": 411.4765625, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 700, "max_number": 69828, "num_digits": 5, "actual_digit_extractions": 7000, "theoretical_digit_extractions": 7000, "digit_efficiency": 1.0, "array_accesses": 11200, "bucket_operations": 3545, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 698, "uniqueness_ratio": 0.997, "was_already_sorted": false, "digit_distribution_entropy": 3.319, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=700, k=10]", "theoretical_space_complexity": "O(n+k) [n=700, k=10]", "theoretical_memory_mb": 0.0107574462890625, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654416.3131204, "execution_time_ms": 3.922075778245926, "setup_time_ms": 0.5789110437035561, "cleanup_time_ms": 22.686040960252285, "total_time_ms": 27.187027782201767, "baseline_memory_mb": 411.4765625, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 800, "max_number": 79998, "num_digits": 5, "actual_digit_extractions": 8000, "theoretical_digit_extractions": 8000, "digit_efficiency": 1.0, "array_accesses": 12800, "bucket_operations": 4045, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 796, "uniqueness_ratio": 0.995, "was_already_sorted": false, "digit_distribution_entropy": 3.319, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=800, k=10]", "theoretical_space_complexity": "O(n+k) [n=800, k=10]", "theoretical_memory_mb": 0.0122833251953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654416.5043566, "execution_time_ms": 4.398687742650509, "setup_time_ms": 0.6109122186899185, "cleanup_time_ms": 22.463575936853886, "total_time_ms": 27.473175898194313, "baseline_memory_mb": 411.4765625, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 900, "max_number": 89734, "num_digits": 5, "actual_digit_extractions": 9000, "theoretical_digit_extractions": 9000, "digit_efficiency": 1.0, "array_accesses": 14400, "bucket_operations": 4545, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 894, "uniqueness_ratio": 0.993, "was_already_sorted": false, "digit_distribution_entropy": 3.318, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=900, k=10]", "theoretical_space_complexity": "O(n+k) [n=900, k=10]", "theoretical_memory_mb": 0.0138092041015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-random-base10", "timestamp": 1753654416.698943, "execution_time_ms": 4.852309636771679, "setup_time_ms": 0.665856059640646, "cleanup_time_ms": 22.093021776527166, "total_time_ms": 27.61118747293949, "baseline_memory_mb": 411.4765625, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 10, "input_size": 1000, "max_number": 99944, "num_digits": 5, "actual_digit_extractions": 10000, "theoretical_digit_extractions": 10000, "digit_efficiency": 1.0, "array_accesses": 16000, "bucket_operations": 5045, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 994, "uniqueness_ratio": 0.994, "was_already_sorted": false, "digit_distribution_entropy": 3.319, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=5, n=1000, k=10]", "theoretical_space_complexity": "O(n+k) [n=1000, k=10]", "theoretical_memory_mb": 0.0153350830078125, "efficiency_ratio": 0.0}]