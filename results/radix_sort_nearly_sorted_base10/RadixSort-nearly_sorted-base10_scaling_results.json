[{"input_size": 100, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654420.5927806, "execution_time_ms": 0.347705464810133, "setup_time_ms": 0.03608502447605133, "cleanup_time_ms": 22.327730897814035, "total_time_ms": 22.71152138710022, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 100, "max_number": 100, "num_digits": 3, "actual_digit_extractions": 600, "theoretical_digit_extractions": 600, "digit_efficiency": 1.0, "array_accesses": 1000, "bucket_operations": 327, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=100, k=10]", "theoretical_space_complexity": "O(n+k) [n=100, k=10]", "theoretical_memory_mb": 0.0016021728515625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654420.7618463, "execution_time_ms": 0.6213324144482613, "setup_time_ms": 0.04015304148197174, "cleanup_time_ms": 23.831395898014307, "total_time_ms": 24.49288135394454, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 200, "max_number": 200, "num_digits": 3, "actual_digit_extractions": 1200, "theoretical_digit_extractions": 1200, "digit_efficiency": 1.0, "array_accesses": 2000, "bucket_operations": 627, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 200, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=200, k=10]", "theoretical_space_complexity": "O(n+k) [n=200, k=10]", "theoretical_memory_mb": 0.0031280517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654420.9279718, "execution_time_ms": 0.9368694387376308, "setup_time_ms": 0.04687579348683357, "cleanup_time_ms": 24.150309152901173, "total_time_ms": 25.134054385125637, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 300, "max_number": 300, "num_digits": 3, "actual_digit_extractions": 1800, "theoretical_digit_extractions": 1800, "digit_efficiency": 1.0, "array_accesses": 3000, "bucket_operations": 927, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 300, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=300, k=10]", "theoretical_space_complexity": "O(n+k) [n=300, k=10]", "theoretical_memory_mb": 0.0046539306640625, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.097603, "execution_time_ms": 1.2312858365476131, "setup_time_ms": 0.05495082587003708, "cleanup_time_ms": 22.753439843654633, "total_time_ms": 24.039676506072283, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 400, "max_number": 400, "num_digits": 3, "actual_digit_extractions": 2400, "theoretical_digit_extractions": 2400, "digit_efficiency": 1.0, "array_accesses": 4000, "bucket_operations": 1227, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 400, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=400, k=10]", "theoretical_space_complexity": "O(n+k) [n=400, k=10]", "theoretical_memory_mb": 0.0061798095703125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.2685792, "execution_time_ms": 1.5224577859044075, "setup_time_ms": 0.06455089896917343, "cleanup_time_ms": 22.775692865252495, "total_time_ms": 24.362701550126076, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 500, "max_number": 500, "num_digits": 3, "actual_digit_extractions": 3000, "theoretical_digit_extractions": 3000, "digit_efficiency": 1.0, "array_accesses": 5000, "bucket_operations": 1527, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 500, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=500, k=10]", "theoretical_space_complexity": "O(n+k) [n=500, k=10]", "theoretical_memory_mb": 0.0077056884765625, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.4426928, "execution_time_ms": 1.8358895555138588, "setup_time_ms": 0.07581803947687149, "cleanup_time_ms": 22.756374906748533, "total_time_ms": 24.668082501739264, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 600, "max_number": 600, "num_digits": 3, "actual_digit_extractions": 3600, "theoretical_digit_extractions": 3600, "digit_efficiency": 1.0, "array_accesses": 6000, "bucket_operations": 1827, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 600, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=600, k=10]", "theoretical_space_complexity": "O(n+k) [n=600, k=10]", "theoretical_memory_mb": 0.0092315673828125, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.6166115, "execution_time_ms": 2.1057242527604103, "setup_time_ms": 0.08371099829673767, "cleanup_time_ms": 22.840968798846006, "total_time_ms": 25.030404049903154, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 700, "max_number": 700, "num_digits": 3, "actual_digit_extractions": 4200, "theoretical_digit_extractions": 4200, "digit_efficiency": 1.0, "array_accesses": 7000, "bucket_operations": 2127, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 700, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=700, k=10]", "theoretical_space_complexity": "O(n+k) [n=700, k=10]", "theoretical_memory_mb": 0.0107574462890625, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.7915802, "execution_time_ms": 2.3757471702992916, "setup_time_ms": 0.09270524606108665, "cleanup_time_ms": 22.73208601400256, "total_time_ms": 25.20053843036294, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 800, "max_number": 800, "num_digits": 3, "actual_digit_extractions": 4800, "theoretical_digit_extractions": 4800, "digit_efficiency": 1.0, "array_accesses": 8000, "bucket_operations": 2427, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 800, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=800, k=10]", "theoretical_space_complexity": "O(n+k) [n=800, k=10]", "theoretical_memory_mb": 0.0122833251953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654421.9673164, "execution_time_ms": 2.6656788773834705, "setup_time_ms": 0.09922171011567116, "cleanup_time_ms": 22.249288856983185, "total_time_ms": 25.014189444482327, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 900, "max_number": 900, "num_digits": 3, "actual_digit_extractions": 5400, "theoretical_digit_extractions": 5400, "digit_efficiency": 1.0, "array_accesses": 9000, "bucket_operations": 2727, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 900, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=900, k=10]", "theoretical_space_complexity": "O(n+k) [n=900, k=10]", "theoretical_memory_mb": 0.0138092041015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-nearly_sorted-base10", "timestamp": 1753654422.1469598, "execution_time_ms": 2.908130642026663, "setup_time_ms": 0.10904576629400253, "cleanup_time_ms": 22.505170200020075, "total_time_ms": 25.52234660834074, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "radix_base": 10, "input_size": 1000, "max_number": 1000, "num_digits": 4, "actual_digit_extractions": 6000, "theoretical_digit_extractions": 8000, "digit_efficiency": 1.333, "array_accesses": 10000, "bucket_operations": 3027, "comparisons_used": 0, "correctness_verified": false, "unique_elements": 1000, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=5, n=1000, k=10]", "theoretical_space_complexity": "O(n+k) [n=1000, k=10]", "theoretical_memory_mb": 0.0153350830078125, "efficiency_ratio": 0.0}]