input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-nearly_sorted-base10,1753654420.5927806,0.3477,0.0361,22.3277,22.7115,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=100, k=10]","O(n+k) [n=100, k=10]",0.00,0.0000,nearly_sorted,10,100,100,3,600,600,1.0,1000,327,0,True,100,1.0,False,3.322,non_comparative
200,RadixSort-nearly_sorted-base10,1753654420.7618463,0.6213,0.0402,23.8314,24.4929,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=200, k=10]","O(n+k) [n=200, k=10]",0.00,0.0000,nearly_sorted,10,200,200,3,1200,1200,1.0,2000,627,0,True,200,1.0,False,3.322,non_comparative
300,RadixSort-nearly_sorted-base10,1753654420.9279718,0.9369,0.0469,24.1503,25.1341,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=300, k=10]","O(n+k) [n=300, k=10]",0.00,0.0000,nearly_sorted,10,300,300,3,1800,1800,1.0,3000,927,0,True,300,1.0,False,3.322,non_comparative
400,RadixSort-nearly_sorted-base10,1753654421.097603,1.2313,0.0550,22.7534,24.0397,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=400, k=10]","O(n+k) [n=400, k=10]",0.01,0.0000,nearly_sorted,10,400,400,3,2400,2400,1.0,4000,1227,0,True,400,1.0,False,3.322,non_comparative
500,RadixSort-nearly_sorted-base10,1753654421.2685792,1.5225,0.0646,22.7757,24.3627,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=500, k=10]","O(n+k) [n=500, k=10]",0.01,0.0000,nearly_sorted,10,500,500,3,3000,3000,1.0,5000,1527,0,True,500,1.0,False,3.322,non_comparative
600,RadixSort-nearly_sorted-base10,1753654421.4426928,1.8359,0.0758,22.7564,24.6681,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=600, k=10]","O(n+k) [n=600, k=10]",0.01,0.0000,nearly_sorted,10,600,600,3,3600,3600,1.0,6000,1827,0,True,600,1.0,False,3.322,non_comparative
700,RadixSort-nearly_sorted-base10,1753654421.6166115,2.1057,0.0837,22.8410,25.0304,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=700, k=10]","O(n+k) [n=700, k=10]",0.01,0.0000,nearly_sorted,10,700,700,3,4200,4200,1.0,7000,2127,0,True,700,1.0,False,3.322,non_comparative
800,RadixSort-nearly_sorted-base10,1753654421.7915802,2.3757,0.0927,22.7321,25.2005,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=800, k=10]","O(n+k) [n=800, k=10]",0.01,0.0000,nearly_sorted,10,800,800,3,4800,4800,1.0,8000,2427,0,True,800,1.0,False,3.322,non_comparative
900,RadixSort-nearly_sorted-base10,1753654421.9673164,2.6657,0.0992,22.2493,25.0142,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=900, k=10]","O(n+k) [n=900, k=10]",0.01,0.0000,nearly_sorted,10,900,900,3,5400,5400,1.0,9000,2727,0,True,900,1.0,False,3.322,non_comparative
1000,RadixSort-nearly_sorted-base10,1753654422.1469598,2.9081,0.1090,22.5052,25.5223,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=5, n=1000, k=10]","O(n+k) [n=1000, k=10]",0.02,0.0000,nearly_sorted,10,1000,1000,4,6000,8000,1.333,10000,3027,0,False,1000,1.0,False,3.322,non_comparative
