[{"input_size": 100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657393.5977678, "execution_time_ms": 0.14802580699324608, "setup_time_ms": 0.04798080772161484, "cleanup_time_ms": 98.60293008387089, "total_time_ms": 98.79893669858575, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657394.2973733, "execution_time_ms": 0.16652243211865425, "setup_time_ms": 0.0524069182574749, "cleanup_time_ms": 107.5735092163086, "total_time_ms": 107.79243856668472, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 1200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657395.0225163, "execution_time_ms": 0.16960063949227333, "setup_time_ms": 0.06545521318912506, "cleanup_time_ms": 100.76583921909332, "total_time_ms": 101.00089507177472, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 2200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657395.7290614, "execution_time_ms": 0.17403783276677132, "setup_time_ms": 0.07565226405858994, "cleanup_time_ms": 100.04467284306884, "total_time_ms": 100.2943629398942, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 3200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 2100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657396.4339726, "execution_time_ms": 0.18125800415873528, "setup_time_ms": 0.08537201210856438, "cleanup_time_ms": 98.22572115808725, "total_time_ms": 98.49235117435455, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 4200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.03204345703125, "efficiency_ratio": 0.0}, {"input_size": 2600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657397.130656, "execution_time_ms": 0.2077566459774971, "setup_time_ms": 0.1140441745519638, "cleanup_time_ms": 97.72970667108893, "total_time_ms": 98.0515074916184, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 5200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0396728515625, "efficiency_ratio": 0.0}, {"input_size": 3100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657397.876988, "execution_time_ms": 0.19602635875344276, "setup_time_ms": 0.11563301086425781, "cleanup_time_ms": 99.60871608927846, "total_time_ms": 99.92037545889616, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 6200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.04730224609375, "efficiency_ratio": 0.0}, {"input_size": 3600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657398.583944, "execution_time_ms": 0.19295746460556984, "setup_time_ms": 0.1311730593442917, "cleanup_time_ms": 99.06678413972259, "total_time_ms": 99.39091466367245, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 7200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 4100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657399.2967753, "execution_time_ms": 0.21318653598427773, "setup_time_ms": 0.12901797890663147, "cleanup_time_ms": 99.19056529179215, "total_time_ms": 99.53276980668306, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 8200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.06256103515625, "efficiency_ratio": 0.0}, {"input_size": 4600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657400.0126758, "execution_time_ms": 0.20396867766976357, "setup_time_ms": 0.14988472685217857, "cleanup_time_ms": 97.90874365717173, "total_time_ms": 98.26259706169367, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 9200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0701904296875, "efficiency_ratio": 0.0}, {"input_size": 5100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657400.7111137, "execution_time_ms": 0.20595192909240723, "setup_time_ms": 0.14776689931750298, "cleanup_time_ms": 99.52088305726647, "total_time_ms": 99.87460188567638, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 10200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.07781982421875, "efficiency_ratio": 0.0}, {"input_size": 5600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657401.4013877, "execution_time_ms": 0.2066175453364849, "setup_time_ms": 0.1610890030860901, "cleanup_time_ms": 98.00062188878655, "total_time_ms": 98.36832843720913, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 11200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.08544921875, "efficiency_ratio": 0.0}, {"input_size": 6100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657402.094646, "execution_time_ms": 0.21431716158986092, "setup_time_ms": 0.16971491277217865, "cleanup_time_ms": 99.53483892604709, "total_time_ms": 99.91887100040913, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 12200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.09307861328125, "efficiency_ratio": 0.0}, {"input_size": 6600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657402.788976, "execution_time_ms": 0.22405125200748444, "setup_time_ms": 0.18954789265990257, "cleanup_time_ms": 99.42879574373364, "total_time_ms": 99.84239488840103, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 13200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1007080078125, "efficiency_ratio": 0.0}, {"input_size": 7100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657403.4906526, "execution_time_ms": 0.228216964751482, "setup_time_ms": 0.20649796351790428, "cleanup_time_ms": 99.26878707483411, "total_time_ms": 99.7035020031035, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 14200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.10833740234375, "efficiency_ratio": 0.0}, {"input_size": 7600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657404.186386, "execution_time_ms": 0.22188769653439522, "setup_time_ms": 0.21190103143453598, "cleanup_time_ms": 101.18165891617537, "total_time_ms": 101.6154476441443, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 15200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.115966796875, "efficiency_ratio": 0.0}, {"input_size": 8100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657404.8971236, "execution_time_ms": 0.22157225757837296, "setup_time_ms": 0.22886693477630615, "cleanup_time_ms": 98.24606403708458, "total_time_ms": 98.69650322943926, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 16200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.12359619140625, "efficiency_ratio": 0.0}, {"input_size": 8600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657405.601339, "execution_time_ms": 0.2303733490407467, "setup_time_ms": 0.2242540940642357, "cleanup_time_ms": 97.93311124667525, "total_time_ms": 98.38773868978024, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 17200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1312255859375, "efficiency_ratio": 0.0}, {"input_size": 9100, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657406.2916396, "execution_time_ms": 0.24251621216535568, "setup_time_ms": 0.2503148280084133, "cleanup_time_ms": 98.70601305738091, "total_time_ms": 99.19884409755468, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9100, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 18200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.13885498046875, "efficiency_ratio": 0.0}, {"input_size": 9600, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657406.9906785, "execution_time_ms": 0.23867590352892876, "setup_time_ms": 0.29017264023423195, "cleanup_time_ms": 98.19201100617647, "total_time_ms": 98.72085954993963, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.56640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9600, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 0, "compression_ratio": 1.0, "theoretical_operations": 19200, "operations_per_sample": 2.0, "coefficient_efficiency": 0.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.146484375, "efficiency_ratio": 0.0}]