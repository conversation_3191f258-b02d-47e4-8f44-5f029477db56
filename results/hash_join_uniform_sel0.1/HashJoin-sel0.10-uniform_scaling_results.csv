input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_join_selectivity,custom_data_distribution,custom_left_table_ratio,custom_right_table_ratio,custom_input_size,custom_left_table_size,custom_right_table_size,custom_result_size,custom_hash_operations,custom_hash_collisions,custom_collision_rate,custom_hash_efficiency,custom_comparisons,custom_memory_accesses,custom_access_efficiency,custom_build_time_ms,custom_probe_time_ms,custom_build_time_ratio,custom_probe_time_ratio,custom_actual_selectivity,custom_expected_selectivity,custom_selectivity_accuracy,custom_unique_left_keys,custom_unique_right_keys,custom_matching_keys,custom_join_ratio,custom_algorithm_type
100,HashJoin-sel0.10-uniform,1753655087.1767576,0.1234,0.2576,22.9869,23.3679,409.96,411.38,1.42,0.00,,,,"O(|R|+|S|) [|R|=100, |S|=100]","O(min(|R|,|S|)+result) [min=100, est_result=100]",0.01,0.0086,0.1,uniform,1.0,1.0,100,100,100,19,200,7,0.035,1.0,19,219,0.913,0.049,0.022,0.687,0.313,0.1613,0.1,0.9387,93,92,15,0.0019,hash_based_join
200,HashJoin-sel0.10-uniform,1753655087.416255,0.1854,0.4701,23.1607,23.8162,411.38,411.38,0.00,0.00,,,,"O(|R|+|S|) [|R|=200, |S|=200]","O(min(|R|,|S|)+result) [min=200, est_result=400]",0.04,0.0000,0.1,uniform,1.0,1.0,200,200,200,25,400,13,0.0325,1.0,25,425,0.941,0.081,0.041,0.664,0.336,0.1123,0.1,0.9877,187,189,21,0.000625,hash_based_join
300,HashJoin-sel0.10-uniform,1753655087.5796747,0.2554,0.6297,23.0137,23.8989,411.38,411.38,0.00,0.00,,,,"O(|R|+|S|) [|R|=300, |S|=300]","O(min(|R|,|S|)+result) [min=300, est_result=900]",0.07,0.0000,0.1,uniform,1.0,1.0,300,300,300,38,600,19,0.0317,1.0,38,638,0.94,0.12,0.065,0.65,0.35,0.1103,0.1,0.9897,281,287,31,0.000422,hash_based_join
400,HashJoin-sel0.10-uniform,1753655087.7429368,0.3520,0.7864,22.4938,23.6322,411.38,411.38,0.00,0.00,,,,"O(|R|+|S|) [|R|=400, |S|=400]","O(min(|R|,|S|)+result) [min=400, est_result=1600]",0.12,0.0000,0.1,uniform,1.0,1.0,400,400,400,56,800,21,0.0262,1.0,56,856,0.935,0.167,0.092,0.644,0.356,0.1055,0.1,0.9945,379,378,40,0.00035,hash_based_join
500,HashJoin-sel0.10-uniform,1753655087.9090807,0.4265,0.9727,22.2204,23.6196,411.38,411.62,0.24,0.00,,,,"O(|R|+|S|) [|R|=500, |S|=500]","O(min(|R|,|S|)+result) [min=500, est_result=2500]",0.18,0.7684,0.1,uniform,1.0,1.0,500,500,500,82,1000,21,0.021,1.0,82,1082,0.924,0.188,0.116,0.618,0.382,0.1315,0.1,0.9685,479,471,63,0.000328,hash_based_join
600,HashJoin-sel0.10-uniform,1753655088.0710897,0.5003,1.1318,23.1014,24.7336,411.62,411.62,0.00,0.00,,,,"O(|R|+|S|) [|R|=600, |S|=600]","O(min(|R|,|S|)+result) [min=600, est_result=3600]",0.26,0.0000,0.1,uniform,1.0,1.0,600,600,600,97,1200,25,0.0208,1.0,97,1297,0.925,0.216,0.143,0.601,0.399,0.1409,0.1,0.9591,575,571,81,0.000269,hash_based_join
700,HashJoin-sel0.10-uniform,1753655088.2352629,0.5929,1.3317,22.5590,24.4835,411.62,411.73,0.11,0.00,,,,"O(|R|+|S|) [|R|=700, |S|=700]","O(min(|R|,|S|)+result) [min=700, est_result=4900]",0.34,3.1250,0.1,uniform,1.0,1.0,700,700,700,115,1400,32,0.0229,1.0,115,1515,0.924,0.254,0.187,0.575,0.425,0.1377,0.1,0.9623,668,658,92,0.000235,hash_based_join
800,HashJoin-sel0.10-uniform,1753655088.4009707,0.6566,1.4747,22.6313,24.7626,411.73,411.73,0.00,0.00,,,,"O(|R|+|S|) [|R|=800, |S|=800]","O(min(|R|,|S|)+result) [min=800, est_result=6400]",0.44,0.0000,0.1,uniform,1.0,1.0,800,800,800,128,1600,30,0.0187,1.0,128,1728,0.926,0.321,0.19,0.628,0.372,0.1312,0.1,0.9688,770,751,101,0.0002,hash_based_join
900,HashJoin-sel0.10-uniform,1753655088.5644152,0.7373,1.7607,22.5645,25.0624,411.73,411.73,0.00,0.00,,,,"O(|R|+|S|) [|R|=900, |S|=900]","O(min(|R|,|S|)+result) [min=900, est_result=8100]",0.55,0.0000,0.1,uniform,1.0,1.0,900,900,900,144,1800,37,0.0206,1.0,144,1944,0.926,0.348,0.208,0.626,0.374,0.1379,0.1,0.9621,863,859,119,0.000178,hash_based_join
1000,HashJoin-sel0.10-uniform,1753655088.730155,0.8014,1.9201,22.9087,25.6302,411.73,411.84,0.12,0.00,,,,"O(|R|+|S|) [|R|=1000, |S|=1000]","O(min(|R|,|S|)+result) [min=1000, est_result=10000]",0.67,5.7292,0.1,uniform,1.0,1.0,1000,1000,1000,165,2000,39,0.0195,1.0,165,2165,0.924,0.372,0.236,0.612,0.388,0.1374,0.1,0.9626,961,951,132,0.000165,hash_based_join
