[{"input_size": 100, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655087.1767576, "execution_time_ms": 0.12340573593974113, "setup_time_ms": 0.25761639699339867, "cleanup_time_ms": 22.98686606809497, "total_time_ms": 23.36788820102811, "baseline_memory_mb": 409.9609375, "peak_memory_mb": 411.37890625, "memory_increment_mb": 1.41796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 100, "left_table_size": 100, "right_table_size": 100, "result_size": 19, "hash_operations": 200, "hash_collisions": 7, "collision_rate": 0.035, "hash_efficiency": 1.0, "comparisons": 19, "memory_accesses": 219, "access_efficiency": 0.913, "build_time_ms": 0.049, "probe_time_ms": 0.022, "build_time_ratio": 0.687, "probe_time_ratio": 0.313, "actual_selectivity": 0.1613, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9387, "unique_left_keys": 93, "unique_right_keys": 92, "matching_keys": 15, "join_ratio": 0.0019, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=100, |S|=100]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=100, est_result=100]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.008608815426997245}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655087.416255, "execution_time_ms": 0.18539410084486008, "setup_time_ms": 0.4700911231338978, "cleanup_time_ms": 23.160687182098627, "total_time_ms": 23.816172406077385, "baseline_memory_mb": 411.37890625, "peak_memory_mb": 411.37890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 200, "left_table_size": 200, "right_table_size": 200, "result_size": 25, "hash_operations": 400, "hash_collisions": 13, "collision_rate": 0.0325, "hash_efficiency": 1.0, "comparisons": 25, "memory_accesses": 425, "access_efficiency": 0.941, "build_time_ms": 0.081, "probe_time_ms": 0.041, "build_time_ratio": 0.664, "probe_time_ratio": 0.336, "actual_selectivity": 0.1123, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9877, "unique_left_keys": 187, "unique_right_keys": 189, "matching_keys": 21, "join_ratio": 0.000625, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=200, |S|=200]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=200, est_result=400]", "theoretical_memory_mb": 0.03662109375, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655087.5796747, "execution_time_ms": 0.25542890653014183, "setup_time_ms": 0.6297240033745766, "cleanup_time_ms": 23.013737984001637, "total_time_ms": 23.898890893906355, "baseline_memory_mb": 411.37890625, "peak_memory_mb": 411.37890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 300, "left_table_size": 300, "right_table_size": 300, "result_size": 38, "hash_operations": 600, "hash_collisions": 19, "collision_rate": 0.0317, "hash_efficiency": 1.0, "comparisons": 38, "memory_accesses": 638, "access_efficiency": 0.94, "build_time_ms": 0.12, "probe_time_ms": 0.065, "build_time_ratio": 0.65, "probe_time_ratio": 0.35, "actual_selectivity": 0.1103, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9897, "unique_left_keys": 281, "unique_right_keys": 287, "matching_keys": 31, "join_ratio": 0.000422, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=300]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=300, est_result=900]", "theoretical_memory_mb": 0.0732421875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655087.7429368, "execution_time_ms": 0.3520187921822071, "setup_time_ms": 0.7864348590373993, "cleanup_time_ms": 22.493781056255102, "total_time_ms": 23.63223470747471, "baseline_memory_mb": 411.37890625, "peak_memory_mb": 411.37890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 400, "left_table_size": 400, "right_table_size": 400, "result_size": 56, "hash_operations": 800, "hash_collisions": 21, "collision_rate": 0.0262, "hash_efficiency": 1.0, "comparisons": 56, "memory_accesses": 856, "access_efficiency": 0.935, "build_time_ms": 0.167, "probe_time_ms": 0.092, "build_time_ratio": 0.644, "probe_time_ratio": 0.356, "actual_selectivity": 0.1055, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9945, "unique_left_keys": 379, "unique_right_keys": 378, "matching_keys": 40, "join_ratio": 0.00035, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=400, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=1600]", "theoretical_memory_mb": 0.1220703125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655087.9090807, "execution_time_ms": 0.42651155963540077, "setup_time_ms": 0.9726881980895996, "cleanup_time_ms": 22.2203959710896, "total_time_ms": 23.619595728814602, "baseline_memory_mb": 411.37890625, "peak_memory_mb": 411.6171875, "memory_increment_mb": 0.23828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 500, "left_table_size": 500, "right_table_size": 500, "result_size": 82, "hash_operations": 1000, "hash_collisions": 21, "collision_rate": 0.021, "hash_efficiency": 1.0, "comparisons": 82, "memory_accesses": 1082, "access_efficiency": 0.924, "build_time_ms": 0.188, "probe_time_ms": 0.116, "build_time_ratio": 0.618, "probe_time_ratio": 0.382, "actual_selectivity": 0.1315, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9685, "unique_left_keys": 479, "unique_right_keys": 471, "matching_keys": 63, "join_ratio": 0.000328, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=500, |S|=500]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=500, est_result=2500]", "theoretical_memory_mb": 0.18310546875, "efficiency_ratio": 0.7684426229508197}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655088.0710897, "execution_time_ms": 0.5002709105610847, "setup_time_ms": 1.1318461038172245, "cleanup_time_ms": 23.101442027837038, "total_time_ms": 24.733559042215347, "baseline_memory_mb": 411.6171875, "peak_memory_mb": 411.6171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 600, "left_table_size": 600, "right_table_size": 600, "result_size": 97, "hash_operations": 1200, "hash_collisions": 25, "collision_rate": 0.0208, "hash_efficiency": 1.0, "comparisons": 97, "memory_accesses": 1297, "access_efficiency": 0.925, "build_time_ms": 0.216, "probe_time_ms": 0.143, "build_time_ratio": 0.601, "probe_time_ratio": 0.399, "actual_selectivity": 0.1409, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9591, "unique_left_keys": 575, "unique_right_keys": 571, "matching_keys": 81, "join_ratio": 0.000269, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=600]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=600, est_result=3600]", "theoretical_memory_mb": 0.25634765625, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655088.2352629, "execution_time_ms": 0.5928799510002136, "setup_time_ms": 1.331660896539688, "cleanup_time_ms": 22.558985743671656, "total_time_ms": 24.483526591211557, "baseline_memory_mb": 411.6171875, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 700, "left_table_size": 700, "right_table_size": 700, "result_size": 115, "hash_operations": 1400, "hash_collisions": 32, "collision_rate": 0.0229, "hash_efficiency": 1.0, "comparisons": 115, "memory_accesses": 1515, "access_efficiency": 0.924, "build_time_ms": 0.254, "probe_time_ms": 0.187, "build_time_ratio": 0.575, "probe_time_ratio": 0.425, "actual_selectivity": 0.1377, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9623, "unique_left_keys": 668, "unique_right_keys": 658, "matching_keys": 92, "join_ratio": 0.000235, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=700, |S|=700]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=700, est_result=4900]", "theoretical_memory_mb": 0.341796875, "efficiency_ratio": 3.125}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655088.4009707, "execution_time_ms": 0.656580738723278, "setup_time_ms": 1.4746878296136856, "cleanup_time_ms": 22.63133740052581, "total_time_ms": 24.762605968862772, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 800, "left_table_size": 800, "right_table_size": 800, "result_size": 128, "hash_operations": 1600, "hash_collisions": 30, "collision_rate": 0.0187, "hash_efficiency": 1.0, "comparisons": 128, "memory_accesses": 1728, "access_efficiency": 0.926, "build_time_ms": 0.321, "probe_time_ms": 0.19, "build_time_ratio": 0.628, "probe_time_ratio": 0.372, "actual_selectivity": 0.1312, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9688, "unique_left_keys": 770, "unique_right_keys": 751, "matching_keys": 101, "join_ratio": 0.0002, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=800, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=6400]", "theoretical_memory_mb": 0.439453125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655088.5644152, "execution_time_ms": 0.7373340427875519, "setup_time_ms": 1.7606602050364017, "cleanup_time_ms": 22.564454935491085, "total_time_ms": 25.06244918331504, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.7265625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 900, "left_table_size": 900, "right_table_size": 900, "result_size": 144, "hash_operations": 1800, "hash_collisions": 37, "collision_rate": 0.0206, "hash_efficiency": 1.0, "comparisons": 144, "memory_accesses": 1944, "access_efficiency": 0.926, "build_time_ms": 0.348, "probe_time_ms": 0.208, "build_time_ratio": 0.626, "probe_time_ratio": 0.374, "actual_selectivity": 0.1379, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9621, "unique_left_keys": 863, "unique_right_keys": 859, "matching_keys": 119, "join_ratio": 0.000178, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=900]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=900, est_result=8100]", "theoretical_memory_mb": 0.54931640625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.10-uniform", "timestamp": 1753655088.730155, "execution_time_ms": 0.8013863116502762, "setup_time_ms": 1.9200942479074001, "cleanup_time_ms": 22.90873508900404, "total_time_ms": 25.630215648561716, "baseline_memory_mb": 411.7265625, "peak_memory_mb": 411.84375, "memory_increment_mb": 0.1171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.1, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 1000, "left_table_size": 1000, "right_table_size": 1000, "result_size": 165, "hash_operations": 2000, "hash_collisions": 39, "collision_rate": 0.0195, "hash_efficiency": 1.0, "comparisons": 165, "memory_accesses": 2165, "access_efficiency": 0.924, "build_time_ms": 0.372, "probe_time_ms": 0.236, "build_time_ratio": 0.612, "probe_time_ratio": 0.388, "actual_selectivity": 0.1374, "expected_selectivity": 0.1, "selectivity_accuracy": 0.9626, "unique_left_keys": 961, "unique_right_keys": 951, "matching_keys": 132, "join_ratio": 0.000165, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1000, |S|=1000]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=1000, est_result=10000]", "theoretical_memory_mb": 0.67138671875, "efficiency_ratio": 5.729166666666667}]