[{"input_size": 100, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655090.5613425, "execution_time_ms": 0.13992823660373688, "setup_time_ms": 0.259320717304945, "cleanup_time_ms": 22.761705797165632, "total_time_ms": 23.160954751074314, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 100, "left_table_size": 100, "right_table_size": 100, "result_size": 119, "hash_operations": 200, "hash_collisions": 37, "collision_rate": 0.185, "hash_efficiency": 1.0, "comparisons": 119, "memory_accesses": 319, "access_efficiency": 0.627, "build_time_ms": 0.045, "probe_time_ms": 0.048, "build_time_ratio": 0.482, "probe_time_ratio": 0.518, "actual_selectivity": 0.6667, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7667, "unique_left_keys": 63, "unique_right_keys": 62, "matching_keys": 42, "join_ratio": 0.0119, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=100, |S|=100]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=100, est_result=900]", "theoretical_memory_mb": 0.06103515625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655090.726291, "execution_time_ms": 0.23774569854140282, "setup_time_ms": 0.3953869454562664, "cleanup_time_ms": 22.65798207372427, "total_time_ms": 23.29111471772194, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 200, "left_table_size": 200, "right_table_size": 200, "result_size": 222, "hash_operations": 400, "hash_collisions": 68, "collision_rate": 0.17, "hash_efficiency": 1.0, "comparisons": 222, "memory_accesses": 622, "access_efficiency": 0.643, "build_time_ms": 0.079, "probe_time_ms": 0.101, "build_time_ratio": 0.437, "probe_time_ratio": 0.563, "actual_selectivity": 0.6818, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7818, "unique_left_keys": 132, "unique_right_keys": 130, "matching_keys": 90, "join_ratio": 0.00555, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=200, |S|=200]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=200, est_result=3600]", "theoretical_memory_mb": 0.23193359375, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655090.8877487, "execution_time_ms": 0.33655883744359016, "setup_time_ms": 0.5971011705696583, "cleanup_time_ms": 22.433511447161436, "total_time_ms": 23.367171455174685, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 300, "left_table_size": 300, "right_table_size": 300, "result_size": 351, "hash_operations": 600, "hash_collisions": 107, "collision_rate": 0.1783, "hash_efficiency": 1.0, "comparisons": 351, "memory_accesses": 951, "access_efficiency": 0.631, "build_time_ms": 0.115, "probe_time_ms": 0.141, "build_time_ratio": 0.449, "probe_time_ratio": 0.551, "actual_selectivity": 0.6943, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7943, "unique_left_keys": 193, "unique_right_keys": 191, "matching_keys": 134, "join_ratio": 0.0039, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=300]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=300, est_result=8100]", "theoretical_memory_mb": 0.5126953125, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.0493941, "execution_time_ms": 0.46515651047229767, "setup_time_ms": 0.791297759860754, "cleanup_time_ms": 22.374140098690987, "total_time_ms": 23.63059436902404, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 400, "left_table_size": 400, "right_table_size": 400, "result_size": 466, "hash_operations": 800, "hash_collisions": 140, "collision_rate": 0.175, "hash_efficiency": 1.0, "comparisons": 466, "memory_accesses": 1266, "access_efficiency": 0.632, "build_time_ms": 0.152, "probe_time_ms": 0.218, "build_time_ratio": 0.41, "probe_time_ratio": 0.59, "actual_selectivity": 0.7038, "expected_selectivity": 0.9, "selectivity_accuracy": 0.8038, "unique_left_keys": 260, "unique_right_keys": 253, "matching_keys": 183, "join_ratio": 0.002913, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=400, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=14400]", "theoretical_memory_mb": 0.9033203125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.2116714, "execution_time_ms": 0.5804749205708504, "setup_time_ms": 1.0771816596388817, "cleanup_time_ms": 22.822299972176552, "total_time_ms": 24.479956552386284, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 500, "left_table_size": 500, "right_table_size": 500, "result_size": 563, "hash_operations": 1000, "hash_collisions": 177, "collision_rate": 0.177, "hash_efficiency": 1.0, "comparisons": 563, "memory_accesses": 1563, "access_efficiency": 0.64, "build_time_ms": 0.188, "probe_time_ms": 0.273, "build_time_ratio": 0.409, "probe_time_ratio": 0.591, "actual_selectivity": 0.6749, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7749, "unique_left_keys": 323, "unique_right_keys": 317, "matching_keys": 218, "join_ratio": 0.002252, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=500, |S|=500]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=500, est_result=22500]", "theoretical_memory_mb": 1.40380859375, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.3818913, "execution_time_ms": 0.6976871751248837, "setup_time_ms": 1.2645372189581394, "cleanup_time_ms": 24.169973097741604, "total_time_ms": 26.132197491824627, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 600, "left_table_size": 600, "right_table_size": 600, "result_size": 651, "hash_operations": 1200, "hash_collisions": 207, "collision_rate": 0.1725, "hash_efficiency": 1.0, "comparisons": 651, "memory_accesses": 1851, "access_efficiency": 0.648, "build_time_ms": 0.234, "probe_time_ms": 0.318, "build_time_ratio": 0.423, "probe_time_ratio": 0.577, "actual_selectivity": 0.6158, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7158, "unique_left_keys": 393, "unique_right_keys": 363, "matching_keys": 242, "join_ratio": 0.001808, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=600]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=600, est_result=32400]", "theoretical_memory_mb": 2.01416015625, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.5586004, "execution_time_ms": 0.7964850403368473, "setup_time_ms": 1.4772722497582436, "cleanup_time_ms": 23.381866980344057, "total_time_ms": 25.655624270439148, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 700, "left_table_size": 700, "right_table_size": 700, "result_size": 779, "hash_operations": 1400, "hash_collisions": 243, "collision_rate": 0.1736, "hash_efficiency": 1.0, "comparisons": 779, "memory_accesses": 2179, "access_efficiency": 0.642, "build_time_ms": 0.25, "probe_time_ms": 0.343, "build_time_ratio": 0.422, "probe_time_ratio": 0.578, "actual_selectivity": 0.6937, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7937, "unique_left_keys": 457, "unique_right_keys": 453, "matching_keys": 317, "join_ratio": 0.00159, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=700, |S|=700]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=700, est_result=44100]", "theoretical_memory_mb": 2.734375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.7326622, "execution_time_ms": 0.9073467925190926, "setup_time_ms": 1.5620100311934948, "cleanup_time_ms": 22.622030694037676, "total_time_ms": 25.091387517750263, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 800, "left_table_size": 800, "right_table_size": 800, "result_size": 936, "hash_operations": 1600, "hash_collisions": 277, "collision_rate": 0.1731, "hash_efficiency": 1.0, "comparisons": 936, "memory_accesses": 2536, "access_efficiency": 0.631, "build_time_ms": 0.298, "probe_time_ms": 0.44, "build_time_ratio": 0.403, "probe_time_ratio": 0.597, "actual_selectivity": 0.6883, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7883, "unique_left_keys": 523, "unique_right_keys": 510, "matching_keys": 360, "join_ratio": 0.001463, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=800, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=57600]", "theoretical_memory_mb": 3.564453125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655091.9007385, "execution_time_ms": 0.9934996254742146, "setup_time_ms": 1.7400272190570831, "cleanup_time_ms": 22.73089997470379, "total_time_ms": 25.464426819235086, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 900, "left_table_size": 900, "right_table_size": 900, "result_size": 1075, "hash_operations": 1800, "hash_collisions": 304, "collision_rate": 0.1689, "hash_efficiency": 1.0, "comparisons": 1075, "memory_accesses": 2875, "access_efficiency": 0.626, "build_time_ms": 0.316, "probe_time_ms": 0.494, "build_time_ratio": 0.39, "probe_time_ratio": 0.61, "actual_selectivity": 0.6829, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7829, "unique_left_keys": 596, "unique_right_keys": 557, "matching_keys": 407, "join_ratio": 0.001327, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=900]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=900, est_result=72900]", "theoretical_memory_mb": 4.50439453125, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.90-uniform", "timestamp": 1753655092.0686722, "execution_time_ms": 1.1177231557667255, "setup_time_ms": 2.1193078719079494, "cleanup_time_ms": 22.602621000260115, "total_time_ms": 25.83965202793479, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.03125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.9, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 1000, "left_table_size": 1000, "right_table_size": 1000, "result_size": 1157, "hash_operations": 2000, "hash_collisions": 325, "collision_rate": 0.1625, "hash_efficiency": 1.0, "comparisons": 1157, "memory_accesses": 3157, "access_efficiency": 0.634, "build_time_ms": 0.364, "probe_time_ms": 0.546, "build_time_ratio": 0.4, "probe_time_ratio": 0.6, "actual_selectivity": 0.6815, "expected_selectivity": 0.9, "selectivity_accuracy": 0.7815, "unique_left_keys": 675, "unique_right_keys": 633, "matching_keys": 460, "join_ratio": 0.001157, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1000, |S|=1000]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=1000, est_result=90000]", "theoretical_memory_mb": 5.55419921875, "efficiency_ratio": 0.0}]