[{"input_size": 16, "algorithm_name": "AES-128-CBC", "timestamp": 1753702855.184189, "execution_time_ms": 0.10272739455103874, "setup_time_ms": 0.02240808680653572, "cleanup_time_ms": 28.350336011499166, "total_time_ms": 28.47547149285674, "baseline_memory_mb": 417.36328125, "peak_memory_mb": 420.40625, "memory_increment_mb": 3.04296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 1, "actual_blocks": 2, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 1.0, "output_size_bytes": 32, "expansion_ratio": 2.0, "throughput_mbps": 2.42, "bytes_per_second": 2535961, "blocks_per_second": 316995, "encryption_time_ms": 0.006309244781732559, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 7.62939453125e-05, "efficiency_ratio": 2.5072207958921696e-05}, {"input_size": 32, "algorithm_name": "AES-128-CBC", "timestamp": 1753702855.4868777, "execution_time_ms": 0.10494254529476166, "setup_time_ms": 0.02185022458434105, "cleanup_time_ms": 28.009941801428795, "total_time_ms": 28.136734571307898, "baseline_memory_mb": 420.40625, "peak_memory_mb": 420.40625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 2, "actual_blocks": 3, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.5, "output_size_bytes": 48, "expansion_ratio": 1.5, "throughput_mbps": 4.31, "bytes_per_second": 4521018, "blocks_per_second": 423845, "encryption_time_ms": 0.007078051567077637, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0001220703125, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "AES-128-CBC", "timestamp": 1753702855.6892397, "execution_time_ms": 0.11098720133304596, "setup_time_ms": 0.022313091903924942, "cleanup_time_ms": 28.204412199556828, "total_time_ms": 28.3377124927938, "baseline_memory_mb": 420.40625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.1953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 4, "actual_blocks": 5, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.25, "output_size_bytes": 80, "expansion_ratio": 1.25, "throughput_mbps": 8.0, "bytes_per_second": 8387072, "blocks_per_second": 655240, "encryption_time_ms": 0.007630791515111923, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000213623046875, "efficiency_ratio": 0.00109375}, {"input_size": 128, "algorithm_name": "AES-128-CBC", "timestamp": 1753702855.892416, "execution_time_ms": 0.1054747961461544, "setup_time_ms": 0.021136365830898285, "cleanup_time_ms": 26.94919379428029, "total_time_ms": 27.075804956257343, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 8, "actual_blocks": 9, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.125, "output_size_bytes": 144, "expansion_ratio": 1.125, "throughput_mbps": 18.66, "bytes_per_second": 19569835, "blocks_per_second": 1376004, "encryption_time_ms": 0.006540678441524506, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000396728515625, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-128-CBC", "timestamp": 1753702856.0899973, "execution_time_ms": 0.10467749089002609, "setup_time_ms": 0.020563136786222458, "cleanup_time_ms": 27.315476909279823, "total_time_ms": 27.440717536956072, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 16, "actual_blocks": 17, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0625, "output_size_bytes": 272, "expansion_ratio": 1.0625, "throughput_mbps": 30.86, "bytes_per_second": 32359515, "blocks_per_second": 2148874, "encryption_time_ms": 0.007911119610071182, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-128-CBC", "timestamp": 1753702856.2827904, "execution_time_ms": 0.1066417433321476, "setup_time_ms": 0.020998995751142502, "cleanup_time_ms": 27.851069811731577, "total_time_ms": 27.978710550814867, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 32, "actual_blocks": 33, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0312, "output_size_bytes": 528, "expansion_ratio": 1.0312, "throughput_mbps": 55.86, "bytes_per_second": 58575016, "blocks_per_second": 3775342, "encryption_time_ms": 0.008740928024053574, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.001495361328125, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-128-CBC", "timestamp": 1753702856.4779944, "execution_time_ms": 0.10781204327940941, "setup_time_ms": 0.023860018700361252, "cleanup_time_ms": 27.059147134423256, "total_time_ms": 27.190819196403027, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 64, "actual_blocks": 65, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0156, "output_size_bytes": 1040, "expansion_ratio": 1.0156, "throughput_mbps": 132.38, "bytes_per_second": 138809699, "blocks_per_second": 8811162, "encryption_time_ms": 0.007377006113529205, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.002960205078125, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "AES-128-CBC", "timestamp": 1753702856.6708329, "execution_time_ms": 0.10975124314427376, "setup_time_ms": 0.02700788900256157, "cleanup_time_ms": 28.820318169891834, "total_time_ms": 28.95707730203867, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 128, "actual_blocks": 129, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0078, "output_size_bytes": 2064, "expansion_ratio": 1.0078, "throughput_mbps": 225.17, "bytes_per_second": 236111371, "blocks_per_second": 14872249, "encryption_time_ms": 0.00867387279868126, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.005889892578125, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-128-CBC", "timestamp": 1753702856.8640783, "execution_time_ms": 0.11459756642580032, "setup_time_ms": 0.030566006898880005, "cleanup_time_ms": 27.131767943501472, "total_time_ms": 27.276931516826153, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 256, "actual_blocks": 257, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0039, "output_size_bytes": 4112, "expansion_ratio": 1.0039, "throughput_mbps": 354.08, "bytes_per_second": 371284159, "blocks_per_second": 23295905, "encryption_time_ms": 0.01103198155760765, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.011749267578125, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.0582669, "execution_time_ms": 0.1215985044836998, "setup_time_ms": 0.04045385867357254, "cleanup_time_ms": 27.360080741345882, "total_time_ms": 27.522133104503155, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 512, "actual_blocks": 513, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.002, "output_size_bytes": 8208, "expansion_ratio": 1.002, "throughput_mbps": 546.79, "bytes_per_second": 573352867, "blocks_per_second": 35904543, "encryption_time_ms": 0.014287885278463364, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=512]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.023468017578125, "efficiency_ratio": 0.0}, {"input_size": 16384, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.2517445, "execution_time_ms": 0.12844549492001534, "setup_time_ms": 0.05861092358827591, "cleanup_time_ms": 27.14992268010974, "total_time_ms": 27.33697909861803, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16384, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 1024, "actual_blocks": 1025, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.001, "output_size_bytes": 16400, "expansion_ratio": 1.001, "throughput_mbps": 680.24, "bytes_per_second": 713288302, "blocks_per_second": 44624054, "encryption_time_ms": 0.022969674319028854, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1024]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.046905517578125, "efficiency_ratio": 0.0}, {"input_size": 32768, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.4464738, "execution_time_ms": 0.1781245693564415, "setup_time_ms": 0.0975457951426506, "cleanup_time_ms": 23.40460428968072, "total_time_ms": 23.68027465417981, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.75390625, "memory_increment_mb": 0.15234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32768, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 2048, "actual_blocks": 2049, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0005, "output_size_bytes": 32784, "expansion_ratio": 1.0005, "throughput_mbps": 836.7, "bytes_per_second": 877339187, "blocks_per_second": 54860473, "encryption_time_ms": 0.03734929487109184, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2048]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.093780517578125, "efficiency_ratio": 0.6155849358974359}, {"input_size": 65536, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.66337, "execution_time_ms": 0.18207747489213943, "setup_time_ms": 0.18315576016902924, "cleanup_time_ms": 24.09819420427084, "total_time_ms": 24.46342743933201, "baseline_memory_mb": 420.75390625, "peak_memory_mb": 420.84765625, "memory_increment_mb": 0.09375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 65536, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 4096, "actual_blocks": 4097, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0002, "output_size_bytes": 65552, "expansion_ratio": 1.0002, "throughput_mbps": 923.98, "bytes_per_second": 968859421, "blocks_per_second": 60568497, "encryption_time_ms": 0.06764242425560951, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4096]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.187530517578125, "efficiency_ratio": 2.0003255208333335}, {"input_size": 131072, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.831017, "execution_time_ms": 0.2572162076830864, "setup_time_ms": 0.34741638228297234, "cleanup_time_ms": 23.791303392499685, "total_time_ms": 24.395935982465744, "baseline_memory_mb": 420.84765625, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.2421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 131072, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 8192, "actual_blocks": 8193, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 131088, "expansion_ratio": 1.0001, "throughput_mbps": 972.72, "bytes_per_second": 1019969911, "blocks_per_second": 63755901, "encryption_time_ms": 0.12850575149059296, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8192]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.375030517578125, "efficiency_ratio": 1.5485131048387097}, {"input_size": 262144, "algorithm_name": "AES-128-CBC", "timestamp": 1753702857.999266, "execution_time_ms": 0.4125024192035198, "setup_time_ms": 0.6184931844472885, "cleanup_time_ms": 23.93317222595215, "total_time_ms": 24.964167829602957, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.60546875, "memory_increment_mb": 0.515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 262144, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 16384, "actual_blocks": 16385, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 262160, "expansion_ratio": 1.0001, "throughput_mbps": 984.69, "bytes_per_second": 1032518897, "blocks_per_second": 64536369, "encryption_time_ms": 0.2538878470659256, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16384]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.750030517578125, "efficiency_ratio": 1.4546046401515151}, {"input_size": 524288, "algorithm_name": "AES-128-CBC", "timestamp": 1753702858.1688702, "execution_time_ms": 0.7476291619241238, "setup_time_ms": 1.2259641662240028, "cleanup_time_ms": 23.904478643089533, "total_time_ms": 25.87807197123766, "baseline_memory_mb": 421.60546875, "peak_memory_mb": 422.89453125, "memory_increment_mb": 1.2890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 524288, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 32768, "actual_blocks": 32769, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 524304, "expansion_ratio": 1.0, "throughput_mbps": 934.16, "bytes_per_second": 979538277, "blocks_per_second": 61223010, "encryption_time_ms": 0.5352399311959743, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32768]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 1.500030517578125, "efficiency_ratio": 1.1636600378787878}, {"input_size": 1048576, "algorithm_name": "AES-128-CBC", "timestamp": 1753702858.3421676, "execution_time_ms": 1.7466310411691666, "setup_time_ms": 5.618221126496792, "cleanup_time_ms": 25.743496138602495, "total_time_ms": 33.108348306268454, "baseline_memory_mb": 420.72265625, "peak_memory_mb": 425.515625, "memory_increment_mb": 4.79296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1048576, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 65536, "actual_blocks": 65537, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 1048592, "expansion_ratio": 1.0, "throughput_mbps": 880.65, "bytes_per_second": 923432296, "blocks_per_second": 57715399, "encryption_time_ms": 1.1355201713740826, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=65536]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 3.000030517578125, "efficiency_ratio": 0.6259232375713122}, {"input_size": 2097152, "algorithm_name": "AES-128-CBC", "timestamp": 1753702858.56043, "execution_time_ms": 3.2177182845771313, "setup_time_ms": 5.767172202467918, "cleanup_time_ms": 24.517650716006756, "total_time_ms": 33.502541203051805, "baseline_memory_mb": 420.72265625, "peak_memory_mb": 430.45703125, "memory_increment_mb": 9.734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2097152, "key_size_bits": 128, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 131072, "actual_blocks": 131073, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 2097168, "expansion_ratio": 1.0, "throughput_mbps": 977.45, "bytes_per_second": 1024935788, "blocks_per_second": 64058975, "encryption_time_ms": 2.046130131930113, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=131072]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 6.000030517578125, "efficiency_ratio": 0.6163755266853933}]