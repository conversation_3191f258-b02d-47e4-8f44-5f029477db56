[{"input_size": 100, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655092.4334092, "execution_time_ms": 0.480820145457983, "setup_time_ms": 0.19494816660881042, "cleanup_time_ms": 22.871986031532288, "total_time_ms": 23.54775434359908, "baseline_memory_mb": 412.03125, "peak_memory_mb": 412.1171875, "memory_increment_mb": 0.0859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 100, "left_table_size": 100, "right_table_size": 100, "result_size": 1550, "hash_operations": 200, "hash_collisions": 85, "collision_rate": 0.425, "hash_efficiency": 1.0, "comparisons": 1550, "memory_accesses": 1750, "access_efficiency": 0.114, "build_time_ms": 0.039, "probe_time_ms": 0.391, "build_time_ratio": 0.091, "probe_time_ratio": 0.909, "actual_selectivity": 1.0, "expected_selectivity": 0.5, "selectivity_accuracy": 0.5, "unique_left_keys": 15, "unique_right_keys": 17, "matching_keys": 15, "join_ratio": 0.155, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=100, |S|=100]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=100, est_result=500]", "theoretical_memory_mb": 0.03662109375, "efficiency_ratio": 0.42613636363636365}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655092.6174784, "execution_time_ms": 0.9731779806315899, "setup_time_ms": 0.3336220979690552, "cleanup_time_ms": 22.98244694247842, "total_time_ms": 24.289247021079063, "baseline_memory_mb": 412.1171875, "peak_memory_mb": 412.1171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 200, "left_table_size": 200, "right_table_size": 200, "result_size": 3227, "hash_operations": 400, "hash_collisions": 168, "collision_rate": 0.42, "hash_efficiency": 1.0, "comparisons": 3227, "memory_accesses": 3627, "access_efficiency": 0.11, "build_time_ms": 0.069, "probe_time_ms": 0.808, "build_time_ratio": 0.079, "probe_time_ratio": 0.921, "actual_selectivity": 0.7812, "expected_selectivity": 0.5, "selectivity_accuracy": 0.7188, "unique_left_keys": 32, "unique_right_keys": 32, "matching_keys": 25, "join_ratio": 0.080675, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=200, |S|=200]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=200, est_result=2000]", "theoretical_memory_mb": 0.13427734375, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655092.7895427, "execution_time_ms": 1.3825141824781895, "setup_time_ms": 0.48074638471007347, "cleanup_time_ms": 22.896941751241684, "total_time_ms": 24.760202318429947, "baseline_memory_mb": 412.1171875, "peak_memory_mb": 412.42578125, "memory_increment_mb": 0.30859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 300, "left_table_size": 300, "right_table_size": 300, "result_size": 4607, "hash_operations": 600, "hash_collisions": 251, "collision_rate": 0.4183, "hash_efficiency": 1.0, "comparisons": 4607, "memory_accesses": 5207, "access_efficiency": 0.115, "build_time_ms": 0.097, "probe_time_ms": 1.107, "build_time_ratio": 0.081, "probe_time_ratio": 0.919, "actual_selectivity": 0.8163, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6837, "unique_left_keys": 49, "unique_right_keys": 47, "matching_keys": 40, "join_ratio": 0.051189, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=300]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=300, est_result=4500]", "theoretical_memory_mb": 0.29296875, "efficiency_ratio": 0.9493670886075949}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655092.9614706, "execution_time_ms": 1.7194089479744434, "setup_time_ms": 0.6210342980921268, "cleanup_time_ms": 23.797409143298864, "total_time_ms": 26.137852389365435, "baseline_memory_mb": 412.3125, "peak_memory_mb": 412.546875, "memory_increment_mb": 0.234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 400, "left_table_size": 400, "right_table_size": 400, "result_size": 5847, "hash_operations": 800, "hash_collisions": 337, "collision_rate": 0.4213, "hash_efficiency": 1.0, "comparisons": 5847, "memory_accesses": 6647, "access_efficiency": 0.12, "build_time_ms": 0.134, "probe_time_ms": 1.403, "build_time_ratio": 0.087, "probe_time_ratio": 0.913, "actual_selectivity": 0.8095, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6905, "unique_left_keys": 63, "unique_right_keys": 64, "matching_keys": 51, "join_ratio": 0.036544, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=400, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=8000]", "theoretical_memory_mb": 0.5126953125, "efficiency_ratio": 2.1875}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655093.137239, "execution_time_ms": 2.387298922985792, "setup_time_ms": 0.7787859067320824, "cleanup_time_ms": 22.49971404671669, "total_time_ms": 25.665798876434565, "baseline_memory_mb": 412.3125, "peak_memory_mb": 412.56640625, "memory_increment_mb": 0.25390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 500, "left_table_size": 500, "right_table_size": 500, "result_size": 8141, "hash_operations": 1000, "hash_collisions": 422, "collision_rate": 0.422, "hash_efficiency": 1.0, "comparisons": 8141, "memory_accesses": 9141, "access_efficiency": 0.109, "build_time_ms": 0.161, "probe_time_ms": 2.043, "build_time_ratio": 0.073, "probe_time_ratio": 0.927, "actual_selectivity": 0.8333, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6667, "unique_left_keys": 78, "unique_right_keys": 79, "matching_keys": 65, "join_ratio": 0.032564, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=500, |S|=500]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=500, est_result=12500]", "theoretical_memory_mb": 0.79345703125, "efficiency_ratio": 3.125}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655093.3179538, "execution_time_ms": 2.7546092867851257, "setup_time_ms": 0.8509429171681404, "cleanup_time_ms": 22.398967295885086, "total_time_ms": 26.004519499838352, "baseline_memory_mb": 412.31640625, "peak_memory_mb": 413.07421875, "memory_increment_mb": 0.7578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 600, "left_table_size": 600, "right_table_size": 600, "result_size": 9590, "hash_operations": 1200, "hash_collisions": 507, "collision_rate": 0.4225, "hash_efficiency": 1.0, "comparisons": 9590, "memory_accesses": 10790, "access_efficiency": 0.111, "build_time_ms": 0.183, "probe_time_ms": 2.297, "build_time_ratio": 0.074, "probe_time_ratio": 0.926, "actual_selectivity": 0.828, "expected_selectivity": 0.5, "selectivity_accuracy": 0.672, "unique_left_keys": 93, "unique_right_keys": 92, "matching_keys": 77, "join_ratio": 0.026639, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=600]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=600, est_result=18000]", "theoretical_memory_mb": 1.13525390625, "efficiency_ratio": 1.4980670103092784}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655093.5002391, "execution_time_ms": 3.265365492552519, "setup_time_ms": 0.9874613024294376, "cleanup_time_ms": 23.775137029588223, "total_time_ms": 28.02796382457018, "baseline_memory_mb": 412.60546875, "peak_memory_mb": 413.13671875, "memory_increment_mb": 0.53125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 700, "left_table_size": 700, "right_table_size": 700, "result_size": 10762, "hash_operations": 1400, "hash_collisions": 590, "collision_rate": 0.4214, "hash_efficiency": 1.0, "comparisons": 10762, "memory_accesses": 12162, "access_efficiency": 0.115, "build_time_ms": 0.215, "probe_time_ms": 2.774, "build_time_ratio": 0.072, "probe_time_ratio": 0.928, "actual_selectivity": 0.8273, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6727, "unique_left_keys": 110, "unique_right_keys": 110, "matching_keys": 91, "join_ratio": 0.021963, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=700, |S|=700]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=700, est_result=24500]", "theoretical_memory_mb": 1.5380859375, "efficiency_ratio": 2.895220588235294}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655093.6917388, "execution_time_ms": 3.8658584468066692, "setup_time_ms": 1.1672088876366615, "cleanup_time_ms": 22.818508092314005, "total_time_ms": 27.851575426757336, "baseline_memory_mb": 412.88671875, "peak_memory_mb": 413.40625, "memory_increment_mb": 0.51953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 800, "left_table_size": 800, "right_table_size": 800, "result_size": 12620, "hash_operations": 1600, "hash_collisions": 670, "collision_rate": 0.4188, "hash_efficiency": 1.0, "comparisons": 12620, "memory_accesses": 14220, "access_efficiency": 0.113, "build_time_ms": 0.257, "probe_time_ms": 3.313, "build_time_ratio": 0.072, "probe_time_ratio": 0.928, "actual_selectivity": 0.8385, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6615, "unique_left_keys": 130, "unique_right_keys": 129, "matching_keys": 109, "join_ratio": 0.019719, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=800, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=32000]", "theoretical_memory_mb": 2.001953125, "efficiency_ratio": 3.8533834586466167}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655093.8849452, "execution_time_ms": 4.273445624858141, "setup_time_ms": 1.3017067685723305, "cleanup_time_ms": 22.941325791180134, "total_time_ms": 28.516478184610605, "baseline_memory_mb": 412.65625, "peak_memory_mb": 413.68359375, "memory_increment_mb": 1.02734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 900, "left_table_size": 900, "right_table_size": 900, "result_size": 14668, "hash_operations": 1800, "hash_collisions": 759, "collision_rate": 0.4217, "hash_efficiency": 1.0, "comparisons": 14668, "memory_accesses": 16468, "access_efficiency": 0.109, "build_time_ms": 0.283, "probe_time_ms": 3.605, "build_time_ratio": 0.073, "probe_time_ratio": 0.927, "actual_selectivity": 0.7943, "expected_selectivity": 0.5, "selectivity_accuracy": 0.7057, "unique_left_keys": 141, "unique_right_keys": 134, "matching_keys": 112, "join_ratio": 0.018109, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=900]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=900, est_result=40500]", "theoretical_memory_mb": 2.52685546875, "efficiency_ratio": 2.459600760456274}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.50-skewed", "timestamp": 1753655094.0818763, "execution_time_ms": 4.695943463593721, "setup_time_ms": 1.399266067892313, "cleanup_time_ms": 22.581581957638264, "total_time_ms": 28.676791489124298, "baseline_memory_mb": 412.93359375, "peak_memory_mb": 413.9765625, "memory_increment_mb": 1.04296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "skewed", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 1000, "left_table_size": 1000, "right_table_size": 1000, "result_size": 16390, "hash_operations": 2000, "hash_collisions": 846, "collision_rate": 0.423, "hash_efficiency": 1.0, "comparisons": 16390, "memory_accesses": 18390, "access_efficiency": 0.109, "build_time_ms": 0.306, "probe_time_ms": 3.942, "build_time_ratio": 0.072, "probe_time_ratio": 0.928, "actual_selectivity": 0.8117, "expected_selectivity": 0.5, "selectivity_accuracy": 0.6883, "unique_left_keys": 154, "unique_right_keys": 152, "matching_keys": 125, "join_ratio": 0.01639, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1000, |S|=1000]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=1000, est_result=50000]", "theoretical_memory_mb": 3.11279296875, "efficiency_ratio": 2.984550561797753}]