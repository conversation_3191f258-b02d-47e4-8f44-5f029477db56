input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_join_selectivity,custom_data_distribution,custom_left_table_ratio,custom_right_table_ratio,custom_input_size,custom_left_table_size,custom_right_table_size,custom_result_size,custom_hash_operations,custom_hash_collisions,custom_collision_rate,custom_hash_efficiency,custom_comparisons,custom_memory_accesses,custom_access_efficiency,custom_build_time_ms,custom_probe_time_ms,custom_build_time_ratio,custom_probe_time_ratio,custom_actual_selectivity,custom_expected_selectivity,custom_selectivity_accuracy,custom_unique_left_keys,custom_unique_right_keys,custom_matching_keys,custom_join_ratio,custom_algorithm_type
100,HashJoin-sel0.50-skewed,1753655092.4334092,0.4808,0.1949,22.8720,23.5478,412.03,412.12,0.09,0.00,,,,"O(|R|+|S|) [|R|=100, |S|=100]","O(min(|R|,|S|)+result) [min=100, est_result=500]",0.04,0.4261,0.5,skewed,1.0,1.0,100,100,100,1550,200,85,0.425,1.0,1550,1750,0.114,0.039,0.391,0.091,0.909,1.0,0.5,0.5,15,17,15,0.155,hash_based_join
200,HashJoin-sel0.50-skewed,1753655092.6174784,0.9732,0.3336,22.9824,24.2892,412.12,412.12,0.00,0.00,,,,"O(|R|+|S|) [|R|=200, |S|=200]","O(min(|R|,|S|)+result) [min=200, est_result=2000]",0.13,0.0000,0.5,skewed,1.0,1.0,200,200,200,3227,400,168,0.42,1.0,3227,3627,0.11,0.069,0.808,0.079,0.921,0.7812,0.5,0.7188,32,32,25,0.080675,hash_based_join
300,HashJoin-sel0.50-skewed,1753655092.7895427,1.3825,0.4807,22.8969,24.7602,412.12,412.43,0.31,0.00,,,,"O(|R|+|S|) [|R|=300, |S|=300]","O(min(|R|,|S|)+result) [min=300, est_result=4500]",0.29,0.9494,0.5,skewed,1.0,1.0,300,300,300,4607,600,251,0.4183,1.0,4607,5207,0.115,0.097,1.107,0.081,0.919,0.8163,0.5,0.6837,49,47,40,0.051189,hash_based_join
400,HashJoin-sel0.50-skewed,1753655092.9614706,1.7194,0.6210,23.7974,26.1379,412.31,412.55,0.23,0.00,,,,"O(|R|+|S|) [|R|=400, |S|=400]","O(min(|R|,|S|)+result) [min=400, est_result=8000]",0.51,2.1875,0.5,skewed,1.0,1.0,400,400,400,5847,800,337,0.4213,1.0,5847,6647,0.12,0.134,1.403,0.087,0.913,0.8095,0.5,0.6905,63,64,51,0.036544,hash_based_join
500,HashJoin-sel0.50-skewed,1753655093.137239,2.3873,0.7788,22.4997,25.6658,412.31,412.57,0.25,0.00,,,,"O(|R|+|S|) [|R|=500, |S|=500]","O(min(|R|,|S|)+result) [min=500, est_result=12500]",0.79,3.1250,0.5,skewed,1.0,1.0,500,500,500,8141,1000,422,0.422,1.0,8141,9141,0.109,0.161,2.043,0.073,0.927,0.8333,0.5,0.6667,78,79,65,0.032564,hash_based_join
600,HashJoin-sel0.50-skewed,1753655093.3179538,2.7546,0.8509,22.3990,26.0045,412.32,413.07,0.76,0.00,,,,"O(|R|+|S|) [|R|=600, |S|=600]","O(min(|R|,|S|)+result) [min=600, est_result=18000]",1.14,1.4981,0.5,skewed,1.0,1.0,600,600,600,9590,1200,507,0.4225,1.0,9590,10790,0.111,0.183,2.297,0.074,0.926,0.828,0.5,0.672,93,92,77,0.026639,hash_based_join
700,HashJoin-sel0.50-skewed,1753655093.5002391,3.2654,0.9875,23.7751,28.0280,412.61,413.14,0.53,0.00,,,,"O(|R|+|S|) [|R|=700, |S|=700]","O(min(|R|,|S|)+result) [min=700, est_result=24500]",1.54,2.8952,0.5,skewed,1.0,1.0,700,700,700,10762,1400,590,0.4214,1.0,10762,12162,0.115,0.215,2.774,0.072,0.928,0.8273,0.5,0.6727,110,110,91,0.021963,hash_based_join
800,HashJoin-sel0.50-skewed,1753655093.6917388,3.8659,1.1672,22.8185,27.8516,412.89,413.41,0.52,0.00,,,,"O(|R|+|S|) [|R|=800, |S|=800]","O(min(|R|,|S|)+result) [min=800, est_result=32000]",2.00,3.8534,0.5,skewed,1.0,1.0,800,800,800,12620,1600,670,0.4188,1.0,12620,14220,0.113,0.257,3.313,0.072,0.928,0.8385,0.5,0.6615,130,129,109,0.019719,hash_based_join
900,HashJoin-sel0.50-skewed,1753655093.8849452,4.2734,1.3017,22.9413,28.5165,412.66,413.68,1.03,0.00,,,,"O(|R|+|S|) [|R|=900, |S|=900]","O(min(|R|,|S|)+result) [min=900, est_result=40500]",2.53,2.4596,0.5,skewed,1.0,1.0,900,900,900,14668,1800,759,0.4217,1.0,14668,16468,0.109,0.283,3.605,0.073,0.927,0.7943,0.5,0.7057,141,134,112,0.018109,hash_based_join
1000,HashJoin-sel0.50-skewed,1753655094.0818763,4.6959,1.3993,22.5816,28.6768,412.93,413.98,1.04,0.00,,,,"O(|R|+|S|) [|R|=1000, |S|=1000]","O(min(|R|,|S|)+result) [min=1000, est_result=50000]",3.11,2.9846,0.5,skewed,1.0,1.0,1000,1000,1000,16390,2000,846,0.423,1.0,16390,18390,0.109,0.306,3.942,0.072,0.928,0.8117,0.5,0.6883,154,152,125,0.01639,hash_based_join
