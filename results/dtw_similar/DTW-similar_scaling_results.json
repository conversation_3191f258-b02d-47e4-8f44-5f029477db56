[{"input_size": 50, "algorithm_name": "DTW-similar", "timestamp": 1753706119.8871963, "execution_time_ms": 2.356885187327862, "setup_time_ms": 0.047274865210056305, "cleanup_time_ms": 2.877690829336643, "total_time_ms": 5.281850881874561, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 50, "sequence_length": 50, "dtw_distance": 8.825982357387053, "normalized_distance": 0.17651964714774107, "distance_calculations": 2500, "theoretical_operations": 2500, "operation_efficiency": 1.0, "matrix_accesses": 7500, "matrix_size": 2601, "theoretical_matrix_size": 2601, "optimal_path_length": 50, "seq1_std": 0.947, "seq2_std": 0.918, "cross_correlation": 0.973, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=50]", "theoretical_space_complexity": "O(n²) [n=50]", "theoretical_memory_mb": 0.01984405517578125, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "DTW-similar", "timestamp": 1753706119.9271247, "execution_time_ms": 9.088325034826994, "setup_time_ms": 0.04067458212375641, "cleanup_time_ms": 2.904808148741722, "total_time_ms": 12.033807765692472, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 100, "sequence_length": 100, "dtw_distance": 17.622436202974892, "normalized_distance": 0.17622436202974892, "distance_calculations": 10000, "theoretical_operations": 10000, "operation_efficiency": 1.0, "matrix_accesses": 30000, "matrix_size": 10201, "theoretical_matrix_size": 10201, "optimal_path_length": 101, "seq1_std": 0.895, "seq2_std": 0.948, "cross_correlation": 0.973, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=100]", "theoretical_space_complexity": "O(n²) [n=100]", "theoretical_memory_mb": 0.07782745361328125, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "DTW-similar", "timestamp": 1753706120.0203407, "execution_time_ms": 19.902165047824383, "setup_time_ms": 0.04120822995901108, "cleanup_time_ms": 2.8744940645992756, "total_time_ms": 22.81786734238267, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 150, "sequence_length": 150, "dtw_distance": 25.163536948519702, "normalized_distance": 0.16775691299013135, "distance_calculations": 22500, "theoretical_operations": 22500, "operation_efficiency": 1.0, "matrix_accesses": 67500, "matrix_size": 22801, "theoretical_matrix_size": 22801, "optimal_path_length": 152, "seq1_std": 0.948, "seq2_std": 0.945, "cross_correlation": 0.977, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=150]", "theoretical_space_complexity": "O(n²) [n=150]", "theoretical_memory_mb": 0.17395782470703125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "DTW-similar", "timestamp": 1753706120.200361, "execution_time_ms": 35.25493470951915, "setup_time_ms": 0.043474603444337845, "cleanup_time_ms": 2.879846841096878, "total_time_ms": 38.178256154060364, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 200, "sequence_length": 200, "dtw_distance": 33.60245481954236, "normalized_distance": 0.1680122740977118, "distance_calculations": 40000, "theoretical_operations": 40000, "operation_efficiency": 1.0, "matrix_accesses": 120000, "matrix_size": 40401, "theoretical_matrix_size": 40401, "optimal_path_length": 205, "seq1_std": 0.954, "seq2_std": 0.921, "cross_correlation": 0.975, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=200]", "theoretical_space_complexity": "O(n²) [n=200]", "theoretical_memory_mb": 0.30823516845703125, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "DTW-similar", "timestamp": 1753706120.5019886, "execution_time_ms": 54.82456646859646, "setup_time_ms": 0.05061877891421318, "cleanup_time_ms": 2.8790365904569626, "total_time_ms": 57.754221837967634, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 250, "sequence_length": 250, "dtw_distance": 39.28442976006027, "normalized_distance": 0.15713771904024107, "distance_calculations": 62500, "theoretical_operations": 62500, "operation_efficiency": 1.0, "matrix_accesses": 187500, "matrix_size": 63001, "theoretical_matrix_size": 63001, "optimal_path_length": 253, "seq1_std": 0.973, "seq2_std": 0.979, "cross_correlation": 0.978, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=250]", "theoretical_space_complexity": "O(n²) [n=250]", "theoretical_memory_mb": 0.48065948486328125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "DTW-similar", "timestamp": 1753706120.9601681, "execution_time_ms": 83.57477122917771, "setup_time_ms": 0.04830025136470795, "cleanup_time_ms": 3.744714893400669, "total_time_ms": 87.36778637394309, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 300, "sequence_length": 300, "dtw_distance": 50.1187357353083, "normalized_distance": 0.16706245245102766, "distance_calculations": 90000, "theoretical_operations": 90000, "operation_efficiency": 1.0, "matrix_accesses": 270000, "matrix_size": 90601, "theoretical_matrix_size": 90601, "optimal_path_length": 305, "seq1_std": 0.987, "seq2_std": 0.988, "cross_correlation": 0.977, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=300]", "theoretical_space_complexity": "O(n²) [n=300]", "theoretical_memory_mb": 0.6912307739257812, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "DTW-similar", "timestamp": 1753706121.6481707, "execution_time_ms": 108.52504838258028, "setup_time_ms": 0.060725025832653046, "cleanup_time_ms": 2.8982185758650303, "total_time_ms": 111.48399198427796, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 350, "sequence_length": 350, "dtw_distance": 57.48574599870317, "normalized_distance": 0.16424498856772335, "distance_calculations": 122500, "theoretical_operations": 122500, "operation_efficiency": 1.0, "matrix_accesses": 367500, "matrix_size": 123201, "theoretical_matrix_size": 123201, "optimal_path_length": 359, "seq1_std": 0.954, "seq2_std": 0.975, "cross_correlation": 0.977, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=350]", "theoretical_space_complexity": "O(n²) [n=350]", "theoretical_memory_mb": 0.9399490356445312, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "DTW-similar", "timestamp": 1753706122.5431948, "execution_time_ms": 143.68635397404432, "setup_time_ms": 0.060745980590581894, "cleanup_time_ms": 2.9135229997336864, "total_time_ms": 146.6606229543686, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 400, "sequence_length": 400, "dtw_distance": 66.83640102131503, "normalized_distance": 0.16709100255328757, "distance_calculations": 160000, "theoretical_operations": 160000, "operation_efficiency": 1.0, "matrix_accesses": 480000, "matrix_size": 160801, "theoretical_matrix_size": 160801, "optimal_path_length": 406, "seq1_std": 0.954, "seq2_std": 0.965, "cross_correlation": 0.976, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=400]", "theoretical_space_complexity": "O(n²) [n=400]", "theoretical_memory_mb": 1.2268142700195312, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "DTW-similar", "timestamp": 1753706123.7184315, "execution_time_ms": 184.11688469350338, "setup_time_ms": 0.06643123924732208, "cleanup_time_ms": 2.9743192717432976, "total_time_ms": 187.157635204494, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 450, "sequence_length": 450, "dtw_distance": 73.24243472654037, "normalized_distance": 0.1627609660589786, "distance_calculations": 202500, "theoretical_operations": 202500, "operation_efficiency": 1.0, "matrix_accesses": 607500, "matrix_size": 203401, "theoretical_matrix_size": 203401, "optimal_path_length": 453, "seq1_std": 0.99, "seq2_std": 0.985, "cross_correlation": 0.979, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=450]", "theoretical_space_complexity": "O(n²) [n=450]", "theoretical_memory_mb": 1.5518264770507812, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "DTW-similar", "timestamp": 1753706125.215804, "execution_time_ms": 228.15348617732525, "setup_time_ms": 0.06891880184412003, "cleanup_time_ms": 3.033543936908245, "total_time_ms": 231.2559489160776, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "similar", "input_size": 500, "sequence_length": 500, "dtw_distance": 79.02181613404608, "normalized_distance": 0.15804363226809215, "distance_calculations": 250000, "theoretical_operations": 250000, "operation_efficiency": 1.0, "matrix_accesses": 750000, "matrix_size": 251001, "theoretical_matrix_size": 251001, "optimal_path_length": 506, "seq1_std": 0.98, "seq2_std": 0.983, "cross_correlation": 0.979, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=500]", "theoretical_space_complexity": "O(n²) [n=500]", "theoretical_memory_mb": 1.9149856567382812, "efficiency_ratio": 0.0}]