input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_sequence_type,custom_input_size,custom_sequence_length,custom_dtw_distance,custom_normalized_distance,custom_distance_calculations,custom_theoretical_operations,custom_operation_efficiency,custom_matrix_accesses,custom_matrix_size,custom_theoretical_matrix_size,custom_optimal_path_length,custom_seq1_std,custom_seq2_std,custom_cross_correlation,custom_algorithm_type
50,DTW-similar,1753706119.8871963,2.3569,0.0473,2.8777,5.2819,63.56,63.56,0.00,0.00,,,,O(n²) [n=50],O(n²) [n=50],0.02,0.0000,similar,50,50,8.825982357387053,0.17651964714774107,2500,2500,1.0,7500,2601,2601,50,0.947,0.918,0.973,dynamic_programming
100,DTW-similar,1753706119.9271247,9.0883,0.0407,2.9048,12.0338,63.56,63.56,0.00,0.00,,,,O(n²) [n=100],O(n²) [n=100],0.08,0.0000,similar,100,100,17.622436202974892,0.17622436202974892,10000,10000,1.0,30000,10201,10201,101,0.895,0.948,0.973,dynamic_programming
150,DTW-similar,1753706120.0203407,19.9022,0.0412,2.8745,22.8179,63.56,63.56,0.00,0.00,,,,O(n²) [n=150],O(n²) [n=150],0.17,0.0000,similar,150,150,25.163536948519702,0.16775691299013135,22500,22500,1.0,67500,22801,22801,152,0.948,0.945,0.977,dynamic_programming
200,DTW-similar,1753706120.200361,35.2549,0.0435,2.8798,38.1783,63.56,63.56,0.00,0.00,,,,O(n²) [n=200],O(n²) [n=200],0.31,0.0000,similar,200,200,33.60245481954236,0.1680122740977118,40000,40000,1.0,120000,40401,40401,205,0.954,0.921,0.975,dynamic_programming
250,DTW-similar,1753706120.5019886,54.8246,0.0506,2.8790,57.7542,63.56,63.56,0.00,0.00,,,,O(n²) [n=250],O(n²) [n=250],0.48,0.0000,similar,250,250,39.28442976006027,0.15713771904024107,62500,62500,1.0,187500,63001,63001,253,0.973,0.979,0.978,dynamic_programming
300,DTW-similar,1753706120.9601681,83.5748,0.0483,3.7447,87.3678,63.56,63.56,0.00,0.00,,,,O(n²) [n=300],O(n²) [n=300],0.69,0.0000,similar,300,300,50.1187357353083,0.16706245245102766,90000,90000,1.0,270000,90601,90601,305,0.987,0.988,0.977,dynamic_programming
350,DTW-similar,1753706121.6481707,108.5250,0.0607,2.8982,111.4840,63.56,63.56,0.00,0.00,,,,O(n²) [n=350],O(n²) [n=350],0.94,0.0000,similar,350,350,57.48574599870317,0.16424498856772335,122500,122500,1.0,367500,123201,123201,359,0.954,0.975,0.977,dynamic_programming
400,DTW-similar,1753706122.5431948,143.6864,0.0607,2.9135,146.6606,63.56,63.56,0.00,0.00,,,,O(n²) [n=400],O(n²) [n=400],1.23,0.0000,similar,400,400,66.83640102131503,0.16709100255328757,160000,160000,1.0,480000,160801,160801,406,0.954,0.965,0.976,dynamic_programming
450,DTW-similar,1753706123.7184315,184.1169,0.0664,2.9743,187.1576,63.56,63.56,0.00,0.00,,,,O(n²) [n=450],O(n²) [n=450],1.55,0.0000,similar,450,450,73.24243472654037,0.1627609660589786,202500,202500,1.0,607500,203401,203401,453,0.99,0.985,0.979,dynamic_programming
500,DTW-similar,1753706125.215804,228.1535,0.0689,3.0335,231.2559,63.56,63.56,0.00,0.00,,,,O(n²) [n=500],O(n²) [n=500],1.91,0.0000,similar,500,500,79.02181613404608,0.15804363226809215,250000,250000,1.0,750000,251001,251001,506,0.98,0.983,0.979,dynamic_programming
