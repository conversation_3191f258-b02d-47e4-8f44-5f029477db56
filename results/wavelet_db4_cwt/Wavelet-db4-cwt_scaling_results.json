[{"input_size": 100, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657407.6819704, "execution_time_ms": 0.0, "setup_time_ms": 0.046436209231615067, "cleanup_time_ms": 100.9178920648992, "total_time_ms": 100.96432827413082, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657407.8826158, "execution_time_ms": 0.0, "setup_time_ms": 0.04668207839131355, "cleanup_time_ms": 98.79339393228292, "total_time_ms": 98.84007601067424, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657408.0828385, "execution_time_ms": 0.0, "setup_time_ms": 0.04960782825946808, "cleanup_time_ms": 99.04098603874445, "total_time_ms": 99.09059386700392, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657408.282104, "execution_time_ms": 0.0, "setup_time_ms": 0.056040938943624496, "cleanup_time_ms": 99.29093532264233, "total_time_ms": 99.34697626158595, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657408.4801025, "execution_time_ms": 0.0, "setup_time_ms": 0.05811499431729317, "cleanup_time_ms": 99.75219890475273, "total_time_ms": 99.81031389907002, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657408.679657, "execution_time_ms": 0.0, "setup_time_ms": 0.06489967927336693, "cleanup_time_ms": 100.62378318980336, "total_time_ms": 100.68868286907673, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 1300, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657408.8792238, "execution_time_ms": 0.0, "setup_time_ms": 0.07036374881863594, "cleanup_time_ms": 106.16677301004529, "total_time_ms": 106.23713675886393, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 1500, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657409.0949247, "execution_time_ms": 0.0, "setup_time_ms": 0.07762573659420013, "cleanup_time_ms": 104.36529712751508, "total_time_ms": 104.44292286410928, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 1700, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657409.3047204, "execution_time_ms": 0.0, "setup_time_ms": 0.0817151740193367, "cleanup_time_ms": 101.46733792498708, "total_time_ms": 101.54905309900641, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}, {"input_size": 1900, "algorithm_name": "Wavelet-db4-cwt", "timestamp": 1753657409.5070124, "execution_time_ms": 0.0, "setup_time_ms": 0.08052214980125427, "cleanup_time_ms": 101.10853798687458, "total_time_ms": 101.18906013667583, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 0.0, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"error": "'pywt._extensions._pywt.Wavelet' object has no attribute 'complex_cwt'"}, "theoretical_time_complexity": null, "theoretical_space_complexity": null, "theoretical_memory_mb": 0.0, "efficiency_ratio": 0.0}]