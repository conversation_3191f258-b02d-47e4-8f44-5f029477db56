input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_sequence_type,custom_input_size,custom_sequence_length,custom_dtw_distance,custom_normalized_distance,custom_distance_calculations,custom_theoretical_operations,custom_operation_efficiency,custom_matrix_accesses,custom_matrix_size,custom_theoretical_matrix_size,custom_optimal_path_length,custom_seq1_std,custom_seq2_std,custom_cross_correlation,custom_algorithm_type
50,DTW-periodic,1753706127.05938,2.2673,0.0936,2.8772,5.2381,63.56,63.56,0.00,0.00,,,,O(n²) [n=50],O(n²) [n=50],0.02,0.0000,periodic,50,50,4.741825127966011,0.09483650255932022,2500,2500,1.0,7500,2601,2601,58,0.695,0.723,0.966,dynamic_programming
100,DTW-periodic,1753706127.0983799,9.0471,0.0724,2.9596,12.0791,63.56,63.56,0.00,0.00,,,,O(n²) [n=100],O(n²) [n=100],0.08,0.0000,periodic,100,100,7.662582557333047,0.07662582557333047,10000,10000,1.0,30000,10201,10201,118,0.708,0.728,0.962,dynamic_programming
150,DTW-periodic,1753706127.1920362,20.3934,0.0703,2.9733,23.4370,63.56,63.56,0.00,0.00,,,,O(n²) [n=150],O(n²) [n=150],0.17,0.0000,periodic,150,150,11.267205757424852,0.07511470504949902,22500,22500,1.0,67500,22801,22801,186,0.705,0.72,0.961,dynamic_programming
200,DTW-periodic,1753706127.3767684,35.6099,0.0779,2.8921,38.5799,63.56,63.56,0.00,0.00,,,,O(n²) [n=200],O(n²) [n=200],0.31,0.0000,periodic,200,200,15.463932883831228,0.07731966441915614,40000,40000,1.0,120000,40401,40401,250,0.715,0.72,0.963,dynamic_programming
250,DTW-periodic,1753706127.6819522,56.1324,0.0851,3.0252,59.2427,63.56,63.56,0.00,0.00,,,,O(n²) [n=250],O(n²) [n=250],0.48,0.0000,periodic,250,250,18.186920618294526,0.0727476824731781,62500,62500,1.0,187500,63001,63001,318,0.705,0.707,0.963,dynamic_programming
300,DTW-periodic,1753706128.152109,79.8503,0.0860,2.9029,82.8392,63.56,63.56,0.00,0.00,,,,O(n²) [n=300],O(n²) [n=300],0.69,0.0000,periodic,300,300,22.46369403205464,0.0748789801068488,90000,90000,1.0,270000,90601,90601,383,0.705,0.707,0.951,dynamic_programming
350,DTW-periodic,1753706128.818369,109.1490,0.0929,2.9837,112.2256,63.56,63.56,0.00,0.00,,,,O(n²) [n=350],O(n²) [n=350],0.94,0.0000,periodic,350,350,24.52187441796781,0.07006249833705087,122500,122500,1.0,367500,123201,123201,445,0.708,0.711,0.956,dynamic_programming
400,DTW-periodic,1753706129.7155495,147.7689,0.0922,2.9749,150.8360,63.56,63.56,0.00,0.00,,,,O(n²) [n=400],O(n²) [n=400],1.23,0.0000,periodic,400,400,27.117883531093064,0.06779470882773266,160000,160000,1.0,480000,160801,160801,511,0.715,0.714,0.96,dynamic_programming
450,DTW-periodic,1753706130.9073322,181.2392,0.1007,2.9733,184.3131,63.56,63.56,0.00,0.00,,,,O(n²) [n=450],O(n²) [n=450],1.55,0.0000,periodic,450,450,30.015734575921385,0.0667016323909364,202500,202500,1.0,607500,203401,203401,567,0.705,0.713,0.957,dynamic_programming
500,DTW-periodic,1753706132.3811502,226.1189,0.0989,3.0105,229.2284,63.56,63.56,0.00,0.00,,,,O(n²) [n=500],O(n²) [n=500],1.91,0.0000,periodic,500,500,34.46312840958379,0.06892625681916757,250000,250000,1.0,750000,251001,251001,632,0.725,0.716,0.957,dynamic_programming
