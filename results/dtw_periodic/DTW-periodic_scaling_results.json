[{"input_size": 50, "algorithm_name": "DTW-periodic", "timestamp": 1753706127.05938, "execution_time_ms": 2.2672532126307487, "setup_time_ms": 0.09360304102301598, "cleanup_time_ms": 2.8772056102752686, "total_time_ms": 5.238061863929033, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 50, "sequence_length": 50, "dtw_distance": 4.741825127966011, "normalized_distance": 0.09483650255932022, "distance_calculations": 2500, "theoretical_operations": 2500, "operation_efficiency": 1.0, "matrix_accesses": 7500, "matrix_size": 2601, "theoretical_matrix_size": 2601, "optimal_path_length": 58, "seq1_std": 0.695, "seq2_std": 0.723, "cross_correlation": 0.966, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=50]", "theoretical_space_complexity": "O(n²) [n=50]", "theoretical_memory_mb": 0.01984405517578125, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "DTW-periodic", "timestamp": 1753706127.0983799, "execution_time_ms": 9.047078993171453, "setup_time_ms": 0.07243920117616653, "cleanup_time_ms": 2.9595610685646534, "total_time_ms": 12.079079262912273, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 100, "sequence_length": 100, "dtw_distance": 7.662582557333047, "normalized_distance": 0.07662582557333047, "distance_calculations": 10000, "theoretical_operations": 10000, "operation_efficiency": 1.0, "matrix_accesses": 30000, "matrix_size": 10201, "theoretical_matrix_size": 10201, "optimal_path_length": 118, "seq1_std": 0.708, "seq2_std": 0.728, "cross_correlation": 0.962, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=100]", "theoretical_space_complexity": "O(n²) [n=100]", "theoretical_memory_mb": 0.07782745361328125, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "DTW-periodic", "timestamp": 1753706127.1920362, "execution_time_ms": 20.39339356124401, "setup_time_ms": 0.07028412073850632, "cleanup_time_ms": 2.9732808470726013, "total_time_ms": 23.43695852905512, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 150, "sequence_length": 150, "dtw_distance": 11.267205757424852, "normalized_distance": 0.07511470504949902, "distance_calculations": 22500, "theoretical_operations": 22500, "operation_efficiency": 1.0, "matrix_accesses": 67500, "matrix_size": 22801, "theoretical_matrix_size": 22801, "optimal_path_length": 186, "seq1_std": 0.705, "seq2_std": 0.72, "cross_correlation": 0.961, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=150]", "theoretical_space_complexity": "O(n²) [n=150]", "theoretical_memory_mb": 0.17395782470703125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "DTW-periodic", "timestamp": 1753706127.3767684, "execution_time_ms": 35.60991296544671, "setup_time_ms": 0.07786881178617477, "cleanup_time_ms": 2.8921090997755527, "total_time_ms": 38.57989087700844, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 200, "sequence_length": 200, "dtw_distance": 15.463932883831228, "normalized_distance": 0.07731966441915614, "distance_calculations": 40000, "theoretical_operations": 40000, "operation_efficiency": 1.0, "matrix_accesses": 120000, "matrix_size": 40401, "theoretical_matrix_size": 40401, "optimal_path_length": 250, "seq1_std": 0.715, "seq2_std": 0.72, "cross_correlation": 0.963, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=200]", "theoretical_space_complexity": "O(n²) [n=200]", "theoretical_memory_mb": 0.30823516845703125, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "DTW-periodic", "timestamp": 1753706127.6819522, "execution_time_ms": 56.13243095576763, "setup_time_ms": 0.08505908772349358, "cleanup_time_ms": 3.0251871794462204, "total_time_ms": 59.242677222937346, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 250, "sequence_length": 250, "dtw_distance": 18.186920618294526, "normalized_distance": 0.0727476824731781, "distance_calculations": 62500, "theoretical_operations": 62500, "operation_efficiency": 1.0, "matrix_accesses": 187500, "matrix_size": 63001, "theoretical_matrix_size": 63001, "optimal_path_length": 318, "seq1_std": 0.705, "seq2_std": 0.707, "cross_correlation": 0.963, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=250]", "theoretical_space_complexity": "O(n²) [n=250]", "theoretical_memory_mb": 0.48065948486328125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "DTW-periodic", "timestamp": 1753706128.152109, "execution_time_ms": 79.85025625675917, "setup_time_ms": 0.08604675531387329, "cleanup_time_ms": 2.9029427096247673, "total_time_ms": 82.83924572169781, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 300, "sequence_length": 300, "dtw_distance": 22.46369403205464, "normalized_distance": 0.0748789801068488, "distance_calculations": 90000, "theoretical_operations": 90000, "operation_efficiency": 1.0, "matrix_accesses": 270000, "matrix_size": 90601, "theoretical_matrix_size": 90601, "optimal_path_length": 383, "seq1_std": 0.705, "seq2_std": 0.707, "cross_correlation": 0.951, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=300]", "theoretical_space_complexity": "O(n²) [n=300]", "theoretical_memory_mb": 0.6912307739257812, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "DTW-periodic", "timestamp": 1753706128.818369, "execution_time_ms": 109.14900600910187, "setup_time_ms": 0.09288731962442398, "cleanup_time_ms": 2.9837461188435555, "total_time_ms": 112.22563944756985, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 350, "sequence_length": 350, "dtw_distance": 24.52187441796781, "normalized_distance": 0.07006249833705087, "distance_calculations": 122500, "theoretical_operations": 122500, "operation_efficiency": 1.0, "matrix_accesses": 367500, "matrix_size": 123201, "theoretical_matrix_size": 123201, "optimal_path_length": 445, "seq1_std": 0.708, "seq2_std": 0.711, "cross_correlation": 0.956, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=350]", "theoretical_space_complexity": "O(n²) [n=350]", "theoretical_memory_mb": 0.9399490356445312, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "DTW-periodic", "timestamp": 1753706129.7155495, "execution_time_ms": 147.76893043890595, "setup_time_ms": 0.09223492816090584, "cleanup_time_ms": 2.9748501256108284, "total_time_ms": 150.8360154926777, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 400, "sequence_length": 400, "dtw_distance": 27.117883531093064, "normalized_distance": 0.06779470882773266, "distance_calculations": 160000, "theoretical_operations": 160000, "operation_efficiency": 1.0, "matrix_accesses": 480000, "matrix_size": 160801, "theoretical_matrix_size": 160801, "optimal_path_length": 511, "seq1_std": 0.715, "seq2_std": 0.714, "cross_correlation": 0.96, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=400]", "theoretical_space_complexity": "O(n²) [n=400]", "theoretical_memory_mb": 1.2268142700195312, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "DTW-periodic", "timestamp": 1753706130.9073322, "execution_time_ms": 181.23915204778314, "setup_time_ms": 0.10066339746117592, "cleanup_time_ms": 2.9732598923146725, "total_time_ms": 184.31307533755898, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 450, "sequence_length": 450, "dtw_distance": 30.015734575921385, "normalized_distance": 0.0667016323909364, "distance_calculations": 202500, "theoretical_operations": 202500, "operation_efficiency": 1.0, "matrix_accesses": 607500, "matrix_size": 203401, "theoretical_matrix_size": 203401, "optimal_path_length": 567, "seq1_std": 0.705, "seq2_std": 0.713, "cross_correlation": 0.957, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=450]", "theoretical_space_complexity": "O(n²) [n=450]", "theoretical_memory_mb": 1.5518264770507812, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "DTW-periodic", "timestamp": 1753706132.3811502, "execution_time_ms": 226.11893331632018, "setup_time_ms": 0.09892880916595459, "cleanup_time_ms": 3.0105230398476124, "total_time_ms": 229.22838516533375, "baseline_memory_mb": 63.5625, "peak_memory_mb": 63.5625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "periodic", "input_size": 500, "sequence_length": 500, "dtw_distance": 34.46312840958379, "normalized_distance": 0.06892625681916757, "distance_calculations": 250000, "theoretical_operations": 250000, "operation_efficiency": 1.0, "matrix_accesses": 750000, "matrix_size": 251001, "theoretical_matrix_size": 251001, "optimal_path_length": 632, "seq1_std": 0.725, "seq2_std": 0.716, "cross_correlation": 0.957, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=500]", "theoretical_space_complexity": "O(n²) [n=500]", "theoretical_memory_mb": 1.9149856567382812, "efficiency_ratio": 0.0}]