input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_join_selectivity,custom_data_distribution,custom_left_table_ratio,custom_right_table_ratio,custom_input_size,custom_left_table_size,custom_right_table_size,custom_result_size,custom_hash_operations,custom_hash_collisions,custom_collision_rate,custom_hash_efficiency,custom_comparisons,custom_memory_accesses,custom_access_efficiency,custom_build_time_ms,custom_probe_time_ms,custom_build_time_ratio,custom_probe_time_ratio,custom_actual_selectivity,custom_expected_selectivity,custom_selectivity_accuracy,custom_unique_left_keys,custom_unique_right_keys,custom_matching_keys,custom_join_ratio,custom_algorithm_type
100,HashJoin-sel0.30-uniform,1753655095.9206922,0.1276,0.2616,25.5638,25.9530,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=150, |S|=80]","O(min(|R|,|S|)+result) [min=80, est_result=360]",0.03,0.0000,0.3,uniform,1.5,0.8,100,150,80,44,230,10,0.0435,1.0,44,274,0.839,0.041,0.042,0.489,0.511,0.2031,0.3,0.9031,128,70,26,0.003667,hash_based_join
200,HashJoin-sel0.30-uniform,1753655096.0929084,0.2348,0.4991,26.0795,26.8135,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=300, |S|=160]","O(min(|R|,|S|)+result) [min=160, est_result=1440]",0.10,0.0000,0.3,uniform,1.5,0.8,200,300,160,105,460,27,0.0587,1.0,105,565,0.814,0.076,0.103,0.423,0.577,0.2581,0.3,0.9581,248,133,64,0.002188,hash_based_join
300,HashJoin-sel0.30-uniform,1753655096.2645218,0.3349,0.7492,22.8877,23.9718,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=450, |S|=240]","O(min(|R|,|S|)+result) [min=240, est_result=3240]",0.21,0.0000,0.3,uniform,1.5,0.8,300,450,240,127,690,26,0.0377,1.0,127,817,0.845,0.106,0.136,0.438,0.562,0.2208,0.3,0.9208,385,214,85,0.001176,hash_based_join
400,HashJoin-sel0.30-uniform,1753655096.4371104,0.4104,0.9246,22.7980,24.1330,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=600, |S|=320]","O(min(|R|,|S|)+result) [min=320, est_result=5760]",0.37,0.0000,0.3,uniform,1.5,0.8,400,600,320,177,920,44,0.0478,1.0,177,1097,0.839,0.131,0.172,0.432,0.568,0.2269,0.3,0.9269,520,276,118,0.000922,hash_based_join
500,HashJoin-sel0.30-uniform,1753655096.601087,0.4960,1.1983,23.0155,24.7098,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=750, |S|=400]","O(min(|R|,|S|)+result) [min=400, est_result=9000]",0.57,0.0000,0.3,uniform,1.5,0.8,500,750,400,229,1150,55,0.0478,1.0,229,1379,0.834,0.156,0.203,0.434,0.566,0.2379,0.3,0.9379,660,345,157,0.000763,hash_based_join
600,HashJoin-sel0.30-uniform,1753655096.7693772,0.5967,1.3554,23.0778,25.0300,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=900, |S|=480]","O(min(|R|,|S|)+result) [min=480, est_result=12960]",0.82,0.0000,0.3,uniform,1.5,0.8,600,900,480,270,1380,76,0.0551,1.0,270,1650,0.836,0.188,0.245,0.434,0.566,0.2119,0.3,0.9119,793,404,168,0.000625,hash_based_join
700,HashJoin-sel0.30-uniform,1753655096.935193,0.6873,1.5441,22.8844,25.1157,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1050, |S|=560]","O(min(|R|,|S|)+result) [min=560, est_result=17640]",1.11,0.0000,0.3,uniform,1.5,0.8,700,1050,560,316,1610,90,0.0559,1.0,316,1926,0.836,0.222,0.309,0.418,0.582,0.2227,0.3,0.9227,925,470,206,0.000537,hash_based_join
800,HashJoin-sel0.30-uniform,1753655097.0994341,0.7880,1.8112,22.7398,25.3390,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1200, |S|=640]","O(min(|R|,|S|)+result) [min=640, est_result=23040]",1.45,0.0000,0.3,uniform,1.5,0.8,800,1200,640,382,1840,111,0.0603,1.0,382,2222,0.828,0.233,0.366,0.389,0.611,0.2277,0.3,0.9277,1054,529,240,0.000497,hash_based_join
900,HashJoin-sel0.30-uniform,1753655097.264579,0.8568,2.1529,22.7836,25.7933,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1350, |S|=720]","O(min(|R|,|S|)+result) [min=720, est_result=29160]",1.82,0.0000,0.3,uniform,1.5,0.8,900,1350,720,423,2070,123,0.0594,1.0,423,2493,0.83,0.272,0.417,0.395,0.605,0.2329,0.3,0.9329,1181,597,275,0.000435,hash_based_join
1000,HashJoin-sel0.30-uniform,1753655097.4338245,0.9881,2.3220,22.8265,26.1366,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1500, |S|=800]","O(min(|R|,|S|)+result) [min=800, est_result=36000]",2.25,0.0000,0.3,uniform,1.5,0.8,1000,1500,800,480,2300,134,0.0583,1.0,480,2780,0.827,0.29,0.446,0.394,0.606,0.243,0.3,0.943,1313,666,319,0.0004,hash_based_join
