[{"input_size": 100, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655095.9206922, "execution_time_ms": 0.12760162353515625, "setup_time_ms": 0.2616140991449356, "cleanup_time_ms": 25.56381095200777, "total_time_ms": 25.953026674687862, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 100, "left_table_size": 150, "right_table_size": 80, "result_size": 44, "hash_operations": 230, "hash_collisions": 10, "collision_rate": 0.0435, "hash_efficiency": 1.0, "comparisons": 44, "memory_accesses": 274, "access_efficiency": 0.839, "build_time_ms": 0.041, "probe_time_ms": 0.042, "build_time_ratio": 0.489, "probe_time_ratio": 0.511, "actual_selectivity": 0.2031, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9031, "unique_left_keys": 128, "unique_right_keys": 70, "matching_keys": 26, "join_ratio": 0.003667, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=150, |S|=80]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=80, est_result=360]", "theoretical_memory_mb": 0.02685546875, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.0929084, "execution_time_ms": 0.23482758551836014, "setup_time_ms": 0.49914419651031494, "cleanup_time_ms": 26.07953641563654, "total_time_ms": 26.813508197665215, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 200, "left_table_size": 300, "right_table_size": 160, "result_size": 105, "hash_operations": 460, "hash_collisions": 27, "collision_rate": 0.0587, "hash_efficiency": 1.0, "comparisons": 105, "memory_accesses": 565, "access_efficiency": 0.814, "build_time_ms": 0.076, "probe_time_ms": 0.103, "build_time_ratio": 0.423, "probe_time_ratio": 0.577, "actual_selectivity": 0.2581, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9581, "unique_left_keys": 248, "unique_right_keys": 133, "matching_keys": 64, "join_ratio": 0.002188, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=160]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=160, est_result=1440]", "theoretical_memory_mb": 0.09765625, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.2645218, "execution_time_ms": 0.33488674089312553, "setup_time_ms": 0.7492327131330967, "cleanup_time_ms": 22.88769092410803, "total_time_ms": 23.97181037813425, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 300, "left_table_size": 450, "right_table_size": 240, "result_size": 127, "hash_operations": 690, "hash_collisions": 26, "collision_rate": 0.0377, "hash_efficiency": 1.0, "comparisons": 127, "memory_accesses": 817, "access_efficiency": 0.845, "build_time_ms": 0.106, "probe_time_ms": 0.136, "build_time_ratio": 0.438, "probe_time_ratio": 0.562, "actual_selectivity": 0.2208, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9208, "unique_left_keys": 385, "unique_right_keys": 214, "matching_keys": 85, "join_ratio": 0.001176, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=450, |S|=240]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=240, est_result=3240]", "theoretical_memory_mb": 0.21240234375, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.4371104, "execution_time_ms": 0.410358514636755, "setup_time_ms": 0.9246449917554855, "cleanup_time_ms": 22.797970566898584, "total_time_ms": 24.132974073290825, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 400, "left_table_size": 600, "right_table_size": 320, "result_size": 177, "hash_operations": 920, "hash_collisions": 44, "collision_rate": 0.0478, "hash_efficiency": 1.0, "comparisons": 177, "memory_accesses": 1097, "access_efficiency": 0.839, "build_time_ms": 0.131, "probe_time_ms": 0.172, "build_time_ratio": 0.432, "probe_time_ratio": 0.568, "actual_selectivity": 0.2269, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9269, "unique_left_keys": 520, "unique_right_keys": 276, "matching_keys": 118, "join_ratio": 0.000922, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=320]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=320, est_result=5760]", "theoretical_memory_mb": 0.37109375, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.601087, "execution_time_ms": 0.4960310645401478, "setup_time_ms": 1.1982829309999943, "cleanup_time_ms": 23.015513084828854, "total_time_ms": 24.709827080368996, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 500, "left_table_size": 750, "right_table_size": 400, "result_size": 229, "hash_operations": 1150, "hash_collisions": 55, "collision_rate": 0.0478, "hash_efficiency": 1.0, "comparisons": 229, "memory_accesses": 1379, "access_efficiency": 0.834, "build_time_ms": 0.156, "probe_time_ms": 0.203, "build_time_ratio": 0.434, "probe_time_ratio": 0.566, "actual_selectivity": 0.2379, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9379, "unique_left_keys": 660, "unique_right_keys": 345, "matching_keys": 157, "join_ratio": 0.000763, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=750, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=9000]", "theoretical_memory_mb": 0.57373046875, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.7693772, "execution_time_ms": 0.5967063829302788, "setup_time_ms": 1.355437096208334, "cleanup_time_ms": 23.077822290360928, "total_time_ms": 25.02996576949954, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 600, "left_table_size": 900, "right_table_size": 480, "result_size": 270, "hash_operations": 1380, "hash_collisions": 76, "collision_rate": 0.0551, "hash_efficiency": 1.0, "comparisons": 270, "memory_accesses": 1650, "access_efficiency": 0.836, "build_time_ms": 0.188, "probe_time_ms": 0.245, "build_time_ratio": 0.434, "probe_time_ratio": 0.566, "actual_selectivity": 0.2119, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9119, "unique_left_keys": 793, "unique_right_keys": 404, "matching_keys": 168, "join_ratio": 0.000625, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=480]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=480, est_result=12960]", "theoretical_memory_mb": 0.8203125, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655096.935193, "execution_time_ms": 0.6872640922665596, "setup_time_ms": 1.544065773487091, "cleanup_time_ms": 22.88439590483904, "total_time_ms": 25.11572577059269, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 700, "left_table_size": 1050, "right_table_size": 560, "result_size": 316, "hash_operations": 1610, "hash_collisions": 90, "collision_rate": 0.0559, "hash_efficiency": 1.0, "comparisons": 316, "memory_accesses": 1926, "access_efficiency": 0.836, "build_time_ms": 0.222, "probe_time_ms": 0.309, "build_time_ratio": 0.418, "probe_time_ratio": 0.582, "actual_selectivity": 0.2227, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9227, "unique_left_keys": 925, "unique_right_keys": 470, "matching_keys": 206, "join_ratio": 0.000537, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1050, |S|=560]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=560, est_result=17640]", "theoretical_memory_mb": 1.11083984375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655097.0994341, "execution_time_ms": 0.7879621349275112, "setup_time_ms": 1.8112263642251492, "cleanup_time_ms": 22.739768028259277, "total_time_ms": 25.338956527411938, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 800, "left_table_size": 1200, "right_table_size": 640, "result_size": 382, "hash_operations": 1840, "hash_collisions": 111, "collision_rate": 0.0603, "hash_efficiency": 1.0, "comparisons": 382, "memory_accesses": 2222, "access_efficiency": 0.828, "build_time_ms": 0.233, "probe_time_ms": 0.366, "build_time_ratio": 0.389, "probe_time_ratio": 0.611, "actual_selectivity": 0.2277, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9277, "unique_left_keys": 1054, "unique_right_keys": 529, "matching_keys": 240, "join_ratio": 0.000497, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1200, |S|=640]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=640, est_result=23040]", "theoretical_memory_mb": 1.4453125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655097.264579, "execution_time_ms": 0.8568405173718929, "setup_time_ms": 2.1529211662709713, "cleanup_time_ms": 22.783583030104637, "total_time_ms": 25.7933447137475, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 900, "left_table_size": 1350, "right_table_size": 720, "result_size": 423, "hash_operations": 2070, "hash_collisions": 123, "collision_rate": 0.0594, "hash_efficiency": 1.0, "comparisons": 423, "memory_accesses": 2493, "access_efficiency": 0.83, "build_time_ms": 0.272, "probe_time_ms": 0.417, "build_time_ratio": 0.395, "probe_time_ratio": 0.605, "actual_selectivity": 0.2329, "expected_selectivity": 0.3, "selectivity_accuracy": 0.9329, "unique_left_keys": 1181, "unique_right_keys": 597, "matching_keys": 275, "join_ratio": 0.000435, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1350, |S|=720]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=720, est_result=29160]", "theoretical_memory_mb": 1.82373046875, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.30-uniform", "timestamp": 1753655097.4338245, "execution_time_ms": 0.9881081990897655, "setup_time_ms": 2.3219771683216095, "cleanup_time_ms": 22.826516069471836, "total_time_ms": 26.13660143688321, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.3, "data_distribution": "uniform", "left_table_ratio": 1.5, "right_table_ratio": 0.8, "input_size": 1000, "left_table_size": 1500, "right_table_size": 800, "result_size": 480, "hash_operations": 2300, "hash_collisions": 134, "collision_rate": 0.0583, "hash_efficiency": 1.0, "comparisons": 480, "memory_accesses": 2780, "access_efficiency": 0.827, "build_time_ms": 0.29, "probe_time_ms": 0.446, "build_time_ratio": 0.394, "probe_time_ratio": 0.606, "actual_selectivity": 0.243, "expected_selectivity": 0.3, "selectivity_accuracy": 0.943, "unique_left_keys": 1313, "unique_right_keys": 666, "matching_keys": 319, "join_ratio": 0.0004, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1500, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=36000]", "theoretical_memory_mb": 2.24609375, "efficiency_ratio": 0.0}]