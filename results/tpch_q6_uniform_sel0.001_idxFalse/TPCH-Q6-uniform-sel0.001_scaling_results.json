[{"input_size": 1000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655658.5986872, "execution_time_ms": 0.5450734868645668, "setup_time_ms": 4.607696086168289, "cleanup_time_ms": 25.76479595154524, "total_time_ms": 30.917565524578094, "baseline_memory_mb": 410.10546875, "peak_memory_mb": 412.3046875, "memory_increment_mb": 2.19921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 1000, "total_rows": 1000, "rows_scanned": 1000, "rows_filtered": 15, "qualifying_rows": 15, "scan_ratio": 1.0, "filter_efficiency": 0.015, "actual_selectivity": 0.015, "expected_selectivity": 0.001, "selectivity_accuracy": 0.986, "comparisons": 2310, "arithmetic_operations": 30, "memory_accesses": 1015, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.985, "index_effectiveness": 0, "scan_time_ms": 0.59, "revenue": 32953.65, "avg_revenue_per_row": 2196.91, "price_variance": 782191991.28, "discount_variance": 0.0, "quantity_variance": 32.67, "effective_scan_size": 1000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=1000]", "theoretical_space_complexity": "O(k) [k≈1]", "theoretical_memory_mb": 6.103515625e-05, "efficiency_ratio": 2.7753108348134992e-05}, {"input_size": 2000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655658.8035085, "execution_time_ms": 0.9280959144234657, "setup_time_ms": 8.83442210033536, "cleanup_time_ms": 23.325751069933176, "total_time_ms": 33.088269084692, "baseline_memory_mb": 412.34375, "peak_memory_mb": 412.82421875, "memory_increment_mb": 0.48046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 2000, "total_rows": 2000, "rows_scanned": 2000, "rows_filtered": 17, "qualifying_rows": 17, "scan_ratio": 1.0, "filter_efficiency": 0.0085, "actual_selectivity": 0.0085, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9925, "comparisons": 4617, "arithmetic_operations": 34, "memory_accesses": 2017, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.992, "index_effectiveness": 0, "scan_time_ms": 0.882, "revenue": 38891.82, "avg_revenue_per_row": 2287.75, "price_variance": 714969634.77, "discount_variance": 0.0, "quantity_variance": 32.7, "effective_scan_size": 2000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=2000]", "theoretical_space_complexity": "O(k) [k≈2]", "theoretical_memory_mb": 0.0001220703125, "efficiency_ratio": 0.00025406504065040653}, {"input_size": 3000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655658.9822083, "execution_time_ms": 1.3391098007559776, "setup_time_ms": 13.331192079931498, "cleanup_time_ms": 23.48155714571476, "total_time_ms": 38.151859026402235, "baseline_memory_mb": 412.74609375, "peak_memory_mb": 413.515625, "memory_increment_mb": 0.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 3000, "total_rows": 3000, "rows_scanned": 3000, "rows_filtered": 22, "qualifying_rows": 22, "scan_ratio": 1.0, "filter_efficiency": 0.0073, "actual_selectivity": 0.007333, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9937, "comparisons": 6904, "arithmetic_operations": 44, "memory_accesses": 3022, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 1.301, "revenue": 49057.87, "avg_revenue_per_row": 2229.9, "price_variance": 692825231.14, "discount_variance": 0.0, "quantity_variance": 29.86, "effective_scan_size": 3000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=3000]", "theoretical_space_complexity": "O(k) [k≈3]", "theoretical_memory_mb": 0.00018310546875, "efficiency_ratio": 0.00023794416243654823}, {"input_size": 4000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655659.169461, "execution_time_ms": 1.7357371747493744, "setup_time_ms": 17.781516071408987, "cleanup_time_ms": 23.994687013328075, "total_time_ms": 43.51194025948644, "baseline_memory_mb": 413.1875, "peak_memory_mb": 414.21484375, "memory_increment_mb": 1.02734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 4000, "total_rows": 4000, "rows_scanned": 4000, "rows_filtered": 25, "qualifying_rows": 25, "scan_ratio": 1.0, "filter_efficiency": 0.0063, "actual_selectivity": 0.00625, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9948, "comparisons": 9172, "arithmetic_operations": 50, "memory_accesses": 4025, "index_lookups": 0, "comparisons_per_row": 2.29, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.994, "index_effectiveness": 0, "scan_time_ms": 1.689, "revenue": 55599.57, "avg_revenue_per_row": 2223.98, "price_variance": 657192437.97, "discount_variance": 0.0, "quantity_variance": 26.6, "effective_scan_size": 4000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=4000]", "theoretical_space_complexity": "O(k) [k≈4]", "theoretical_memory_mb": 0.000244140625, "efficiency_ratio": 0.0002376425855513308}, {"input_size": 5000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655659.3650591, "execution_time_ms": 2.1516935899853706, "setup_time_ms": 22.103692404925823, "cleanup_time_ms": 23.840351961553097, "total_time_ms": 48.09573795646429, "baseline_memory_mb": 413.609375, "peak_memory_mb": 414.89453125, "memory_increment_mb": 1.28515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 5000, "total_rows": 5000, "rows_scanned": 5000, "rows_filtered": 32, "qualifying_rows": 32, "scan_ratio": 1.0, "filter_efficiency": 0.0064, "actual_selectivity": 0.0064, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9946, "comparisons": 11470, "arithmetic_operations": 64, "memory_accesses": 5032, "index_lookups": 0, "comparisons_per_row": 2.29, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.994, "index_effectiveness": 0, "scan_time_ms": 2.107, "revenue": 72809.46, "avg_revenue_per_row": 2275.3, "price_variance": 711262708.58, "discount_variance": 0.0, "quantity_variance": 31.62, "effective_scan_size": 5000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=5000]", "theoretical_space_complexity": "O(k) [k≈5]", "theoretical_memory_mb": 0.00030517578125, "efficiency_ratio": 0.00023746200607902736}, {"input_size": 6000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655659.568285, "execution_time_ms": 2.574795391410589, "setup_time_ms": 25.923200882971287, "cleanup_time_ms": 30.594380106776953, "total_time_ms": 59.09237638115883, "baseline_memory_mb": 414.046875, "peak_memory_mb": 415.58984375, "memory_increment_mb": 1.54296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 6000, "total_rows": 6000, "rows_scanned": 6000, "rows_filtered": 40, "qualifying_rows": 40, "scan_ratio": 1.0, "filter_efficiency": 0.0067, "actual_selectivity": 0.006667, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9943, "comparisons": 13774, "arithmetic_operations": 80, "memory_accesses": 6040, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 2.531, "revenue": 95681.8, "avg_revenue_per_row": 2392.04, "price_variance": 724690259.84, "discount_variance": 0.0, "quantity_variance": 32.67, "effective_scan_size": 6000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=6000]", "theoretical_space_complexity": "O(k) [k≈6]", "theoretical_memory_mb": 0.0003662109375, "efficiency_ratio": 0.00023734177215189873}, {"input_size": 7000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655659.785922, "execution_time_ms": 3.0561295337975025, "setup_time_ms": 30.759500339627266, "cleanup_time_ms": 24.06379533931613, "total_time_ms": 57.8794252127409, "baseline_memory_mb": 414.46484375, "peak_memory_mb": 416.26171875, "memory_increment_mb": 1.796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 7000, "total_rows": 7000, "rows_scanned": 7000, "rows_filtered": 46, "qualifying_rows": 46, "scan_ratio": 1.0, "filter_efficiency": 0.0066, "actual_selectivity": 0.006571, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9944, "comparisons": 16092, "arithmetic_operations": 92, "memory_accesses": 7046, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 3.007, "revenue": 108658.62, "avg_revenue_per_row": 2362.14, "price_variance": 752119125.48, "discount_variance": 0.0, "quantity_variance": 32.09, "effective_scan_size": 7000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=7000]", "theoretical_space_complexity": "O(k) [k≈7]", "theoretical_memory_mb": 0.00042724609375, "efficiency_ratio": 0.0002377717391304348}, {"input_size": 8000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655660.0028863, "execution_time_ms": 3.9159676991403103, "setup_time_ms": 35.12100409716368, "cleanup_time_ms": 40.33977445214987, "total_time_ms": 79.37674624845386, "baseline_memory_mb": 414.890625, "peak_memory_mb": 416.94921875, "memory_increment_mb": 2.05859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 8000, "total_rows": 8000, "rows_scanned": 8000, "rows_filtered": 54, "qualifying_rows": 54, "scan_ratio": 1.0, "filter_efficiency": 0.0067, "actual_selectivity": 0.00675, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9942, "comparisons": 18414, "arithmetic_operations": 108, "memory_accesses": 8054, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 3.877, "revenue": 133186.14, "avg_revenue_per_row": 2466.41, "price_variance": 777741646.86, "discount_variance": 0.0, "quantity_variance": 34.57, "effective_scan_size": 8000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=8000]", "theoretical_space_complexity": "O(k) [k≈8]", "theoretical_memory_mb": 0.00048828125, "efficiency_ratio": 0.00023719165085388995}, {"input_size": 9000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655660.3408165, "execution_time_ms": 4.13349000737071, "setup_time_ms": 42.24998410791159, "cleanup_time_ms": 36.131241358816624, "total_time_ms": 82.51471547409892, "baseline_memory_mb": 415.0625, "peak_memory_mb": 417.63671875, "memory_increment_mb": 2.57421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 9000, "total_rows": 9000, "rows_scanned": 9000, "rows_filtered": 61, "qualifying_rows": 61, "scan_ratio": 1.0, "filter_efficiency": 0.0068, "actual_selectivity": 0.006778, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9942, "comparisons": 20732, "arithmetic_operations": 122, "memory_accesses": 9061, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 4.013, "revenue": 152140.76, "avg_revenue_per_row": 2494.11, "price_variance": 799576038.73, "discount_variance": 0.0, "quantity_variance": 33.24, "effective_scan_size": 9000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=9000]", "theoretical_space_complexity": "O(k) [k≈9]", "theoretical_memory_mb": 0.00054931640625, "efficiency_ratio": 0.00021339150227617603}, {"input_size": 10000, "algorithm_name": "TPCH-Q6-uniform-sel0.001", "timestamp": 1753655660.6726706, "execution_time_ms": 4.5106531120836735, "setup_time_ms": 44.53773610293865, "cleanup_time_ms": 36.06172185391188, "total_time_ms": 85.1101110689342, "baseline_memory_mb": 414.9921875, "peak_memory_mb": 418.41796875, "memory_increment_mb": 3.42578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.001, "data_distribution": "uniform", "use_indexes": false, "input_size": 10000, "total_rows": 10000, "rows_scanned": 10000, "rows_filtered": 68, "qualifying_rows": 68, "scan_ratio": 1.0, "filter_efficiency": 0.0068, "actual_selectivity": 0.0068, "expected_selectivity": 0.001, "selectivity_accuracy": 0.9942, "comparisons": 22988, "arithmetic_operations": 136, "memory_accesses": 10068, "index_lookups": 0, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0, "scan_time_ms": 4.406, "revenue": 174029.7, "avg_revenue_per_row": 2559.26, "price_variance": 775809962.13, "discount_variance": 0.0, "quantity_variance": 33.53, "effective_scan_size": 10000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=10000]", "theoretical_space_complexity": "O(k) [k≈10]", "theoretical_memory_mb": 0.0006103515625, "efficiency_ratio": 0.0001781641961231471}]