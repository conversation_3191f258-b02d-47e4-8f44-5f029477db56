[{"input_size": 1000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655664.0174246, "execution_time_ms": 0.5995487794280052, "setup_time_ms": 4.601235035806894, "cleanup_time_ms": 28.48857780918479, "total_time_ms": 33.68936162441969, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.24609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 1000, "total_rows": 1000, "rows_scanned": 1000, "rows_filtered": 27, "qualifying_rows": 27, "scan_ratio": 1.0, "filter_efficiency": 0.027, "actual_selectivity": 0.027, "expected_selectivity": 0.05, "selectivity_accuracy": 0.977, "comparisons": 2400, "arithmetic_operations": 54, "memory_accesses": 1027, "index_lookups": 0, "comparisons_per_row": 2.4, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.974, "index_effectiveness": 0, "scan_time_ms": 0.551, "revenue": 67498.22, "avg_revenue_per_row": 2499.93, "price_variance": 749037695.34, "discount_variance": 0.0, "quantity_variance": 35.14, "effective_scan_size": 1000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=1000]", "theoretical_space_complexity": "O(k) [k≈50]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655664.2365434, "execution_time_ms": 1.1464666575193405, "setup_time_ms": 8.942913729697466, "cleanup_time_ms": 28.751404024660587, "total_time_ms": 38.840784411877394, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.24609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 2000, "total_rows": 2000, "rows_scanned": 2000, "rows_filtered": 40, "qualifying_rows": 40, "scan_ratio": 1.0, "filter_efficiency": 0.02, "actual_selectivity": 0.02, "expected_selectivity": 0.05, "selectivity_accuracy": 0.97, "comparisons": 4779, "arithmetic_operations": 80, "memory_accesses": 2040, "index_lookups": 0, "comparisons_per_row": 2.39, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 1.036, "revenue": 107759.74, "avg_revenue_per_row": 2693.99, "price_variance": 782073410.04, "discount_variance": 0.0, "quantity_variance": 42.42, "effective_scan_size": 2000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=2000]", "theoretical_space_complexity": "O(k) [k≈100]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 3000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655664.4563005, "execution_time_ms": 1.5732972882688046, "setup_time_ms": 13.590312097221613, "cleanup_time_ms": 25.014547165483236, "total_time_ms": 40.178156550973654, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.24609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 3000, "total_rows": 3000, "rows_scanned": 3000, "rows_filtered": 57, "qualifying_rows": 57, "scan_ratio": 1.0, "filter_efficiency": 0.019, "actual_selectivity": 0.019, "expected_selectivity": 0.05, "selectivity_accuracy": 0.969, "comparisons": 7167, "arithmetic_operations": 114, "memory_accesses": 3057, "index_lookups": 0, "comparisons_per_row": 2.39, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.981, "index_effectiveness": 0, "scan_time_ms": 1.486, "revenue": 135931.88, "avg_revenue_per_row": 2384.77, "price_variance": 762213562.21, "discount_variance": 0.0, "quantity_variance": 39.82, "effective_scan_size": 3000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=3000]", "theoretical_space_complexity": "O(k) [k≈150]", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 4000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655664.6892464, "execution_time_ms": 2.0284696482121944, "setup_time_ms": 17.550788819789886, "cleanup_time_ms": 26.886523235589266, "total_time_ms": 46.46578170359135, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.24609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 4000, "total_rows": 4000, "rows_scanned": 4000, "rows_filtered": 70, "qualifying_rows": 70, "scan_ratio": 1.0, "filter_efficiency": 0.0175, "actual_selectivity": 0.0175, "expected_selectivity": 0.05, "selectivity_accuracy": 0.9675, "comparisons": 9496, "arithmetic_operations": 140, "memory_accesses": 4070, "index_lookups": 0, "comparisons_per_row": 2.37, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.983, "index_effectiveness": 0, "scan_time_ms": 2.069, "revenue": 162378.8, "avg_revenue_per_row": 2319.7, "price_variance": 735324703.09, "discount_variance": 0.0, "quantity_variance": 42.98, "effective_scan_size": 4000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=4000]", "theoretical_space_complexity": "O(k) [k≈200]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.0}, {"input_size": 5000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655664.9050624, "execution_time_ms": 2.5342862121760845, "setup_time_ms": 24.376221001148224, "cleanup_time_ms": 26.41541976481676, "total_time_ms": 53.32592697814107, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.24609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 5000, "total_rows": 5000, "rows_scanned": 5000, "rows_filtered": 90, "qualifying_rows": 90, "scan_ratio": 1.0, "filter_efficiency": 0.018, "actual_selectivity": 0.018, "expected_selectivity": 0.05, "selectivity_accuracy": 0.968, "comparisons": 11892, "arithmetic_operations": 180, "memory_accesses": 5090, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 2.379, "revenue": 220604.65, "avg_revenue_per_row": 2451.16, "price_variance": 749677745.47, "discount_variance": 0.0, "quantity_variance": 42.98, "effective_scan_size": 5000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=5000]", "theoretical_space_complexity": "O(k) [k≈250]", "theoretical_memory_mb": 0.0152587890625, "efficiency_ratio": 0.0}, {"input_size": 6000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655665.1358974, "execution_time_ms": 3.0021144077181816, "setup_time_ms": 26.403600815683603, "cleanup_time_ms": 25.393981020897627, "total_time_ms": 54.79969624429941, "baseline_memory_mb": 416.24609375, "peak_memory_mb": 416.25390625, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 6000, "total_rows": 6000, "rows_scanned": 6000, "rows_filtered": 107, "qualifying_rows": 107, "scan_ratio": 1.0, "filter_efficiency": 0.0178, "actual_selectivity": 0.017833, "expected_selectivity": 0.05, "selectivity_accuracy": 0.9678, "comparisons": 14272, "arithmetic_operations": 214, "memory_accesses": 6107, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 2.858, "revenue": 265558.44, "avg_revenue_per_row": 2481.85, "price_variance": 773092827.57, "discount_variance": 0.0, "quantity_variance": 42.94, "effective_scan_size": 6000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=6000]", "theoretical_space_complexity": "O(k) [k≈300]", "theoretical_memory_mb": 0.018310546875, "efficiency_ratio": 2.34375}, {"input_size": 7000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655665.3605316, "execution_time_ms": 3.4295501187443733, "setup_time_ms": 31.0739460401237, "cleanup_time_ms": 25.493691209703684, "total_time_ms": 59.99718736857176, "baseline_memory_mb": 416.25390625, "peak_memory_mb": 416.5546875, "memory_increment_mb": 0.30078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 7000, "total_rows": 7000, "rows_scanned": 7000, "rows_filtered": 126, "qualifying_rows": 126, "scan_ratio": 1.0, "filter_efficiency": 0.018, "actual_selectivity": 0.018, "expected_selectivity": 0.05, "selectivity_accuracy": 0.968, "comparisons": 16676, "arithmetic_operations": 252, "memory_accesses": 7126, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 3.35, "revenue": 315797.47, "avg_revenue_per_row": 2506.33, "price_variance": 844219597.27, "discount_variance": 0.0, "quantity_variance": 42.21, "effective_scan_size": 7000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=7000]", "theoretical_space_complexity": "O(k) [k≈350]", "theoretical_memory_mb": 0.0213623046875, "efficiency_ratio": 0.07102272727272728}, {"input_size": 8000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655665.5938141, "execution_time_ms": 3.95953431725502, "setup_time_ms": 35.43809102848172, "cleanup_time_ms": 25.674098636955023, "total_time_ms": 65.07172398269176, "baseline_memory_mb": 416.5546875, "peak_memory_mb": 417.32421875, "memory_increment_mb": 0.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 8000, "total_rows": 8000, "rows_scanned": 8000, "rows_filtered": 148, "qualifying_rows": 148, "scan_ratio": 1.0, "filter_efficiency": 0.0185, "actual_selectivity": 0.0185, "expected_selectivity": 0.05, "selectivity_accuracy": 0.9685, "comparisons": 19049, "arithmetic_operations": 296, "memory_accesses": 8148, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 3.785, "revenue": 374043.81, "avg_revenue_per_row": 2527.32, "price_variance": 886910086.58, "discount_variance": 0.0, "quantity_variance": 42.5, "effective_scan_size": 8000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=8000]", "theoretical_space_complexity": "O(k) [k≈400]", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.031725888324873094}, {"input_size": 9000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655665.8381495, "execution_time_ms": 4.378082137554884, "setup_time_ms": 39.70031812787056, "cleanup_time_ms": 25.87483637034893, "total_time_ms": 69.95323663577437, "baseline_memory_mb": 417.32421875, "peak_memory_mb": 417.83984375, "memory_increment_mb": 0.515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 9000, "total_rows": 9000, "rows_scanned": 9000, "rows_filtered": 167, "qualifying_rows": 167, "scan_ratio": 1.0, "filter_efficiency": 0.0186, "actual_selectivity": 0.018556, "expected_selectivity": 0.05, "selectivity_accuracy": 0.9686, "comparisons": 21433, "arithmetic_operations": 334, "memory_accesses": 9167, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 4.356, "revenue": 426298.02, "avg_revenue_per_row": 2552.68, "price_variance": 875199705.43, "discount_variance": 0.0, "quantity_variance": 43.26, "effective_scan_size": 9000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=9000]", "theoretical_space_complexity": "O(k) [k≈450]", "theoretical_memory_mb": 0.0274658203125, "efficiency_ratio": 0.053267045454545456}, {"input_size": 10000, "algorithm_name": "TPCH-Q6-uniform-sel0.050", "timestamp": 1753655666.088597, "execution_time_ms": 4.856197722256184, "setup_time_ms": 44.9409862048924, "cleanup_time_ms": 25.988860987126827, "total_time_ms": 75.78604491427541, "baseline_memory_mb": 417.76171875, "peak_memory_mb": 418.52734375, "memory_increment_mb": 0.765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.05, "data_distribution": "uniform", "use_indexes": false, "input_size": 10000, "total_rows": 10000, "rows_scanned": 10000, "rows_filtered": 190, "qualifying_rows": 190, "scan_ratio": 1.0, "filter_efficiency": 0.019, "actual_selectivity": 0.019, "expected_selectivity": 0.05, "selectivity_accuracy": 0.969, "comparisons": 23833, "arithmetic_operations": 380, "memory_accesses": 10190, "index_lookups": 0, "comparisons_per_row": 2.38, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.981, "index_effectiveness": 0, "scan_time_ms": 4.782, "revenue": 495102.36, "avg_revenue_per_row": 2605.8, "price_variance": 885422023.64, "discount_variance": 0.0, "quantity_variance": 43.44, "effective_scan_size": 10000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=10000]", "theoretical_space_complexity": "O(k) [k≈500]", "theoretical_memory_mb": 0.030517578125, "efficiency_ratio": 0.03985969387755102}]