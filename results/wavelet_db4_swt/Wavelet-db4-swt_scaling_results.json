[{"input_size": 100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657409.7111328, "execution_time_ms": 0.16834773123264313, "setup_time_ms": 0.040984246879816055, "cleanup_time_ms": 99.99068407341838, "total_time_ms": 100.20001605153084, "baseline_memory_mb": 649.56640625, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.21484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 400, "compression_ratio": 0.25, "theoretical_operations": 200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.007102272727272727}, {"input_size": 600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657410.4147215, "execution_time_ms": 0.19677644595503807, "setup_time_ms": 0.055998098105192184, "cleanup_time_ms": 100.74279084801674, "total_time_ms": 100.99556539207697, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 3600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 1200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657411.1169274, "execution_time_ms": 0.19847191870212555, "setup_time_ms": 0.06475113332271576, "cleanup_time_ms": 98.28986180946231, "total_time_ms": 98.55308486148715, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 4400, "compression_ratio": 0.25, "theoretical_operations": 2200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657411.8104875, "execution_time_ms": 0.23046480491757393, "setup_time_ms": 0.0795968808233738, "cleanup_time_ms": 98.31742383539677, "total_time_ms": 98.62748552113771, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 9600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 3200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 2100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657412.497782, "execution_time_ms": 0.21875565871596336, "setup_time_ms": 0.08430378511548042, "cleanup_time_ms": 98.07151416316628, "total_time_ms": 98.37457360699773, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 8400, "compression_ratio": 0.25, "theoretical_operations": 4200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.03204345703125, "efficiency_ratio": 0.0}, {"input_size": 2600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657413.1908255, "execution_time_ms": 0.26392852887511253, "setup_time_ms": 0.0955057330429554, "cleanup_time_ms": 110.99369125440717, "total_time_ms": 111.35312551632524, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 15600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 5200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0396728515625, "efficiency_ratio": 0.0}, {"input_size": 3100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657413.899471, "execution_time_ms": 0.253966823220253, "setup_time_ms": 0.1335800625383854, "cleanup_time_ms": 111.27008683979511, "total_time_ms": 111.65763372555375, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 12400, "compression_ratio": 0.25, "theoretical_operations": 6200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.04730224609375, "efficiency_ratio": 0.0}, {"input_size": 3600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657414.6338542, "execution_time_ms": 0.3082470968365669, "setup_time_ms": 0.11808378621935844, "cleanup_time_ms": 102.32915123924613, "total_time_ms": 102.75548212230206, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 21600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 7200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 4100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657415.3329632, "execution_time_ms": 0.26271920651197433, "setup_time_ms": 0.13130437582731247, "cleanup_time_ms": 97.73740684613585, "total_time_ms": 98.13143042847514, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 16400, "compression_ratio": 0.25, "theoretical_operations": 8200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.06256103515625, "efficiency_ratio": 0.0}, {"input_size": 4600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657416.0244231, "execution_time_ms": 0.3461201675236225, "setup_time_ms": 0.1398869790136814, "cleanup_time_ms": 100.10248376056552, "total_time_ms": 100.58849090710282, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.78125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 27600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 9200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0701904296875, "efficiency_ratio": 0.0}, {"input_size": 5100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657416.717793, "execution_time_ms": 0.2902042120695114, "setup_time_ms": 0.14974502846598625, "cleanup_time_ms": 99.35948299244046, "total_time_ms": 99.79943223297596, "baseline_memory_mb": 649.78125, "peak_memory_mb": 649.828125, "memory_increment_mb": 0.046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 20400, "compression_ratio": 0.25, "theoretical_operations": 10200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.07781982421875, "efficiency_ratio": 1.66015625}, {"input_size": 5600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657417.4135242, "execution_time_ms": 0.37459395825862885, "setup_time_ms": 0.16081379726529121, "cleanup_time_ms": 98.00449013710022, "total_time_ms": 98.53989789262414, "baseline_memory_mb": 649.828125, "peak_memory_mb": 649.828125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 5600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 33600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 11200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.08544921875, "efficiency_ratio": 0.0}, {"input_size": 6100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657418.1061187, "execution_time_ms": 0.31702183187007904, "setup_time_ms": 0.1721302978694439, "cleanup_time_ms": 102.21912013366818, "total_time_ms": 102.70827226340771, "baseline_memory_mb": 649.828125, "peak_memory_mb": 649.828125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 24400, "compression_ratio": 0.25, "theoretical_operations": 12200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.09307861328125, "efficiency_ratio": 0.0}, {"input_size": 6600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657418.8134854, "execution_time_ms": 0.42137932032346725, "setup_time_ms": 0.18355203792452812, "cleanup_time_ms": 98.97891897708178, "total_time_ms": 99.58385033532977, "baseline_memory_mb": 649.828125, "peak_memory_mb": 649.828125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 39600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 13200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1007080078125, "efficiency_ratio": 0.0}, {"input_size": 7100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657419.508144, "execution_time_ms": 0.33724345266819, "setup_time_ms": 0.1929616555571556, "cleanup_time_ms": 99.03421299532056, "total_time_ms": 99.5644181035459, "baseline_memory_mb": 649.828125, "peak_memory_mb": 649.828125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 28400, "compression_ratio": 0.25, "theoretical_operations": 14200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.10833740234375, "efficiency_ratio": 0.0}, {"input_size": 7600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657420.205864, "execution_time_ms": 0.45392634347081184, "setup_time_ms": 0.203070230782032, "cleanup_time_ms": 97.75422792881727, "total_time_ms": 98.41122450307012, "baseline_memory_mb": 649.828125, "peak_memory_mb": 649.9609375, "memory_increment_mb": 0.1328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 7600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 45600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 15200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.115966796875, "efficiency_ratio": 0.8731617647058824}, {"input_size": 8100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657420.9038146, "execution_time_ms": 0.36619389429688454, "setup_time_ms": 0.21426938474178314, "cleanup_time_ms": 98.2634830288589, "total_time_ms": 98.84394630789757, "baseline_memory_mb": 649.9609375, "peak_memory_mb": 649.9609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 32400, "compression_ratio": 0.25, "theoretical_operations": 16200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.12359619140625, "efficiency_ratio": 0.0}, {"input_size": 8600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657421.5945106, "execution_time_ms": 0.4759090952575207, "setup_time_ms": 0.22522779181599617, "cleanup_time_ms": 97.24941430613399, "total_time_ms": 97.9505511932075, "baseline_memory_mb": 649.9609375, "peak_memory_mb": 649.9609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 51600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 17200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.1312255859375, "efficiency_ratio": 0.0}, {"input_size": 9100, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657422.2857053, "execution_time_ms": 0.38076192140579224, "setup_time_ms": 0.23491913452744484, "cleanup_time_ms": 98.11710426583886, "total_time_ms": 98.7327853217721, "baseline_memory_mb": 649.9609375, "peak_memory_mb": 649.9609375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9100, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 36400, "compression_ratio": 0.25, "theoretical_operations": 18200, "operations_per_sample": 2.0, "coefficient_efficiency": 4.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.13885498046875, "efficiency_ratio": 0.0}, {"input_size": 9600, "algorithm_name": "Wavelet-db4-swt", "timestamp": 1753657422.9752614, "execution_time_ms": 0.5453865975141525, "setup_time_ms": 0.2452358603477478, "cleanup_time_ms": 97.73193392902613, "total_time_ms": 98.52255638688803, "baseline_memory_mb": 649.9609375, "peak_memory_mb": 650.21875, "memory_increment_mb": 0.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 9600, "wavelet_type": "db4", "transform_type": "swt", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 57600, "compression_ratio": 0.16666666666666666, "theoretical_operations": 19200, "operations_per_sample": 2.0, "coefficient_efficiency": 6.0}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.146484375, "efficiency_ratio": 0.5681818181818182}]