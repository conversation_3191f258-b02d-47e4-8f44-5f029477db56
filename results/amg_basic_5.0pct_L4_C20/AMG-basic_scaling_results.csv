input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_solution_error,custom_relative_solution_error,custom_final_residual_norm,custom_computed_residual_norm,custom_relative_residual,custom_converged,custom_iterations_performed,custom_max_iterations,custom_iteration_efficiency,custom_convergence_rate,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_b_norm,custom_solution_norm,custom_true_solution_norm,custom_theoretical_flops,custom_storage_format,custom_tolerance,custom_residual_history_length,custom_initial_residual,custom_final_residual,custom_num_levels,custom_level_sizes,custom_avg_coarsening_ratio,custom_coarsening_ratios,custom_setup_work,custom_solve_work,custom_total_nnz_all_levels,custom_hierarchy_efficiency,custom_max_levels,custom_coarsening_factor
50,AMG-basic,1753656825.6792555,4.5195,0.8745,26.5754,31.9695,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=50, nnz≈125, k≤20]","O(nnz) [N=50, memory≈437 elements]",0.00,0.0000,True,3.9885061540178885e-16,5.580080655520642e-17,2.1990264548214973e-14,2.1990264548214973e-14,5.87157246640397e-17,True,3,20,0.15,18.684643471343477,972,algebraic_multigrid,50×50,basic_amg,292,0.1168,0.05,374.52087450234364,7.147757174570349,7.147757174570349,972,CSR,1e-06,3,374.52087450234364,2.1990264548214973e-14,2,"[50, 10]",0.2,[0.2],324,972,324,1.1095890410958904,4,0.2
100,AMG-basic,1753656825.8978708,9.7216,1.0424,26.2134,36.9774,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=100, nnz≈500, k≤20]","O(nnz) [N=100, memory≈1,250 elements]",0.01,0.0000,True,1.300802036419135e-15,1.28361356685645e-16,1.0652773606933016e-13,1.0652773606933016e-13,9.968861856939258e-17,True,3,20,0.15,18.421770938940103,3468,algebraic_multigrid,100×100,basic_amg,1050,0.105,0.05,1068.6047975996069,10.133906886048107,10.133906886048107,3468,CSR,1e-06,3,1068.6047975996069,1.0652773606933016e-13,3,"[100, 20, 4]",0.2,"[0.2, 0.2]",1156,3468,1156,1.100952380952381,4,0.2
150,AMG-basic,1753656826.1581783,13.3572,1.2997,25.2123,39.8692,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=150, nnz≈1,125, k≤20]","O(nnz) [N=150, memory≈2,437 elements]",0.03,0.0000,True,1.941893345745703e-15,1.6836109806453885e-16,3.0174554675158965e-13,3.0174554675158965e-13,1.659844469598732e-16,True,3,20,0.15,18.167153116392242,7488,algebraic_multigrid,150×150,basic_amg,2322,0.1032,0.05,1817.9145834340536,11.53409765123595,11.53409765123595,7488,CSR,1e-06,3,1817.9145834340536,3.0174554675158965e-13,3,"[150, 30, 6]",0.2,"[0.2, 0.2]",2496,7488,2496,1.074935400516796,4,0.2
200,AMG-basic,1753656826.4454198,17.1662,1.5851,25.8014,44.5527,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=200, nnz≈2,000, k≤20]","O(nnz) [N=200, memory≈4,000 elements]",0.04,0.0000,True,2.2316683627814365e-15,1.742164700542537e-16,3.944837227080367e-13,3.944837227080367e-13,1.4680949242017002e-16,True,3,20,0.15,18.228571216890433,13044,algebraic_multigrid,200×200,basic_amg,4066,0.10165,0.05,2687.0450691227848,12.809743889808239,12.809743889808239,13044,CSR,1e-06,3,2687.0450691227848,3.944837227080367e-13,3,"[200, 40, 8]",0.2,"[0.2, 0.2]",4348,13044,4348,1.0693556320708313,4,0.2
250,AMG-basic,1753656826.7679443,21.7593,1.8058,27.1173,50.6825,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=250, nnz≈3,125, k≤20]","O(nnz) [N=250, memory≈5,937 elements]",0.06,0.0000,True,2.8355529081231547e-15,1.8075784175965073e-16,7.530176526852335e-13,7.530176526852335e-13,1.8256082720527188e-16,True,3,20,0.15,18.119657733226394,20328,algebraic_multigrid,250×250,basic_amg,6318,0.101088,0.05,4124.749346356425,15.687025694262934,15.687025694262934,20328,CSR,1e-06,3,4124.749346356425,7.530176526852335e-13,3,"[250, 50, 10]",0.2,"[0.2, 0.2]",6776,20328,6776,1.072491294713517,4,0.2
300,AMG-basic,1753656827.1438715,26.3055,2.3801,25.7216,54.4072,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=300, nnz≈4,500, k≤20]","O(nnz) [N=300, memory≈8,250 elements]",0.09,0.0000,True,3.9722989666806694e-15,2.2111693080652095e-16,1.2485487577855488e-12,1.2485487577855488e-12,2.2021411552915168e-16,True,3,20,0.15,18.02592562932905,28944,algebraic_multigrid,300×300,basic_amg,9068,0.10075555555555556,0.05,5669.703573657917,17.964698371091547,17.964698371091547,28944,CSR,1e-06,3,5669.703573657917,1.2485487577855488e-12,4,"[300, 60, 12, 2]",0.18888888888888888,"[0.2, 0.2, 0.16666666666666666]",9648,28944,9648,1.0639611821790913,4,0.2
350,AMG-basic,1753656827.5377815,30.8343,3.4704,25.9041,60.2089,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=350, nnz≈6,125, k≤20]","O(nnz) [N=350, memory≈10,937 elements]",0.12,0.0000,True,3.599791244397213e-15,1.8341706189677884e-16,1.2781609585541175e-12,1.2781609585541175e-12,1.7713086186125052e-16,True,3,20,0.15,18.13478232379697,39186,algebraic_multigrid,350×350,basic_amg,12274,0.10019591836734694,0.05,7215.913393767156,19.626261631118368,19.626261631118368,39186,CSR,1e-06,3,7215.913393767156,1.2781609585541175e-12,4,"[350, 70, 14, 2]",0.18095238095238098,"[0.2, 0.2, 0.14285714285714285]",13062,39186,13062,1.0642007495518984,4,0.2
400,AMG-basic,1753656827.9716315,35.5797,4.0231,25.4938,65.0966,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=400, nnz≈8,000, k≤20]","O(nnz) [N=400, memory≈14,000 elements]",0.15,0.0000,True,3.6227281609390284e-15,1.8507560101943991e-16,1.4807695057324294e-12,1.4807695057324294e-12,1.8039807124488075e-16,True,3,20,0.15,18.12564911384695,50979,algebraic_multigrid,400×400,basic_amg,15970,0.0998125,0.05,8208.344443563168,19.574315258111767,19.574315258111767,50979,CSR,1e-06,3,8208.344443563168,1.4807695057324294e-12,4,"[400, 80, 16, 3]",0.19583333333333333,"[0.2, 0.2, 0.1875]",16993,50979,16993,1.0640576080150281,4,0.2
450,AMG-basic,1753656828.4562683,40.3498,5.5304,29.3928,75.2731,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=450, nnz≈10,125, k≤20]","O(nnz) [N=450, memory≈17,437 elements]",0.19,0.0000,True,4.5362437780085e-15,2.1532388131625508e-16,1.9198317207507717e-12,1.9198317207507717e-12,1.930830479126378e-16,True,3,20,0.15,18.09167959546646,64569,algebraic_multigrid,450×450,basic_amg,20206,0.09978271604938271,0.05,9943.036126192794,21.067072311156842,21.067072311156846,64569,CSR,1e-06,3,9943.036126192794,1.9198317207507717e-12,4,"[450, 90, 18, 3]",0.18888888888888888,"[0.2, 0.2, 0.16666666666666666]",21523,64569,21523,1.0651786598040187,4,0.2
500,AMG-basic,1753656828.9808354,43.1267,6.3688,27.9012,77.3968,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=500, nnz≈12,500, k≤20]","O(nnz) [N=500, memory≈21,250 elements]",0.24,0.0000,True,5.2026263672580226e-15,2.243467389946597e-16,2.75999594139361e-12,2.75999594139361e-12,2.266810041237201e-16,True,3,20,0.15,18.01147584081161,79038,algebraic_multigrid,500×500,basic_amg,24838,0.099352,0.05,12175.682528242347,23.190113618642187,23.190113618642187,79038,CSR,1e-06,3,12175.682528242347,2.75999594139361e-12,4,"[500, 100, 20, 4]",0.20000000000000004,"[0.2, 0.2, 0.2]",26346,79038,26346,1.0607134229809163,4,0.2
550,AMG-basic,1753656829.5263495,47.6394,7.9408,32.4453,88.0255,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=550, nnz≈15,125, k≤20]","O(nnz) [N=550, memory≈25,437 elements]",0.28,0.0000,True,5.86902433451205e-15,2.6110582948534076e-16,3.4080580690970635e-12,3.4080580690970635e-12,2.622636539520154e-16,True,3,20,0.15,17.938576010769253,95436,algebraic_multigrid,550×550,basic_amg,29974,0.09908760330578513,0.05,12994.778413788945,22.477569137695387,22.477569137695387,95436,CSR,1e-06,3,12994.778413788945,3.4080580690970635e-12,4,"[550, 110, 22, 4]",0.19393939393939394,"[0.2, 0.2, 0.18181818181818182]",31812,95436,31812,1.0613198105024355,4,0.2
600,AMG-basic,1753656830.1085165,51.1298,9.2660,26.7379,87.1337,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=600, nnz≈18,000, k≤20]","O(nnz) [N=600, memory≈30,000 elements]",0.34,0.0000,True,6.363998130653395e-15,2.688097541070778e-16,3.9335261601815354e-12,3.9335261601815354e-12,2.6365771940010747e-16,True,3,20,0.15,17.935927253608433,113040,algebraic_multigrid,600×600,basic_amg,35626,0.09896111111111111,0.05,14919.063129012002,23.6747291845606,23.674729184560604,113040,CSR,1e-06,3,14919.063129012002,3.9335261601815354e-12,4,"[600, 120, 24, 4]",0.18888888888888888,"[0.2, 0.2, 0.16666666666666666]",37680,113040,37680,1.0576545219783304,4,0.2
650,AMG-basic,1753656830.712477,55.8641,10.5398,28.6825,95.0864,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=650, nnz≈21,125, k≤20]","O(nnz) [N=650, memory≈34,937 elements]",0.39,0.0000,True,6.420865475431083e-15,2.6079780950663236e-16,4.329275633653419e-12,4.329275633653419e-12,2.575764552070357e-16,True,3,20,0.15,17.947595993074053,132555,algebraic_multigrid,650×650,basic_amg,41810,0.09895857988165681,0.05,16807.730466566205,24.620089745300536,24.62008974530054,132555,CSR,1e-06,3,16807.730466566205,4.329275633653419e-12,4,"[650, 130, 26, 5]",0.19743589743589743,"[0.2, 0.2, 0.19230769230769232]",44185,132555,44185,1.0568045922028222,4,0.2
700,AMG-basic,1753656831.3647258,59.7155,11.9973,26.5935,98.3063,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=700, nnz≈24,500, k≤20]","O(nnz) [N=700, memory≈40,250 elements]",0.45,0.0000,True,7.433800234028656e-15,2.8171730854158745e-16,5.510192941128242e-12,5.510192941128242e-12,2.8375009540093487e-16,True,3,20,0.15,17.899209810431177,153435,algebraic_multigrid,700×700,basic_amg,48442,0.09886122448979592,0.05,19419.17564236381,26.38744588506982,26.38744588506982,153435,CSR,1e-06,3,19419.17564236381,5.510192941128242e-12,4,"[700, 140, 28, 5]",0.19285714285714287,"[0.2, 0.2, 0.17857142857142858]",51145,153435,51145,1.0557986870897156,4,0.2
750,AMG-basic,1753656832.0459433,65.0147,14.3676,29.1682,108.5505,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=750, nnz≈28,125, k≤20]","O(nnz) [N=750, memory≈45,937 elements]",0.52,0.0000,True,7.807345803338442e-15,2.794945515735966e-16,5.991067798031164e-12,5.991067798031164e-12,2.7236724587843515e-16,True,3,20,0.15,17.9196818291041,176124,algebraic_multigrid,750×750,basic_amg,55624,0.0988871111111111,0.05,21996.28585555085,27.93380321506056,27.93380321506056,176124,CSR,1e-06,3,21996.28585555085,5.991067798031164e-12,4,"[750, 150, 30, 6]",0.20000000000000004,"[0.2, 0.2, 0.2]",58708,176124,58708,1.0554436933697684,4,0.2
800,AMG-basic,1753656832.7716663,68.3958,16.0733,28.9769,113.4460,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=800, nnz≈32,000, k≤20]","O(nnz) [N=800, memory≈52,000 elements]",0.58,0.0000,True,8.479824819515684e-15,3.0077538697997773e-16,7.0556015602429275e-12,7.0556015602429275e-12,2.9803407432473407e-16,True,3,20,0.15,17.87465483875852,199806,algebraic_multigrid,800×800,basic_amg,63134,0.098646875,0.05,23673.808359762366,28.193213895125588,28.193213895125588,199806,CSR,1e-06,3,23673.808359762366,7.0556015602429275e-12,4,"[800, 160, 32, 6]",0.19583333333333333,"[0.2, 0.2, 0.1875]",66602,199806,66602,1.054930782145912,4,0.2
850,AMG-basic,1753656833.5321283,73.0655,19.1499,26.8592,119.0746,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=850, nnz≈36,125, k≤20]","O(nnz) [N=850, memory≈58,437 elements]",0.66,0.0000,True,8.462746879521018e-15,2.9234337625204815e-16,7.441080644933376e-12,7.441080644933376e-12,2.8805831499889865e-16,True,3,20,0.15,17.891677646400357,225066,algebraic_multigrid,850×850,basic_amg,71236,0.09859653979238754,0.05,25831.855070602025,28.94796861148903,28.94796861148903,225066,CSR,1e-06,3,25831.855070602025,7.441080644933376e-12,4,"[850, 170, 34, 6]",0.19215686274509805,"[0.2, 0.2, 0.17647058823529413]",75022,225066,75022,1.0531472850805772,4,0.2
900,AMG-basic,1753656834.353867,77.7547,20.7444,28.4417,126.9408,440.88,440.88,0.00,0.00,,,,"O(k*nnz) [N=900, nnz≈40,500, k≤20]","O(nnz) [N=900, memory≈65,250 elements]",0.73,188.0874,True,9.154846402730919e-15,3.1165911937876354e-16,8.457412555530492e-12,8.457412555530492e-12,3.0475947845989856e-16,True,3,20,0.15,17.863498489863144,252753,algebraic_multigrid,900×900,basic_amg,79808,0.09852839506172839,0.05,27751.10588280965,29.374550056418887,29.374550056418887,252753,CSR,1e-06,3,27751.10588280965,8.457412555530492e-12,4,"[900, 180, 36, 7]",0.19814814814814816,"[0.2, 0.2, 0.19444444444444445]",84251,252753,84251,1.0556711106655974,4,0.2
950,AMG-basic,1753656835.193378,82.0065,22.3655,27.6172,131.9892,440.88,441.47,0.59,0.00,,,,"O(k*nnz) [N=950, nnz≈45,125, k≤20]","O(nnz) [N=950, memory≈72,437 elements]",0.82,1.3839,True,9.197516711546967e-15,2.9614377171495647e-16,9.177822492407552e-12,9.177822492407552e-12,2.9604755097809e-16,True,3,20,0.15,17.878000345751623,281283,algebraic_multigrid,950×950,basic_amg,89014,0.09863047091412742,0.05,31001.176878800758,31.05760643988737,31.05760643988737,281283,CSR,1e-06,3,31001.176878800758,9.177822492407552e-12,4,"[950, 190, 38, 7]",0.19473684210526318,"[0.2, 0.2, 0.18421052631578946]",93761,281283,93761,1.0533286898690093,4,0.2
1000,AMG-basic,1753656836.067287,85.8367,24.9894,27.9712,138.7973,441.47,443.27,1.79,0.00,,,,"O(k*nnz) [N=1000, nnz≈50,000, k≤20]","O(nnz) [N=1000, memory≈80,000 elements]",0.90,0.5032,True,1.0129205011727299e-14,3.2078158500369884e-16,1.0508559570894602e-11,1.0508559570894602e-11,3.171287141850538e-16,True,3,20,0.15,17.843607213990705,311316,algebraic_multigrid,1000×1000,basic_amg,98360,0.09836,0.05,33136.57546873082,31.576641195318306,31.57664119531831,311316,CSR,1e-06,3,33136.57546873082,1.0508559570894602e-11,4,"[1000, 200, 40, 8]",0.20000000000000004,"[0.2, 0.2, 0.2]",103772,311316,103772,1.0550223668157788,4,0.2
