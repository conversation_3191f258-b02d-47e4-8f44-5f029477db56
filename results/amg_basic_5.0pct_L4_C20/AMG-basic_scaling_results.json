[{"input_size": 50, "algorithm_name": "AMG-basic", "timestamp": 1753656825.6792555, "execution_time_ms": 4.519525729119778, "setup_time_ms": 0.8745403029024601, "cleanup_time_ms": 26.575435884296894, "total_time_ms": 31.969501916319132, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.9885061540178885e-16, "relative_solution_error": 5.580080655520642e-17, "final_residual_norm": 2.1990264548214973e-14, "computed_residual_norm": 2.1990264548214973e-14, "relative_residual": 5.87157246640397e-17, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.684643471343477, "operations_count": 972, "algorithm_type": "algebraic_multigrid", "matrix_size": "50×50", "implementation": "basic_amg", "nnz": 292, "actual_sparsity_ratio": 0.1168, "target_sparsity_ratio": 0.05, "b_norm": 374.52087450234364, "solution_norm": 7.147757174570349, "true_solution_norm": 7.147757174570349, "theoretical_flops": 972, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 374.52087450234364, "final_residual": 2.1990264548214973e-14, "num_levels": 2, "level_sizes": [50, 10], "avg_coarsening_ratio": 0.2, "coarsening_ratios": [0.2], "setup_work": 324, "solve_work": 972, "total_nnz_all_levels": 324, "hierarchy_efficiency": 1.1095890410958904, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=50, memory≈437 elements]", "theoretical_memory_mb": 0.004344940185546875, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "AMG-basic", "timestamp": 1753656825.8978708, "execution_time_ms": 9.721599332988262, "setup_time_ms": 1.04239908978343, "cleanup_time_ms": 26.2133851647377, "total_time_ms": 36.977383587509394, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.300802036419135e-15, "relative_solution_error": 1.28361356685645e-16, "final_residual_norm": 1.0652773606933016e-13, "computed_residual_norm": 1.0652773606933016e-13, "relative_residual": 9.968861856939258e-17, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.421770938940103, "operations_count": 3468, "algorithm_type": "algebraic_multigrid", "matrix_size": "100×100", "implementation": "basic_amg", "nnz": 1050, "actual_sparsity_ratio": 0.105, "target_sparsity_ratio": 0.05, "b_norm": 1068.6047975996069, "solution_norm": 10.133906886048107, "true_solution_norm": 10.133906886048107, "theoretical_flops": 3468, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 1068.6047975996069, "final_residual": 1.0652773606933016e-13, "num_levels": 3, "level_sizes": [100, 20, 4], "avg_coarsening_ratio": 0.2, "coarsening_ratios": [0.2, 0.2], "setup_work": 1156, "solve_work": 3468, "total_nnz_all_levels": 1156, "hierarchy_efficiency": 1.100952380952381, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=100, nnz≈500, k≤20]", "theoretical_space_complexity": "O(nnz) [N=100, memory≈1,250 elements]", "theoretical_memory_mb": 0.012975692749023438, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "AMG-basic", "timestamp": 1753656826.1581783, "execution_time_ms": 13.357199914753437, "setup_time_ms": 1.299732830375433, "cleanup_time_ms": 25.212306063622236, "total_time_ms": 39.869238808751106, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.941893345745703e-15, "relative_solution_error": 1.6836109806453885e-16, "final_residual_norm": 3.0174554675158965e-13, "computed_residual_norm": 3.0174554675158965e-13, "relative_residual": 1.659844469598732e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.167153116392242, "operations_count": 7488, "algorithm_type": "algebraic_multigrid", "matrix_size": "150×150", "implementation": "basic_amg", "nnz": 2322, "actual_sparsity_ratio": 0.1032, "target_sparsity_ratio": 0.05, "b_norm": 1817.9145834340536, "solution_norm": 11.53409765123595, "true_solution_norm": 11.53409765123595, "theoretical_flops": 7488, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 1817.9145834340536, "final_residual": 3.0174554675158965e-13, "num_levels": 3, "level_sizes": [150, 30, 6], "avg_coarsening_ratio": 0.2, "coarsening_ratios": [0.2, 0.2], "setup_work": 2496, "solve_work": 7488, "total_nnz_all_levels": 2496, "hierarchy_efficiency": 1.074935400516796, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈1,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=150, memory≈2,437 elements]", "theoretical_memory_mb": 0.025897979736328125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "AMG-basic", "timestamp": 1753656826.4454198, "execution_time_ms": 17.16623529791832, "setup_time_ms": 1.5850751660764217, "cleanup_time_ms": 25.801395997405052, "total_time_ms": 44.552706461399794, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.2316683627814365e-15, "relative_solution_error": 1.742164700542537e-16, "final_residual_norm": 3.944837227080367e-13, "computed_residual_norm": 3.944837227080367e-13, "relative_residual": 1.4680949242017002e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.228571216890433, "operations_count": 13044, "algorithm_type": "algebraic_multigrid", "matrix_size": "200×200", "implementation": "basic_amg", "nnz": 4066, "actual_sparsity_ratio": 0.10165, "target_sparsity_ratio": 0.05, "b_norm": 2687.0450691227848, "solution_norm": 12.809743889808239, "true_solution_norm": 12.809743889808239, "theoretical_flops": 13044, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 2687.0450691227848, "final_residual": 3.944837227080367e-13, "num_levels": 3, "level_sizes": [200, 40, 8], "avg_coarsening_ratio": 0.2, "coarsening_ratios": [0.2, 0.2], "setup_work": 4348, "solve_work": 13044, "total_nnz_all_levels": 4348, "hierarchy_efficiency": 1.0693556320708313, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=200, nnz≈2,000, k≤20]", "theoretical_space_complexity": "O(nnz) [N=200, memory≈4,000 elements]", "theoretical_memory_mb": 0.04311180114746094, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "AMG-basic", "timestamp": 1753656826.7679443, "execution_time_ms": 21.759339049458504, "setup_time_ms": 1.805790700018406, "cleanup_time_ms": 27.117323130369186, "total_time_ms": 50.682452879846096, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.8355529081231547e-15, "relative_solution_error": 1.8075784175965073e-16, "final_residual_norm": 7.530176526852335e-13, "computed_residual_norm": 7.530176526852335e-13, "relative_residual": 1.8256082720527188e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.119657733226394, "operations_count": 20328, "algorithm_type": "algebraic_multigrid", "matrix_size": "250×250", "implementation": "basic_amg", "nnz": 6318, "actual_sparsity_ratio": 0.101088, "target_sparsity_ratio": 0.05, "b_norm": 4124.749346356425, "solution_norm": 15.687025694262934, "true_solution_norm": 15.687025694262934, "theoretical_flops": 20328, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 4124.749346356425, "final_residual": 7.530176526852335e-13, "num_levels": 3, "level_sizes": [250, 50, 10], "avg_coarsening_ratio": 0.2, "coarsening_ratios": [0.2, 0.2], "setup_work": 6776, "solve_work": 20328, "total_nnz_all_levels": 6776, "hierarchy_efficiency": 1.072491294713517, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈3,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=250, memory≈5,937 elements]", "theoretical_memory_mb": 0.06461715698242188, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "AMG-basic", "timestamp": 1753656827.1438715, "execution_time_ms": 26.305469032377005, "setup_time_ms": 2.380107995122671, "cleanup_time_ms": 25.721625424921513, "total_time_ms": 54.40720245242119, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.9722989666806694e-15, "relative_solution_error": 2.2111693080652095e-16, "final_residual_norm": 1.2485487577855488e-12, "computed_residual_norm": 1.2485487577855488e-12, "relative_residual": 2.2021411552915168e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.02592562932905, "operations_count": 28944, "algorithm_type": "algebraic_multigrid", "matrix_size": "300×300", "implementation": "basic_amg", "nnz": 9068, "actual_sparsity_ratio": 0.10075555555555556, "target_sparsity_ratio": 0.05, "b_norm": 5669.703573657917, "solution_norm": 17.964698371091547, "true_solution_norm": 17.964698371091547, "theoretical_flops": 28944, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 5669.703573657917, "final_residual": 1.2485487577855488e-12, "num_levels": 4, "level_sizes": [300, 60, 12, 2], "avg_coarsening_ratio": 0.18888888888888888, "coarsening_ratios": [0.2, 0.2, 0.16666666666666666], "setup_work": 9648, "solve_work": 28944, "total_nnz_all_levels": 9648, "hierarchy_efficiency": 1.0639611821790913, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=300, nnz≈4,500, k≤20]", "theoretical_space_complexity": "O(nnz) [N=300, memory≈8,250 elements]", "theoretical_memory_mb": 0.09041404724121094, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "AMG-basic", "timestamp": 1753656827.5377815, "execution_time_ms": 30.83431515842676, "setup_time_ms": 3.47040593624115, "cleanup_time_ms": 25.904135778546333, "total_time_ms": 60.208856873214245, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.599791244397213e-15, "relative_solution_error": 1.8341706189677884e-16, "final_residual_norm": 1.2781609585541175e-12, "computed_residual_norm": 1.2781609585541175e-12, "relative_residual": 1.7713086186125052e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.13478232379697, "operations_count": 39186, "algorithm_type": "algebraic_multigrid", "matrix_size": "350×350", "implementation": "basic_amg", "nnz": 12274, "actual_sparsity_ratio": 0.10019591836734694, "target_sparsity_ratio": 0.05, "b_norm": 7215.913393767156, "solution_norm": 19.626261631118368, "true_solution_norm": 19.626261631118368, "theoretical_flops": 39186, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 7215.913393767156, "final_residual": 1.2781609585541175e-12, "num_levels": 4, "level_sizes": [350, 70, 14, 2], "avg_coarsening_ratio": 0.18095238095238098, "coarsening_ratios": [0.2, 0.2, 0.14285714285714285], "setup_work": 13062, "solve_work": 39186, "total_nnz_all_levels": 13062, "hierarchy_efficiency": 1.0642007495518984, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈6,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=350, memory≈10,937 elements]", "theoretical_memory_mb": 0.12050247192382812, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "AMG-basic", "timestamp": 1753656827.9716315, "execution_time_ms": 35.57969331741333, "setup_time_ms": 4.023090936243534, "cleanup_time_ms": 25.493811815977097, "total_time_ms": 65.09659606963396, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.6227281609390284e-15, "relative_solution_error": 1.8507560101943991e-16, "final_residual_norm": 1.4807695057324294e-12, "computed_residual_norm": 1.4807695057324294e-12, "relative_residual": 1.8039807124488075e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.12564911384695, "operations_count": 50979, "algorithm_type": "algebraic_multigrid", "matrix_size": "400×400", "implementation": "basic_amg", "nnz": 15970, "actual_sparsity_ratio": 0.0998125, "target_sparsity_ratio": 0.05, "b_norm": 8208.344443563168, "solution_norm": 19.574315258111767, "true_solution_norm": 19.574315258111767, "theoretical_flops": 50979, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 8208.344443563168, "final_residual": 1.4807695057324294e-12, "num_levels": 4, "level_sizes": [400, 80, 16, 3], "avg_coarsening_ratio": 0.19583333333333333, "coarsening_ratios": [0.2, 0.2, 0.1875], "setup_work": 16993, "solve_work": 50979, "total_nnz_all_levels": 16993, "hierarchy_efficiency": 1.0640576080150281, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=400, nnz≈8,000, k≤20]", "theoretical_space_complexity": "O(nnz) [N=400, memory≈14,000 elements]", "theoretical_memory_mb": 0.15488243103027344, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "AMG-basic", "timestamp": 1753656828.4562683, "execution_time_ms": 40.34984977915883, "setup_time_ms": 5.530436057597399, "cleanup_time_ms": 29.3928487226367, "total_time_ms": 75.27313455939293, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 4.5362437780085e-15, "relative_solution_error": 2.1532388131625508e-16, "final_residual_norm": 1.9198317207507717e-12, "computed_residual_norm": 1.9198317207507717e-12, "relative_residual": 1.930830479126378e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.09167959546646, "operations_count": 64569, "algorithm_type": "algebraic_multigrid", "matrix_size": "450×450", "implementation": "basic_amg", "nnz": 20206, "actual_sparsity_ratio": 0.09978271604938271, "target_sparsity_ratio": 0.05, "b_norm": 9943.036126192794, "solution_norm": 21.067072311156842, "true_solution_norm": 21.067072311156846, "theoretical_flops": 64569, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 9943.036126192794, "final_residual": 1.9198317207507717e-12, "num_levels": 4, "level_sizes": [450, 90, 18, 3], "avg_coarsening_ratio": 0.18888888888888888, "coarsening_ratios": [0.2, 0.2, 0.16666666666666666], "setup_work": 21523, "solve_work": 64569, "total_nnz_all_levels": 21523, "hierarchy_efficiency": 1.0651786598040187, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈10,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=450, memory≈17,437 elements]", "theoretical_memory_mb": 0.19355392456054688, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "AMG-basic", "timestamp": 1753656828.9808354, "execution_time_ms": 43.12673062086105, "setup_time_ms": 6.368824280798435, "cleanup_time_ms": 27.901242021471262, "total_time_ms": 77.39679692313075, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 5.2026263672580226e-15, "relative_solution_error": 2.243467389946597e-16, "final_residual_norm": 2.75999594139361e-12, "computed_residual_norm": 2.75999594139361e-12, "relative_residual": 2.266810041237201e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 18.01147584081161, "operations_count": 79038, "algorithm_type": "algebraic_multigrid", "matrix_size": "500×500", "implementation": "basic_amg", "nnz": 24838, "actual_sparsity_ratio": 0.099352, "target_sparsity_ratio": 0.05, "b_norm": 12175.682528242347, "solution_norm": 23.190113618642187, "true_solution_norm": 23.190113618642187, "theoretical_flops": 79038, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 12175.682528242347, "final_residual": 2.75999594139361e-12, "num_levels": 4, "level_sizes": [500, 100, 20, 4], "avg_coarsening_ratio": 0.20000000000000004, "coarsening_ratios": [0.2, 0.2, 0.2], "setup_work": 26346, "solve_work": 79038, "total_nnz_all_levels": 26346, "hierarchy_efficiency": 1.0607134229809163, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=500, nnz≈12,500, k≤20]", "theoretical_space_complexity": "O(nnz) [N=500, memory≈21,250 elements]", "theoretical_memory_mb": 0.23651695251464844, "efficiency_ratio": 0.0}, {"input_size": 550, "algorithm_name": "AMG-basic", "timestamp": 1753656829.5263495, "execution_time_ms": 47.63940433040261, "setup_time_ms": 7.9408073797822, "cleanup_time_ms": 32.44529524818063, "total_time_ms": 88.02550695836544, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 5.86902433451205e-15, "relative_solution_error": 2.6110582948534076e-16, "final_residual_norm": 3.4080580690970635e-12, "computed_residual_norm": 3.4080580690970635e-12, "relative_residual": 2.622636539520154e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.938576010769253, "operations_count": 95436, "algorithm_type": "algebraic_multigrid", "matrix_size": "550×550", "implementation": "basic_amg", "nnz": 29974, "actual_sparsity_ratio": 0.09908760330578513, "target_sparsity_ratio": 0.05, "b_norm": 12994.778413788945, "solution_norm": 22.477569137695387, "true_solution_norm": 22.477569137695387, "theoretical_flops": 95436, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 12994.778413788945, "final_residual": 3.4080580690970635e-12, "num_levels": 4, "level_sizes": [550, 110, 22, 4], "avg_coarsening_ratio": 0.19393939393939394, "coarsening_ratios": [0.2, 0.2, 0.18181818181818182], "setup_work": 31812, "solve_work": 95436, "total_nnz_all_levels": 31812, "hierarchy_efficiency": 1.0613198105024355, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈15,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=550, memory≈25,437 elements]", "theoretical_memory_mb": 0.2837715148925781, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "AMG-basic", "timestamp": 1753656830.1085165, "execution_time_ms": 51.12983398139477, "setup_time_ms": 9.26596112549305, "cleanup_time_ms": 26.737898122519255, "total_time_ms": 87.13369322940707, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.363998130653395e-15, "relative_solution_error": 2.688097541070778e-16, "final_residual_norm": 3.9335261601815354e-12, "computed_residual_norm": 3.9335261601815354e-12, "relative_residual": 2.6365771940010747e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.935927253608433, "operations_count": 113040, "algorithm_type": "algebraic_multigrid", "matrix_size": "600×600", "implementation": "basic_amg", "nnz": 35626, "actual_sparsity_ratio": 0.09896111111111111, "target_sparsity_ratio": 0.05, "b_norm": 14919.063129012002, "solution_norm": 23.6747291845606, "true_solution_norm": 23.674729184560604, "theoretical_flops": 113040, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 14919.063129012002, "final_residual": 3.9335261601815354e-12, "num_levels": 4, "level_sizes": [600, 120, 24, 4], "avg_coarsening_ratio": 0.18888888888888888, "coarsening_ratios": [0.2, 0.2, 0.16666666666666666], "setup_work": 37680, "solve_work": 113040, "total_nnz_all_levels": 37680, "hierarchy_efficiency": 1.0576545219783304, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=600, nnz≈18,000, k≤20]", "theoretical_space_complexity": "O(nnz) [N=600, memory≈30,000 elements]", "theoretical_memory_mb": 0.33531761169433594, "efficiency_ratio": 0.0}, {"input_size": 650, "algorithm_name": "AMG-basic", "timestamp": 1753656830.712477, "execution_time_ms": 55.864106863737106, "setup_time_ms": 10.539777111262083, "cleanup_time_ms": 28.682501055300236, "total_time_ms": 95.08638503029943, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.420865475431083e-15, "relative_solution_error": 2.6079780950663236e-16, "final_residual_norm": 4.329275633653419e-12, "computed_residual_norm": 4.329275633653419e-12, "relative_residual": 2.575764552070357e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.947595993074053, "operations_count": 132555, "algorithm_type": "algebraic_multigrid", "matrix_size": "650×650", "implementation": "basic_amg", "nnz": 41810, "actual_sparsity_ratio": 0.09895857988165681, "target_sparsity_ratio": 0.05, "b_norm": 16807.730466566205, "solution_norm": 24.620089745300536, "true_solution_norm": 24.62008974530054, "theoretical_flops": 132555, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 16807.730466566205, "final_residual": 4.329275633653419e-12, "num_levels": 4, "level_sizes": [650, 130, 26, 5], "avg_coarsening_ratio": 0.19743589743589743, "coarsening_ratios": [0.2, 0.2, 0.19230769230769232], "setup_work": 44185, "solve_work": 132555, "total_nnz_all_levels": 44185, "hierarchy_efficiency": 1.0568045922028222, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈21,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=650, memory≈34,937 elements]", "theoretical_memory_mb": 0.3911552429199219, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "AMG-basic", "timestamp": 1753656831.3647258, "execution_time_ms": 59.71550187096, "setup_time_ms": 11.997301131486893, "cleanup_time_ms": 26.59348165616393, "total_time_ms": 98.30628465861082, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 7.433800234028656e-15, "relative_solution_error": 2.8171730854158745e-16, "final_residual_norm": 5.510192941128242e-12, "computed_residual_norm": 5.510192941128242e-12, "relative_residual": 2.8375009540093487e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.899209810431177, "operations_count": 153435, "algorithm_type": "algebraic_multigrid", "matrix_size": "700×700", "implementation": "basic_amg", "nnz": 48442, "actual_sparsity_ratio": 0.09886122448979592, "target_sparsity_ratio": 0.05, "b_norm": 19419.17564236381, "solution_norm": 26.38744588506982, "true_solution_norm": 26.38744588506982, "theoretical_flops": 153435, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 19419.17564236381, "final_residual": 5.510192941128242e-12, "num_levels": 4, "level_sizes": [700, 140, 28, 5], "avg_coarsening_ratio": 0.19285714285714287, "coarsening_ratios": [0.2, 0.2, 0.17857142857142858], "setup_work": 51145, "solve_work": 153435, "total_nnz_all_levels": 51145, "hierarchy_efficiency": 1.0557986870897156, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=700, nnz≈24,500, k≤20]", "theoretical_space_complexity": "O(nnz) [N=700, memory≈40,250 elements]", "theoretical_memory_mb": 0.45128440856933594, "efficiency_ratio": 0.0}, {"input_size": 750, "algorithm_name": "AMG-basic", "timestamp": 1753656832.0459433, "execution_time_ms": 65.01469602808356, "setup_time_ms": 14.367613010108471, "cleanup_time_ms": 29.168188106268644, "total_time_ms": 108.55049714446068, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 7.807345803338442e-15, "relative_solution_error": 2.794945515735966e-16, "final_residual_norm": 5.991067798031164e-12, "computed_residual_norm": 5.991067798031164e-12, "relative_residual": 2.7236724587843515e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.9196818291041, "operations_count": 176124, "algorithm_type": "algebraic_multigrid", "matrix_size": "750×750", "implementation": "basic_amg", "nnz": 55624, "actual_sparsity_ratio": 0.0988871111111111, "target_sparsity_ratio": 0.05, "b_norm": 21996.28585555085, "solution_norm": 27.93380321506056, "true_solution_norm": 27.93380321506056, "theoretical_flops": 176124, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 21996.28585555085, "final_residual": 5.991067798031164e-12, "num_levels": 4, "level_sizes": [750, 150, 30, 6], "avg_coarsening_ratio": 0.20000000000000004, "coarsening_ratios": [0.2, 0.2, 0.2], "setup_work": 58708, "solve_work": 176124, "total_nnz_all_levels": 58708, "hierarchy_efficiency": 1.0554436933697684, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈28,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=750, memory≈45,937 elements]", "theoretical_memory_mb": 0.5157051086425781, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "AMG-basic", "timestamp": 1753656832.7716663, "execution_time_ms": 68.39581560343504, "setup_time_ms": 16.07329212129116, "cleanup_time_ms": 28.97692285478115, "total_time_ms": 113.44603057950735, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 8.479824819515684e-15, "relative_solution_error": 3.0077538697997773e-16, "final_residual_norm": 7.0556015602429275e-12, "computed_residual_norm": 7.0556015602429275e-12, "relative_residual": 2.9803407432473407e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.87465483875852, "operations_count": 199806, "algorithm_type": "algebraic_multigrid", "matrix_size": "800×800", "implementation": "basic_amg", "nnz": 63134, "actual_sparsity_ratio": 0.098646875, "target_sparsity_ratio": 0.05, "b_norm": 23673.808359762366, "solution_norm": 28.193213895125588, "true_solution_norm": 28.193213895125588, "theoretical_flops": 199806, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 23673.808359762366, "final_residual": 7.0556015602429275e-12, "num_levels": 4, "level_sizes": [800, 160, 32, 6], "avg_coarsening_ratio": 0.19583333333333333, "coarsening_ratios": [0.2, 0.2, 0.1875], "setup_work": 66602, "solve_work": 199806, "total_nnz_all_levels": 66602, "hierarchy_efficiency": 1.054930782145912, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=800, nnz≈32,000, k≤20]", "theoretical_space_complexity": "O(nnz) [N=800, memory≈52,000 elements]", "theoretical_memory_mb": 0.5844173431396484, "efficiency_ratio": 0.0}, {"input_size": 850, "algorithm_name": "AMG-basic", "timestamp": 1753656833.5321283, "execution_time_ms": 73.06554894894361, "setup_time_ms": 19.14986316114664, "cleanup_time_ms": 26.859221048653126, "total_time_ms": 119.07463315874338, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.87890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 8.462746879521018e-15, "relative_solution_error": 2.9234337625204815e-16, "final_residual_norm": 7.441080644933376e-12, "computed_residual_norm": 7.441080644933376e-12, "relative_residual": 2.8805831499889865e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.891677646400357, "operations_count": 225066, "algorithm_type": "algebraic_multigrid", "matrix_size": "850×850", "implementation": "basic_amg", "nnz": 71236, "actual_sparsity_ratio": 0.09859653979238754, "target_sparsity_ratio": 0.05, "b_norm": 25831.855070602025, "solution_norm": 28.94796861148903, "true_solution_norm": 28.94796861148903, "theoretical_flops": 225066, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 25831.855070602025, "final_residual": 7.441080644933376e-12, "num_levels": 4, "level_sizes": [850, 170, 34, 6], "avg_coarsening_ratio": 0.19215686274509805, "coarsening_ratios": [0.2, 0.2, 0.17647058823529413], "setup_work": 75022, "solve_work": 225066, "total_nnz_all_levels": 75022, "hierarchy_efficiency": 1.0531472850805772, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈36,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=850, memory≈58,437 elements]", "theoretical_memory_mb": 0.6574211120605469, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "AMG-basic", "timestamp": 1753656834.353867, "execution_time_ms": 77.75473464280367, "setup_time_ms": 20.74440522119403, "cleanup_time_ms": 28.44168385490775, "total_time_ms": 126.94082371890545, "baseline_memory_mb": 440.87890625, "peak_memory_mb": 440.8828125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 9.154846402730919e-15, "relative_solution_error": 3.1165911937876354e-16, "final_residual_norm": 8.457412555530492e-12, "computed_residual_norm": 8.457412555530492e-12, "relative_residual": 3.0475947845989856e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.863498489863144, "operations_count": 252753, "algorithm_type": "algebraic_multigrid", "matrix_size": "900×900", "implementation": "basic_amg", "nnz": 79808, "actual_sparsity_ratio": 0.09852839506172839, "target_sparsity_ratio": 0.05, "b_norm": 27751.10588280965, "solution_norm": 29.374550056418887, "true_solution_norm": 29.374550056418887, "theoretical_flops": 252753, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 27751.10588280965, "final_residual": 8.457412555530492e-12, "num_levels": 4, "level_sizes": [900, 180, 36, 7], "avg_coarsening_ratio": 0.19814814814814816, "coarsening_ratios": [0.2, 0.2, 0.19444444444444445], "setup_work": 84251, "solve_work": 252753, "total_nnz_all_levels": 84251, "hierarchy_efficiency": 1.0556711106655974, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=900, nnz≈40,500, k≤20]", "theoretical_space_complexity": "O(nnz) [N=900, memory≈65,250 elements]", "theoretical_memory_mb": 0.7347164154052734, "efficiency_ratio": 188.08740234375}, {"input_size": 950, "algorithm_name": "AMG-basic", "timestamp": 1753656835.193378, "execution_time_ms": 82.0065338164568, "setup_time_ms": 22.365461103618145, "cleanup_time_ms": 27.617210056632757, "total_time_ms": 131.9892049767077, "baseline_memory_mb": 440.8828125, "peak_memory_mb": 441.47265625, "memory_increment_mb": 0.58984375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 9.197516711546967e-15, "relative_solution_error": 2.9614377171495647e-16, "final_residual_norm": 9.177822492407552e-12, "computed_residual_norm": 9.177822492407552e-12, "relative_residual": 2.9604755097809e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.878000345751623, "operations_count": 281283, "algorithm_type": "algebraic_multigrid", "matrix_size": "950×950", "implementation": "basic_amg", "nnz": 89014, "actual_sparsity_ratio": 0.09863047091412742, "target_sparsity_ratio": 0.05, "b_norm": 31001.176878800758, "solution_norm": 31.05760643988737, "true_solution_norm": 31.05760643988737, "theoretical_flops": 281283, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 31001.176878800758, "final_residual": 9.177822492407552e-12, "num_levels": 4, "level_sizes": [950, 190, 38, 7], "avg_coarsening_ratio": 0.19473684210526318, "coarsening_ratios": [0.2, 0.2, 0.18421052631578946], "setup_work": 93761, "solve_work": 281283, "total_nnz_all_levels": 93761, "hierarchy_efficiency": 1.0533286898690093, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈45,125, k≤20]", "theoretical_space_complexity": "O(nnz) [N=950, memory≈72,437 elements]", "theoretical_memory_mb": 0.8163032531738281, "efficiency_ratio": 1.3839313431291391}, {"input_size": 1000, "algorithm_name": "AMG-basic", "timestamp": 1753656836.067287, "execution_time_ms": 85.83667166531086, "setup_time_ms": 24.989422876387835, "cleanup_time_ms": 27.97124721109867, "total_time_ms": 138.79734175279737, "baseline_memory_mb": 441.47265625, "peak_memory_mb": 443.265625, "memory_increment_mb": 1.79296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.0129205011727299e-14, "relative_solution_error": 3.2078158500369884e-16, "final_residual_norm": 1.0508559570894602e-11, "computed_residual_norm": 1.0508559570894602e-11, "relative_residual": 3.171287141850538e-16, "converged": true, "iterations_performed": 3, "max_iterations": 20, "iteration_efficiency": 0.15, "convergence_rate": 17.843607213990705, "operations_count": 311316, "algorithm_type": "algebraic_multigrid", "matrix_size": "1000×1000", "implementation": "basic_amg", "nnz": 98360, "actual_sparsity_ratio": 0.09836, "target_sparsity_ratio": 0.05, "b_norm": 33136.57546873082, "solution_norm": 31.576641195318306, "true_solution_norm": 31.57664119531831, "theoretical_flops": 311316, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 33136.57546873082, "final_residual": 1.0508559570894602e-11, "num_levels": 4, "level_sizes": [1000, 200, 40, 8], "avg_coarsening_ratio": 0.20000000000000004, "coarsening_ratios": [0.2, 0.2, 0.2], "setup_work": 103772, "solve_work": 311316, "total_nnz_all_levels": 103772, "hierarchy_efficiency": 1.0550223668157788, "max_levels": 4, "coarsening_factor": 0.2}, "theoretical_time_complexity": "O(k*nnz) [N=1000, nnz≈50,000, k≤20]", "theoretical_space_complexity": "O(nnz) [N=1000, memory≈80,000 elements]", "theoretical_memory_mb": 0.9021816253662109, "efficiency_ratio": 0.5031775514025054}]