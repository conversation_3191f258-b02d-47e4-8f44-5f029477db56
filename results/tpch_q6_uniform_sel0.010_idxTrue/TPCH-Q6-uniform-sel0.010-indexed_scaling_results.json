[{"input_size": 1000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655666.4098644, "execution_time_ms": 0.17454233020544052, "setup_time_ms": 4.352733958512545, "cleanup_time_ms": 24.732477962970734, "total_time_ms": 29.25975425168872, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 1000, "total_rows": 1000, "rows_scanned": 100, "rows_filtered": 1, "qualifying_rows": 1, "scan_ratio": 0.1, "filter_efficiency": 0.01, "actual_selectivity": 0.001, "expected_selectivity": 0.01, "selectivity_accuracy": 0.991, "comparisons": 239, "arithmetic_operations": 2, "memory_accesses": 101, "index_lookups": 1, "comparisons_per_row": 2.39, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.99, "index_effectiveness": 0.9, "scan_time_ms": 0.16, "revenue": 1117.07, "avg_revenue_per_row": 1117.07, "price_variance": 0, "discount_variance": 0, "quantity_variance": 0, "effective_scan_size": 100, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=1000, k≈10]", "theoretical_space_complexity": "O(k + log n) [k≈10]", "theoretical_memory_mb": 0.0006863844626210182, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655666.594313, "execution_time_ms": 0.3161175176501274, "setup_time_ms": 8.714384865015745, "cleanup_time_ms": 25.69691091775894, "total_time_ms": 34.727413300424814, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 2000, "total_rows": 2000, "rows_scanned": 200, "rows_filtered": 2, "qualifying_rows": 2, "scan_ratio": 0.1, "filter_efficiency": 0.01, "actual_selectivity": 0.001, "expected_selectivity": 0.01, "selectivity_accuracy": 0.991, "comparisons": 471, "arithmetic_operations": 4, "memory_accesses": 202, "index_lookups": 1, "comparisons_per_row": 2.35, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.99, "index_effectiveness": 0.9, "scan_time_ms": 0.274, "revenue": 1417.17, "avg_revenue_per_row": 708.58, "price_variance": 8498595.11, "discount_variance": 0.0, "quantity_variance": 12.25, "effective_scan_size": 200, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=2000, k≈20]", "theoretical_space_complexity": "O(k + log n) [k≈20]", "theoretical_memory_mb": 0.001304365419652268, "efficiency_ratio": 0.0}, {"input_size": 3000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655666.781611, "execution_time_ms": 0.44072652235627174, "setup_time_ms": 12.857257388532162, "cleanup_time_ms": 24.674685206264257, "total_time_ms": 37.97266911715269, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 3000, "total_rows": 3000, "rows_scanned": 300, "rows_filtered": 2, "qualifying_rows": 2, "scan_ratio": 0.1, "filter_efficiency": 0.0067, "actual_selectivity": 0.000667, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9907, "comparisons": 686, "arithmetic_operations": 4, "memory_accesses": 302, "index_lookups": 1, "comparisons_per_row": 2.29, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0.9, "scan_time_ms": 0.396, "revenue": 3153.3, "avg_revenue_per_row": 1576.65, "price_variance": 39582846.42, "discount_variance": 0.0, "quantity_variance": 6.25, "effective_scan_size": 300, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=3000, k≈30]", "theoretical_space_complexity": "O(k + log n) [k≈30]", "theoretical_memory_mb": 0.0019191798918562564, "efficiency_ratio": 0.0}, {"input_size": 4000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655666.972972, "execution_time_ms": 0.5282108671963215, "setup_time_ms": 17.829133197665215, "cleanup_time_ms": 25.436364114284515, "total_time_ms": 43.79370817914605, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 4000, "total_rows": 4000, "rows_scanned": 400, "rows_filtered": 8, "qualifying_rows": 8, "scan_ratio": 0.1, "filter_efficiency": 0.02, "actual_selectivity": 0.002, "expected_selectivity": 0.01, "selectivity_accuracy": 0.992, "comparisons": 933, "arithmetic_operations": 16, "memory_accesses": 408, "index_lookups": 1, "comparisons_per_row": 2.33, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0.9, "scan_time_ms": 0.485, "revenue": 11652.91, "avg_revenue_per_row": 1456.61, "price_variance": 226659427.22, "discount_variance": 0.0, "quantity_variance": 24.0, "effective_scan_size": 400, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=4000, k≈40]", "theoretical_space_complexity": "O(k + log n) [k≈40]", "theoretical_memory_mb": 0.0025326979391835183, "efficiency_ratio": 0.0}, {"input_size": 5000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655667.178411, "execution_time_ms": 0.6792956963181496, "setup_time_ms": 22.828334011137486, "cleanup_time_ms": 24.926876183599234, "total_time_ms": 48.43450589105487, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 5000, "total_rows": 5000, "rows_scanned": 500, "rows_filtered": 9, "qualifying_rows": 9, "scan_ratio": 0.1, "filter_efficiency": 0.018, "actual_selectivity": 0.0018, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9918, "comparisons": 1182, "arithmetic_operations": 18, "memory_accesses": 509, "index_lookups": 1, "comparisons_per_row": 2.36, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0.9, "scan_time_ms": 0.663, "revenue": 23558.79, "avg_revenue_per_row": 2617.64, "price_variance": 535801866.54, "discount_variance": 0.0, "quantity_variance": 42.17, "effective_scan_size": 500, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=5000, k≈50]", "theoretical_space_complexity": "O(k + log n) [k≈50]", "theoretical_memory_mb": 0.0031455056181301077, "efficiency_ratio": 0.0}, {"input_size": 6000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655667.3822517, "execution_time_ms": 0.7780071347951889, "setup_time_ms": 25.559894740581512, "cleanup_time_ms": 25.27664788067341, "total_time_ms": 51.61454975605011, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 6000, "total_rows": 6000, "rows_scanned": 600, "rows_filtered": 6, "qualifying_rows": 6, "scan_ratio": 0.1, "filter_efficiency": 0.01, "actual_selectivity": 0.001, "expected_selectivity": 0.01, "selectivity_accuracy": 0.991, "comparisons": 1377, "arithmetic_operations": 12, "memory_accesses": 606, "index_lookups": 1, "comparisons_per_row": 2.29, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.99, "index_effectiveness": 0.9, "scan_time_ms": 0.745, "revenue": 15509.98, "avg_revenue_per_row": 2585.0, "price_variance": 381489459.04, "discount_variance": 0.0, "quantity_variance": 24.47, "effective_scan_size": 600, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=6000, k≈60]", "theoretical_space_complexity": "O(k + log n) [k≈60]", "theoretical_memory_mb": 0.0037578639738875064, "efficiency_ratio": 0.0}, {"input_size": 7000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655667.5874062, "execution_time_ms": 0.8762530982494354, "setup_time_ms": 30.48623539507389, "cleanup_time_ms": 25.483818724751472, "total_time_ms": 56.8463072180748, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 7000, "total_rows": 7000, "rows_scanned": 700, "rows_filtered": 5, "qualifying_rows": 5, "scan_ratio": 0.1, "filter_efficiency": 0.0071, "actual_selectivity": 0.000714, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9907, "comparisons": 1609, "arithmetic_operations": 10, "memory_accesses": 705, "index_lookups": 1, "comparisons_per_row": 2.3, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.993, "index_effectiveness": 0.9, "scan_time_ms": 0.848, "revenue": 11664.31, "avg_revenue_per_row": 2332.86, "price_variance": 915757695.35, "discount_variance": 0.0, "quantity_variance": 15.76, "effective_scan_size": 700, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=7000, k≈70]", "theoretical_space_complexity": "O(k + log n) [k≈70]", "theoretical_memory_mb": 0.004369912255910642, "efficiency_ratio": 0.0}, {"input_size": 8000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655667.797462, "execution_time_ms": 1.007742527872324, "setup_time_ms": 34.20731285586953, "cleanup_time_ms": 25.546055752784014, "total_time_ms": 60.76111113652587, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 8000, "total_rows": 8000, "rows_scanned": 800, "rows_filtered": 7, "qualifying_rows": 7, "scan_ratio": 0.1, "filter_efficiency": 0.0088, "actual_selectivity": 0.000875, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9909, "comparisons": 1862, "arithmetic_operations": 14, "memory_accesses": 807, "index_lookups": 1, "comparisons_per_row": 2.33, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.991, "index_effectiveness": 0.9, "scan_time_ms": 0.99, "revenue": 21720.87, "avg_revenue_per_row": 3102.98, "price_variance": 834705282.15, "discount_variance": 0.0, "quantity_variance": 45.39, "effective_scan_size": 800, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=8000, k≈80]", "theoretical_space_complexity": "O(k + log n) [k≈80]", "theoretical_memory_mb": 0.004981733583714768, "efficiency_ratio": 0.0}, {"input_size": 9000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655668.0128243, "execution_time_ms": 1.1684313416481018, "setup_time_ms": 40.71711516007781, "cleanup_time_ms": 25.650888681411743, "total_time_ms": 67.53643518313766, "baseline_memory_mb": 417.19921875, "peak_memory_mb": 417.671875, "memory_increment_mb": 0.47265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 9000, "total_rows": 9000, "rows_scanned": 900, "rows_filtered": 4, "qualifying_rows": 4, "scan_ratio": 0.1, "filter_efficiency": 0.0044, "actual_selectivity": 0.000444, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9904, "comparisons": 2089, "arithmetic_operations": 8, "memory_accesses": 904, "index_lookups": 1, "comparisons_per_row": 2.32, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.996, "index_effectiveness": 0.9, "scan_time_ms": 1.131, "revenue": 8190.58, "avg_revenue_per_row": 2047.64, "price_variance": 562161747.71, "discount_variance": 0.0, "quantity_variance": 30.0, "effective_scan_size": 900, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=9000, k≈90]", "theoretical_space_complexity": "O(k + log n) [k≈90]", "theoretical_memory_mb": 0.005593381571091495, "efficiency_ratio": 0.011833931257846469}, {"input_size": 10000, "algorithm_name": "TPCH-Q6-uniform-sel0.010-indexed", "timestamp": 1753655668.2402284, "execution_time_ms": 1.2728214263916016, "setup_time_ms": 43.66875905543566, "cleanup_time_ms": 26.036803144961596, "total_time_ms": 70.97838362678885, "baseline_memory_mb": 417.671875, "peak_memory_mb": 418.4453125, "memory_increment_mb": 0.7734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": true, "input_size": 10000, "total_rows": 10000, "rows_scanned": 1000, "rows_filtered": 6, "qualifying_rows": 6, "scan_ratio": 0.1, "filter_efficiency": 0.006, "actual_selectivity": 0.0006, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9906, "comparisons": 2306, "arithmetic_operations": 12, "memory_accesses": 1006, "index_lookups": 1, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.994, "index_effectiveness": 0.9, "scan_time_ms": 1.25, "revenue": 7283.91, "avg_revenue_per_row": 1213.99, "price_variance": 735986380.6, "discount_variance": 0.0, "quantity_variance": 50.33, "effective_scan_size": 1000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(log n + k) [n=10000, k≈100]", "theoretical_space_complexity": "O(k + log n) [k≈100]", "theoretical_memory_mb": 0.006204892825161358, "efficiency_ratio": 0.008022487693137917}]