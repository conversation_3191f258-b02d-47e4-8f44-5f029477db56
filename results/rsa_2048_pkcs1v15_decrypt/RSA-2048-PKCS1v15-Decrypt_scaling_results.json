[{"input_size": 16, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705354.7636323, "execution_time_ms": 0.626701582223177, "setup_time_ms": 0.10472023859620094, "cleanup_time_ms": 23.46293069422245, "total_time_ms": 24.194352515041828, "baseline_memory_mb": 421.16796875, "peak_memory_mb": 421.16796875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 16, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 16.0, "throughput_mbps": 0.03, "bytes_per_second": 26385, "chunks_per_second": 1649, "bits_per_second": 211080, "decryption_rate_per_second": 1649.07, "decryption_time_ms": 0.606403686106205, "chunk_processing_time_ms": 0.606403686106205, "key_utilization": 0.065, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007476806640625, "efficiency_ratio": 0.0}, {"input_size": 32, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705354.9361548, "execution_time_ms": 0.6037249229848385, "setup_time_ms": 0.13142498210072517, "cleanup_time_ms": 23.241489194333553, "total_time_ms": 23.976639099419117, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 32, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 8.0, "throughput_mbps": 0.05, "bytes_per_second": 52001, "chunks_per_second": 1625, "bits_per_second": 416010, "decryption_rate_per_second": 1625.04, "decryption_time_ms": 0.6153690628707409, "chunk_processing_time_ms": 0.6153690628707409, "key_utilization": 0.131, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.1181896, "execution_time_ms": 0.5993770435452461, "setup_time_ms": 0.09118812158703804, "cleanup_time_ms": 23.5910601913929, "total_time_ms": 24.281625356525183, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 64, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 4.0, "throughput_mbps": 0.1, "bytes_per_second": 109835, "chunks_per_second": 1716, "bits_per_second": 878680, "decryption_rate_per_second": 1716.17, "decryption_time_ms": 0.5826922133564949, "chunk_processing_time_ms": 0.5826922133564949, "key_utilization": 0.261, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.00079345703125, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.2875884, "execution_time_ms": 0.6166229024529457, "setup_time_ms": 0.08731987327337265, "cleanup_time_ms": 24.191730190068483, "total_time_ms": 24.8956729657948, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 128, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 2.0, "throughput_mbps": 0.2, "bytes_per_second": 205259, "chunks_per_second": 1603, "bits_per_second": 1642078, "decryption_rate_per_second": 1603.59, "decryption_time_ms": 0.6236000917851925, "chunk_processing_time_ms": 0.6236000917851925, "key_utilization": 0.522, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008544921875, "efficiency_ratio": 0.0}, {"input_size": 61, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.4583018, "execution_time_ms": 0.6151112727820873, "setup_time_ms": 0.08651288226246834, "cleanup_time_ms": 25.924453977495432, "total_time_ms": 26.626078132539988, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 61, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 61, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 4.2, "throughput_mbps": 0.09, "bytes_per_second": 99448, "chunks_per_second": 1630, "bits_per_second": 795587, "decryption_rate_per_second": 1630.3, "decryption_time_ms": 0.6133830174803734, "chunk_processing_time_ms": 0.6133830174803734, "key_utilization": 0.249, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007905960083007812, "efficiency_ratio": 0.0}, {"input_size": 122, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.6371408, "execution_time_ms": 0.6565668620169163, "setup_time_ms": 0.09325891733169556, "cleanup_time_ms": 24.265651125460863, "total_time_ms": 25.015476904809475, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 122, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 122, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 2.1, "throughput_mbps": 0.19, "bytes_per_second": 194807, "chunks_per_second": 1596, "bits_per_second": 1558460, "decryption_rate_per_second": 1596.78, "decryption_time_ms": 0.6262590177357197, "chunk_processing_time_ms": 0.6262590177357197, "key_utilization": 0.498, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008487701416015625, "efficiency_ratio": 0.0}, {"input_size": 235, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.8187013, "execution_time_ms": 0.6129084154963493, "setup_time_ms": 0.0858609564602375, "cleanup_time_ms": 23.623981047421694, "total_time_ms": 24.32275041937828, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 235, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 235, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 1.09, "throughput_mbps": 0.36, "bytes_per_second": 376127, "chunks_per_second": 1600, "bits_per_second": 3009020, "decryption_rate_per_second": 1600.54, "decryption_time_ms": 0.6247879937291145, "chunk_processing_time_ms": 0.6247879937291145, "key_utilization": 0.959, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0009565353393554688, "efficiency_ratio": 0.0}, {"input_size": 245, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705355.9896207, "execution_time_ms": 0.6105693057179451, "setup_time_ms": 0.09009893983602524, "cleanup_time_ms": 23.39171478524804, "total_time_ms": 24.09238303080201, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 245, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 245, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 1.04, "throughput_mbps": 0.39, "bytes_per_second": 404534, "chunks_per_second": 1651, "bits_per_second": 3236278, "decryption_rate_per_second": 1651.16, "decryption_time_ms": 0.6056339479982853, "chunk_processing_time_ms": 0.6056339479982853, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0009660720825195312, "efficiency_ratio": 0.0}, {"input_size": 255, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705356.1607924, "execution_time_ms": 1.1503820307552814, "setup_time_ms": 0.11249398812651634, "cleanup_time_ms": 24.22449877485633, "total_time_ms": 25.487374793738127, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 255, "ciphertext_size_bytes": 512, "plaintext_size_bytes": 255, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 2, "actual_chunks": 2, "compression_ratio": 2.01, "throughput_mbps": 0.22, "bytes_per_second": 228948, "chunks_per_second": 1795, "bits_per_second": 1831588, "decryption_rate_per_second": 897.84, "decryption_time_ms": 1.113787293434143, "chunk_processing_time_ms": 0.5568936467170715, "key_utilization": 0.52, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0012197494506835938, "efficiency_ratio": 0.0}, {"input_size": 490, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705356.3357472, "execution_time_ms": 1.2132253497838974, "setup_time_ms": 0.1157289370894432, "cleanup_time_ms": 23.31191999837756, "total_time_ms": 24.640874285250902, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 490, "ciphertext_size_bytes": 512, "plaintext_size_bytes": 490, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 2, "actual_chunks": 2, "compression_ratio": 1.04, "throughput_mbps": 0.4, "bytes_per_second": 414736, "chunks_per_second": 1692, "bits_per_second": 3317888, "decryption_rate_per_second": 846.4, "decryption_time_ms": 1.1814744211733341, "chunk_processing_time_ms": 0.5907372105866671, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0014438629150390625, "efficiency_ratio": 0.0}, {"input_size": 735, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705356.5184174, "execution_time_ms": 1.666952483355999, "setup_time_ms": 0.1354701817035675, "cleanup_time_ms": 22.965074982494116, "total_time_ms": 24.767497647553682, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.19921875, "memory_increment_mb": 0.01171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 735, "ciphertext_size_bytes": 768, "plaintext_size_bytes": 735, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 3, "actual_chunks": 3, "compression_ratio": 1.04, "throughput_mbps": 0.42, "bytes_per_second": 435381, "chunks_per_second": 1777, "bits_per_second": 3483055, "decryption_rate_per_second": 592.36, "decryption_time_ms": 1.6881730407476425, "chunk_processing_time_ms": 0.5627243469158808, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=3, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0019216537475585938, "efficiency_ratio": 0.16398111979166666}, {"input_size": 980, "algorithm_name": "RSA-2048-PKCS1v15-Decrypt", "timestamp": 1753705356.7101133, "execution_time_ms": 2.2368209436535835, "setup_time_ms": 0.16679195687174797, "cleanup_time_ms": 22.849703207612038, "total_time_ms": 25.25331610813737, "baseline_memory_mb": 421.19921875, "peak_memory_mb": 421.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 980, "ciphertext_size_bytes": 1024, "plaintext_size_bytes": 980, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 4, "actual_chunks": 4, "compression_ratio": 1.04, "throughput_mbps": 0.38, "bytes_per_second": 402103, "chunks_per_second": 1641, "bits_per_second": 3216828, "decryption_rate_per_second": 410.31, "decryption_time_ms": 2.437183167785406, "chunk_processing_time_ms": 0.6092957919463515, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=4, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.002399444580078125, "efficiency_ratio": 0.0}]