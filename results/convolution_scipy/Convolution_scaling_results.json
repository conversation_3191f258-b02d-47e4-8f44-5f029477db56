[{"input_size": 50, "algorithm_name": "Convolution", "timestamp": 1753651399.672199, "execution_time_ms": 0.17065415158867836, "setup_time_ms": 0.07619895040988922, "cleanup_time_ms": 29.65381694957614, "total_time_ms": 29.900670051574707, "baseline_memory_mb": 460.16796875, "peak_memory_mb": 460.16796875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "50×50", "kernel_size": "5×5", "output_size": "46×46", "convolution_type": "scipy", "input_elements": 2500, "kernel_elements": 25, "output_elements": 2116, "theoretical_operations": 52900, "operations_per_output": 25, "reduction_ratio": 0.8464}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.03814697265625, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "Convolution", "timestamp": 1753651399.8918123, "execution_time_ms": 3.7608958780765533, "setup_time_ms": 1.4226669445633888, "cleanup_time_ms": 29.950983822345734, "total_time_ms": 35.134546644985676, "baseline_memory_mb": 460.16796875, "peak_memory_mb": 461.66796875, "memory_increment_mb": 1.5, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "250×250", "kernel_size": "5×5", "output_size": "246×246", "convolution_type": "scipy", "input_elements": 62500, "kernel_elements": 25, "output_elements": 60516, "theoretical_operations": 1512900, "operations_per_output": 25, "reduction_ratio": 0.968256}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.95367431640625, "efficiency_ratio": 0.6357828776041666}, {"input_size": 450, "algorithm_name": "Convolution", "timestamp": 1753651400.1359298, "execution_time_ms": 12.327656522393227, "setup_time_ms": 4.642454907298088, "cleanup_time_ms": 30.05179390311241, "total_time_ms": 47.021905332803726, "baseline_memory_mb": 461.66796875, "peak_memory_mb": 464.81640625, "memory_increment_mb": 3.1484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "450×450", "kernel_size": "5×5", "output_size": "446×446", "convolution_type": "scipy", "input_elements": 202500, "kernel_elements": 25, "output_elements": 198916, "theoretical_operations": 4972900, "operations_per_output": 25, "reduction_ratio": 0.9823012345679012}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 3.08990478515625, "efficiency_ratio": 0.9814089640198511}, {"input_size": 650, "algorithm_name": "Convolution", "timestamp": 1753651400.4487443, "execution_time_ms": 25.046508945524693, "setup_time_ms": 9.568382985889912, "cleanup_time_ms": 29.986844398081303, "total_time_ms": 64.6017363294959, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 470.0, "memory_increment_mb": 9.41015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "650×650", "kernel_size": "5×5", "output_size": "646×646", "convolution_type": "scipy", "input_elements": 422500, "kernel_elements": 25, "output_elements": 417316, "theoretical_operations": 10432900, "operations_per_output": 25, "reduction_ratio": 0.9877301775147929}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 6.44683837890625, "efficiency_ratio": 0.6850936591946866}, {"input_size": 850, "algorithm_name": "Convolution", "timestamp": 1753651400.873469, "execution_time_ms": 44.420750718563795, "setup_time_ms": 16.184881329536438, "cleanup_time_ms": 30.128599610179663, "total_time_ms": 90.7342316582799, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 476.65234375, "memory_increment_mb": 16.0625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "850×850", "kernel_size": "5×5", "output_size": "846×846", "convolution_type": "scipy", "input_elements": 722500, "kernel_elements": 25, "output_elements": 715716, "theoretical_operations": 17892900, "operations_per_output": 25, "reduction_ratio": 0.9906103806228373}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 11.02447509765625, "efficiency_ratio": 0.6863486442120622}, {"input_size": 1050, "algorithm_name": "Convolution", "timestamp": 1753651401.4606361, "execution_time_ms": 65.34946672618389, "setup_time_ms": 24.058387614786625, "cleanup_time_ms": 30.067165847867727, "total_time_ms": 119.47502018883824, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 485.55078125, "memory_increment_mb": 24.9609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1050×1050", "kernel_size": "5×5", "output_size": "1046×1046", "convolution_type": "scipy", "input_elements": 1102500, "kernel_elements": 25, "output_elements": 1094116, "theoretical_operations": 27352900, "operations_per_output": 25, "reduction_ratio": 0.9923954648526077}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 16.82281494140625, "efficiency_ratio": 0.6739656690140845}, {"input_size": 1250, "algorithm_name": "Convolution", "timestamp": 1753651402.234402, "execution_time_ms": 95.45832620933652, "setup_time_ms": 30.57668311521411, "cleanup_time_ms": 30.303121078759432, "total_time_ms": 156.33813040331006, "baseline_memory_mb": 477.16015625, "peak_memory_mb": 496.02734375, "memory_increment_mb": 18.8671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1250×1250", "kernel_size": "5×5", "output_size": "1246×1246", "convolution_type": "scipy", "input_elements": 1562500, "kernel_elements": 25, "output_elements": 1552516, "theoretical_operations": 38812900, "operations_per_output": 25, "reduction_ratio": 0.99361024}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 23.84185791015625, "efficiency_ratio": 1.26366783126294}, {"input_size": 1450, "algorithm_name": "Convolution", "timestamp": 1753651403.2547393, "execution_time_ms": 124.89710636436939, "setup_time_ms": 45.322290156036615, "cleanup_time_ms": 30.477556865662336, "total_time_ms": 200.69695338606834, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 508.18359375, "memory_increment_mb": 47.59375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1450×1450", "kernel_size": "5×5", "output_size": "1446×1446", "convolution_type": "scipy", "input_elements": 2102500, "kernel_elements": 25, "output_elements": 2090916, "theoretical_operations": 52272900, "operations_per_output": 25, "reduction_ratio": 0.994490368608799}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 32.08160400390625, "efficiency_ratio": 0.6740717847176625}, {"input_size": 1650, "algorithm_name": "Convolution", "timestamp": 1753651404.5379586, "execution_time_ms": 162.4501991085708, "setup_time_ms": 53.27658914029598, "cleanup_time_ms": 30.629021115601063, "total_time_ms": 246.35580936446786, "baseline_memory_mb": 492.37109375, "peak_memory_mb": 522.44140625, "memory_increment_mb": 30.0703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1650×1650", "kernel_size": "5×5", "output_size": "1646×1646", "convolution_type": "scipy", "input_elements": 2722500, "kernel_elements": 25, "output_elements": 2709316, "theoretical_operations": 67732900, "operations_per_output": 25, "reduction_ratio": 0.9951573921028466}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 41.54205322265625, "efficiency_ratio": 1.3814972233047544}, {"input_size": 1850, "algorithm_name": "Convolution", "timestamp": 1753651406.1287918, "execution_time_ms": 203.42544233426452, "setup_time_ms": 73.5715813934803, "cleanup_time_ms": 30.684747267514467, "total_time_ms": 307.6817709952593, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 538.47265625, "memory_increment_mb": 77.8828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1850×1850", "kernel_size": "5×5", "output_size": "1846×1846", "convolution_type": "scipy", "input_elements": 3422500, "kernel_elements": 25, "output_elements": 3407716, "theoretical_operations": 85192900, "operations_per_output": 25, "reduction_ratio": 0.9956803506208911}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 52.22320556640625, "efficiency_ratio": 0.6705356918948742}, {"input_size": 2050, "algorithm_name": "Convolution", "timestamp": 1753651408.0767214, "execution_time_ms": 251.81822888553143, "setup_time_ms": 82.29319890961051, "cleanup_time_ms": 30.844980850815773, "total_time_ms": 364.9564086459577, "baseline_memory_mb": 512.46484375, "peak_memory_mb": 556.40234375, "memory_increment_mb": 43.9375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2050×2050", "kernel_size": "5×5", "output_size": "2046×2046", "convolution_type": "scipy", "input_elements": 4202500, "kernel_elements": 25, "output_elements": 4186116, "theoretical_operations": 104652900, "operations_per_output": 25, "reduction_ratio": 0.9961013682331945}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 64.12506103515625, "efficiency_ratio": 1.4594608485953058}, {"input_size": 2250, "algorithm_name": "Convolution", "timestamp": 1753651410.415554, "execution_time_ms": 309.66627234593034, "setup_time_ms": 109.14553888142109, "cleanup_time_ms": 44.746635016053915, "total_time_ms": 463.55844624340534, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 537.703125, "memory_increment_mb": 77.11328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2250×2250", "kernel_size": "5×5", "output_size": "2246×2246", "convolution_type": "scipy", "input_elements": 5062500, "kernel_elements": 25, "output_elements": 5044516, "theoretical_operations": 126112900, "operations_per_output": 25, "reduction_ratio": 0.9964476049382716}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 77.24761962890625, "efficiency_ratio": 1.0017420913327593}, {"input_size": 2450, "algorithm_name": "Convolution", "timestamp": 1753651413.2969677, "execution_time_ms": 366.82309648022056, "setup_time_ms": 129.20986721292138, "cleanup_time_ms": 44.67651713639498, "total_time_ms": 540.7094808295369, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 552.03515625, "memory_increment_mb": 91.4453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2450×2450", "kernel_size": "5×5", "output_size": "2446×2446", "convolution_type": "scipy", "input_elements": 6002500, "kernel_elements": 25, "output_elements": 5982916, "theoretical_operations": 149572900, "operations_per_output": 25, "reduction_ratio": 0.9967373594335693}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 91.59088134765625, "efficiency_ratio": 1.001591867791542}, {"input_size": 2650, "algorithm_name": "Convolution", "timestamp": 1753651416.6577888, "execution_time_ms": 429.7247037291527, "setup_time_ms": 150.81435814499855, "cleanup_time_ms": 44.33293826878071, "total_time_ms": 624.8720001429319, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 567.5859375, "memory_increment_mb": 106.99609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2650×2650", "kernel_size": "5×5", "output_size": "2646×2646", "convolution_type": "scipy", "input_elements": 7022500, "kernel_elements": 25, "output_elements": 7001316, "theoretical_operations": 175032900, "operations_per_output": 25, "reduction_ratio": 0.9969834104663582}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 107.15484619140625, "efficiency_ratio": 1.001483721842941}, {"input_size": 2850, "algorithm_name": "Convolution", "timestamp": 1753651420.5394049, "execution_time_ms": 496.3168503716588, "setup_time_ms": 174.24366110935807, "cleanup_time_ms": 44.999671168625355, "total_time_ms": 715.5601826496422, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 584.359375, "memory_increment_mb": 123.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2850×2850", "kernel_size": "5×5", "output_size": "2846×2846", "convolution_type": "scipy", "input_elements": 8122500, "kernel_elements": 25, "output_elements": 8099716, "theoretical_operations": 202492900, "operations_per_output": 25, "reduction_ratio": 0.9971949522930132}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 123.93951416015625, "efficiency_ratio": 1.0013733825153859}, {"input_size": 3050, "algorithm_name": "Convolution", "timestamp": 1753651424.9795835, "execution_time_ms": 568.6862172558904, "setup_time_ms": 199.34756169095635, "cleanup_time_ms": 45.08492397144437, "total_time_ms": 813.1187029182911, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 602.3515625, "memory_increment_mb": 141.76171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3050×3050", "kernel_size": "5×5", "output_size": "3046×3046", "convolution_type": "scipy", "input_elements": 9302500, "kernel_elements": 25, "output_elements": 9278116, "theoretical_operations": 231952900, "operations_per_output": 25, "reduction_ratio": 0.9973787691480784}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 141.94488525390625, "efficiency_ratio": 1.0012920731035242}, {"input_size": 3250, "algorithm_name": "Convolution", "timestamp": 1753651430.025489, "execution_time_ms": 645.5614678561687, "setup_time_ms": 226.27581330016255, "cleanup_time_ms": 44.84744183719158, "total_time_ms": 916.6847229935229, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 621.56640625, "memory_increment_mb": 160.9765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3250×3250", "kernel_size": "5×5", "output_size": "3246×3246", "convolution_type": "scipy", "input_elements": 10562500, "kernel_elements": 25, "output_elements": 10536516, "theoretical_operations": 263412900, "operations_per_output": 25, "reduction_ratio": 0.9975399763313609}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 161.17095947265625, "efficiency_ratio": 1.0012076104100947}, {"input_size": 3450, "algorithm_name": "Convolution", "timestamp": 1753651435.7105575, "execution_time_ms": 727.2390058264136, "setup_time_ms": 255.0395536236465, "cleanup_time_ms": 45.26096861809492, "total_time_ms": 1027.539528068155, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 642.00390625, "memory_increment_mb": 181.4140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3450×3450", "kernel_size": "5×5", "output_size": "3446×3446", "convolution_type": "scipy", "input_elements": 11902500, "kernel_elements": 25, "output_elements": 11874916, "theoretical_operations": 296872900, "operations_per_output": 25, "reduction_ratio": 0.9976825036756983}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 181.61773681640625, "efficiency_ratio": 1.001122704125576}, {"input_size": 3650, "algorithm_name": "Convolution", "timestamp": 1753651442.0834317, "execution_time_ms": 813.9590047299862, "setup_time_ms": 283.67914305999875, "cleanup_time_ms": 44.52038509771228, "total_time_ms": 1142.1585328876972, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 663.65625, "memory_increment_mb": 203.06640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3650×3650", "kernel_size": "5×5", "output_size": "3646×3646", "convolution_type": "scipy", "input_elements": 13322500, "kernel_elements": 25, "output_elements": 13293316, "theoretical_operations": 332332900, "operations_per_output": 25, "reduction_ratio": 0.997809420153875}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 203.28521728515625, "efficiency_ratio": 1.0010775343849188}, {"input_size": 3850, "algorithm_name": "Convolution", "timestamp": 1753651449.1739583, "execution_time_ms": 906.6815400496125, "setup_time_ms": 317.9808030836284, "cleanup_time_ms": 44.64748315513134, "total_time_ms": 1269.3098262883723, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 686.53515625, "memory_increment_mb": 225.9453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3850×3850", "kernel_size": "5×5", "output_size": "3846×3846", "convolution_type": "scipy", "input_elements": 14822500, "kernel_elements": 25, "output_elements": 14791716, "theoretical_operations": 369792900, "operations_per_output": 25, "reduction_ratio": 0.9979231573621185}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 226.17340087890625, "efficiency_ratio": 1.0010094848898725}, {"input_size": 4050, "algorithm_name": "Convolution", "timestamp": 1753651457.0378299, "execution_time_ms": 1002.4362169206142, "setup_time_ms": 351.7978950403631, "cleanup_time_ms": 44.99694285914302, "total_time_ms": 1399.2310548201203, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 710.62890625, "memory_increment_mb": 250.0390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4050×4050", "kernel_size": "5×5", "output_size": "4046×4046", "convolution_type": "scipy", "input_elements": 16402500, "kernel_elements": 25, "output_elements": 16370116, "theoretical_operations": 409252900, "operations_per_output": 25, "reduction_ratio": 0.9980256668190824}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 250.28228759765625, "efficiency_ratio": 1.0009727483986877}, {"input_size": 4250, "algorithm_name": "Convolution", "timestamp": 1753651465.7038302, "execution_time_ms": 1104.1951487772167, "setup_time_ms": 384.43075586110353, "cleanup_time_ms": 44.814201071858406, "total_time_ms": 1533.4401057101786, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 735.9453125, "memory_increment_mb": 275.35546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4250×4250", "kernel_size": "5×5", "output_size": "4246×4246", "convolution_type": "scipy", "input_elements": 18062500, "kernel_elements": 25, "output_elements": 18028516, "theoretical_operations": 450712900, "operations_per_output": 25, "reduction_ratio": 0.9981185328719723}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 275.61187744140625, "efficiency_ratio": 1.0009311915705552}, {"input_size": 4450, "algorithm_name": "Convolution", "timestamp": 1753651475.215854, "execution_time_ms": 1210.2552009746432, "setup_time_ms": 424.0066227503121, "cleanup_time_ms": 45.06363999098539, "total_time_ms": 1679.3254637159407, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 762.484375, "memory_increment_mb": 301.89453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4450×4450", "kernel_size": "5×5", "output_size": "4446×4446", "convolution_type": "scipy", "input_elements": 19802500, "kernel_elements": 25, "output_elements": 19766916, "theoretical_operations": 494172900, "operations_per_output": 25, "reduction_ratio": 0.9982030551698018}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 302.16217041015625, "efficiency_ratio": 1.0008865319919777}, {"input_size": 4650, "algorithm_name": "Convolution", "timestamp": 1753651485.6173744, "execution_time_ms": 1321.6246445663273, "setup_time_ms": 463.27723981812596, "cleanup_time_ms": 45.01872416585684, "total_time_ms": 1829.9206085503101, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 790.2421875, "memory_increment_mb": 329.65234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4650×4650", "kernel_size": "5×5", "output_size": "4646×4646", "convolution_type": "scipy", "input_elements": 21622500, "kernel_elements": 25, "output_elements": 21585316, "theoretical_operations": 539632900, "operations_per_output": 25, "reduction_ratio": 0.9982803098624118}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 329.93316650390625, "efficiency_ratio": 1.0008518754962021}, {"input_size": 4850, "algorithm_name": "Convolution", "timestamp": 1753651496.9461532, "execution_time_ms": 1437.9555275663733, "setup_time_ms": 503.76794021576643, "cleanup_time_ms": 44.75477710366249, "total_time_ms": 1986.4782448858023, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 819.22265625, "memory_increment_mb": 358.6328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4850×4850", "kernel_size": "5×5", "output_size": "4846×4846", "convolution_type": "scipy", "input_elements": 23522500, "kernel_elements": 25, "output_elements": 23483716, "theoretical_operations": 587092900, "operations_per_output": 25, "reduction_ratio": 0.9983511956637262}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 358.92486572265625, "efficiency_ratio": 1.000814351650147}, {"input_size": 5050, "algorithm_name": "Convolution", "timestamp": 1753651509.247726, "execution_time_ms": 1558.2059039734304, "setup_time_ms": 546.3137850165367, "cleanup_time_ms": 45.55532103404403, "total_time_ms": 2150.075010024011, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 849.421875, "memory_increment_mb": 388.83203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5050×5050", "kernel_size": "5×5", "output_size": "5046×5046", "convolution_type": "scipy", "input_elements": 25502500, "kernel_elements": 25, "output_elements": 25462116, "theoretical_operations": 636552900, "operations_per_output": 25, "reduction_ratio": 0.99841646897363}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 389.13726806640625, "efficiency_ratio": 1.000785009443345}, {"input_size": 5250, "algorithm_name": "Convolution", "timestamp": 1753651522.5581849, "execution_time_ms": 1684.7647943533957, "setup_time_ms": 590.214944910258, "cleanup_time_ms": 45.2983477152884, "total_time_ms": 2320.278086978942, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 880.84765625, "memory_increment_mb": 420.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5250×5250", "kernel_size": "5×5", "output_size": "5246×5246", "convolution_type": "scipy", "input_elements": 27562500, "kernel_elements": 25, "output_elements": 27520516, "theoretical_operations": 688012900, "operations_per_output": 25, "reduction_ratio": 0.9984767709750567}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 420.57037353515625, "efficiency_ratio": 1.000743736406224}, {"input_size": 5450, "algorithm_name": "Convolution", "timestamp": 1753651536.9214149, "execution_time_ms": 1815.092123299837, "setup_time_ms": 636.0400291159749, "cleanup_time_ms": 45.09774874895811, "total_time_ms": 2496.22990116477, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 913.484375, "memory_increment_mb": 452.89453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5450×5450", "kernel_size": "5×5", "output_size": "5446×5446", "convolution_type": "scipy", "input_elements": 29702500, "kernel_elements": 25, "output_elements": 29658916, "theoretical_operations": 741472900, "operations_per_output": 25, "reduction_ratio": 0.9985326487669388}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 453.22418212890625, "efficiency_ratio": 1.000727875600521}, {"input_size": 5650, "algorithm_name": "Convolution", "timestamp": 1753651552.3797479, "execution_time_ms": 1950.6503743119538, "setup_time_ms": 683.80456417799, "cleanup_time_ms": 45.09392799809575, "total_time_ms": 2679.5488664880395, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 947.34765625, "memory_increment_mb": 486.7578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5650×5650", "kernel_size": "5×5", "output_size": "5646×5646", "convolution_type": "scipy", "input_elements": 31922500, "kernel_elements": 25, "output_elements": 31877316, "theoretical_operations": 796932900, "operations_per_output": 25, "reduction_ratio": 0.9985845720103376}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 487.09869384765625, "efficiency_ratio": 1.0007003099670972}, {"input_size": 5850, "algorithm_name": "Convolution", "timestamp": 1753651568.9663608, "execution_time_ms": 2091.37349370867, "setup_time_ms": 734.4245910644531, "cleanup_time_ms": 45.54999899119139, "total_time_ms": 2871.3480837643147, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 982.4296875, "memory_increment_mb": 521.83984375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5850×5850", "kernel_size": "5×5", "output_size": "5846×5846", "convolution_type": "scipy", "input_elements": 34222500, "kernel_elements": 25, "output_elements": 34175716, "theoretical_operations": 854392900, "operations_per_output": 25, "reduction_ratio": 0.9986329461611513}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 522.1939086914062, "efficiency_ratio": 1.0006784934988135}, {"input_size": 6050, "algorithm_name": "Convolution", "timestamp": 1753651586.7326639, "execution_time_ms": 2237.793970387429, "setup_time_ms": 783.8726751506329, "cleanup_time_ms": 45.261139050126076, "total_time_ms": 3066.927784588188, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1018.734375, "memory_increment_mb": 558.14453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6050×6050", "kernel_size": "5×5", "output_size": "6046×6046", "convolution_type": "scipy", "input_elements": 36602500, "kernel_elements": 25, "output_elements": 36554116, "theoretical_operations": 913852900, "operations_per_output": 25, "reduction_ratio": 0.9986781230790247}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 558.5098266601562, "efficiency_ratio": 1.0006544817510585}, {"input_size": 6250, "algorithm_name": "Convolution", "timestamp": 1753651605.7117634, "execution_time_ms": 2387.2718463651836, "setup_time_ms": 836.0863928683102, "cleanup_time_ms": 44.90779293701053, "total_time_ms": 3268.2660321705043, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1056.2578125, "memory_increment_mb": 595.66796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6250×6250", "kernel_size": "5×5", "output_size": "6246×6246", "convolution_type": "scipy", "input_elements": 39062500, "kernel_elements": 25, "output_elements": 39012516, "theoretical_operations": 975312900, "operations_per_output": 25, "reduction_ratio": 0.9987204096}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 596.0464477539062, "efficiency_ratio": 1.0006353858588375}, {"input_size": 6450, "algorithm_name": "Convolution", "timestamp": 1753651625.9424684, "execution_time_ms": 2544.508129544556, "setup_time_ms": 890.9245212562382, "cleanup_time_ms": 45.48320407047868, "total_time_ms": 3480.915854871273, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1095.00390625, "memory_increment_mb": 634.4140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6450×6450", "kernel_size": "5×5", "output_size": "6446×6446", "convolution_type": "scipy", "input_elements": 41602500, "kernel_elements": 25, "output_elements": 41550916, "theoretical_operations": 1038772900, "operations_per_output": 25, "reduction_ratio": 0.9987600745147527}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 634.8037719726562, "efficiency_ratio": 1.0006142825257065}, {"input_size": 6650, "algorithm_name": "Convolution", "timestamp": 1753651647.481746, "execution_time_ms": 2714.0551982447505, "setup_time_ms": 946.8021960929036, "cleanup_time_ms": 45.06194172427058, "total_time_ms": 3705.9193360619247, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1134.97265625, "memory_increment_mb": 674.3828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6650×6650", "kernel_size": "5×5", "output_size": "6646×6646", "convolution_type": "scipy", "input_elements": 44222500, "kernel_elements": 25, "output_elements": 44169316, "theoretical_operations": 1104232900, "operations_per_output": 25, "reduction_ratio": 0.9987973542879756}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 674.7817993164062, "efficiency_ratio": 1.0005916325401698}, {"input_size": 6850, "algorithm_name": "Convolution", "timestamp": 1753651670.408914, "execution_time_ms": 2881.57792231068, "setup_time_ms": 1004.2500039562583, "cleanup_time_ms": 45.08549300953746, "total_time_ms": 3930.913419276476, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1176.15625, "memory_increment_mb": 715.56640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6850×6850", "kernel_size": "5×5", "output_size": "6846×6846", "convolution_type": "scipy", "input_elements": 46922500, "kernel_elements": 25, "output_elements": 46867716, "theoretical_operations": 1171692900, "operations_per_output": 25, "reduction_ratio": 0.9988324577761202}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 715.9805297851562, "efficiency_ratio": 1.0005787352949205}, {"input_size": 7050, "algorithm_name": "Convolution", "timestamp": 1753651694.7267313, "execution_time_ms": 3053.3453124575317, "setup_time_ms": 1064.8974678479135, "cleanup_time_ms": 45.17138469964266, "total_time_ms": 4163.414165005088, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1218.56640625, "memory_increment_mb": 757.9765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7050×7050", "kernel_size": "5×5", "output_size": "7046×7046", "convolution_type": "scipy", "input_elements": 49702500, "kernel_elements": 25, "output_elements": 49646116, "theoretical_operations": 1241152900, "operations_per_output": 25, "reduction_ratio": 0.998865570142347}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 758.3999633789062, "efficiency_ratio": 1.0005585936292143}, {"input_size": 7250, "algorithm_name": "Convolution", "timestamp": 1753651720.4717128, "execution_time_ms": 3227.8730794787407, "setup_time_ms": 1126.072240062058, "cleanup_time_ms": 45.42161710560322, "total_time_ms": 4399.366936646402, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1262.19140625, "memory_increment_mb": 801.6015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7250×7250", "kernel_size": "5×5", "output_size": "7246×7246", "convolution_type": "scipy", "input_elements": 52562500, "kernel_elements": 25, "output_elements": 52504516, "theoretical_operations": 1312612900, "operations_per_output": 25, "reduction_ratio": 0.9988968561236623}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 802.0401000976562, "efficiency_ratio": 1.0005470767750109}, {"input_size": 7450, "algorithm_name": "Convolution", "timestamp": 1753651747.6754937, "execution_time_ms": 3410.5034516192973, "setup_time_ms": 1188.512294087559, "cleanup_time_ms": 45.33365182578564, "total_time_ms": 4644.349397532642, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1307.0390625, "memory_increment_mb": 846.44921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7450×7450", "kernel_size": "5×5", "output_size": "7446×7446", "convolution_type": "scipy", "input_elements": 55502500, "kernel_elements": 25, "output_elements": 55442916, "theoretical_operations": 1386072900, "operations_per_output": 25, "reduction_ratio": 0.9989264627719472}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 846.9009399414062, "efficiency_ratio": 1.0005336660267385}, {"input_size": 7650, "algorithm_name": "Convolution", "timestamp": 1753651776.3970306, "execution_time_ms": 3593.059573508799, "setup_time_ms": 1252.851510886103, "cleanup_time_ms": 45.065850019454956, "total_time_ms": 4890.976934414357, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1353.109375, "memory_increment_mb": 892.51953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7650×7650", "kernel_size": "5×5", "output_size": "7646×7646", "convolution_type": "scipy", "input_elements": 58522500, "kernel_elements": 25, "output_elements": 58461316, "theoretical_operations": 1461532900, "operations_per_output": 25, "reduction_ratio": 0.9989545217651331}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 892.9824829101562, "efficiency_ratio": 1.0005187019935664}, {"input_size": 7850, "algorithm_name": "Convolution", "timestamp": 1753651806.6430423, "execution_time_ms": 3782.463426887989, "setup_time_ms": 1320.8780246786773, "cleanup_time_ms": 45.17592582851648, "total_time_ms": 5148.517377395183, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1400.3984375, "memory_increment_mb": 939.80859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7850×7850", "kernel_size": "5×5", "output_size": "7846×7846", "convolution_type": "scipy", "input_elements": 61622500, "kernel_elements": 25, "output_elements": 61559716, "theoretical_operations": 1538992900, "operations_per_output": 25, "reduction_ratio": 0.998981151365167}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 940.2847290039062, "efficiency_ratio": 1.0005066300277234}, {"input_size": 8050, "algorithm_name": "Convolution", "timestamp": 1753651838.4751728, "execution_time_ms": 3978.2040077261627, "setup_time_ms": 1386.7890969850123, "cleanup_time_ms": 45.60039797797799, "total_time_ms": 5410.593502689153, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1448.91015625, "memory_increment_mb": 988.3203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8050×8050", "kernel_size": "5×5", "output_size": "8046×8046", "convolution_type": "scipy", "input_elements": 64802500, "kernel_elements": 25, "output_elements": 64738116, "theoretical_operations": 1618452900, "operations_per_output": 25, "reduction_ratio": 0.9990064580841789}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 988.8076782226562, "efficiency_ratio": 1.0004931252717284}, {"input_size": 8250, "algorithm_name": "Convolution", "timestamp": 1753651871.9361818, "execution_time_ms": 4177.030411083251, "setup_time_ms": 1458.4982609376311, "cleanup_time_ms": 45.56741006672382, "total_time_ms": 5681.096082087606, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1498.640625, "memory_increment_mb": 1038.05078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8250×8250", "kernel_size": "5×5", "output_size": "8246×8246", "convolution_type": "scipy", "input_elements": 68062500, "kernel_elements": 25, "output_elements": 67996516, "theoretical_operations": 1699912900, "operations_per_output": 25, "reduction_ratio": 0.9990305381083563}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1038.5513305664062, "efficiency_ratio": 1.0004822011846122}, {"input_size": 8450, "algorithm_name": "Convolution", "timestamp": 1753651907.057962, "execution_time_ms": 4383.894096128643, "setup_time_ms": 1528.6092134192586, "cleanup_time_ms": 45.4473658464849, "total_time_ms": 5957.950675394386, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1549.59765625, "memory_increment_mb": 1089.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8450×8450", "kernel_size": "5×5", "output_size": "8446×8446", "convolution_type": "scipy", "input_elements": 71402500, "kernel_elements": 25, "output_elements": 71334916, "theoretical_operations": 1783372900, "operations_per_output": 25, "reduction_ratio": 0.9990534785196596}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1089.5156860351562, "efficiency_ratio": 1.0004663635369064}, {"input_size": 8650, "algorithm_name": "Convolution", "timestamp": 1753651943.901559, "execution_time_ms": 4592.955980543047, "setup_time_ms": 1602.6723589748144, "cleanup_time_ms": 45.68382818251848, "total_time_ms": 6241.31216770038, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1601.765625, "memory_increment_mb": 1141.17578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8650×8650", "kernel_size": "5×5", "output_size": "8646×8646", "convolution_type": "scipy", "input_elements": 74822500, "kernel_elements": 25, "output_elements": 74753316, "theoretical_operations": 1868832900, "operations_per_output": 25, "reduction_ratio": 0.9990753583480905}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1141.7007446289062, "efficiency_ratio": 1.0004600197336218}, {"input_size": 8850, "algorithm_name": "Convolution", "timestamp": 1753651982.493582, "execution_time_ms": 4807.158888038248, "setup_time_ms": 1677.3402309045196, "cleanup_time_ms": 45.5261180177331, "total_time_ms": 6530.0252369605005, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1655.16015625, "memory_increment_mb": 1194.5703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8850×8850", "kernel_size": "5×5", "output_size": "8846×8846", "convolution_type": "scipy", "input_elements": 78322500, "kernel_elements": 25, "output_elements": 78251716, "theoretical_operations": 1956292900, "operations_per_output": 25, "reduction_ratio": 0.9990962494813113}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1195.1065063476562, "efficiency_ratio": 1.000448859177267}, {"input_size": 9050, "algorithm_name": "Convolution", "timestamp": 1753652022.8705945, "execution_time_ms": 5027.333435602486, "setup_time_ms": 1753.183457069099, "cleanup_time_ms": 45.99741334095597, "total_time_ms": 6826.514306012541, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1709.7734375, "memory_increment_mb": 1249.18359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9050×9050", "kernel_size": "5×5", "output_size": "9046×9046", "convolution_type": "scipy", "input_elements": 81902500, "kernel_elements": 25, "output_elements": 81830116, "theoretical_operations": 2045752900, "operations_per_output": 25, "reduction_ratio": 0.9991162174536797}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1249.7329711914062, "efficiency_ratio": 1.00043978919044}, {"input_size": 9250, "algorithm_name": "Convolution", "timestamp": 1753652065.0787742, "execution_time_ms": 5250.497572869062, "setup_time_ms": 1831.8018917925656, "cleanup_time_ms": 45.96777772530913, "total_time_ms": 7128.267242386937, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1765.609375, "memory_increment_mb": 1305.01953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9250×9250", "kernel_size": "5×5", "output_size": "9246×9246", "convolution_type": "scipy", "input_elements": 85562500, "kernel_elements": 25, "output_elements": 85488516, "theoretical_operations": 2137212900, "operations_per_output": 25, "reduction_ratio": 0.9991353221329438}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1305.5801391601562, "efficiency_ratio": 1.0004295781762127}, {"input_size": 9450, "algorithm_name": "Convolution", "timestamp": 1753652109.1766531, "execution_time_ms": 5479.8480577766895, "setup_time_ms": 1904.7683607786894, "cleanup_time_ms": 46.07610683888197, "total_time_ms": 7430.692525394261, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1822.6640625, "memory_increment_mb": 1362.07421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9450×9450", "kernel_size": "5×5", "output_size": "9446×9446", "convolution_type": "scipy", "input_elements": 89302500, "kernel_elements": 25, "output_elements": 89226916, "theoretical_operations": 2230672900, "operations_per_output": 25, "reduction_ratio": 0.9991536183197559}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1362.6480102539062, "efficiency_ratio": 1.0004212630237086}, {"input_size": 9650, "algorithm_name": "Convolution", "timestamp": 1753652155.1541798, "execution_time_ms": 5715.749260224402, "setup_time_ms": 1994.020903017372, "cleanup_time_ms": 45.63628928735852, "total_time_ms": 7755.406452529132, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1880.94140625, "memory_increment_mb": 1420.3515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9650×9650", "kernel_size": "5×5", "output_size": "9646×9646", "convolution_type": "scipy", "input_elements": 93122500, "kernel_elements": 25, "output_elements": 93045316, "theoretical_operations": 2326132900, "operations_per_output": 25, "reduction_ratio": 0.9991711562726516}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1420.9365844726562, "efficiency_ratio": 1.0004118853304365}, {"input_size": 9850, "algorithm_name": "Convolution", "timestamp": 1753652203.1102815, "execution_time_ms": 5953.1444281339645, "setup_time_ms": 2072.136087808758, "cleanup_time_ms": 46.254724729806185, "total_time_ms": 8071.535240672529, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 1940.44140625, "memory_increment_mb": 1479.8515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9850×9850", "kernel_size": "5×5", "output_size": "9846×9846", "convolution_type": "scipy", "input_elements": 97022500, "kernel_elements": 25, "output_elements": 96943716, "theoretical_operations": 2423592900, "operations_per_output": 25, "reduction_ratio": 0.9991879821690844}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1480.4458618164062, "efficiency_ratio": 1.0004015938702677}]