input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_max_error,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_vector_norm,custom_result_norm,custom_matrix_frobenius_norm,custom_theoretical_flops,custom_storage_format,custom_memory_efficiency
50,SpMV-scipy,1753651021.5918906,0.0560,0.6112,26.6779,27.3451,428.95,429.21,0.26,0.00,,,,"O(nnz) [N=50, nnz≈2]","O(nnz+N) [N=50, memory≈102 elements]",0.00,0.0038,True,0.0,4,spmv_scipy,50×50,scipy_sparse,2,0.0008,0.001,6.783556821459465,1.373042161818696,0.967850774584777,4,CSR,0.0008
250,SpMV-scipy,1753651021.8067615,0.0589,1.3582,26.2219,27.6390,429.21,430.38,1.17,0.00,,,,"O(nnz) [N=250, nnz≈62]","O(nnz+N) [N=250, memory≈562 elements]",0.01,0.0047,True,0.0,124,spmv_scipy,250×250,scipy_sparse,62,0.000992,0.001,16.88362723607604,4.582649702694678,4.586130276537388,124,CSR,0.000992
450,SpMV-scipy,1753651021.9968066,0.0608,4.0430,26.2631,30.3668,430.38,430.51,0.13,0.00,,,,"O(nnz) [N=450, nnz≈202]","O(nnz+N) [N=450, memory≈1,102 elements]",0.01,0.0821,True,0.0,404,spmv_scipy,450×450,scipy_sparse,202,0.000997530864197531,0.001,21.634848763597688,8.363569132396771,7.9435773924718385,404,CSR,0.000997530864197531
650,SpMV-scipy,1753651022.1860337,0.0637,8.6171,26.2098,34.8907,430.51,430.52,0.00,0.00,,,,"O(nnz) [N=650, nnz≈422]","O(nnz+N) [N=650, memory≈1,722 elements]",0.02,4.4111,True,0.0,844,spmv_scipy,650×650,scipy_sparse,422,0.0009988165680473373,0.001,25.380860338292496,11.61329527849103,11.750884029077534,844,CSR,0.0009988165680473373
850,SpMV-scipy,1753651022.3810568,0.0637,13.8572,26.4089,40.3298,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=850, nnz≈722]","O(nnz+N) [N=850, memory≈2,422 elements]",0.02,6.2666,True,0.0,1444,spmv_scipy,850×850,scipy_sparse,722,0.0009993079584775086,0.001,29.427626890862854,15.565508395293504,15.89706125727268,1444,CSR,0.0009993079584775086
1050,SpMV-scipy,1753651022.582652,0.0655,18.5462,25.0073,43.6190,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=1050, nnz≈1,102]","O(nnz+N) [N=1050, memory≈3,202 elements]",0.03,0.0000,True,0.0,2204,spmv_scipy,1050×1050,scipy_sparse,1102,0.000999546485260771,0.001,32.651925258613105,19.567200018296,19.223272864412007,2204,CSR,0.000999546485260771
1250,SpMV-scipy,1753651022.794986,0.0687,29.2629,24.8950,54.2266,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=1250, nnz≈1,562]","O(nnz+N) [N=1250, memory≈4,062 elements]",0.04,0.0000,True,0.0,3124,spmv_scipy,1250×1250,scipy_sparse,1562,0.00099968,0.001,35.14421573401539,22.39941167417662,22.712494715366486,3124,CSR,0.00099968
1450,SpMV-scipy,1753651023.0074835,0.0696,37.8153,24.8080,62.6929,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=1450, nnz≈2,102]","O(nnz+N) [N=1450, memory≈5,002 elements]",0.05,0.0000,True,0.0,4204,spmv_scipy,1450×1450,scipy_sparse,2102,0.0009997621878715815,0.001,38.550670331462456,27.60513019581863,26.505666339562488,4204,CSR,0.0009997621878715815
1650,SpMV-scipy,1753651023.2313666,0.0730,50.7130,24.8505,75.6365,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=1650, nnz≈2,722]","O(nnz+N) [N=1650, memory≈6,022 elements]",0.06,0.0000,True,0.0,5444,spmv_scipy,1650×1650,scipy_sparse,2722,0.0009998163452708908,0.001,40.591144553837054,29.28372718931155,30.225579271724918,5444,CSR,0.0009998163452708908
1850,SpMV-scipy,1753651023.4690104,0.0854,73.4043,24.1347,97.6244,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=1850, nnz≈3,422]","O(nnz+N) [N=1850, memory≈7,122 elements]",0.07,0.0000,True,0.0,6844,spmv_scipy,1850×1850,scipy_sparse,3422,0.000999853907962016,0.001,45.045364915447614,36.14263430228665,33.65993578889743,6844,CSR,0.000999853907962016
2050,SpMV-scipy,1753651023.7328691,0.0716,93.3654,24.1617,117.5986,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=2050, nnz≈4,202]","O(nnz+N) [N=2050, memory≈8,302 elements]",0.09,0.0000,True,0.0,8404,spmv_scipy,2050×2050,scipy_sparse,4202,0.0009998810232004758,0.001,46.19595439026895,37.658477440215194,37.48613433687853,8404,CSR,0.0009998810232004758
2250,SpMV-scipy,1753651024.0103498,0.0954,125.0622,24.2083,149.3659,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=2250, nnz≈5,062]","O(nnz+N) [N=2250, memory≈9,562 elements]",0.10,0.0000,True,0.0,10124,spmv_scipy,2250×2250,scipy_sparse,5062,0.0009999012345679012,0.001,47.5020825253188,42.02192960303974,40.95713754351974,10124,CSR,0.0009999012345679012
2450,SpMV-scipy,1753651024.338176,0.0751,151.9153,24.2054,176.1958,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=2450, nnz≈6,002]","O(nnz+N) [N=2450, memory≈10,902 elements]",0.12,0.0000,True,0.0,12004,spmv_scipy,2450×2450,scipy_sparse,6002,0.0009999167013744273,0.001,49.316353044828716,44.92806648569283,44.78207818184049,12004,CSR,0.0009999167013744273
2650,SpMV-scipy,1753651024.675363,0.0787,196.3739,24.2272,220.6798,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=2650, nnz≈7,022]","O(nnz+N) [N=2650, memory≈12,322 elements]",0.13,0.0000,True,0.0,14044,spmv_scipy,2650×2650,scipy_sparse,7022,0.000999928800284799,0.001,52.038825388060985,50.179090014840305,48.457872470530184,14044,CSR,0.000999928800284799
2850,SpMV-scipy,1753651025.056813,0.0817,223.9216,24.2287,248.2319,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=2850, nnz≈8,122]","O(nnz+N) [N=2850, memory≈13,822 elements]",0.15,0.0000,True,0.0,16244,spmv_scipy,2850×2850,scipy_sparse,8122,0.0009999384425977223,0.001,53.409375255615544,52.51271733715601,52.291443126806115,16244,CSR,0.0009999384425977223
3050,SpMV-scipy,1753651025.4661474,0.0843,283.1422,24.2032,307.4296,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=3050, nnz≈9,302]","O(nnz+N) [N=3050, memory≈15,402 elements]",0.16,0.0000,True,0.0,18604,spmv_scipy,3050×3050,scipy_sparse,9302,0.0009999462510077936,0.001,54.607707720486395,54.91933809015556,56.04942778103253,18604,CSR,0.0009999462510077936
3250,SpMV-scipy,1753651025.9478633,0.0891,324.9519,34.9428,359.9838,430.52,430.52,0.00,0.00,,,,"O(nnz) [N=3250, nnz≈10,562]","O(nnz+N) [N=3250, memory≈17,062 elements]",0.18,0.0000,True,0.0,21124,spmv_scipy,3250×3250,scipy_sparse,10562,0.0009999526627218936,0.001,57.61434760285121,59.1153463075734,58.78833999717575,21124,CSR,0.0009999526627218936
3450,SpMV-scipy,1753651026.4881768,0.0895,390.8045,34.4330,425.3270,430.89,431.02,0.12,0.00,,,,"O(nnz) [N=3450, nnz≈11,902]","O(nnz+N) [N=3450, memory≈18,802 elements]",0.20,1.6161,True,0.0,23804,spmv_scipy,3450×3450,scipy_sparse,11902,0.0009999579920184835,0.001,57.90849114602314,61.548414310716076,62.74092447035354,23804,CSR,0.0009999579920184835
3650,SpMV-scipy,1753651027.0877824,0.0918,451.9564,68.7030,520.7512,431.02,431.19,0.17,0.00,,,,"O(nnz) [N=3650, nnz≈13,322]","O(nnz+N) [N=3650, memory≈20,622 elements]",0.22,1.2921,True,0.0,26644,spmv_scipy,3650×3650,scipy_sparse,13322,0.000999962469506474,0.001,60.656839744899436,67.13590156782197,66.72281469372443,26644,CSR,0.000999962469506474
3850,SpMV-scipy,1753651027.7849543,0.0960,503.9272,38.3957,542.4190,431.19,431.19,0.00,0.00,,,,"O(nnz) [N=3850, nnz≈14,822]","O(nnz+N) [N=3850, memory≈22,522 elements]",0.24,0.0000,True,0.0,29644,spmv_scipy,3850×3850,scipy_sparse,14822,0.000999966267498735,0.001,61.65598497231728,71.1519117846674,70.51571858381011,29644,CSR,0.000999966267498735
4050,SpMV-scipy,1753651028.5331738,0.1012,563.2436,38.4047,601.7495,431.19,431.25,0.06,0.00,,,,"O(nnz) [N=4050, nnz≈16,402]","O(nnz+N) [N=4050, memory≈24,502 elements]",0.26,4.2393,True,0.0,32804,spmv_scipy,4050×4050,scipy_sparse,16402,0.0009999695168419448,0.001,63.88466274120535,75.51863045760584,73.86085717611373,32804,CSR,0.0009999695168419448
4250,SpMV-scipy,1753651029.3242376,0.1118,638.1788,38.4220,676.7126,431.25,431.39,0.14,0.00,,,,"O(nnz) [N=4250, nnz≈18,062]","O(nnz+N) [N=4250, memory≈26,562 elements]",0.29,2.1048,True,0.0,36124,spmv_scipy,4250×4250,scipy_sparse,18062,0.0009999723183391003,0.001,65.06442504966482,78.7193620163003,77.53087906413936,36124,CSR,0.0009999723183391003
4450,SpMV-scipy,1753651030.215533,0.1083,715.1606,38.4738,753.7427,431.39,431.39,0.00,0.00,,,,"O(nnz) [N=4450, nnz≈19,802]","O(nnz+N) [N=4450, memory≈28,702 elements]",0.31,0.0000,True,0.0,39604,spmv_scipy,4450×4450,scipy_sparse,19802,0.0009999747506627952,0.001,67.13818535715164,82.16973513162883,81.35818536720897,39604,CSR,0.0009999747506627952
4650,SpMV-scipy,1753651031.158615,0.1086,798.8346,38.3370,837.2802,431.39,431.46,0.07,0.00,,,,"O(nnz) [N=4650, nnz≈21,622]","O(nnz+N) [N=4650, memory≈30,922 elements]",0.34,4.5290,True,0.0,43244,spmv_scipy,4650×4650,scipy_sparse,21622,0.000999976875939415,0.001,69.24514267974816,86.04587259558217,84.95493142158804,43244,CSR,0.000999976875939415
4850,SpMV-scipy,1753651032.1863434,0.1141,880.5636,38.5852,919.2630,431.46,431.62,0.16,0.00,,,,"O(nnz) [N=4850, nnz≈23,522]","O(nnz+N) [N=4850, memory≈33,222 elements]",0.36,2.3149,True,0.0,47044,spmv_scipy,4850×4850,scipy_sparse,23522,0.0009999787437559784,0.001,69.26687220136174,88.79581218870963,88.60268048306173,47044,CSR,0.0009999787437559784
5050,SpMV-scipy,1753651033.3006635,0.1179,957.1078,38.3419,995.5677,431.62,431.62,0.00,0.00,,,,"O(nnz) [N=5050, nnz≈25,502]","O(nnz+N) [N=5050, memory≈35,602 elements]",0.39,0.0000,True,0.0,51004,spmv_scipy,5050×5050,scipy_sparse,25502,0.000999980394079012,0.001,70.8429205084888,90.19906667882685,91.99319206849596,51004,CSR,0.000999980394079012
5250,SpMV-scipy,1753651034.4873984,0.1193,1039.3055,38.4578,1077.8827,431.62,431.70,0.08,0.00,,,,"O(nnz) [N=5250, nnz≈27,562]","O(nnz+N) [N=5250, memory≈38,062 elements]",0.42,5.0659,True,0.0,55124,spmv_scipy,5250×5250,scipy_sparse,27562,0.0009999818594104309,0.001,73.1274829600724,96.88697380078737,95.85391256012664,55124,CSR,0.0009999818594104309
5450,SpMV-scipy,1753651035.7543452,0.1266,1131.9595,38.4215,1170.5075,431.70,431.88,0.18,0.00,,,,"O(nnz) [N=5450, nnz≈29,702]","O(nnz+N) [N=5450, memory≈40,602 elements]",0.44,2.5251,True,0.0,59404,spmv_scipy,5450×5450,scipy_sparse,29702,0.0009999831664001346,0.001,73.52219164774858,98.75646685024151,99.8186648714177,59404,CSR,0.0009999831664001346
5650,SpMV-scipy,1753651037.1232696,0.1258,1218.5774,31.4676,1250.1708,431.88,431.88,0.00,0.00,,,,"O(nnz) [N=5650, nnz≈31,922]","O(nnz+N) [N=5650, memory≈43,222 elements]",0.47,0.0000,True,0.0,63844,spmv_scipy,5650×5650,scipy_sparse,31922,0.0009999843370663325,0.001,74.87874472796565,102.26455722161181,103.14727202896616,63844,CSR,0.0009999843370663325
5850,SpMV-scipy,1753651038.5626435,0.1307,1323.1877,31.0524,1354.3708,431.88,432.07,0.19,0.00,,,,"O(nnz) [N=5850, nnz≈34,222]","O(nnz+N) [N=5850, memory≈45,922 elements]",0.50,2.6291,True,0.0,68444,spmv_scipy,5850×5850,scipy_sparse,34222,0.0009999853897289795,0.001,77.20743553439137,108.22475906586982,106.91947295151712,68444,CSR,0.0009999853897289795
6050,SpMV-scipy,1753651040.1038253,0.1377,1429.3478,38.3513,1467.8369,432.07,432.16,0.10,0.00,,,,"O(nnz) [N=6050, nnz≈36,602]","O(nnz+N) [N=6050, memory≈48,702 elements]",0.53,5.4710,True,0.0,73204,spmv_scipy,6050×6050,scipy_sparse,36602,0.0009999863397308927,0.001,78.27931817124151,110.59441275412215,110.41826598827598,73204,CSR,0.0009999863397308927
6250,SpMV-scipy,1753651041.7657104,0.1382,1552.4264,38.3584,1590.9231,432.16,432.16,0.00,0.00,,,,"O(nnz) [N=6250, nnz≈39,062]","O(nnz+N) [N=6250, memory≈51,562 elements]",0.57,0.0000,True,0.0,78124,spmv_scipy,6250×6250,scipy_sparse,39062,0.0009999872,0.001,77.51185945909548,110.02374031957216,114.00223903318908,78124,CSR,0.0009999872
6450,SpMV-scipy,1753651043.5455573,0.1402,1670.4632,38.4920,1709.0953,432.16,432.27,0.11,0.00,,,,"O(nnz) [N=6450, nnz≈41,602]","O(nnz+N) [N=6450, memory≈54,502 elements]",0.60,5.6806,True,0.0,83204,spmv_scipy,6450×6450,scipy_sparse,41602,0.000999987981491497,0.001,80.34325135606315,118.6178254772554,117.51483003014923,83204,CSR,0.000999987981491497
6650,SpMV-scipy,1753651045.443478,0.1468,1789.6489,38.3201,1828.1158,432.27,432.48,0.21,0.00,,,,"O(nnz) [N=6650, nnz≈44,222]","O(nnz+N) [N=6650, memory≈57,522 elements]",0.63,2.9460,True,0.0,88444,spmv_scipy,6650×6650,scipy_sparse,44222,0.0009999886935383572,0.001,80.85066388714088,119.94554106096416,121.62381954538154,88444,CSR,0.0009999886935383572
6850,SpMV-scipy,1753651047.4655771,0.1474,1914.5798,38.8102,1953.5373,432.48,432.48,0.00,0.00,,,,"O(nnz) [N=6850, nnz≈46,922]","O(nnz+N) [N=6850, memory≈60,622 elements]",0.67,0.0000,True,0.0,93844,spmv_scipy,6850×6850,scipy_sparse,46922,0.0009999893441312803,0.001,83.59799104639536,124.36779832413158,124.89920644032549,93844,CSR,0.0009999893441312803
7050,SpMV-scipy,1753651049.6077104,0.1502,2037.7377,38.4013,2076.2892,432.48,432.71,0.23,0.00,,,,"O(nnz) [N=7050, nnz≈49,702]","O(nnz+N) [N=7050, memory≈63,802 elements]",0.70,3.0515,True,0.0,99404,spmv_scipy,7050×7050,scipy_sparse,49702,0.000999989940143856,0.001,82.91132833034067,129.43781799022776,129.2509152117475,99404,CSR,0.000999989940143856
7250,SpMV-scipy,1753651051.882632,0.1550,2151.6054,38.3890,2190.1494,432.71,432.84,0.12,0.00,,,,"O(nnz) [N=7250, nnz≈52,562]","O(nnz+N) [N=7250, memory≈67,062 elements]",0.74,6.1094,True,0.0,105124,spmv_scipy,7250×7250,scipy_sparse,52562,0.0009999904875148632,0.001,84.75721664381759,132.46006736051834,132.56587037293983,105124,CSR,0.0009999904875148632
7450,SpMV-scipy,1753651054.26687,0.1604,2272.7801,38.4960,2311.4366,432.84,432.84,0.00,0.00,,,,"O(nnz) [N=7450, nnz≈55,502]","O(nnz+N) [N=7450, memory≈70,402 elements]",0.78,0.0000,True,0.0,111004,spmv_scipy,7450×7450,scipy_sparse,55502,0.000999990991396784,0.001,86.2425846424148,133.7028179456156,136.0778642931506,111004,CSR,0.000999990991396784
7650,SpMV-scipy,1753651056.767578,0.1667,2397.0083,38.2097,2435.3846,432.84,432.96,0.12,0.00,,,,"O(nnz) [N=7650, nnz≈58,522]","O(nnz+N) [N=7650, memory≈73,822 elements]",0.82,6.7357,True,0.0,117044,spmv_scipy,7650×7650,scipy_sparse,58522,0.0009999914562775001,0.001,87.36871033712782,139.7727344334873,139.39035344302889,117044,CSR,0.0009999914562775001
7850,SpMV-scipy,1753651059.3922021,0.1646,2505.8631,38.3362,2544.3638,432.96,433.09,0.13,0.00,,,,"O(nnz) [N=7850, nnz≈61,622]","O(nnz+N) [N=7850, memory≈77,322 elements]",0.85,6.6323,True,0.0,123244,spmv_scipy,7850×7850,scipy_sparse,61622,0.0009999918860805713,0.001,87.93370163046609,144.6729862328984,143.94370474453544,123244,CSR,0.0009999918860805713
8050,SpMV-scipy,1753651062.1256862,0.1713,2634.8319,38.2506,2673.2538,433.09,433.21,0.13,0.00,,,,"O(nnz) [N=8050, nnz≈64,802]","O(nnz+N) [N=8050, memory≈80,902 elements]",0.90,6.9442,True,0.0,129604,spmv_scipy,8050×8050,scipy_sparse,64802,0.000999992284248293,0.001,89.2075160929974,146.9175135825103,146.58109552114036,129604,CSR,0.000999992284248293
8250,SpMV-scipy,1753651064.9880648,0.1731,2775.3110,38.4101,2813.8942,433.21,433.48,0.27,0.00,,,,"O(nnz) [N=8250, nnz≈68,062]","O(nnz+N) [N=8250, memory≈84,562 elements]",0.94,3.4737,True,0.0,136124,spmv_scipy,8250×8250,scipy_sparse,68062,0.0009999926538108357,0.001,90.16400998071336,150.3375071514213,150.61910746106483,136124,CSR,0.0009999926538108357
8450,SpMV-scipy,1753651068.0001507,0.1776,2941.5932,38.3292,2980.0999,433.48,433.48,0.00,0.00,,,,"O(nnz) [N=8450, nnz≈71,402]","O(nnz+N) [N=8450, memory≈88,302 elements]",0.98,0.0000,True,0.0,142804,spmv_scipy,8450×8450,scipy_sparse,71402,0.0009999929974440671,0.001,91.8205874767425,152.4236024952062,153.83856889231853,142804,CSR,0.0009999929974440671
8650,SpMV-scipy,1753651071.1694403,0.1842,3103.0532,38.2261,3141.4635,433.48,433.62,0.14,0.00,,,,"O(nnz) [N=8650, nnz≈74,822]","O(nnz+N) [N=8650, memory≈92,122 elements]",1.02,7.2623,True,0.0,149644,spmv_scipy,8650×8650,scipy_sparse,74822,0.0009999933175181262,0.001,92.49037703670662,157.82246334400728,158.11235072832795,149644,CSR,0.0009999933175181262
8850,SpMV-scipy,1753651074.499832,0.1874,3273.4571,38.3135,3311.9580,433.62,433.77,0.14,0.00,,,,"O(nnz) [N=8850, nnz≈78,322]","O(nnz+N) [N=8850, memory≈96,022 elements]",1.07,7.3695,True,0.0,156644,spmv_scipy,8850×8850,scipy_sparse,78322,0.000999993616138402,0.001,94.04842098142971,163.49001197323872,161.87755405802338,156644,CSR,0.000999993616138402
9050,SpMV-scipy,1753651078.000981,0.1920,3439.1656,38.4490,3477.8066,433.77,434.07,0.30,0.00,,,,"O(nnz) [N=9050, nnz≈81,902]","O(nnz+N) [N=9050, memory≈100,002 elements]",1.11,3.7387,True,0.0,163804,spmv_scipy,9050×9050,scipy_sparse,81902,0.0009999938951802448,0.001,95.67391718673528,168.0673389102802,165.3789159520791,163804,CSR,0.0009999938951802448
9250,SpMV-scipy,1753651081.6731813,0.1982,3608.9477,38.2590,3647.4049,434.07,434.07,0.00,0.00,,,,"O(nnz) [N=9250, nnz≈85,562]","O(nnz+N) [N=9250, memory≈104,062 elements]",1.16,0.0000,True,0.0,171124,spmv_scipy,9250×9250,scipy_sparse,85562,0.0009999941563184807,0.001,95.30771492708696,166.61509817382557,168.9244173426714,171124,CSR,0.0009999941563184807
9450,SpMV-scipy,1753651085.509569,0.2012,3772.4718,38.2083,3810.8812,434.07,434.22,0.15,0.00,,,,"O(nnz) [N=9450, nnz≈89,302]","O(nnz+N) [N=9450, memory≈108,202 elements]",1.20,7.8916,True,0.0,178604,spmv_scipy,9450×9450,scipy_sparse,89302,0.000999994401052602,0.001,97.06098433200592,170.91873479822317,172.33412894211213,178604,CSR,0.000999994401052602
9650,SpMV-scipy,1753651089.5093982,0.2066,3930.7722,38.4238,3969.4026,434.22,434.38,0.16,0.00,,,,"O(nnz) [N=9650, nnz≈93,122]","O(nnz+N) [N=9650, memory≈112,422 elements]",1.25,7.8034,True,0.0,186244,spmv_scipy,9650×9650,scipy_sparse,93122,0.0009999946307283417,0.001,98.65309033156433,174.37761285366767,176.1979498313833,186244,CSR,0.0009999946307283417
9850,SpMV-scipy,1753651093.6677186,0.2047,4113.3442,38.3220,4151.8709,434.38,434.54,0.16,0.00,,,,"O(nnz) [N=9850, nnz≈97,022]","O(nnz+N) [N=9850, memory≈116,722 elements]",1.30,8.1059,True,0.0,194044,spmv_scipy,9850×9850,scipy_sparse,97022,0.0009999948465562111,0.001,97.68853467303968,177.85984306379845,180.014976011111,194044,CSR,0.0009999948465562111
