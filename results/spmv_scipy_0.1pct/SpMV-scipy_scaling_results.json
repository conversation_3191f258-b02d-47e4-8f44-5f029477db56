[{"input_size": 50, "algorithm_name": "SpMV-scipy", "timestamp": 1753651021.5918906, "execution_time_ms": 0.055978912860155106, "setup_time_ms": 0.6112139672040939, "cleanup_time_ms": 26.67789300903678, "total_time_ms": 27.34508588910103, "baseline_memory_mb": 428.94921875, "peak_memory_mb": 429.20703125, "memory_increment_mb": 0.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 4, "algorithm_type": "spmv_scipy", "matrix_size": "50×50", "implementation": "scipy_sparse", "nnz": 2, "actual_sparsity_ratio": 0.0008, "target_sparsity_ratio": 0.001, "vector_norm": 6.783556821459465, "result_norm": 1.373042161818696, "matrix_frobenius_norm": 0.967850774584777, "theoretical_flops": 4, "storage_format": "CSR", "memory_efficiency": 0.0008}, "theoretical_time_complexity": "O(nnz) [N=50, nnz≈2]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈102 elements]", "theoretical_memory_mb": 0.000980377197265625, "efficiency_ratio": 0.0038026751893939395}, {"input_size": 250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651021.8067615, "execution_time_ms": 0.05886415019631386, "setup_time_ms": 1.3581928797066212, "cleanup_time_ms": 26.221931912004948, "total_time_ms": 27.638988941907883, "baseline_memory_mb": 429.20703125, "peak_memory_mb": 430.37890625, "memory_increment_mb": 1.171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 124, "algorithm_type": "spmv_scipy", "matrix_size": "250×250", "implementation": "scipy_sparse", "nnz": 62, "actual_sparsity_ratio": 0.000992, "target_sparsity_ratio": 0.001, "vector_norm": 16.88362723607604, "result_norm": 4.582649702694678, "matrix_frobenius_norm": 4.586130276537388, "theoretical_flops": 124, "storage_format": "CSR", "memory_efficiency": 0.000992}, "theoretical_time_complexity": "O(nnz) [N=250, nnz≈62]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈562 elements]", "theoretical_memory_mb": 0.005481719970703125, "efficiency_ratio": 0.004677734375}, {"input_size": 450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651021.9968066, "execution_time_ms": 0.060776062309741974, "setup_time_ms": 4.042996559292078, "cleanup_time_ms": 26.263055857270956, "total_time_ms": 30.366828478872776, "baseline_memory_mb": 430.37890625, "peak_memory_mb": 430.51171875, "memory_increment_mb": 0.1328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 404, "algorithm_type": "spmv_scipy", "matrix_size": "450×450", "implementation": "scipy_sparse", "nnz": 202, "actual_sparsity_ratio": 0.000997530864197531, "target_sparsity_ratio": 0.001, "vector_norm": 21.634848763597688, "result_norm": 8.363569132396771, "matrix_frobenius_norm": 7.9435773924718385, "theoretical_flops": 404, "storage_format": "CSR", "memory_efficiency": 0.000997530864197531}, "theoretical_time_complexity": "O(nnz) [N=450, nnz≈202]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈1,102 elements]", "theoretical_memory_mb": 0.010898590087890625, "efficiency_ratio": 0.08205997242647059}, {"input_size": 650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651022.1860337, "execution_time_ms": 0.06371550261974335, "setup_time_ms": 8.617136161774397, "cleanup_time_ms": 26.209847070276737, "total_time_ms": 34.89069873467088, "baseline_memory_mb": 430.51171875, "peak_memory_mb": 430.515625, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 844, "algorithm_type": "spmv_scipy", "matrix_size": "650×650", "implementation": "scipy_sparse", "nnz": 422, "actual_sparsity_ratio": 0.0009988165680473373, "target_sparsity_ratio": 0.001, "vector_norm": 25.380860338292496, "result_norm": 11.61329527849103, "matrix_frobenius_norm": 11.750884029077534, "theoretical_flops": 844, "storage_format": "CSR", "memory_efficiency": 0.0009988165680473373}, "theoretical_time_complexity": "O(nnz) [N=650, nnz≈422]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈1,722 elements]", "theoretical_memory_mb": 0.017230987548828125, "efficiency_ratio": 4.4111328125}, {"input_size": 850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651022.3810568, "execution_time_ms": 0.06367899477481842, "setup_time_ms": 13.85719794780016, "cleanup_time_ms": 26.408923789858818, "total_time_ms": 40.329800732433796, "baseline_memory_mb": 430.515625, "peak_memory_mb": 430.51953125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1444, "algorithm_type": "spmv_scipy", "matrix_size": "850×850", "implementation": "scipy_sparse", "nnz": 722, "actual_sparsity_ratio": 0.0009993079584775086, "target_sparsity_ratio": 0.001, "vector_norm": 29.427626890862854, "result_norm": 15.565508395293504, "matrix_frobenius_norm": 15.89706125727268, "theoretical_flops": 1444, "storage_format": "CSR", "memory_efficiency": 0.0009993079584775086}, "theoretical_time_complexity": "O(nnz) [N=850, nnz≈722]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈2,422 elements]", "theoretical_memory_mb": 0.024478912353515625, "efficiency_ratio": 6.2666015625}, {"input_size": 1050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651022.582652, "execution_time_ms": 0.06553428247570992, "setup_time_ms": 18.546210136264563, "cleanup_time_ms": 25.007287971675396, "total_time_ms": 43.61903239041567, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 2204, "algorithm_type": "spmv_scipy", "matrix_size": "1050×1050", "implementation": "scipy_sparse", "nnz": 1102, "actual_sparsity_ratio": 0.000999546485260771, "target_sparsity_ratio": 0.001, "vector_norm": 32.651925258613105, "result_norm": 19.567200018296, "matrix_frobenius_norm": 19.223272864412007, "theoretical_flops": 2204, "storage_format": "CSR", "memory_efficiency": 0.000999546485260771}, "theoretical_time_complexity": "O(nnz) [N=1050, nnz≈1,102]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈3,202 elements]", "theoretical_memory_mb": 0.032642364501953125, "efficiency_ratio": 0.0}, {"input_size": 1250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651022.794986, "execution_time_ms": 0.06870627403259277, "setup_time_ms": 29.262909665703773, "cleanup_time_ms": 24.894968140870333, "total_time_ms": 54.2265840806067, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 3124, "algorithm_type": "spmv_scipy", "matrix_size": "1250×1250", "implementation": "scipy_sparse", "nnz": 1562, "actual_sparsity_ratio": 0.00099968, "target_sparsity_ratio": 0.001, "vector_norm": 35.14421573401539, "result_norm": 22.39941167417662, "matrix_frobenius_norm": 22.712494715366486, "theoretical_flops": 3124, "storage_format": "CSR", "memory_efficiency": 0.00099968}, "theoretical_time_complexity": "O(nnz) [N=1250, nnz≈1,562]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈4,062 elements]", "theoretical_memory_mb": 0.041721343994140625, "efficiency_ratio": 0.0}, {"input_size": 1450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651023.0074835, "execution_time_ms": 0.06959754973649979, "setup_time_ms": 37.81525511294603, "cleanup_time_ms": 24.808034766465425, "total_time_ms": 62.69288742914796, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 4204, "algorithm_type": "spmv_scipy", "matrix_size": "1450×1450", "implementation": "scipy_sparse", "nnz": 2102, "actual_sparsity_ratio": 0.0009997621878715815, "target_sparsity_ratio": 0.001, "vector_norm": 38.550670331462456, "result_norm": 27.60513019581863, "matrix_frobenius_norm": 26.505666339562488, "theoretical_flops": 4204, "storage_format": "CSR", "memory_efficiency": 0.0009997621878715815}, "theoretical_time_complexity": "O(nnz) [N=1450, nnz≈2,102]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈5,002 elements]", "theoretical_memory_mb": 0.051715850830078125, "efficiency_ratio": 0.0}, {"input_size": 1650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651023.2313666, "execution_time_ms": 0.07300544530153275, "setup_time_ms": 50.71295332163572, "cleanup_time_ms": 24.85053613781929, "total_time_ms": 75.63649490475655, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 5444, "algorithm_type": "spmv_scipy", "matrix_size": "1650×1650", "implementation": "scipy_sparse", "nnz": 2722, "actual_sparsity_ratio": 0.0009998163452708908, "target_sparsity_ratio": 0.001, "vector_norm": 40.591144553837054, "result_norm": 29.28372718931155, "matrix_frobenius_norm": 30.225579271724918, "theoretical_flops": 5444, "storage_format": "CSR", "memory_efficiency": 0.0009998163452708908}, "theoretical_time_complexity": "O(nnz) [N=1650, nnz≈2,722]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈6,022 elements]", "theoretical_memory_mb": 0.06262588500976562, "efficiency_ratio": 0.0}, {"input_size": 1850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651023.4690104, "execution_time_ms": 0.08540237322449684, "setup_time_ms": 73.40430794283748, "cleanup_time_ms": 24.13467364385724, "total_time_ms": 97.62438395991921, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 6844, "algorithm_type": "spmv_scipy", "matrix_size": "1850×1850", "implementation": "scipy_sparse", "nnz": 3422, "actual_sparsity_ratio": 0.000999853907962016, "target_sparsity_ratio": 0.001, "vector_norm": 45.045364915447614, "result_norm": 36.14263430228665, "matrix_frobenius_norm": 33.65993578889743, "theoretical_flops": 6844, "storage_format": "CSR", "memory_efficiency": 0.000999853907962016}, "theoretical_time_complexity": "O(nnz) [N=1850, nnz≈3,422]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈7,122 elements]", "theoretical_memory_mb": 0.07445144653320312, "efficiency_ratio": 0.0}, {"input_size": 2050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651023.7328691, "execution_time_ms": 0.07158694788813591, "setup_time_ms": 93.36536191403866, "cleanup_time_ms": 24.16169922798872, "total_time_ms": 117.59864808991551, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 8404, "algorithm_type": "spmv_scipy", "matrix_size": "2050×2050", "implementation": "scipy_sparse", "nnz": 4202, "actual_sparsity_ratio": 0.0009998810232004758, "target_sparsity_ratio": 0.001, "vector_norm": 46.19595439026895, "result_norm": 37.658477440215194, "matrix_frobenius_norm": 37.48613433687853, "theoretical_flops": 8404, "storage_format": "CSR", "memory_efficiency": 0.0009998810232004758}, "theoretical_time_complexity": "O(nnz) [N=2050, nnz≈4,202]", "theoretical_space_complexity": "O(nnz+N) [N=2050, memory≈8,302 elements]", "theoretical_memory_mb": 0.08719253540039062, "efficiency_ratio": 0.0}, {"input_size": 2250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651024.0103498, "execution_time_ms": 0.09537041187286377, "setup_time_ms": 125.06223283708096, "cleanup_time_ms": 24.20825557783246, "total_time_ms": 149.36585882678628, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 10124, "algorithm_type": "spmv_scipy", "matrix_size": "2250×2250", "implementation": "scipy_sparse", "nnz": 5062, "actual_sparsity_ratio": 0.0009999012345679012, "target_sparsity_ratio": 0.001, "vector_norm": 47.5020825253188, "result_norm": 42.02192960303974, "matrix_frobenius_norm": 40.95713754351974, "theoretical_flops": 10124, "storage_format": "CSR", "memory_efficiency": 0.0009999012345679012}, "theoretical_time_complexity": "O(nnz) [N=2250, nnz≈5,062]", "theoretical_space_complexity": "O(nnz+N) [N=2250, memory≈9,562 elements]", "theoretical_memory_mb": 0.10084915161132812, "efficiency_ratio": 0.0}, {"input_size": 2450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651024.338176, "execution_time_ms": 0.07512131705880165, "setup_time_ms": 151.91529598087072, "cleanup_time_ms": 24.205376859754324, "total_time_ms": 176.19579415768385, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 12004, "algorithm_type": "spmv_scipy", "matrix_size": "2450×2450", "implementation": "scipy_sparse", "nnz": 6002, "actual_sparsity_ratio": 0.0009999167013744273, "target_sparsity_ratio": 0.001, "vector_norm": 49.316353044828716, "result_norm": 44.92806648569283, "matrix_frobenius_norm": 44.78207818184049, "theoretical_flops": 12004, "storage_format": "CSR", "memory_efficiency": 0.0009999167013744273}, "theoretical_time_complexity": "O(nnz) [N=2450, nnz≈6,002]", "theoretical_space_complexity": "O(nnz+N) [N=2450, memory≈10,902 elements]", "theoretical_memory_mb": 0.11542129516601562, "efficiency_ratio": 0.0}, {"input_size": 2650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651024.675363, "execution_time_ms": 0.07870672270655632, "setup_time_ms": 196.37388130649924, "cleanup_time_ms": 24.22723500058055, "total_time_ms": 220.67982302978635, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 14044, "algorithm_type": "spmv_scipy", "matrix_size": "2650×2650", "implementation": "scipy_sparse", "nnz": 7022, "actual_sparsity_ratio": 0.000999928800284799, "target_sparsity_ratio": 0.001, "vector_norm": 52.038825388060985, "result_norm": 50.179090014840305, "matrix_frobenius_norm": 48.457872470530184, "theoretical_flops": 14044, "storage_format": "CSR", "memory_efficiency": 0.000999928800284799}, "theoretical_time_complexity": "O(nnz) [N=2650, nnz≈7,022]", "theoretical_space_complexity": "O(nnz+N) [N=2650, memory≈12,322 elements]", "theoretical_memory_mb": 0.13090896606445312, "efficiency_ratio": 0.0}, {"input_size": 2850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651025.056813, "execution_time_ms": 0.08166143670678139, "setup_time_ms": 223.92159700393677, "cleanup_time_ms": 24.228683672845364, "total_time_ms": 248.2319421134889, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 16244, "algorithm_type": "spmv_scipy", "matrix_size": "2850×2850", "implementation": "scipy_sparse", "nnz": 8122, "actual_sparsity_ratio": 0.0009999384425977223, "target_sparsity_ratio": 0.001, "vector_norm": 53.409375255615544, "result_norm": 52.51271733715601, "matrix_frobenius_norm": 52.291443126806115, "theoretical_flops": 16244, "storage_format": "CSR", "memory_efficiency": 0.0009999384425977223}, "theoretical_time_complexity": "O(nnz) [N=2850, nnz≈8,122]", "theoretical_space_complexity": "O(nnz+N) [N=2850, memory≈13,822 elements]", "theoretical_memory_mb": 0.14731216430664062, "efficiency_ratio": 0.0}, {"input_size": 3050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651025.4661474, "execution_time_ms": 0.08425610139966011, "setup_time_ms": 283.14218297600746, "cleanup_time_ms": 24.203195236623287, "total_time_ms": 307.4296343140304, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 18604, "algorithm_type": "spmv_scipy", "matrix_size": "3050×3050", "implementation": "scipy_sparse", "nnz": 9302, "actual_sparsity_ratio": 0.0009999462510077936, "target_sparsity_ratio": 0.001, "vector_norm": 54.607707720486395, "result_norm": 54.91933809015556, "matrix_frobenius_norm": 56.04942778103253, "theoretical_flops": 18604, "storage_format": "CSR", "memory_efficiency": 0.0009999462510077936}, "theoretical_time_complexity": "O(nnz) [N=3050, nnz≈9,302]", "theoretical_space_complexity": "O(nnz+N) [N=3050, memory≈15,402 elements]", "theoretical_memory_mb": 0.16463088989257812, "efficiency_ratio": 0.0}, {"input_size": 3250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651025.9478633, "execution_time_ms": 0.08912337943911552, "setup_time_ms": 324.95192624628544, "cleanup_time_ms": 34.9427810870111, "total_time_ms": 359.98383071273565, "baseline_memory_mb": 430.5234375, "peak_memory_mb": 430.5234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 21124, "algorithm_type": "spmv_scipy", "matrix_size": "3250×3250", "implementation": "scipy_sparse", "nnz": 10562, "actual_sparsity_ratio": 0.0009999526627218936, "target_sparsity_ratio": 0.001, "vector_norm": 57.61434760285121, "result_norm": 59.1153463075734, "matrix_frobenius_norm": 58.78833999717575, "theoretical_flops": 21124, "storage_format": "CSR", "memory_efficiency": 0.0009999526627218936}, "theoretical_time_complexity": "O(nnz) [N=3250, nnz≈10,562]", "theoretical_space_complexity": "O(nnz+N) [N=3250, memory≈17,062 elements]", "theoretical_memory_mb": 0.18286514282226562, "efficiency_ratio": 0.0}, {"input_size": 3450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651026.4881768, "execution_time_ms": 0.0895356759428978, "setup_time_ms": 390.80446818843484, "cleanup_time_ms": 34.433013293892145, "total_time_ms": 425.3270171582699, "baseline_memory_mb": 430.890625, "peak_memory_mb": 431.015625, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 23804, "algorithm_type": "spmv_scipy", "matrix_size": "3450×3450", "implementation": "scipy_sparse", "nnz": 11902, "actual_sparsity_ratio": 0.0009999579920184835, "target_sparsity_ratio": 0.001, "vector_norm": 57.90849114602314, "result_norm": 61.548414310716076, "matrix_frobenius_norm": 62.74092447035354, "theoretical_flops": 23804, "storage_format": "CSR", "memory_efficiency": 0.0009999579920184835}, "theoretical_time_complexity": "O(nnz) [N=3450, nnz≈11,902]", "theoretical_space_complexity": "O(nnz+N) [N=3450, memory≈18,802 elements]", "theoretical_memory_mb": 0.20201492309570312, "efficiency_ratio": 1.616119384765625}, {"input_size": 3650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651027.0877824, "execution_time_ms": 0.09177587926387787, "setup_time_ms": 451.9564271904528, "cleanup_time_ms": 68.70296411216259, "total_time_ms": 520.7511671818793, "baseline_memory_mb": 431.015625, "peak_memory_mb": 431.1875, "memory_increment_mb": 0.171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 26644, "algorithm_type": "spmv_scipy", "matrix_size": "3650×3650", "implementation": "scipy_sparse", "nnz": 13322, "actual_sparsity_ratio": 0.000999962469506474, "target_sparsity_ratio": 0.001, "vector_norm": 60.656839744899436, "result_norm": 67.13590156782197, "matrix_frobenius_norm": 66.72281469372443, "theoretical_flops": 26644, "storage_format": "CSR", "memory_efficiency": 0.000999962469506474}, "theoretical_time_complexity": "O(nnz) [N=3650, nnz≈13,322]", "theoretical_space_complexity": "O(nnz+N) [N=3650, memory≈20,622 elements]", "theoretical_memory_mb": 0.22208023071289062, "efficiency_ratio": 1.2921031605113635}, {"input_size": 3850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651027.7849543, "execution_time_ms": 0.09602811187505722, "setup_time_ms": 503.92720801755786, "cleanup_time_ms": 38.39573170989752, "total_time_ms": 542.4189678393304, "baseline_memory_mb": 431.1875, "peak_memory_mb": 431.1875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 29644, "algorithm_type": "spmv_scipy", "matrix_size": "3850×3850", "implementation": "scipy_sparse", "nnz": 14822, "actual_sparsity_ratio": 0.000999966267498735, "target_sparsity_ratio": 0.001, "vector_norm": 61.65598497231728, "result_norm": 71.1519117846674, "matrix_frobenius_norm": 70.51571858381011, "theoretical_flops": 29644, "storage_format": "CSR", "memory_efficiency": 0.000999966267498735}, "theoretical_time_complexity": "O(nnz) [N=3850, nnz≈14,822]", "theoretical_space_complexity": "O(nnz+N) [N=3850, memory≈22,522 elements]", "theoretical_memory_mb": 0.24306106567382812, "efficiency_ratio": 0.0}, {"input_size": 4050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651028.5331738, "execution_time_ms": 0.10121632367372513, "setup_time_ms": 563.243571203202, "cleanup_time_ms": 38.40471524745226, "total_time_ms": 601.749502774328, "baseline_memory_mb": 431.1875, "peak_memory_mb": 431.25, "memory_increment_mb": 0.0625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 32804, "algorithm_type": "spmv_scipy", "matrix_size": "4050×4050", "implementation": "scipy_sparse", "nnz": 16402, "actual_sparsity_ratio": 0.0009999695168419448, "target_sparsity_ratio": 0.001, "vector_norm": 63.88466274120535, "result_norm": 75.51863045760584, "matrix_frobenius_norm": 73.86085717611373, "theoretical_flops": 32804, "storage_format": "CSR", "memory_efficiency": 0.0009999695168419448}, "theoretical_time_complexity": "O(nnz) [N=4050, nnz≈16,402]", "theoretical_space_complexity": "O(nnz+N) [N=4050, memory≈24,502 elements]", "theoretical_memory_mb": 0.2649574279785156, "efficiency_ratio": 4.23931884765625}, {"input_size": 4250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651029.3242376, "execution_time_ms": 0.11183992028236389, "setup_time_ms": 638.1788388825953, "cleanup_time_ms": 38.42196799814701, "total_time_ms": 676.7126468010247, "baseline_memory_mb": 431.25, "peak_memory_mb": 431.38671875, "memory_increment_mb": 0.13671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 36124, "algorithm_type": "spmv_scipy", "matrix_size": "4250×4250", "implementation": "scipy_sparse", "nnz": 18062, "actual_sparsity_ratio": 0.0009999723183391003, "target_sparsity_ratio": 0.001, "vector_norm": 65.06442504966482, "result_norm": 78.7193620163003, "matrix_frobenius_norm": 77.53087906413936, "theoretical_flops": 36124, "storage_format": "CSR", "memory_efficiency": 0.0009999723183391003}, "theoretical_time_complexity": "O(nnz) [N=4250, nnz≈18,062]", "theoretical_space_complexity": "O(nnz+N) [N=4250, memory≈26,562 elements]", "theoretical_memory_mb": 0.2877693176269531, "efficiency_ratio": 2.1048270089285714}, {"input_size": 4450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651030.215533, "execution_time_ms": 0.10828636586666107, "setup_time_ms": 715.1605738326907, "cleanup_time_ms": 38.47379935905337, "total_time_ms": 753.7426595576108, "baseline_memory_mb": 431.38671875, "peak_memory_mb": 431.38671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 39604, "algorithm_type": "spmv_scipy", "matrix_size": "4450×4450", "implementation": "scipy_sparse", "nnz": 19802, "actual_sparsity_ratio": 0.0009999747506627952, "target_sparsity_ratio": 0.001, "vector_norm": 67.13818535715164, "result_norm": 82.16973513162883, "matrix_frobenius_norm": 81.35818536720897, "theoretical_flops": 39604, "storage_format": "CSR", "memory_efficiency": 0.0009999747506627952}, "theoretical_time_complexity": "O(nnz) [N=4450, nnz≈19,802]", "theoretical_space_complexity": "O(nnz+N) [N=4450, memory≈28,702 elements]", "theoretical_memory_mb": 0.3114967346191406, "efficiency_ratio": 0.0}, {"input_size": 4650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651031.158615, "execution_time_ms": 0.10858029127120972, "setup_time_ms": 798.8345911726356, "cleanup_time_ms": 38.33699272945523, "total_time_ms": 837.280164193362, "baseline_memory_mb": 431.38671875, "peak_memory_mb": 431.4609375, "memory_increment_mb": 0.07421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 43244, "algorithm_type": "spmv_scipy", "matrix_size": "4650×4650", "implementation": "scipy_sparse", "nnz": 21622, "actual_sparsity_ratio": 0.000999976875939415, "target_sparsity_ratio": 0.001, "vector_norm": 69.24514267974816, "result_norm": 86.04587259558217, "matrix_frobenius_norm": 84.95493142158804, "theoretical_flops": 43244, "storage_format": "CSR", "memory_efficiency": 0.000999976875939415}, "theoretical_time_complexity": "O(nnz) [N=4650, nnz≈21,622]", "theoretical_space_complexity": "O(nnz+N) [N=4650, memory≈30,922 elements]", "theoretical_memory_mb": 0.3361396789550781, "efficiency_ratio": 4.529039884868421}, {"input_size": 4850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651032.1863434, "execution_time_ms": 0.11411923915147781, "setup_time_ms": 880.5636460892856, "cleanup_time_ms": 38.585240021348, "total_time_ms": 919.2630053497851, "baseline_memory_mb": 431.4609375, "peak_memory_mb": 431.6171875, "memory_increment_mb": 0.15625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 47044, "algorithm_type": "spmv_scipy", "matrix_size": "4850×4850", "implementation": "scipy_sparse", "nnz": 23522, "actual_sparsity_ratio": 0.0009999787437559784, "target_sparsity_ratio": 0.001, "vector_norm": 69.26687220136174, "result_norm": 88.79581218870963, "matrix_frobenius_norm": 88.60268048306173, "theoretical_flops": 47044, "storage_format": "CSR", "memory_efficiency": 0.0009999787437559784}, "theoretical_time_complexity": "O(nnz) [N=4850, nnz≈23,522]", "theoretical_space_complexity": "O(nnz+N) [N=4850, memory≈33,222 elements]", "theoretical_memory_mb": 0.3616981506347656, "efficiency_ratio": 2.3148681640625}, {"input_size": 5050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651033.3006635, "execution_time_ms": 0.11794082820415497, "setup_time_ms": 957.1077697910368, "cleanup_time_ms": 38.34194829687476, "total_time_ms": 995.5676589161158, "baseline_memory_mb": 431.6171875, "peak_memory_mb": 431.6171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 51004, "algorithm_type": "spmv_scipy", "matrix_size": "5050×5050", "implementation": "scipy_sparse", "nnz": 25502, "actual_sparsity_ratio": 0.000999980394079012, "target_sparsity_ratio": 0.001, "vector_norm": 70.8429205084888, "result_norm": 90.19906667882685, "matrix_frobenius_norm": 91.99319206849596, "theoretical_flops": 51004, "storage_format": "CSR", "memory_efficiency": 0.000999980394079012}, "theoretical_time_complexity": "O(nnz) [N=5050, nnz≈25,502]", "theoretical_space_complexity": "O(nnz+N) [N=5050, memory≈35,602 elements]", "theoretical_memory_mb": 0.3881721496582031, "efficiency_ratio": 0.0}, {"input_size": 5250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651034.4873984, "execution_time_ms": 0.11934684589505196, "setup_time_ms": 1039.3055342137814, "cleanup_time_ms": 38.457831367850304, "total_time_ms": 1077.8827124275267, "baseline_memory_mb": 431.6171875, "peak_memory_mb": 431.69921875, "memory_increment_mb": 0.08203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 55124, "algorithm_type": "spmv_scipy", "matrix_size": "5250×5250", "implementation": "scipy_sparse", "nnz": 27562, "actual_sparsity_ratio": 0.0009999818594104309, "target_sparsity_ratio": 0.001, "vector_norm": 73.1274829600724, "result_norm": 96.88697380078737, "matrix_frobenius_norm": 95.85391256012664, "theoretical_flops": 55124, "storage_format": "CSR", "memory_efficiency": 0.0009999818594104309}, "theoretical_time_complexity": "O(nnz) [N=5250, nnz≈27,562]", "theoretical_space_complexity": "O(nnz+N) [N=5250, memory≈38,062 elements]", "theoretical_memory_mb": 0.4155616760253906, "efficiency_ratio": 5.065894717261905}, {"input_size": 5450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651035.7543452, "execution_time_ms": 0.1265520229935646, "setup_time_ms": 1131.959460210055, "cleanup_time_ms": 38.42147486284375, "total_time_ms": 1170.5074870958924, "baseline_memory_mb": 431.69921875, "peak_memory_mb": 431.875, "memory_increment_mb": 0.17578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 59404, "algorithm_type": "spmv_scipy", "matrix_size": "5450×5450", "implementation": "scipy_sparse", "nnz": 29702, "actual_sparsity_ratio": 0.0009999831664001346, "target_sparsity_ratio": 0.001, "vector_norm": 73.52219164774858, "result_norm": 98.75646685024151, "matrix_frobenius_norm": 99.8186648714177, "theoretical_flops": 59404, "storage_format": "CSR", "memory_efficiency": 0.0009999831664001346}, "theoretical_time_complexity": "O(nnz) [N=5450, nnz≈29,702]", "theoretical_space_complexity": "O(nnz+N) [N=5450, memory≈40,602 elements]", "theoretical_memory_mb": 0.4438667297363281, "efficiency_ratio": 2.5251085069444446}, {"input_size": 5650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651037.1232696, "execution_time_ms": 0.12576011940836906, "setup_time_ms": 1218.5774222016335, "cleanup_time_ms": 31.46761329844594, "total_time_ms": 1250.1707956194878, "baseline_memory_mb": 431.875, "peak_memory_mb": 431.875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 63844, "algorithm_type": "spmv_scipy", "matrix_size": "5650×5650", "implementation": "scipy_sparse", "nnz": 31922, "actual_sparsity_ratio": 0.0009999843370663325, "target_sparsity_ratio": 0.001, "vector_norm": 74.87874472796565, "result_norm": 102.26455722161181, "matrix_frobenius_norm": 103.14727202896616, "theoretical_flops": 63844, "storage_format": "CSR", "memory_efficiency": 0.0009999843370663325}, "theoretical_time_complexity": "O(nnz) [N=5650, nnz≈31,922]", "theoretical_space_complexity": "O(nnz+N) [N=5650, memory≈43,222 elements]", "theoretical_memory_mb": 0.4730873107910156, "efficiency_ratio": 0.0}, {"input_size": 5850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651038.5626435, "execution_time_ms": 0.1307259313762188, "setup_time_ms": 1323.1876636855304, "cleanup_time_ms": 31.05236915871501, "total_time_ms": 1354.3707587756217, "baseline_memory_mb": 431.875, "peak_memory_mb": 432.06640625, "memory_increment_mb": 0.19140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 68444, "algorithm_type": "spmv_scipy", "matrix_size": "5850×5850", "implementation": "scipy_sparse", "nnz": 34222, "actual_sparsity_ratio": 0.0009999853897289795, "target_sparsity_ratio": 0.001, "vector_norm": 77.20743553439137, "result_norm": 108.22475906586982, "matrix_frobenius_norm": 106.91947295151712, "theoretical_flops": 68444, "storage_format": "CSR", "memory_efficiency": 0.0009999853897289795}, "theoretical_time_complexity": "O(nnz) [N=5850, nnz≈34,222]", "theoretical_space_complexity": "O(nnz+N) [N=5850, memory≈45,922 elements]", "theoretical_memory_mb": 0.5032234191894531, "efficiency_ratio": 2.629085618622449}, {"input_size": 6050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651040.1038253, "execution_time_ms": 0.13772854581475258, "setup_time_ms": 1429.3478331528604, "cleanup_time_ms": 38.351311814039946, "total_time_ms": 1467.836873512715, "baseline_memory_mb": 432.06640625, "peak_memory_mb": 432.1640625, "memory_increment_mb": 0.09765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 73204, "algorithm_type": "spmv_scipy", "matrix_size": "6050×6050", "implementation": "scipy_sparse", "nnz": 36602, "actual_sparsity_ratio": 0.0009999863397308927, "target_sparsity_ratio": 0.001, "vector_norm": 78.27931817124151, "result_norm": 110.59441275412215, "matrix_frobenius_norm": 110.41826598827598, "theoretical_flops": 73204, "storage_format": "CSR", "memory_efficiency": 0.0009999863397308927}, "theoretical_time_complexity": "O(nnz) [N=6050, nnz≈36,602]", "theoretical_space_complexity": "O(nnz+N) [N=6050, memory≈48,702 elements]", "theoretical_memory_mb": 0.5342750549316406, "efficiency_ratio": 5.4709765625}, {"input_size": 6250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651041.7657104, "execution_time_ms": 0.13819672167301178, "setup_time_ms": 1552.4264429695904, "cleanup_time_ms": 38.35842199623585, "total_time_ms": 1590.9230616874993, "baseline_memory_mb": 432.1640625, "peak_memory_mb": 432.1640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 78124, "algorithm_type": "spmv_scipy", "matrix_size": "6250×6250", "implementation": "scipy_sparse", "nnz": 39062, "actual_sparsity_ratio": 0.0009999872, "target_sparsity_ratio": 0.001, "vector_norm": 77.51185945909548, "result_norm": 110.02374031957216, "matrix_frobenius_norm": 114.00223903318908, "theoretical_flops": 78124, "storage_format": "CSR", "memory_efficiency": 0.0009999872}, "theoretical_time_complexity": "O(nnz) [N=6250, nnz≈39,062]", "theoretical_space_complexity": "O(nnz+N) [N=6250, memory≈51,562 elements]", "theoretical_memory_mb": 0.5662422180175781, "efficiency_ratio": 0.0}, {"input_size": 6450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651043.5455573, "execution_time_ms": 0.14016041532158852, "setup_time_ms": 1670.4631932079792, "cleanup_time_ms": 38.491987623274326, "total_time_ms": 1709.0953412465751, "baseline_memory_mb": 432.1640625, "peak_memory_mb": 432.26953125, "memory_increment_mb": 0.10546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 83204, "algorithm_type": "spmv_scipy", "matrix_size": "6450×6450", "implementation": "scipy_sparse", "nnz": 41602, "actual_sparsity_ratio": 0.000999987981491497, "target_sparsity_ratio": 0.001, "vector_norm": 80.34325135606315, "result_norm": 118.6178254772554, "matrix_frobenius_norm": 117.51483003014923, "theoretical_flops": 83204, "storage_format": "CSR", "memory_efficiency": 0.000999987981491497}, "theoretical_time_complexity": "O(nnz) [N=6450, nnz≈41,602]", "theoretical_space_complexity": "O(nnz+N) [N=6450, memory≈54,502 elements]", "theoretical_memory_mb": 0.5991249084472656, "efficiency_ratio": 5.680591724537037}, {"input_size": 6650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651045.443478, "execution_time_ms": 0.14683017507195473, "setup_time_ms": 1789.648867212236, "cleanup_time_ms": 38.32013066858053, "total_time_ms": 1828.1158280558884, "baseline_memory_mb": 432.26953125, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.21484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 88444, "algorithm_type": "spmv_scipy", "matrix_size": "6650×6650", "implementation": "scipy_sparse", "nnz": 44222, "actual_sparsity_ratio": 0.0009999886935383572, "target_sparsity_ratio": 0.001, "vector_norm": 80.85066388714088, "result_norm": 119.94554106096416, "matrix_frobenius_norm": 121.62381954538154, "theoretical_flops": 88444, "storage_format": "CSR", "memory_efficiency": 0.0009999886935383572}, "theoretical_time_complexity": "O(nnz) [N=6650, nnz≈44,222]", "theoretical_space_complexity": "O(nnz+N) [N=6650, memory≈57,522 elements]", "theoretical_memory_mb": 0.6329231262207031, "efficiency_ratio": 2.9459694602272726}, {"input_size": 6850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651047.4655771, "execution_time_ms": 0.14736410230398178, "setup_time_ms": 1914.57978496328, "cleanup_time_ms": 38.81017304956913, "total_time_ms": 1953.537322115153, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 93844, "algorithm_type": "spmv_scipy", "matrix_size": "6850×6850", "implementation": "scipy_sparse", "nnz": 46922, "actual_sparsity_ratio": 0.0009999893441312803, "target_sparsity_ratio": 0.001, "vector_norm": 83.59799104639536, "result_norm": 124.36779832413158, "matrix_frobenius_norm": 124.89920644032549, "theoretical_flops": 93844, "storage_format": "CSR", "memory_efficiency": 0.0009999893441312803}, "theoretical_time_complexity": "O(nnz) [N=6850, nnz≈46,922]", "theoretical_space_complexity": "O(nnz+N) [N=6850, memory≈60,622 elements]", "theoretical_memory_mb": 0.6676368713378906, "efficiency_ratio": 0.0}, {"input_size": 7050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651049.6077104, "execution_time_ms": 0.1501970924437046, "setup_time_ms": 2037.7376568503678, "cleanup_time_ms": 38.40131824836135, "total_time_ms": 2076.289172191173, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.71484375, "memory_increment_mb": 0.23046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 99404, "algorithm_type": "spmv_scipy", "matrix_size": "7050×7050", "implementation": "scipy_sparse", "nnz": 49702, "actual_sparsity_ratio": 0.000999989940143856, "target_sparsity_ratio": 0.001, "vector_norm": 82.91132833034067, "result_norm": 129.43781799022776, "matrix_frobenius_norm": 129.2509152117475, "theoretical_flops": 99404, "storage_format": "CSR", "memory_efficiency": 0.000999989940143856}, "theoretical_time_complexity": "O(nnz) [N=7050, nnz≈49,702]", "theoretical_space_complexity": "O(nnz+N) [N=7050, memory≈63,802 elements]", "theoretical_memory_mb": 0.7032661437988281, "efficiency_ratio": 3.0514598781779663}, {"input_size": 7250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651051.882632, "execution_time_ms": 0.15502674505114555, "setup_time_ms": 2151.6054291278124, "cleanup_time_ms": 38.3889633230865, "total_time_ms": 2190.14941919595, "baseline_memory_mb": 432.71484375, "peak_memory_mb": 432.8359375, "memory_increment_mb": 0.12109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 105124, "algorithm_type": "spmv_scipy", "matrix_size": "7250×7250", "implementation": "scipy_sparse", "nnz": 52562, "actual_sparsity_ratio": 0.0009999904875148632, "target_sparsity_ratio": 0.001, "vector_norm": 84.75721664381759, "result_norm": 132.46006736051834, "matrix_frobenius_norm": 132.56587037293983, "theoretical_flops": 105124, "storage_format": "CSR", "memory_efficiency": 0.0009999904875148632}, "theoretical_time_complexity": "O(nnz) [N=7250, nnz≈52,562]", "theoretical_space_complexity": "O(nnz+N) [N=7250, memory≈67,062 elements]", "theoretical_memory_mb": 0.7398109436035156, "efficiency_ratio": 6.109406502016129}, {"input_size": 7450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651054.26687, "execution_time_ms": 0.16043484210968018, "setup_time_ms": 2272.7801450528204, "cleanup_time_ms": 38.49599603563547, "total_time_ms": 2311.4365759305656, "baseline_memory_mb": 432.8359375, "peak_memory_mb": 432.8359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 111004, "algorithm_type": "spmv_scipy", "matrix_size": "7450×7450", "implementation": "scipy_sparse", "nnz": 55502, "actual_sparsity_ratio": 0.000999990991396784, "target_sparsity_ratio": 0.001, "vector_norm": 86.2425846424148, "result_norm": 133.7028179456156, "matrix_frobenius_norm": 136.0778642931506, "theoretical_flops": 111004, "storage_format": "CSR", "memory_efficiency": 0.000999990991396784}, "theoretical_time_complexity": "O(nnz) [N=7450, nnz≈55,502]", "theoretical_space_complexity": "O(nnz+N) [N=7450, memory≈70,402 elements]", "theoretical_memory_mb": 0.7772712707519531, "efficiency_ratio": 0.0}, {"input_size": 7650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651056.767578, "execution_time_ms": 0.16665197908878326, "setup_time_ms": 2397.008329164237, "cleanup_time_ms": 38.20965625345707, "total_time_ms": 2435.3846373967826, "baseline_memory_mb": 432.8359375, "peak_memory_mb": 432.95703125, "memory_increment_mb": 0.12109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 117044, "algorithm_type": "spmv_scipy", "matrix_size": "7650×7650", "implementation": "scipy_sparse", "nnz": 58522, "actual_sparsity_ratio": 0.0009999914562775001, "target_sparsity_ratio": 0.001, "vector_norm": 87.36871033712782, "result_norm": 139.7727344334873, "matrix_frobenius_norm": 139.39035344302889, "theoretical_flops": 117044, "storage_format": "CSR", "memory_efficiency": 0.0009999914562775001}, "theoretical_time_complexity": "O(nnz) [N=7650, nnz≈58,522]", "theoretical_space_complexity": "O(nnz+N) [N=7650, memory≈73,822 elements]", "theoretical_memory_mb": 0.8156471252441406, "efficiency_ratio": 6.73566658266129}, {"input_size": 7850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651059.3922021, "execution_time_ms": 0.16456982120871544, "setup_time_ms": 2505.863052792847, "cleanup_time_ms": 38.33615966141224, "total_time_ms": 2544.363782275468, "baseline_memory_mb": 432.95703125, "peak_memory_mb": 433.0859375, "memory_increment_mb": 0.12890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 123244, "algorithm_type": "spmv_scipy", "matrix_size": "7850×7850", "implementation": "scipy_sparse", "nnz": 61622, "actual_sparsity_ratio": 0.0009999918860805713, "target_sparsity_ratio": 0.001, "vector_norm": 87.93370163046609, "result_norm": 144.6729862328984, "matrix_frobenius_norm": 143.94370474453544, "theoretical_flops": 123244, "storage_format": "CSR", "memory_efficiency": 0.0009999918860805713}, "theoretical_time_complexity": "O(nnz) [N=7850, nnz≈61,622]", "theoretical_space_complexity": "O(nnz+N) [N=7850, memory≈77,322 elements]", "theoretical_memory_mb": 0.8549385070800781, "efficiency_ratio": 6.632250236742424}, {"input_size": 8050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651062.1256862, "execution_time_ms": 0.17128139734268188, "setup_time_ms": 2634.8318941891193, "cleanup_time_ms": 38.250585086643696, "total_time_ms": 2673.2537606731057, "baseline_memory_mb": 433.0859375, "peak_memory_mb": 433.21484375, "memory_increment_mb": 0.12890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 129604, "algorithm_type": "spmv_scipy", "matrix_size": "8050×8050", "implementation": "scipy_sparse", "nnz": 64802, "actual_sparsity_ratio": 0.000999992284248293, "target_sparsity_ratio": 0.001, "vector_norm": 89.2075160929974, "result_norm": 146.9175135825103, "matrix_frobenius_norm": 146.58109552114036, "theoretical_flops": 129604, "storage_format": "CSR", "memory_efficiency": 0.000999992284248293}, "theoretical_time_complexity": "O(nnz) [N=8050, nnz≈64,802]", "theoretical_space_complexity": "O(nnz+N) [N=8050, memory≈80,902 elements]", "theoretical_memory_mb": 0.8951454162597656, "efficiency_ratio": 6.944158380681818}, {"input_size": 8250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651064.9880648, "execution_time_ms": 0.17308276146650314, "setup_time_ms": 2775.3110229969025, "cleanup_time_ms": 38.410093169659376, "total_time_ms": 2813.8941989280283, "baseline_memory_mb": 433.21484375, "peak_memory_mb": 433.484375, "memory_increment_mb": 0.26953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 136124, "algorithm_type": "spmv_scipy", "matrix_size": "8250×8250", "implementation": "scipy_sparse", "nnz": 68062, "actual_sparsity_ratio": 0.0009999926538108357, "target_sparsity_ratio": 0.001, "vector_norm": 90.16400998071336, "result_norm": 150.3375071514213, "matrix_frobenius_norm": 150.61910746106483, "theoretical_flops": 136124, "storage_format": "CSR", "memory_efficiency": 0.0009999926538108357}, "theoretical_time_complexity": "O(nnz) [N=8250, nnz≈68,062]", "theoretical_space_complexity": "O(nnz+N) [N=8250, memory≈84,562 elements]", "theoretical_memory_mb": 0.9362678527832031, "efficiency_ratio": 3.473689424818841}, {"input_size": 8450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651068.0001507, "execution_time_ms": 0.17755674198269844, "setup_time_ms": 2941.593164112419, "cleanup_time_ms": 38.329165894538164, "total_time_ms": 2980.0998867489398, "baseline_memory_mb": 433.484375, "peak_memory_mb": 433.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 142804, "algorithm_type": "spmv_scipy", "matrix_size": "8450×8450", "implementation": "scipy_sparse", "nnz": 71402, "actual_sparsity_ratio": 0.0009999929974440671, "target_sparsity_ratio": 0.001, "vector_norm": 91.8205874767425, "result_norm": 152.4236024952062, "matrix_frobenius_norm": 153.83856889231853, "theoretical_flops": 142804, "storage_format": "CSR", "memory_efficiency": 0.0009999929974440671}, "theoretical_time_complexity": "O(nnz) [N=8450, nnz≈71,402]", "theoretical_space_complexity": "O(nnz+N) [N=8450, memory≈88,302 elements]", "theoretical_memory_mb": 0.9783058166503906, "efficiency_ratio": 0.0}, {"input_size": 8650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651071.1694403, "execution_time_ms": 0.18420470878481865, "setup_time_ms": 3103.053238708526, "cleanup_time_ms": 38.22605684399605, "total_time_ms": 3141.4635002613068, "baseline_memory_mb": 433.484375, "peak_memory_mb": 433.625, "memory_increment_mb": 0.140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 149644, "algorithm_type": "spmv_scipy", "matrix_size": "8650×8650", "implementation": "scipy_sparse", "nnz": 74822, "actual_sparsity_ratio": 0.0009999933175181262, "target_sparsity_ratio": 0.001, "vector_norm": 92.49037703670662, "result_norm": 157.82246334400728, "matrix_frobenius_norm": 158.11235072832795, "theoretical_flops": 149644, "storage_format": "CSR", "memory_efficiency": 0.0009999933175181262}, "theoretical_time_complexity": "O(nnz) [N=8650, nnz≈74,822]", "theoretical_space_complexity": "O(nnz+N) [N=8650, memory≈92,122 elements]", "theoretical_memory_mb": 1.0212593078613281, "efficiency_ratio": 7.262288411458333}, {"input_size": 8850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651074.499832, "execution_time_ms": 0.18741609528660774, "setup_time_ms": 3273.4571299515665, "cleanup_time_ms": 38.31346007063985, "total_time_ms": 3311.958006117493, "baseline_memory_mb": 433.625, "peak_memory_mb": 433.76953125, "memory_increment_mb": 0.14453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 156644, "algorithm_type": "spmv_scipy", "matrix_size": "8850×8850", "implementation": "scipy_sparse", "nnz": 78322, "actual_sparsity_ratio": 0.000999993616138402, "target_sparsity_ratio": 0.001, "vector_norm": 94.04842098142971, "result_norm": 163.49001197323872, "matrix_frobenius_norm": 161.87755405802338, "theoretical_flops": 156644, "storage_format": "CSR", "memory_efficiency": 0.000999993616138402}, "theoretical_time_complexity": "O(nnz) [N=8850, nnz≈78,322]", "theoretical_space_complexity": "O(nnz+N) [N=8850, memory≈96,022 elements]", "theoretical_memory_mb": 1.0651283264160156, "efficiency_ratio": 7.369536528716216}, {"input_size": 9050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651078.000981, "execution_time_ms": 0.19196132197976112, "setup_time_ms": 3439.1656210646033, "cleanup_time_ms": 38.4490299038589, "total_time_ms": 3477.806612290442, "baseline_memory_mb": 433.76953125, "peak_memory_mb": 434.06640625, "memory_increment_mb": 0.296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 163804, "algorithm_type": "spmv_scipy", "matrix_size": "9050×9050", "implementation": "scipy_sparse", "nnz": 81902, "actual_sparsity_ratio": 0.0009999938951802448, "target_sparsity_ratio": 0.001, "vector_norm": 95.67391718673528, "result_norm": 168.0673389102802, "matrix_frobenius_norm": 165.3789159520791, "theoretical_flops": 163804, "storage_format": "CSR", "memory_efficiency": 0.0009999938951802448}, "theoretical_time_complexity": "O(nnz) [N=9050, nnz≈81,902]", "theoretical_space_complexity": "O(nnz+N) [N=9050, memory≈100,002 elements]", "theoretical_memory_mb": 1.1099128723144531, "efficiency_ratio": 3.7386538856907894}, {"input_size": 9250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651081.6731813, "execution_time_ms": 0.19816439598798752, "setup_time_ms": 3608.947705011815, "cleanup_time_ms": 38.25900610536337, "total_time_ms": 3647.404875513166, "baseline_memory_mb": 434.06640625, "peak_memory_mb": 434.06640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 171124, "algorithm_type": "spmv_scipy", "matrix_size": "9250×9250", "implementation": "scipy_sparse", "nnz": 85562, "actual_sparsity_ratio": 0.0009999941563184807, "target_sparsity_ratio": 0.001, "vector_norm": 95.30771492708696, "result_norm": 166.61509817382557, "matrix_frobenius_norm": 168.9244173426714, "theoretical_flops": 171124, "storage_format": "CSR", "memory_efficiency": 0.0009999941563184807}, "theoretical_time_complexity": "O(nnz) [N=9250, nnz≈85,562]", "theoretical_space_complexity": "O(nnz+N) [N=9250, memory≈104,062 elements]", "theoretical_memory_mb": 1.1556129455566406, "efficiency_ratio": 0.0}, {"input_size": 9450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651085.509569, "execution_time_ms": 0.20115943625569344, "setup_time_ms": 3772.47176785022, "cleanup_time_ms": 38.20825321599841, "total_time_ms": 3810.8811805024743, "baseline_memory_mb": 434.06640625, "peak_memory_mb": 434.21875, "memory_increment_mb": 0.15234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 178604, "algorithm_type": "spmv_scipy", "matrix_size": "9450×9450", "implementation": "scipy_sparse", "nnz": 89302, "actual_sparsity_ratio": 0.000999994401052602, "target_sparsity_ratio": 0.001, "vector_norm": 97.06098433200592, "result_norm": 170.91873479822317, "matrix_frobenius_norm": 172.33412894211213, "theoretical_flops": 178604, "storage_format": "CSR", "memory_efficiency": 0.000999994401052602}, "theoretical_time_complexity": "O(nnz) [N=9450, nnz≈89,302]", "theoretical_space_complexity": "O(nnz+N) [N=9450, memory≈108,202 elements]", "theoretical_memory_mb": 1.2022285461425781, "efficiency_ratio": 7.891551482371795}, {"input_size": 9650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651089.5093982, "execution_time_ms": 0.20656846463680267, "setup_time_ms": 3930.772239342332, "cleanup_time_ms": 38.42378035187721, "total_time_ms": 3969.402588158846, "baseline_memory_mb": 434.21875, "peak_memory_mb": 434.37890625, "memory_increment_mb": 0.16015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 186244, "algorithm_type": "spmv_scipy", "matrix_size": "9650×9650", "implementation": "scipy_sparse", "nnz": 93122, "actual_sparsity_ratio": 0.0009999946307283417, "target_sparsity_ratio": 0.001, "vector_norm": 98.65309033156433, "result_norm": 174.37761285366767, "matrix_frobenius_norm": 176.1979498313833, "theoretical_flops": 186244, "storage_format": "CSR", "memory_efficiency": 0.0009999946307283417}, "theoretical_time_complexity": "O(nnz) [N=9650, nnz≈93,122]", "theoretical_space_complexity": "O(nnz+N) [N=9650, memory≈112,422 elements]", "theoretical_memory_mb": 1.2497596740722656, "efficiency_ratio": 7.803377477134147}, {"input_size": 9850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651093.6677186, "execution_time_ms": 0.20465729758143425, "setup_time_ms": 4113.344231620431, "cleanup_time_ms": 38.32196490839124, "total_time_ms": 4151.870853826404, "baseline_memory_mb": 434.37890625, "peak_memory_mb": 434.5390625, "memory_increment_mb": 0.16015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 194044, "algorithm_type": "spmv_scipy", "matrix_size": "9850×9850", "implementation": "scipy_sparse", "nnz": 97022, "actual_sparsity_ratio": 0.0009999948465562111, "target_sparsity_ratio": 0.001, "vector_norm": 97.68853467303968, "result_norm": 177.85984306379845, "matrix_frobenius_norm": 180.014976011111, "theoretical_flops": 194044, "storage_format": "CSR", "memory_efficiency": 0.0009999948465562111}, "theoretical_time_complexity": "O(nnz) [N=9850, nnz≈97,022]", "theoretical_space_complexity": "O(nnz+N) [N=9850, memory≈116,722 elements]", "theoretical_memory_mb": 1.2982063293457031, "efficiency_ratio": 8.105873666158537}]