[{"input_size": 100, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.8198128, "execution_time_ms": 0.202992744743824, "setup_time_ms": 0.004231929779052734, "cleanup_time_ms": 2.903164830058813, "total_time_ms": 3.11038950458169, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 100, "actual_comparisons": 316, "theoretical_comparisons": 664, "comparison_efficiency": 2.102, "array_accesses": 1660, "max_recursion_depth": 7, "theoretical_depth": 7, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=100]", "theoretical_space_complexity": "O(n) [n=100]", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.8464878, "execution_time_ms": 0.39814887568354607, "setup_time_ms": 0.004722271114587784, "cleanup_time_ms": 2.9146708548069, "total_time_ms": 3.317542001605034, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 200, "actual_comparisons": 732, "theoretical_comparisons": 1528, "comparison_efficiency": 2.088, "array_accesses": 3820, "max_recursion_depth": 8, "theoretical_depth": 8, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=200]", "theoretical_space_complexity": "O(n) [n=200]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.87068, "execution_time_ms": 0.6213819608092308, "setup_time_ms": 0.005010981112718582, "cleanup_time_ms": 2.7785152196884155, "total_time_ms": 3.404908161610365, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 300, "actual_comparisons": 1180, "theoretical_comparisons": 2468, "comparison_efficiency": 2.092, "array_accesses": 6156, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=300]", "theoretical_space_complexity": "O(n) [n=300]", "theoretical_memory_mb": 0.00457763671875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.897111, "execution_time_ms": 0.8757698349654675, "setup_time_ms": 0.005692243576049805, "cleanup_time_ms": 2.8853928670287132, "total_time_ms": 3.7668549455702305, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 400, "actual_comparisons": 1664, "theoretical_comparisons": 3457, "comparison_efficiency": 2.078, "array_accesses": 8640, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=400]", "theoretical_space_complexity": "O(n) [n=400]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.9268708, "execution_time_ms": 1.0056579485535622, "setup_time_ms": 0.006591901183128357, "cleanup_time_ms": 2.836348954588175, "total_time_ms": 3.8485988043248653, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 500, "actual_comparisons": 2216, "theoretical_comparisons": 4482, "comparison_efficiency": 2.023, "array_accesses": 11192, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=500]", "theoretical_space_complexity": "O(n) [n=500]", "theoretical_memory_mb": 0.00762939453125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.9590037, "execution_time_ms": 1.2942069210112095, "setup_time_ms": 0.007008202373981476, "cleanup_time_ms": 2.905928064137697, "total_time_ms": 4.207143187522888, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 600, "actual_comparisons": 2660, "theoretical_comparisons": 5537, "comparison_efficiency": 2.082, "array_accesses": 13812, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=600]", "theoretical_space_complexity": "O(n) [n=600]", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653398.9948666, "execution_time_ms": 1.506117358803749, "setup_time_ms": 0.008671078830957413, "cleanup_time_ms": 2.9076081700623035, "total_time_ms": 4.42239660769701, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 700, "actual_comparisons": 3172, "theoretical_comparisons": 6615, "comparison_efficiency": 2.086, "array_accesses": 16524, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=700]", "theoretical_space_complexity": "O(n) [n=700]", "theoretical_memory_mb": 0.01068115234375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.0351007, "execution_time_ms": 1.7045938409864902, "setup_time_ms": 0.010979361832141876, "cleanup_time_ms": 3.129725344479084, "total_time_ms": 4.845298547297716, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 800, "actual_comparisons": 3728, "theoretical_comparisons": 7715, "comparison_efficiency": 2.069, "array_accesses": 19280, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=800]", "theoretical_space_complexity": "O(n) [n=800]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.0798383, "execution_time_ms": 1.9855431281030178, "setup_time_ms": 0.011369120329618454, "cleanup_time_ms": 2.780395094305277, "total_time_ms": 4.777307342737913, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 900, "actual_comparisons": 4304, "theoretical_comparisons": 8832, "comparison_efficiency": 2.052, "array_accesses": 22056, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=900]", "theoretical_space_complexity": "O(n) [n=900]", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.128999, "execution_time_ms": 2.1578213199973106, "setup_time_ms": 0.011421740055084229, "cleanup_time_ms": 2.9772501438856125, "total_time_ms": 5.146493203938007, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1000, "actual_comparisons": 4932, "theoretical_comparisons": 9965, "comparison_efficiency": 2.021, "array_accesses": 24884, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1000]", "theoretical_space_complexity": "O(n) [n=1000]", "theoretical_memory_mb": 0.0152587890625, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.1818497, "execution_time_ms": 2.393473591655493, "setup_time_ms": 0.011733733117580414, "cleanup_time_ms": 2.765003126114607, "total_time_ms": 5.17021045088768, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1100, "actual_comparisons": 5420, "theoretical_comparisons": 11113, "comparison_efficiency": 2.05, "array_accesses": 27724, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1100]", "theoretical_space_complexity": "O(n) [n=1100]", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1200, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.2397857, "execution_time_ms": 2.7240686118602753, "setup_time_ms": 0.014126300811767578, "cleanup_time_ms": 2.8418428264558315, "total_time_ms": 5.580037739127874, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1200, "actual_comparisons": 5920, "theoretical_comparisons": 12274, "comparison_efficiency": 2.073, "array_accesses": 30624, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1200]", "theoretical_space_complexity": "O(n) [n=1200]", "theoretical_memory_mb": 0.018310546875, "efficiency_ratio": 0.0}, {"input_size": 1300, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.303415, "execution_time_ms": 2.9299823567271233, "setup_time_ms": 0.014724675565958023, "cleanup_time_ms": 2.819858957082033, "total_time_ms": 5.764565989375114, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1300, "actual_comparisons": 6480, "theoretical_comparisons": 13447, "comparison_efficiency": 2.075, "array_accesses": 33584, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1300]", "theoretical_space_complexity": "O(n) [n=1300]", "theoretical_memory_mb": 0.01983642578125, "efficiency_ratio": 0.0}, {"input_size": 1400, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.3734112, "execution_time_ms": 3.127928916364908, "setup_time_ms": 0.016056932508945465, "cleanup_time_ms": 2.772402949631214, "total_time_ms": 5.916388798505068, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1400, "actual_comparisons": 7044, "theoretical_comparisons": 14631, "comparison_efficiency": 2.077, "array_accesses": 36548, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1400]", "theoretical_space_complexity": "O(n) [n=1400]", "theoretical_memory_mb": 0.0213623046875, "efficiency_ratio": 0.0}, {"input_size": 1500, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.4490733, "execution_time_ms": 3.417855128645897, "setup_time_ms": 0.015543773770332336, "cleanup_time_ms": 3.0280821956694126, "total_time_ms": 6.461481098085642, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1500, "actual_comparisons": 7664, "theoretical_comparisons": 15826, "comparison_efficiency": 2.065, "array_accesses": 39568, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1500]", "theoretical_space_complexity": "O(n) [n=1500]", "theoretical_memory_mb": 0.02288818359375, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.5331771, "execution_time_ms": 3.6865396425127983, "setup_time_ms": 0.018938910216093063, "cleanup_time_ms": 2.892628777772188, "total_time_ms": 6.59810733050108, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1600, "actual_comparisons": 8256, "theoretical_comparisons": 17030, "comparison_efficiency": 2.063, "array_accesses": 42560, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1600]", "theoretical_space_complexity": "O(n) [n=1600]", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 1700, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.6225374, "execution_time_ms": 3.8122269324958324, "setup_time_ms": 0.01927139237523079, "cleanup_time_ms": 2.8904490172863007, "total_time_ms": 6.721947342157364, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1700, "actual_comparisons": 8836, "theoretical_comparisons": 18243, "comparison_efficiency": 2.065, "array_accesses": 45540, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1700]", "theoretical_space_complexity": "O(n) [n=1700]", "theoretical_memory_mb": 0.02593994140625, "efficiency_ratio": 0.0}, {"input_size": 1800, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.718165, "execution_time_ms": 4.011991247534752, "setup_time_ms": 0.01905905082821846, "cleanup_time_ms": 2.777556888759136, "total_time_ms": 6.8086071871221066, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1800, "actual_comparisons": 9508, "theoretical_comparisons": 19464, "comparison_efficiency": 2.047, "array_accesses": 48612, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1800]", "theoretical_space_complexity": "O(n) [n=1800]", "theoretical_memory_mb": 0.0274658203125, "efficiency_ratio": 0.0}, {"input_size": 1900, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.819144, "execution_time_ms": 4.253342468291521, "setup_time_ms": 0.01971004530787468, "cleanup_time_ms": 2.780904993414879, "total_time_ms": 7.053957507014275, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 1900, "actual_comparisons": 10144, "theoretical_comparisons": 20694, "comparison_efficiency": 2.04, "array_accesses": 51648, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1900]", "theoretical_space_complexity": "O(n) [n=1900]", "theoretical_memory_mb": 0.02899169921875, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "MergeSort-sorted", "timestamp": 1753653399.9274724, "execution_time_ms": 4.4614979065954685, "setup_time_ms": 0.019620172679424286, "cleanup_time_ms": 2.777612302452326, "total_time_ms": 7.258730381727219, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "input_size": 2000, "actual_comparisons": 10864, "theoretical_comparisons": 21931, "comparison_efficiency": 2.019, "array_accesses": 54768, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 0, "was_already_sorted": true, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=2000]", "theoretical_space_complexity": "O(n) [n=2000]", "theoretical_memory_mb": 0.030517578125, "efficiency_ratio": 0.0}]