input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_input_size,custom_actual_comparisons,custom_theoretical_comparisons,custom_comparison_efficiency,custom_array_accesses,custom_max_recursion_depth,custom_theoretical_depth,custom_correctness_verified,custom_input_inversions,custom_was_already_sorted,custom_algorithm_type
100,MergeSort-sorted,1753653398.8198128,0.2030,0.0042,2.9032,3.1104,57.32,57.32,0.00,0.00,,,,O(n log n) [n=100],O(n) [n=100],0.00,0.0000,sorted,100,316,664,2.102,1660,7,7,True,0,True,divide_and_conquer
200,MergeSort-sorted,1753653398.8464878,0.3981,0.0047,2.9147,3.3175,57.32,57.32,0.00,0.00,,,,O(n log n) [n=200],O(n) [n=200],0.00,0.0000,sorted,200,732,1528,2.088,3820,8,8,True,0,True,divide_and_conquer
300,MergeSort-sorted,1753653398.87068,0.6214,0.0050,2.7785,3.4049,57.32,57.32,0.00,0.00,,,,O(n log n) [n=300],O(n) [n=300],0.00,0.0000,sorted,300,1180,2468,2.092,6156,9,9,True,0,True,divide_and_conquer
400,MergeSort-sorted,1753653398.897111,0.8758,0.0057,2.8854,3.7669,57.32,57.32,0.00,0.00,,,,O(n log n) [n=400],O(n) [n=400],0.01,0.0000,sorted,400,1664,3457,2.078,8640,9,9,True,0,True,divide_and_conquer
500,MergeSort-sorted,1753653398.9268708,1.0057,0.0066,2.8363,3.8486,57.32,57.32,0.00,0.00,,,,O(n log n) [n=500],O(n) [n=500],0.01,0.0000,sorted,500,2216,4482,2.023,11192,9,9,True,0,True,divide_and_conquer
600,MergeSort-sorted,1753653398.9590037,1.2942,0.0070,2.9059,4.2071,57.32,57.32,0.00,0.00,,,,O(n log n) [n=600],O(n) [n=600],0.01,0.0000,sorted,600,2660,5537,2.082,13812,10,10,True,0,True,divide_and_conquer
700,MergeSort-sorted,1753653398.9948666,1.5061,0.0087,2.9076,4.4224,57.32,57.32,0.00,0.00,,,,O(n log n) [n=700],O(n) [n=700],0.01,0.0000,sorted,700,3172,6615,2.086,16524,10,10,True,0,True,divide_and_conquer
800,MergeSort-sorted,1753653399.0351007,1.7046,0.0110,3.1297,4.8453,57.32,57.32,0.00,0.00,,,,O(n log n) [n=800],O(n) [n=800],0.01,0.0000,sorted,800,3728,7715,2.069,19280,10,10,True,0,True,divide_and_conquer
900,MergeSort-sorted,1753653399.0798383,1.9855,0.0114,2.7804,4.7773,57.32,57.32,0.00,0.00,,,,O(n log n) [n=900],O(n) [n=900],0.01,0.0000,sorted,900,4304,8832,2.052,22056,10,10,True,0,True,divide_and_conquer
1000,MergeSort-sorted,1753653399.128999,2.1578,0.0114,2.9773,5.1465,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1000],O(n) [n=1000],0.02,0.0000,sorted,1000,4932,9965,2.021,24884,10,10,True,0,True,divide_and_conquer
1100,MergeSort-sorted,1753653399.1818497,2.3935,0.0117,2.7650,5.1702,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1100],O(n) [n=1100],0.02,0.0000,sorted,1100,5420,11113,2.05,27724,11,11,True,0,True,divide_and_conquer
1200,MergeSort-sorted,1753653399.2397857,2.7241,0.0141,2.8418,5.5800,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1200],O(n) [n=1200],0.02,0.0000,sorted,1200,5920,12274,2.073,30624,11,11,True,0,True,divide_and_conquer
1300,MergeSort-sorted,1753653399.303415,2.9300,0.0147,2.8199,5.7646,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1300],O(n) [n=1300],0.02,0.0000,sorted,1300,6480,13447,2.075,33584,11,11,True,0,True,divide_and_conquer
1400,MergeSort-sorted,1753653399.3734112,3.1279,0.0161,2.7724,5.9164,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1400],O(n) [n=1400],0.02,0.0000,sorted,1400,7044,14631,2.077,36548,11,11,True,0,True,divide_and_conquer
1500,MergeSort-sorted,1753653399.4490733,3.4179,0.0155,3.0281,6.4615,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1500],O(n) [n=1500],0.02,0.0000,sorted,1500,7664,15826,2.065,39568,11,11,True,0,True,divide_and_conquer
1600,MergeSort-sorted,1753653399.5331771,3.6865,0.0189,2.8926,6.5981,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1600],O(n) [n=1600],0.02,0.0000,sorted,1600,8256,17030,2.063,42560,11,11,True,0,True,divide_and_conquer
1700,MergeSort-sorted,1753653399.6225374,3.8122,0.0193,2.8904,6.7219,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1700],O(n) [n=1700],0.03,0.0000,sorted,1700,8836,18243,2.065,45540,11,11,True,0,True,divide_and_conquer
1800,MergeSort-sorted,1753653399.718165,4.0120,0.0191,2.7776,6.8086,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1800],O(n) [n=1800],0.03,0.0000,sorted,1800,9508,19464,2.047,48612,11,11,True,0,True,divide_and_conquer
1900,MergeSort-sorted,1753653399.819144,4.2533,0.0197,2.7809,7.0540,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1900],O(n) [n=1900],0.03,0.0000,sorted,1900,10144,20694,2.04,51648,11,11,True,0,True,divide_and_conquer
2000,MergeSort-sorted,1753653399.9274724,4.4615,0.0196,2.7776,7.2587,57.32,57.32,0.00,0.00,,,,O(n log n) [n=2000],O(n) [n=2000],0.03,0.0000,sorted,2000,10864,21931,2.019,54768,11,11,True,0,True,divide_and_conquer
