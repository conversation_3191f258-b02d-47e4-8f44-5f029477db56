[{"input_size": 1000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655661.1852808, "execution_time_ms": 0.6205903366208076, "setup_time_ms": 5.062446929514408, "cleanup_time_ms": 34.65486690402031, "total_time_ms": 40.337904170155525, "baseline_memory_mb": 415.671875, "peak_memory_mb": 415.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 1000, "total_rows": 1000, "rows_scanned": 1000, "rows_filtered": 18, "qualifying_rows": 18, "scan_ratio": 1.0, "filter_efficiency": 0.018, "actual_selectivity": 0.018, "expected_selectivity": 0.01, "selectivity_accuracy": 0.992, "comparisons": 2329, "arithmetic_operations": 36, "memory_accesses": 1018, "index_lookups": 0, "comparisons_per_row": 2.33, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.982, "index_effectiveness": 0, "scan_time_ms": 0.623, "revenue": 43279.12, "avg_revenue_per_row": 2404.4, "price_variance": 795056741.63, "discount_variance": 0.0, "quantity_variance": 33.44, "effective_scan_size": 1000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=1000]", "theoretical_space_complexity": "O(k) [k≈10]", "theoretical_memory_mb": 0.0006103515625, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655661.4526622, "execution_time_ms": 1.1701160110533237, "setup_time_ms": 9.067439008504152, "cleanup_time_ms": 22.72727293893695, "total_time_ms": 32.964827958494425, "baseline_memory_mb": 415.671875, "peak_memory_mb": 415.7109375, "memory_increment_mb": 0.0390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 2000, "total_rows": 2000, "rows_scanned": 2000, "rows_filtered": 21, "qualifying_rows": 21, "scan_ratio": 1.0, "filter_efficiency": 0.0105, "actual_selectivity": 0.0105, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9995, "comparisons": 4642, "arithmetic_operations": 42, "memory_accesses": 2021, "index_lookups": 0, "comparisons_per_row": 2.32, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.99, "index_effectiveness": 0, "scan_time_ms": 0.904, "revenue": 53673.76, "avg_revenue_per_row": 2555.89, "price_variance": 764688159.39, "discount_variance": 0.0, "quantity_variance": 35.11, "effective_scan_size": 2000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=2000]", "theoretical_space_complexity": "O(k) [k≈20]", "theoretical_memory_mb": 0.001220703125, "efficiency_ratio": 0.03125}, {"input_size": 3000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655661.7512996, "execution_time_ms": 1.412565540522337, "setup_time_ms": 13.81724001839757, "cleanup_time_ms": 23.21377070620656, "total_time_ms": 38.44357626512647, "baseline_memory_mb": 415.7109375, "peak_memory_mb": 415.7109375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 3000, "total_rows": 3000, "rows_scanned": 3000, "rows_filtered": 28, "qualifying_rows": 28, "scan_ratio": 1.0, "filter_efficiency": 0.0093, "actual_selectivity": 0.009333, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9993, "comparisons": 6945, "arithmetic_operations": 56, "memory_accesses": 3028, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.991, "index_effectiveness": 0, "scan_time_ms": 1.352, "revenue": 66993.11, "avg_revenue_per_row": 2392.61, "price_variance": 719288648.79, "discount_variance": 0.0, "quantity_variance": 30.77, "effective_scan_size": 3000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=3000]", "theoretical_space_complexity": "O(k) [k≈30]", "theoretical_memory_mb": 0.0018310546875, "efficiency_ratio": 0.0}, {"input_size": 4000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655661.9388866, "execution_time_ms": 3.1991831958293915, "setup_time_ms": 18.402394838631153, "cleanup_time_ms": 36.50743095204234, "total_time_ms": 58.109008986502886, "baseline_memory_mb": 415.7109375, "peak_memory_mb": 415.7109375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 4000, "total_rows": 4000, "rows_scanned": 4000, "rows_filtered": 33, "qualifying_rows": 33, "scan_ratio": 1.0, "filter_efficiency": 0.0083, "actual_selectivity": 0.00825, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9982, "comparisons": 9240, "arithmetic_operations": 66, "memory_accesses": 4033, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.992, "index_effectiveness": 0, "scan_time_ms": 1.927, "revenue": 80908.43, "avg_revenue_per_row": 2451.77, "price_variance": 693509116.67, "discount_variance": 0.0, "quantity_variance": 30.55, "effective_scan_size": 4000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=4000]", "theoretical_space_complexity": "O(k) [k≈40]", "theoretical_memory_mb": 0.00244140625, "efficiency_ratio": 0.0}, {"input_size": 5000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655662.2215862, "execution_time_ms": 2.4715743958950043, "setup_time_ms": 22.17209292575717, "cleanup_time_ms": 37.37484198063612, "total_time_ms": 62.018509302288294, "baseline_memory_mb": 415.7109375, "peak_memory_mb": 415.7109375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 5000, "total_rows": 5000, "rows_scanned": 5000, "rows_filtered": 41, "qualifying_rows": 41, "scan_ratio": 1.0, "filter_efficiency": 0.0082, "actual_selectivity": 0.0082, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9982, "comparisons": 11562, "arithmetic_operations": 82, "memory_accesses": 5041, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.992, "index_effectiveness": 0, "scan_time_ms": 2.412, "revenue": 101482.28, "avg_revenue_per_row": 2475.18, "price_variance": 798499872.18, "discount_variance": 0.0, "quantity_variance": 33.21, "effective_scan_size": 5000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=5000]", "theoretical_space_complexity": "O(k) [k≈50]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 6000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655662.5181758, "execution_time_ms": 4.124182835221291, "setup_time_ms": 26.50183904916048, "cleanup_time_ms": 29.122819658368826, "total_time_ms": 59.7488415427506, "baseline_memory_mb": 415.71484375, "peak_memory_mb": 415.83203125, "memory_increment_mb": 0.1171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 6000, "total_rows": 6000, "rows_scanned": 6000, "rows_filtered": 51, "qualifying_rows": 51, "scan_ratio": 1.0, "filter_efficiency": 0.0085, "actual_selectivity": 0.0085, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9985, "comparisons": 13895, "arithmetic_operations": 102, "memory_accesses": 6051, "index_lookups": 0, "comparisons_per_row": 2.32, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.992, "index_effectiveness": 0, "scan_time_ms": 2.635, "revenue": 127019.14, "avg_revenue_per_row": 2490.57, "price_variance": 783395193.85, "discount_variance": 0.0, "quantity_variance": 35.85, "effective_scan_size": 6000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=6000]", "theoretical_space_complexity": "O(k) [k≈60]", "theoretical_memory_mb": 0.003662109375, "efficiency_ratio": 0.03125}, {"input_size": 7000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655662.8554049, "execution_time_ms": 3.1999018974602222, "setup_time_ms": 30.603427905589342, "cleanup_time_ms": 30.293652787804604, "total_time_ms": 64.09698259085417, "baseline_memory_mb": 415.83203125, "peak_memory_mb": 416.4765625, "memory_increment_mb": 0.64453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 7000, "total_rows": 7000, "rows_scanned": 7000, "rows_filtered": 59, "qualifying_rows": 59, "scan_ratio": 1.0, "filter_efficiency": 0.0084, "actual_selectivity": 0.008429, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9984, "comparisons": 16203, "arithmetic_operations": 118, "memory_accesses": 7059, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.992, "index_effectiveness": 0, "scan_time_ms": 3.179, "revenue": 147731.63, "avg_revenue_per_row": 2503.93, "price_variance": 834851706.9, "discount_variance": 0.0, "quantity_variance": 35.56, "effective_scan_size": 7000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=7000]", "theoretical_space_complexity": "O(k) [k≈70]", "theoretical_memory_mb": 0.0042724609375, "efficiency_ratio": 0.006628787878787879}, {"input_size": 8000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655663.1151006, "execution_time_ms": 3.5687413066625595, "setup_time_ms": 35.86517460644245, "cleanup_time_ms": 29.512107837945223, "total_time_ms": 68.94602375105023, "baseline_memory_mb": 416.4765625, "peak_memory_mb": 416.98828125, "memory_increment_mb": 0.51171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 8000, "total_rows": 8000, "rows_scanned": 8000, "rows_filtered": 70, "qualifying_rows": 70, "scan_ratio": 1.0, "filter_efficiency": 0.0088, "actual_selectivity": 0.00875, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9988, "comparisons": 18531, "arithmetic_operations": 140, "memory_accesses": 8070, "index_lookups": 0, "comparisons_per_row": 2.32, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.991, "index_effectiveness": 0, "scan_time_ms": 3.466, "revenue": 179795.03, "avg_revenue_per_row": 2568.5, "price_variance": 879451875.96, "discount_variance": 0.0, "quantity_variance": 39.71, "effective_scan_size": 8000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=8000]", "theoretical_space_complexity": "O(k) [k≈80]", "theoretical_memory_mb": 0.0048828125, "efficiency_ratio": 0.009541984732824428}, {"input_size": 9000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655663.3830163, "execution_time_ms": 4.169142805039883, "setup_time_ms": 39.018243085592985, "cleanup_time_ms": 35.12077499181032, "total_time_ms": 78.30816088244319, "baseline_memory_mb": 416.921875, "peak_memory_mb": 417.6875, "memory_increment_mb": 0.765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 9000, "total_rows": 9000, "rows_scanned": 9000, "rows_filtered": 78, "qualifying_rows": 78, "scan_ratio": 1.0, "filter_efficiency": 0.0087, "actual_selectivity": 0.008667, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9987, "comparisons": 20853, "arithmetic_operations": 156, "memory_accesses": 9078, "index_lookups": 0, "comparisons_per_row": 2.32, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.991, "index_effectiveness": 0, "scan_time_ms": 4.344, "revenue": 200877.8, "avg_revenue_per_row": 2575.36, "price_variance": 875945116.03, "discount_variance": 0.0, "quantity_variance": 37.67, "effective_scan_size": 9000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=9000]", "theoretical_space_complexity": "O(k) [k≈90]", "theoretical_memory_mb": 0.0054931640625, "efficiency_ratio": 0.007174744897959184}, {"input_size": 10000, "algorithm_name": "TPCH-Q6-uniform-sel0.010", "timestamp": 1753655663.6787035, "execution_time_ms": 4.504444450139999, "setup_time_ms": 45.84049107506871, "cleanup_time_ms": 30.945731792598963, "total_time_ms": 81.29066731780767, "baseline_memory_mb": 416.84375, "peak_memory_mb": 418.38671875, "memory_increment_mb": 1.54296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "uniform", "use_indexes": false, "input_size": 10000, "total_rows": 10000, "rows_scanned": 10000, "rows_filtered": 86, "qualifying_rows": 86, "scan_ratio": 1.0, "filter_efficiency": 0.0086, "actual_selectivity": 0.0086, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9986, "comparisons": 23122, "arithmetic_operations": 172, "memory_accesses": 10086, "index_lookups": 0, "comparisons_per_row": 2.31, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.991, "index_effectiveness": 0, "scan_time_ms": 4.323, "revenue": 227744.95, "avg_revenue_per_row": 2648.2, "price_variance": 863693115.68, "discount_variance": 0.0, "quantity_variance": 37.02, "effective_scan_size": 10000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=10000]", "theoretical_space_complexity": "O(k) [k≈100]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.003955696202531646}]