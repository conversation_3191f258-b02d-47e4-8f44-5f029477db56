[{"input_size": 16, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.134451, "execution_time_ms": 0.09986963123083115, "setup_time_ms": 0.018148217350244522, "cleanup_time_ms": 24.38172698020935, "total_time_ms": 24.499744828790426, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 1, "actual_blocks": 2, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 1.0, "output_size_bytes": 32, "expansion_ratio": 2.0, "throughput_mbps": 2.4, "bytes_per_second": 2515354, "blocks_per_second": 314419, "encryption_time_ms": 0.006360933184623718, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 9.1552734375e-05, "efficiency_ratio": 0.0}, {"input_size": 32, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.3145132, "execution_time_ms": 0.09636515751481056, "setup_time_ms": 0.018390361219644547, "cleanup_time_ms": 23.11419602483511, "total_time_ms": 23.228951543569565, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 2, "actual_blocks": 3, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.5, "output_size_bytes": 48, "expansion_ratio": 1.5, "throughput_mbps": 4.96, "bytes_per_second": 5205232, "blocks_per_second": 487990, "encryption_time_ms": 0.006147660315036774, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0001373291015625, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.4861925, "execution_time_ms": 0.09841350838541985, "setup_time_ms": 0.019792933017015457, "cleanup_time_ms": 24.474674835801125, "total_time_ms": 24.59288127720356, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 4, "actual_blocks": 5, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.25, "output_size_bytes": 80, "expansion_ratio": 1.25, "throughput_mbps": 9.83, "bytes_per_second": 10302770, "blocks_per_second": 804903, "encryption_time_ms": 0.006211921572685242, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.6573656, "execution_time_ms": 0.0956331379711628, "setup_time_ms": 0.019278377294540405, "cleanup_time_ms": 23.226427379995584, "total_time_ms": 23.341338895261288, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 8, "actual_blocks": 9, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.125, "output_size_bytes": 144, "expansion_ratio": 1.125, "throughput_mbps": 15.33, "bytes_per_second": 16076611, "blocks_per_second": 1130386, "encryption_time_ms": 0.007961876690387726, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.8229651, "execution_time_ms": 0.09935814887285233, "setup_time_ms": 0.021209008991718292, "cleanup_time_ms": 23.853187914937735, "total_time_ms": 23.973755072802305, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 16, "actual_blocks": 17, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0625, "output_size_bytes": 272, "expansion_ratio": 1.0625, "throughput_mbps": 32.15, "bytes_per_second": 33710805, "blocks_per_second": 2238608, "encryption_time_ms": 0.007594004273414612, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-ECB", "timestamp": 1753702865.990273, "execution_time_ms": 0.09903153404593468, "setup_time_ms": 0.020790845155715942, "cleanup_time_ms": 23.92234280705452, "total_time_ms": 24.04216518625617, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 32, "actual_blocks": 33, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0312, "output_size_bytes": 528, "expansion_ratio": 1.0312, "throughput_mbps": 65.47, "bytes_per_second": 68650825, "blocks_per_second": 4424760, "encryption_time_ms": 0.007458031177520752, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.156269, "execution_time_ms": 0.09669400751590729, "setup_time_ms": 0.02180691808462143, "cleanup_time_ms": 24.517121724784374, "total_time_ms": 24.635622650384903, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 64, "actual_blocks": 65, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0156, "output_size_bytes": 1040, "expansion_ratio": 1.0156, "throughput_mbps": 144.7, "bytes_per_second": 151730025, "blocks_per_second": 9631300, "encryption_time_ms": 0.006748829036951065, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.322775, "execution_time_ms": 0.09917933493852615, "setup_time_ms": 0.02436293289065361, "cleanup_time_ms": 23.342611733824015, "total_time_ms": 23.466154001653194, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 128, "actual_blocks": 129, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0078, "output_size_bytes": 2064, "expansion_ratio": 1.0078, "throughput_mbps": 227.53, "bytes_per_second": 238583406, "blocks_per_second": 15027958, "encryption_time_ms": 0.008584000170230865, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0059051513671875, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.4881108, "execution_time_ms": 0.10214168578386307, "setup_time_ms": 0.029095914214849472, "cleanup_time_ms": 23.544952273368835, "total_time_ms": 23.676189873367548, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 256, "actual_blocks": 257, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0039, "output_size_bytes": 4112, "expansion_ratio": 1.0039, "throughput_mbps": 423.77, "bytes_per_second": 444359334, "blocks_per_second": 27880944, "encryption_time_ms": 0.009217765182256699, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0117645263671875, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.6540265, "execution_time_ms": 0.10805120691657066, "setup_time_ms": 0.039272941648960114, "cleanup_time_ms": 23.882766719907522, "total_time_ms": 24.030090868473053, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 512, "actual_blocks": 513, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.002, "output_size_bytes": 8208, "expansion_ratio": 1.002, "throughput_mbps": 734.52, "bytes_per_second": 770202094, "blocks_per_second": 48231649, "encryption_time_ms": 0.010636169463396072, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=512]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0234832763671875, "efficiency_ratio": 0.0}, {"input_size": 16384, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.8227458, "execution_time_ms": 0.1187669113278389, "setup_time_ms": 0.06047030910849571, "cleanup_time_ms": 23.958547972142696, "total_time_ms": 24.13778519257903, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16384, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 1024, "actual_blocks": 1025, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.001, "output_size_bytes": 16400, "expansion_ratio": 1.001, "throughput_mbps": 988.35, "bytes_per_second": 1036358529, "blocks_per_second": 64835662, "encryption_time_ms": 0.015809200704097748, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1024]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0469207763671875, "efficiency_ratio": 0.0}, {"input_size": 32768, "algorithm_name": "AES-256-ECB", "timestamp": 1753702866.9949768, "execution_time_ms": 0.11999784037470818, "setup_time_ms": 0.0955723226070404, "cleanup_time_ms": 23.804208263754845, "total_time_ms": 24.019778426736593, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32768, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 2048, "actual_blocks": 2049, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0005, "output_size_bytes": 32784, "expansion_ratio": 1.0005, "throughput_mbps": 1299.1, "bytes_per_second": 1362204192, "blocks_per_second": 85179333, "encryption_time_ms": 0.024055130779743195, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2048]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0937957763671875, "efficiency_ratio": 0.0}, {"input_size": 65536, "algorithm_name": "AES-256-ECB", "timestamp": 1753702867.1626072, "execution_time_ms": 0.1541084609925747, "setup_time_ms": 0.19401265308260918, "cleanup_time_ms": 25.635322090238333, "total_time_ms": 25.983443204313517, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 65536, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 4096, "actual_blocks": 4097, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0002, "output_size_bytes": 65552, "expansion_ratio": 1.0002, "throughput_mbps": 1484.96, "bytes_per_second": 1557088989, "blocks_per_second": 97341821, "encryption_time_ms": 0.04208879545331001, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4096]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.1875457763671875, "efficiency_ratio": 24.005859375}, {"input_size": 131072, "algorithm_name": "AES-256-ECB", "timestamp": 1753702867.3425713, "execution_time_ms": 0.21574394777417183, "setup_time_ms": 0.3506704233586788, "cleanup_time_ms": 24.84575007110834, "total_time_ms": 25.412164442241192, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 421.1875, "memory_increment_mb": 0.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 131072, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 8192, "actual_blocks": 8193, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 131088, "expansion_ratio": 1.0001, "throughput_mbps": 1690.4, "bytes_per_second": 1772512447, "blocks_per_second": 110795551, "encryption_time_ms": 0.073947012424469, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8192]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.3750457763671875, "efficiency_ratio": 1.4547230113636365}, {"input_size": 262144, "algorithm_name": "AES-256-ECB", "timestamp": 1753702867.521295, "execution_time_ms": 0.2910868264734745, "setup_time_ms": 0.617416575551033, "cleanup_time_ms": 23.90829473733902, "total_time_ms": 24.816798139363527, "baseline_memory_mb": 421.1875, "peak_memory_mb": 421.9609375, "memory_increment_mb": 0.7734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 262144, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 16384, "actual_blocks": 16385, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 262160, "expansion_ratio": 1.0001, "throughput_mbps": 1877.6, "bytes_per_second": 1968803935, "blocks_per_second": 123057756, "encryption_time_ms": 0.13314886018633842, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16384]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.7500457763671875, "efficiency_ratio": 0.9697561553030303}, {"input_size": 524288, "algorithm_name": "AES-256-ECB", "timestamp": 1753702867.6904438, "execution_time_ms": 0.49674464389681816, "setup_time_ms": 1.1865501292049885, "cleanup_time_ms": 24.288661312311888, "total_time_ms": 25.971956085413694, "baseline_memory_mb": 421.9609375, "peak_memory_mb": 423.25, "memory_increment_mb": 1.2890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 524288, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 32768, "actual_blocks": 32769, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 524304, "expansion_ratio": 1.0, "throughput_mbps": 1857.62, "bytes_per_second": 1947852944, "blocks_per_second": 121744524, "encryption_time_ms": 0.26916200295090675, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32768]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 1.5000457763671875, "efficiency_ratio": 1.163671875}, {"input_size": 1048576, "algorithm_name": "AES-256-ECB", "timestamp": 1753702867.8626683, "execution_time_ms": 0.9642016142606735, "setup_time_ms": 2.3651602678000927, "cleanup_time_ms": 23.599945940077305, "total_time_ms": 26.92930782213807, "baseline_memory_mb": 423.25, "peak_memory_mb": 425.5703125, "memory_increment_mb": 2.3203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1048576, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 65536, "actual_blocks": 65537, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 1048592, "expansion_ratio": 1.0, "throughput_mbps": 1744.5, "bytes_per_second": 1829241393, "blocks_per_second": 114329331, "encryption_time_ms": 0.5732299759984016, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=65536]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 3.0000457763671875, "efficiency_ratio": 1.2929490214646464}, {"input_size": 2097152, "algorithm_name": "AES-256-ECB", "timestamp": 1753702868.0398412, "execution_time_ms": 2.2810117341578007, "setup_time_ms": 5.314079113304615, "cleanup_time_ms": 24.40802287310362, "total_time_ms": 32.003113720566034, "baseline_memory_mb": 420.921875, "peak_memory_mb": 430.65234375, "memory_increment_mb": 9.73046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2097152, "key_size_bits": 256, "encryption_mode": "ECB", "padding_algorithm": "PKCS7", "theoretical_blocks": 131072, "actual_blocks": 131073, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 2097168, "expansion_ratio": 1.0, "throughput_mbps": 1729.27, "bytes_per_second": 1813273272, "blocks_per_second": 113330444, "encryption_time_ms": 1.1565559543669224, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=131072]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 6.0000457763671875, "efficiency_ratio": 0.6166245358289844}]