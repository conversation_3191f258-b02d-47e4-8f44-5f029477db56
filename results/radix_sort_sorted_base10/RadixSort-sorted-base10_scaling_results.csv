input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-sorted-base10,1753654416.9524612,0.3328,0.0080,22.6765,23.0173,411.48,411.48,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=100, k=10]","O(n+k) [n=100, k=10]",0.00,0.0000,sorted,10,100,100,3,600,600,1.0,1000,327,0,True,100,1.0,True,3.322,non_comparative
200,RadixSort-sorted-base10,1753654417.1207383,0.6249,0.0094,22.3882,23.0224,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=200, k=10]","O(n+k) [n=200, k=10]",0.00,0.0000,sorted,10,200,200,3,1200,1200,1.0,2000,627,0,True,200,1.0,True,3.322,non_comparative
300,RadixSort-sorted-base10,1753654417.286047,0.9529,0.0106,23.5628,24.5264,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=300, k=10]","O(n+k) [n=300, k=10]",0.00,0.0000,sorted,10,300,300,3,1800,1800,1.0,3000,927,0,True,300,1.0,True,3.322,non_comparative
400,RadixSort-sorted-base10,1753654417.4528384,1.2220,0.0110,22.9435,24.1765,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=400, k=10]","O(n+k) [n=400, k=10]",0.01,0.0000,sorted,10,400,400,3,2400,2400,1.0,4000,1227,0,True,400,1.0,True,3.322,non_comparative
500,RadixSort-sorted-base10,1753654417.6226845,1.4792,0.0144,22.8245,24.3181,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=500, k=10]","O(n+k) [n=500, k=10]",0.01,0.0000,sorted,10,500,500,3,3000,3000,1.0,5000,1527,0,True,500,1.0,True,3.322,non_comparative
600,RadixSort-sorted-base10,1753654417.7923727,1.8535,0.0174,22.9059,24.7768,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=600, k=10]","O(n+k) [n=600, k=10]",0.01,0.0000,sorted,10,600,600,3,3600,3600,1.0,6000,1827,0,True,600,1.0,True,3.322,non_comparative
700,RadixSort-sorted-base10,1753654417.966634,2.1078,0.0190,32.5835,34.7103,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=700, k=10]","O(n+k) [n=700, k=10]",0.01,0.0000,sorted,10,700,700,3,4200,4200,1.0,7000,2127,0,True,700,1.0,True,3.322,non_comparative
800,RadixSort-sorted-base10,1753654418.1536212,2.4386,0.0233,22.7611,25.2231,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=800, k=10]","O(n+k) [n=800, k=10]",0.01,0.0000,sorted,10,800,800,3,4800,4800,1.0,8000,2427,0,True,800,1.0,True,3.322,non_comparative
900,RadixSort-sorted-base10,1753654418.346893,2.6986,0.0247,23.0348,25.7580,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=900, k=10]","O(n+k) [n=900, k=10]",0.01,0.0000,sorted,10,900,900,3,5400,5400,1.0,9000,2727,0,True,900,1.0,True,3.322,non_comparative
1000,RadixSort-sorted-base10,1753654418.538479,2.8686,0.0255,22.5121,25.4063,411.59,411.59,0.00,0.00,,,,"O(d*(n+k)) [d=5, n=1000, k=10]","O(n+k) [n=1000, k=10]",0.02,0.0000,sorted,10,1000,1000,4,6000,8000,1.333,10000,3027,0,False,1000,1.0,True,3.322,non_comparative
