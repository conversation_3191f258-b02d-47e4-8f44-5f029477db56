[{"input_size": 100, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654416.9524612, "execution_time_ms": 0.33284882083535194, "setup_time_ms": 0.007957685738801956, "cleanup_time_ms": 22.676501888781786, "total_time_ms": 23.01730839535594, "baseline_memory_mb": 411.4765625, "peak_memory_mb": 411.4765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 100, "max_number": 100, "num_digits": 3, "actual_digit_extractions": 600, "theoretical_digit_extractions": 600, "digit_efficiency": 1.0, "array_accesses": 1000, "bucket_operations": 327, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=100, k=10]", "theoretical_space_complexity": "O(n+k) [n=100, k=10]", "theoretical_memory_mb": 0.0016021728515625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.1207383, "execution_time_ms": 0.6248614750802517, "setup_time_ms": 0.009379815310239792, "cleanup_time_ms": 22.388165816664696, "total_time_ms": 23.022407107055187, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 200, "max_number": 200, "num_digits": 3, "actual_digit_extractions": 1200, "theoretical_digit_extractions": 1200, "digit_efficiency": 1.0, "array_accesses": 2000, "bucket_operations": 627, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 200, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=200, k=10]", "theoretical_space_complexity": "O(n+k) [n=200, k=10]", "theoretical_memory_mb": 0.0031280517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.286047, "execution_time_ms": 0.9529256261885166, "setup_time_ms": 0.01062825322151184, "cleanup_time_ms": 23.562805727124214, "total_time_ms": 24.526359606534243, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 300, "max_number": 300, "num_digits": 3, "actual_digit_extractions": 1800, "theoretical_digit_extractions": 1800, "digit_efficiency": 1.0, "array_accesses": 3000, "bucket_operations": 927, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 300, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=300, k=10]", "theoretical_space_complexity": "O(n+k) [n=300, k=10]", "theoretical_memory_mb": 0.0046539306640625, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.4528384, "execution_time_ms": 1.2220473028719425, "setup_time_ms": 0.01096632331609726, "cleanup_time_ms": 22.94349018484354, "total_time_ms": 24.17650381103158, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 400, "max_number": 400, "num_digits": 3, "actual_digit_extractions": 2400, "theoretical_digit_extractions": 2400, "digit_efficiency": 1.0, "array_accesses": 4000, "bucket_operations": 1227, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 400, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=400, k=10]", "theoretical_space_complexity": "O(n+k) [n=400, k=10]", "theoretical_memory_mb": 0.0061798095703125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.6226845, "execution_time_ms": 1.4792111702263355, "setup_time_ms": 0.014381948858499527, "cleanup_time_ms": 22.824478801339865, "total_time_ms": 24.3180719204247, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 500, "max_number": 500, "num_digits": 3, "actual_digit_extractions": 3000, "theoretical_digit_extractions": 3000, "digit_efficiency": 1.0, "array_accesses": 5000, "bucket_operations": 1527, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 500, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=500, k=10]", "theoretical_space_complexity": "O(n+k) [n=500, k=10]", "theoretical_memory_mb": 0.0077056884765625, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.7923727, "execution_time_ms": 1.8534918315708637, "setup_time_ms": 0.01743389293551445, "cleanup_time_ms": 22.90590526536107, "total_time_ms": 24.77683098986745, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 600, "max_number": 600, "num_digits": 3, "actual_digit_extractions": 3600, "theoretical_digit_extractions": 3600, "digit_efficiency": 1.0, "array_accesses": 6000, "bucket_operations": 1827, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 600, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=600, k=10]", "theoretical_space_complexity": "O(n+k) [n=600, k=10]", "theoretical_memory_mb": 0.0092315673828125, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654417.966634, "execution_time_ms": 2.1078085526823997, "setup_time_ms": 0.019012950360774994, "cleanup_time_ms": 32.58346673101187, "total_time_ms": 34.71028823405504, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 700, "max_number": 700, "num_digits": 3, "actual_digit_extractions": 4200, "theoretical_digit_extractions": 4200, "digit_efficiency": 1.0, "array_accesses": 7000, "bucket_operations": 2127, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 700, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=700, k=10]", "theoretical_space_complexity": "O(n+k) [n=700, k=10]", "theoretical_memory_mb": 0.0107574462890625, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654418.1536212, "execution_time_ms": 2.438629325479269, "setup_time_ms": 0.023303087800741196, "cleanup_time_ms": 22.761147003620863, "total_time_ms": 25.223079416900873, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 800, "max_number": 800, "num_digits": 3, "actual_digit_extractions": 4800, "theoretical_digit_extractions": 4800, "digit_efficiency": 1.0, "array_accesses": 8000, "bucket_operations": 2427, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 800, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=800, k=10]", "theoretical_space_complexity": "O(n+k) [n=800, k=10]", "theoretical_memory_mb": 0.0122833251953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654418.346893, "execution_time_ms": 2.698565274477005, "setup_time_ms": 0.024721957743167877, "cleanup_time_ms": 23.03475420922041, "total_time_ms": 25.758041441440582, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 900, "max_number": 900, "num_digits": 3, "actual_digit_extractions": 5400, "theoretical_digit_extractions": 5400, "digit_efficiency": 1.0, "array_accesses": 9000, "bucket_operations": 2727, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 900, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=900, k=10]", "theoretical_space_complexity": "O(n+k) [n=900, k=10]", "theoretical_memory_mb": 0.0138092041015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-sorted-base10", "timestamp": 1753654418.538479, "execution_time_ms": 2.8685953468084335, "setup_time_ms": 0.025539658963680267, "cleanup_time_ms": 22.51212578266859, "total_time_ms": 25.406260788440704, "baseline_memory_mb": 411.58984375, "peak_memory_mb": 411.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "sorted", "radix_base": 10, "input_size": 1000, "max_number": 1000, "num_digits": 4, "actual_digit_extractions": 6000, "theoretical_digit_extractions": 8000, "digit_efficiency": 1.333, "array_accesses": 10000, "bucket_operations": 3027, "comparisons_used": 0, "correctness_verified": false, "unique_elements": 1000, "uniqueness_ratio": 1.0, "was_already_sorted": true, "digit_distribution_entropy": 3.322, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=5, n=1000, k=10]", "theoretical_space_complexity": "O(n+k) [n=1000, k=10]", "theoretical_memory_mb": 0.0153350830078125, "efficiency_ratio": 0.0}]