input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_solution_error,custom_relative_solution_error,custom_final_residual_norm,custom_computed_residual_norm,custom_relative_residual,custom_converged,custom_iterations_performed,custom_max_iterations,custom_iteration_efficiency,custom_convergence_rate,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_b_norm,custom_solution_norm,custom_true_solution_norm,custom_theoretical_flops,custom_storage_format,custom_tolerance,custom_residual_history_length,custom_initial_residual,custom_final_residual,custom_num_levels,custom_level_sizes,custom_avg_coarsening_ratio,custom_coarsening_ratios,custom_setup_work,custom_solve_work,custom_total_nnz_all_levels,custom_hierarchy_efficiency,custom_max_levels,custom_coarsening_factor
50,AMG-basic,1753656815.144127,3.9675,0.8482,25.5530,30.3687,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=50, nnz≈50, k≤15]","O(nnz) [N=50, memory≈325 elements]",0.00,0.0000,True,2.184432593082837e-09,3.0870017577413004e-10,1.1325865374344605e-07,1.1325865374344605e-07,3.1415755116245085e-10,True,2,15,0.13333333333333333,,402,algebraic_multigrid,50×50,basic_amg,150,0.06,0.02,360.5154589611631,7.076227240929518,7.076227240897797,402,CSR,1e-06,2,360.5154589611631,1.1325865374344605e-07,3,"[50, 15, 4]",0.2833333333333333,"[0.3, 0.26666666666666666]",201,402,201,1.34,4,0.3
100,AMG-basic,1753656815.3598454,10.2751,1.0167,25.7468,37.0386,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=100, nnz≈200, k≤15]","O(nnz) [N=100, memory≈800 elements]",0.01,0.0000,True,6.800470041813176e-16,7.973782937000028e-17,4.3800795900274466e-14,4.3800795900274466e-14,5.032244130541848e-17,True,3,15,0.2,18.76290004288094,1827,algebraic_multigrid,100×100,basic_amg,482,0.0482,0.02,870.4028414368324,8.528536700262514,8.528536700262514,1827,CSR,1e-06,3,870.4028414368324,4.3800795900274466e-14,3,"[100, 30, 9]",0.3,"[0.3, 0.3]",609,1827,609,1.2634854771784232,4,0.3
150,AMG-basic,1753656815.6259837,9.3252,1.1702,26.3327,36.8281,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=150, nnz≈450, k≤15]","O(nnz) [N=150, memory≈1,425 elements]",0.01,0.0000,True,4.270858301684663e-09,3.5842682313964805e-10,6.556487306431258e-07,6.556487306431258e-07,3.5863904186644194e-10,True,2,15,0.13333333333333333,,2506,algebraic_multigrid,150×150,basic_amg,1036,0.04604444444444444,0.02,1828.1577132009265,11.915565538367323,11.915565537963873,2506,CSR,1e-06,2,1828.1577132009265,6.556487306431258e-07,4,"[150, 45, 13, 3]",0.27321937321937323,"[0.3, 0.28888888888888886, 0.23076923076923078]",1253,2506,1253,1.2094594594594594,4,0.3
200,AMG-basic,1753656815.8842583,11.5591,1.3942,26.5304,39.4836,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=200, nnz≈800, k≤15]","O(nnz) [N=200, memory≈2,200 elements]",0.02,0.0000,True,3.4046376995838223e-09,2.626411309571477e-10,6.985188025090517e-07,6.985188025090517e-07,2.643327829930697e-10,True,2,15,0.13333333333333333,,4230,algebraic_multigrid,200×200,basic_amg,1764,0.0441,0.02,2642.5734810478107,12.9630788872404,12.963078887058707,4230,CSR,1e-06,2,2642.5734810478107,6.985188025090517e-07,4,"[200, 60, 18, 5]",0.29259259259259257,"[0.3, 0.3, 0.2777777777777778]",2115,4230,2115,1.1989795918367347,4,0.3
250,AMG-basic,1753656816.1598246,14.4530,1.6750,26.4310,42.5589,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=250, nnz≈1,250, k≤15]","O(nnz) [N=250, memory≈3,125 elements]",0.03,0.0000,True,2.9564518237199333e-09,1.9460386495654167e-10,7.560550775635349e-07,7.560550775635349e-07,1.952571214252741e-10,True,2,15,0.13333333333333333,,6498,algebraic_multigrid,250×250,basic_amg,2712,0.043392,0.02,3872.09988575439,15.19215368314149,15.19215368297109,6498,CSR,1e-06,2,3872.09988575439,7.560550775635349e-07,4,"[250, 75, 22, 6]",0.28868686868686866,"[0.3, 0.29333333333333333, 0.2727272727272727]",3249,6498,3249,1.198008849557522,4,0.3
300,AMG-basic,1753656816.47571,16.3462,2.3451,25.2994,43.9907,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=300, nnz≈1,800, k≤15]","O(nnz) [N=300, memory≈4,200 elements]",0.04,0.0000,True,2.032337813031467e-09,1.1197204414086102e-10,6.235551612847339e-07,6.235551612847339e-07,1.1228156600812424e-10,True,2,15,0.13333333333333333,,9118,algebraic_multigrid,300×300,basic_amg,3842,0.04268888888888889,0.02,5553.4954084948895,18.150403778284016,18.150403778239347,9118,CSR,1e-06,2,5553.4954084948895,6.235551612847339e-07,4,"[300, 90, 27, 8]",0.29876543209876544,"[0.3, 0.3, 0.2962962962962963]",4559,9118,4559,1.1866215512753775,4,0.3
350,AMG-basic,1753656816.7965713,33.2682,2.7655,26.0182,62.0519,432.48,432.48,0.00,0.00,,,,"O(k*nnz) [N=350, nnz≈2,450, k≤15]","O(nnz) [N=350, memory≈5,425 elements]",0.06,0.0000,True,2.730349497512101e-15,1.3770071057146831e-16,9.263201942343921e-13,9.263201942343921e-13,1.3081416120155375e-16,True,3,15,0.2,18.286323013228824,18237,algebraic_multigrid,350×350,basic_amg,5206,0.04249795918367347,0.02,7081.192018707756,19.828143850390788,19.828143850390788,18237,CSR,1e-06,3,7081.192018707756,9.263201942343921e-13,4,"[350, 105, 31, 9]",0.29518689196108555,"[0.3, 0.29523809523809524, 0.2903225806451613]",6079,18237,6079,1.1676911256242797,4,0.3
400,AMG-basic,1753656817.249045,20.7216,3.6758,25.5619,49.9594,432.48,433.62,1.14,0.00,,,,"O(k*nnz) [N=400, nnz≈3,200, k≤15]","O(nnz) [N=400, memory≈6,800 elements]",0.07,0.0638,True,1.8582543458392085e-09,9.714603240947612e-11,7.627372333972415e-07,7.627372333972415e-07,9.76635680022397e-11,True,2,15,0.13333333333333333,,15772,algebraic_multigrid,400×400,basic_amg,6744,0.04215,0.02,7809.844028837343,19.128463610334887,19.128463610397997,15772,CSR,1e-06,2,7809.844028837343,7.627372333972415e-07,4,"[400, 120, 36, 10]",0.29259259259259257,"[0.3, 0.3, 0.2777777777777778]",7886,15772,7886,1.1693357058125742,4,0.3
450,AMG-basic,1753656817.601018,43.4106,4.4746,25.7197,73.6049,433.62,434.14,0.52,0.00,,,,"O(k*nnz) [N=450, nnz≈4,050, k≤15]","O(nnz) [N=450, memory≈8,325 elements]",0.09,0.1731,True,3.329483978648518e-15,1.5828453673898246e-16,1.3949066041726876e-12,1.3949066041726876e-12,1.445182822195809e-16,True,3,15,0.2,18.23652698348738,29610,algebraic_multigrid,450×450,basic_amg,8458,0.0417679012345679,0.02,9652.111710359719,21.03480256027138,21.03480256027138,29610,CSR,1e-06,3,9652.111710359719,1.3949066041726876e-12,5,"[450, 135, 40, 12, 3]",0.2865740740740741,"[0.3, 0.2962962962962963, 0.3, 0.25]",9870,29610,9870,1.1669425396074722,4,0.3
500,AMG-basic,1753656818.134589,26.3776,5.4808,25.9303,57.7887,434.14,434.39,0.25,0.00,,,,"O(k*nnz) [N=500, nnz≈5,000, k≤15]","O(nnz) [N=500, memory≈10,000 elements]",0.11,0.4245,True,1.6453916262939129e-09,7.127842816997961e-11,8.44188212963451e-07,8.44188212963451e-07,7.174137318287866e-11,True,2,15,0.13333333333333333,,24070,algebraic_multigrid,500×500,basic_amg,10376,0.041504,0.02,11767.104189816646,23.084005477431763,23.084005477366905,24070,CSR,1e-06,2,11767.104189816646,8.44188212963451e-07,5,"[500, 150, 45, 13, 3]",0.2799145299145299,"[0.3, 0.3, 0.28888888888888886, 0.23076923076923078]",12035,24070,12035,1.159888203546646,4,0.3
550,AMG-basic,1753656818.5333478,52.5521,6.7409,25.8746,85.1676,434.39,434.91,0.52,0.00,,,,"O(k*nnz) [N=550, nnz≈6,050, k≤15]","O(nnz) [N=550, memory≈11,825 elements]",0.13,0.2482,True,3.4104924936412217e-15,1.4981769595020737e-16,1.8668090594765794e-12,1.8668090594765794e-12,1.4611489832305312e-16,True,3,15,0.2,18.231042410481848,43842,algebraic_multigrid,550×550,basic_amg,12504,0.04133553719008264,0.02,12776.308787822258,22.76428343134255,22.76428343134255,43842,CSR,1e-06,3,12776.308787822258,1.8668090594765794e-12,5,"[550, 165, 49, 14, 4]",0.2920995670995671,"[0.3, 0.296969696969697, 0.2857142857142857, 0.2857142857142857]",14614,43842,14614,1.1687460012795905,4,0.3
600,AMG-basic,1753656819.1472814,59.2497,8.0880,26.1957,93.5334,434.91,435.16,0.26,0.00,,,,"O(k*nnz) [N=600, nnz≈7,200, k≤15]","O(nnz) [N=600, memory≈13,800 elements]",0.15,0.5815,True,4.185307998456295e-15,1.774450210765958e-16,2.4114408262639846e-12,2.4114408262639846e-12,1.6707037702404164e-16,True,3,15,0.2,18.16403753140943,51630,algebraic_multigrid,600×600,basic_amg,14828,0.041188888888888886,0.02,14433.68279414953,23.586505685328127,23.586505685328124,51630,CSR,1e-06,3,14433.68279414953,2.4114408262639846e-12,5,"[600, 180, 54, 16, 4]",0.2865740740740741,"[0.3, 0.3, 0.2962962962962963, 0.25]",17210,51630,17210,1.160642028594551,4,0.3
650,AMG-basic,1753656819.8344278,63.0999,9.5377,26.5780,99.2156,435.16,435.85,0.68,0.00,,,,"O(k*nnz) [N=650, nnz≈8,450, k≤15]","O(nnz) [N=650, memory≈15,925 elements]",0.17,0.2539,True,4.848716493328378e-15,1.9563774601260892e-16,3.1664158296886315e-12,3.1664158296886315e-12,1.927146298305705e-16,True,3,15,0.2,18.09264480008116,60069,algebraic_multigrid,650×650,basic_amg,17334,0.04102721893491124,0.02,16430.59394334752,24.784156391864567,24.784156391864567,60069,CSR,1e-06,3,16430.59394334752,3.1664158296886315e-12,5,"[650, 195, 58, 17, 5]",0.29616424819264575,"[0.3, 0.29743589743589743, 0.29310344827586204, 0.29411764705882354]",20023,60069,20023,1.1551286488981194,4,0.3
700,AMG-basic,1753656820.5510323,35.9184,11.1561,25.7404,72.8148,435.85,436.33,0.48,0.00,,,,"O(k*nnz) [N=700, nnz≈9,800, k≤15]","O(nnz) [N=700, memory≈18,200 elements]",0.20,0.4107,True,1.0572949427136111e-09,4.011405202056155e-11,7.616371297898493e-07,7.616371297898493e-07,4.04939612007651e-11,True,2,15,0.13333333333333333,,46360,algebraic_multigrid,700×700,basic_amg,20092,0.04100408163265306,0.02,18808.65954342493,26.35722120944161,26.35722120945712,46360,CSR,1e-06,2,18808.65954342493,7.616371297898493e-07,5,"[700, 210, 63, 18, 5]",0.29087301587301584,"[0.3, 0.3, 0.2857142857142857, 0.2777777777777778]",23180,46360,23180,1.1536930121441369,4,0.3
750,AMG-basic,1753656821.0343928,71.0139,12.6588,26.2679,109.9406,436.33,436.77,0.44,0.00,,,,"O(k*nnz) [N=750, nnz≈11,250, k≤15]","O(nnz) [N=750, memory≈20,625 elements]",0.23,0.5166,True,5.4565357093870165e-15,1.931192779079536e-16,3.8973069380876835e-12,3.8973069380876835e-12,1.8025042466053789e-16,True,3,15,0.2,18.126079441681465,79284,algebraic_multigrid,750×750,basic_amg,23006,0.04089955555555556,0.02,21621.62416775109,28.254743744370067,28.254743744370067,79284,CSR,1e-06,3,21621.62416775109,3.8973069380876835e-12,5,"[750, 225, 67, 20, 6]",0.29907131011608623,"[0.3, 0.29777777777777775, 0.29850746268656714, 0.3]",26428,79284,26428,1.1487438059636617,4,0.3
800,AMG-basic,1753656821.8067024,40.9303,14.8350,26.5770,82.3423,436.77,437.53,0.76,0.00,,,,"O(k*nnz) [N=800, nnz≈12,800, k≤15]","O(nnz) [N=800, memory≈23,200 elements]",0.25,0.3345,True,6.608416214814904e-10,2.2594454518314266e-11,5.408199290223858e-07,5.408199290223858e-07,2.265111886407887e-11,True,2,15,0.13333333333333333,,59842,algebraic_multigrid,800×800,basic_amg,26106,0.040790625,0.02,23876.08012953574,29.247956437563207,29.247956437533624,59842,CSR,1e-06,2,23876.08012953574,5.408199290223858e-07,5,"[800, 240, 72, 21, 6]",0.29434523809523805,"[0.3, 0.3, 0.2916666666666667, 0.2857142857142857]",29921,59842,29921,1.146134988125335,4,0.3
850,AMG-basic,1753656822.3385293,80.3059,16.4736,26.1479,122.9274,437.53,438.29,0.76,0.00,,,,"O(k*nnz) [N=850, nnz≈14,450, k≤15]","O(nnz) [N=850, memory≈25,925 elements]",0.29,0.3765,True,6.040840206655916e-15,2.1240168429836648e-16,5.104454511020274e-12,5.104454511020274e-12,2.070076871156043e-16,True,3,15,0.2,18.05687807747638,100797,algebraic_multigrid,850×850,basic_amg,29442,0.04075017301038062,0.02,24658.284830599892,28.440641733189747,28.44064173318975,100797,CSR,1e-06,3,24658.284830599892,5.104454511020274e-12,5,"[850, 255, 76, 22, 6]",0.2900600431560184,"[0.3, 0.2980392156862745, 0.2894736842105263, 0.2727272727272727]",33599,100797,33599,1.1411928537463487,4,0.3
900,AMG-basic,1753656823.1837037,85.1412,18.4034,26.6700,130.2146,438.29,439.31,1.02,0.00,,,,"O(k*nnz) [N=900, nnz≈16,200, k≤15]","O(nnz) [N=900, memory≈28,800 elements]",0.32,0.3115,True,5.8999105739586665e-15,1.9284429541626404e-16,5.321965410458414e-12,5.321965410458414e-12,1.8957084915603624e-16,True,3,15,0.2,18.100875027597677,112914,algebraic_multigrid,900×900,basic_amg,32946,0.04067407407407407,0.02,28073.754135467796,30.594166974052378,30.59416697405238,112914,CSR,1e-06,3,28073.754135467796,5.321965410458414e-12,5,"[900, 270, 81, 24, 7]",0.2969907407407407,"[0.3, 0.3, 0.2962962962962963, 0.2916666666666667]",37638,112914,37638,1.1424148606811146,4,0.3
950,AMG-basic,1753656824.0770898,48.6877,20.9272,26.3666,95.9815,439.31,439.88,0.57,0.00,,,,"O(k*nnz) [N=950, nnz≈18,050, k≤15]","O(nnz) [N=950, memory≈31,825 elements]",0.35,0.6164,True,3.459957415655658e-10,1.1822234044777136e-11,3.356634506397332e-07,3.356634506397332e-07,1.183415739810243e-11,True,2,15,0.13333333333333333,,83736,algebraic_multigrid,950×950,basic_amg,36614,0.04056952908587257,0.02,28363.950161213488,29.266527819975337,29.266527819961482,83736,CSR,1e-06,2,28363.950161213488,3.356634506397332e-07,5,"[950, 285, 85, 25, 7]",0.2930908152734778,"[0.3, 0.2982456140350877, 0.29411764705882354, 0.28]",41868,83736,41868,1.1434970229966679,4,0.3
1000,AMG-basic,1753656824.6942503,95.0381,22.1442,26.7713,143.9537,439.88,440.65,0.77,0.00,,,,"O(k*nnz) [N=1000, nnz≈20,000, k≤15]","O(nnz) [N=1000, memory≈35,000 elements]",0.39,0.5032,True,6.488195458812928e-15,2.034429505735185e-16,6.567248626815519e-12,6.567248626815519e-12,2.018677758668569e-16,True,3,15,0.2,18.069451769387438,138831,algebraic_multigrid,1000×1000,basic_amg,40538,0.040538,0.02,32532.426726428028,31.891964998159416,31.891964998159416,138831,CSR,1e-06,3,32532.426726428028,6.567248626815519e-12,5,"[1000, 300, 90, 27, 8]",0.29907407407407405,"[0.3, 0.3, 0.3, 0.2962962962962963]",46277,138831,46277,1.1415708717746311,4,0.3
