[{"input_size": 50, "algorithm_name": "AMG-basic", "timestamp": 1753656815.144127, "execution_time_ms": 3.9675211533904076, "setup_time_ms": 0.8482299745082855, "cleanup_time_ms": 25.55295964702964, "total_time_ms": 30.36871077492833, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.184432593082837e-09, "relative_solution_error": 3.0870017577413004e-10, "final_residual_norm": 1.1325865374344605e-07, "computed_residual_norm": 1.1325865374344605e-07, "relative_residual": 3.1415755116245085e-10, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 402, "algorithm_type": "algebraic_multigrid", "matrix_size": "50×50", "implementation": "basic_amg", "nnz": 150, "actual_sparsity_ratio": 0.06, "target_sparsity_ratio": 0.02, "b_norm": 360.5154589611631, "solution_norm": 7.076227240929518, "true_solution_norm": 7.076227240897797, "theoretical_flops": 402, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 360.5154589611631, "final_residual": 1.1325865374344605e-07, "num_levels": 3, "level_sizes": [50, 15, 4], "avg_coarsening_ratio": 0.2833333333333333, "coarsening_ratios": [0.3, 0.26666666666666666], "setup_work": 201, "solve_work": 402, "total_nnz_all_levels": 201, "hierarchy_efficiency": 1.34, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈50, k≤15]", "theoretical_space_complexity": "O(nnz) [N=50, memory≈325 elements]", "theoretical_memory_mb": 0.0030574798583984375, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "AMG-basic", "timestamp": 1753656815.3598454, "execution_time_ms": 10.27510054409504, "setup_time_ms": 1.0167029686272144, "cleanup_time_ms": 25.746775325387716, "total_time_ms": 37.03857883810997, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.800470041813176e-16, "relative_solution_error": 7.973782937000028e-17, "final_residual_norm": 4.3800795900274466e-14, "computed_residual_norm": 4.3800795900274466e-14, "relative_residual": 5.032244130541848e-17, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.76290004288094, "operations_count": 1827, "algorithm_type": "algebraic_multigrid", "matrix_size": "100×100", "implementation": "basic_amg", "nnz": 482, "actual_sparsity_ratio": 0.0482, "target_sparsity_ratio": 0.02, "b_norm": 870.4028414368324, "solution_norm": 8.528536700262514, "true_solution_norm": 8.528536700262514, "theoretical_flops": 1827, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 870.4028414368324, "final_residual": 4.3800795900274466e-14, "num_levels": 3, "level_sizes": [100, 30, 9], "avg_coarsening_ratio": 0.3, "coarsening_ratios": [0.3, 0.3], "setup_work": 609, "solve_work": 1827, "total_nnz_all_levels": 609, "hierarchy_efficiency": 1.2634854771784232, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=100, nnz≈200, k≤15]", "theoretical_space_complexity": "O(nnz) [N=100, memory≈800 elements]", "theoretical_memory_mb": 0.007825851440429688, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "AMG-basic", "timestamp": 1753656815.6259837, "execution_time_ms": 9.325185511261225, "setup_time_ms": 1.170191913843155, "cleanup_time_ms": 26.332703884691, "total_time_ms": 36.82808130979538, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 4.270858301684663e-09, "relative_solution_error": 3.5842682313964805e-10, "final_residual_norm": 6.556487306431258e-07, "computed_residual_norm": 6.556487306431258e-07, "relative_residual": 3.5863904186644194e-10, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 2506, "algorithm_type": "algebraic_multigrid", "matrix_size": "150×150", "implementation": "basic_amg", "nnz": 1036, "actual_sparsity_ratio": 0.04604444444444444, "target_sparsity_ratio": 0.02, "b_norm": 1828.1577132009265, "solution_norm": 11.915565538367323, "true_solution_norm": 11.915565537963873, "theoretical_flops": 2506, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 1828.1577132009265, "final_residual": 6.556487306431258e-07, "num_levels": 4, "level_sizes": [150, 45, 13, 3], "avg_coarsening_ratio": 0.27321937321937323, "coarsening_ratios": [0.3, 0.28888888888888886, 0.23076923076923078], "setup_work": 1253, "solve_work": 2506, "total_nnz_all_levels": 1253, "hierarchy_efficiency": 1.2094594594594594, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈450, k≤15]", "theoretical_space_complexity": "O(nnz) [N=150, memory≈1,425 elements]", "theoretical_memory_mb": 0.014310836791992188, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "AMG-basic", "timestamp": 1753656815.8842583, "execution_time_ms": 11.559082195162773, "setup_time_ms": 1.3941987417638302, "cleanup_time_ms": 26.530366390943527, "total_time_ms": 39.48364732787013, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.4046376995838223e-09, "relative_solution_error": 2.626411309571477e-10, "final_residual_norm": 6.985188025090517e-07, "computed_residual_norm": 6.985188025090517e-07, "relative_residual": 2.643327829930697e-10, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 4230, "algorithm_type": "algebraic_multigrid", "matrix_size": "200×200", "implementation": "basic_amg", "nnz": 1764, "actual_sparsity_ratio": 0.0441, "target_sparsity_ratio": 0.02, "b_norm": 2642.5734810478107, "solution_norm": 12.9630788872404, "true_solution_norm": 12.963078887058707, "theoretical_flops": 4230, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 2642.5734810478107, "final_residual": 6.985188025090517e-07, "num_levels": 4, "level_sizes": [200, 60, 18, 5], "avg_coarsening_ratio": 0.29259259259259257, "coarsening_ratios": [0.3, 0.3, 0.2777777777777778], "setup_work": 2115, "solve_work": 4230, "total_nnz_all_levels": 2115, "hierarchy_efficiency": 1.1989795918367347, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=200, nnz≈800, k≤15]", "theoretical_space_complexity": "O(nnz) [N=200, memory≈2,200 elements]", "theoretical_memory_mb": 0.022512435913085938, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "AMG-basic", "timestamp": 1753656816.1598246, "execution_time_ms": 14.453002996742725, "setup_time_ms": 1.6749678179621696, "cleanup_time_ms": 26.43095562234521, "total_time_ms": 42.558926437050104, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.9564518237199333e-09, "relative_solution_error": 1.9460386495654167e-10, "final_residual_norm": 7.560550775635349e-07, "computed_residual_norm": 7.560550775635349e-07, "relative_residual": 1.952571214252741e-10, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 6498, "algorithm_type": "algebraic_multigrid", "matrix_size": "250×250", "implementation": "basic_amg", "nnz": 2712, "actual_sparsity_ratio": 0.043392, "target_sparsity_ratio": 0.02, "b_norm": 3872.09988575439, "solution_norm": 15.19215368314149, "true_solution_norm": 15.19215368297109, "theoretical_flops": 6498, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 3872.09988575439, "final_residual": 7.560550775635349e-07, "num_levels": 4, "level_sizes": [250, 75, 22, 6], "avg_coarsening_ratio": 0.28868686868686866, "coarsening_ratios": [0.3, 0.29333333333333333, 0.2727272727272727], "setup_work": 3249, "solve_work": 6498, "total_nnz_all_levels": 3249, "hierarchy_efficiency": 1.198008849557522, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈1,250, k≤15]", "theoretical_space_complexity": "O(nnz) [N=250, memory≈3,125 elements]", "theoretical_memory_mb": 0.03243064880371094, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "AMG-basic", "timestamp": 1753656816.47571, "execution_time_ms": 16.34620511904359, "setup_time_ms": 2.345100976526737, "cleanup_time_ms": 25.299351196736097, "total_time_ms": 43.99065729230642, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.032337813031467e-09, "relative_solution_error": 1.1197204414086102e-10, "final_residual_norm": 6.235551612847339e-07, "computed_residual_norm": 6.235551612847339e-07, "relative_residual": 1.1228156600812424e-10, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 9118, "algorithm_type": "algebraic_multigrid", "matrix_size": "300×300", "implementation": "basic_amg", "nnz": 3842, "actual_sparsity_ratio": 0.04268888888888889, "target_sparsity_ratio": 0.02, "b_norm": 5553.4954084948895, "solution_norm": 18.150403778284016, "true_solution_norm": 18.150403778239347, "theoretical_flops": 9118, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 5553.4954084948895, "final_residual": 6.235551612847339e-07, "num_levels": 4, "level_sizes": [300, 90, 27, 8], "avg_coarsening_ratio": 0.29876543209876544, "coarsening_ratios": [0.3, 0.3, 0.2962962962962963], "setup_work": 4559, "solve_work": 9118, "total_nnz_all_levels": 4559, "hierarchy_efficiency": 1.1866215512753775, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=300, nnz≈1,800, k≤15]", "theoretical_space_complexity": "O(nnz) [N=300, memory≈4,200 elements]", "theoretical_memory_mb": 0.04406547546386719, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "AMG-basic", "timestamp": 1753656816.7965713, "execution_time_ms": 33.268244564533234, "setup_time_ms": 2.7654869481921196, "cleanup_time_ms": 26.018151082098484, "total_time_ms": 62.05188259482384, "baseline_memory_mb": 432.484375, "peak_memory_mb": 432.484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.730349497512101e-15, "relative_solution_error": 1.3770071057146831e-16, "final_residual_norm": 9.263201942343921e-13, "computed_residual_norm": 9.263201942343921e-13, "relative_residual": 1.3081416120155375e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.286323013228824, "operations_count": 18237, "algorithm_type": "algebraic_multigrid", "matrix_size": "350×350", "implementation": "basic_amg", "nnz": 5206, "actual_sparsity_ratio": 0.04249795918367347, "target_sparsity_ratio": 0.02, "b_norm": 7081.192018707756, "solution_norm": 19.828143850390788, "true_solution_norm": 19.828143850390788, "theoretical_flops": 18237, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 7081.192018707756, "final_residual": 9.263201942343921e-13, "num_levels": 4, "level_sizes": [350, 105, 31, 9], "avg_coarsening_ratio": 0.29518689196108555, "coarsening_ratios": [0.3, 0.29523809523809524, 0.2903225806451613], "setup_work": 6079, "solve_work": 18237, "total_nnz_all_levels": 6079, "hierarchy_efficiency": 1.1676911256242797, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈2,450, k≤15]", "theoretical_space_complexity": "O(nnz) [N=350, memory≈5,425 elements]", "theoretical_memory_mb": 0.05741691589355469, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "AMG-basic", "timestamp": 1753656817.249045, "execution_time_ms": 20.721578877419233, "setup_time_ms": 3.6758417263627052, "cleanup_time_ms": 25.561946909874678, "total_time_ms": 49.959367513656616, "baseline_memory_mb": 432.484375, "peak_memory_mb": 433.62109375, "memory_increment_mb": 1.13671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.8582543458392085e-09, "relative_solution_error": 9.714603240947612e-11, "final_residual_norm": 7.627372333972415e-07, "computed_residual_norm": 7.627372333972415e-07, "relative_residual": 9.76635680022397e-11, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 15772, "algorithm_type": "algebraic_multigrid", "matrix_size": "400×400", "implementation": "basic_amg", "nnz": 6744, "actual_sparsity_ratio": 0.04215, "target_sparsity_ratio": 0.02, "b_norm": 7809.844028837343, "solution_norm": 19.128463610334887, "true_solution_norm": 19.128463610397997, "theoretical_flops": 15772, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 7809.844028837343, "final_residual": 7.627372333972415e-07, "num_levels": 4, "level_sizes": [400, 120, 36, 10], "avg_coarsening_ratio": 0.29259259259259257, "coarsening_ratios": [0.3, 0.3, 0.2777777777777778], "setup_work": 7886, "solve_work": 15772, "total_nnz_all_levels": 7886, "hierarchy_efficiency": 1.1693357058125742, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=400, nnz≈3,200, k≤15]", "theoretical_space_complexity": "O(nnz) [N=400, memory≈6,800 elements]", "theoretical_memory_mb": 0.07248497009277344, "efficiency_ratio": 0.06376684654209622}, {"input_size": 450, "algorithm_name": "AMG-basic", "timestamp": 1753656817.601018, "execution_time_ms": 43.41058386489749, "setup_time_ms": 4.474588204175234, "cleanup_time_ms": 25.719725992530584, "total_time_ms": 73.60489806160331, "baseline_memory_mb": 433.62109375, "peak_memory_mb": 434.13671875, "memory_increment_mb": 0.515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.329483978648518e-15, "relative_solution_error": 1.5828453673898246e-16, "final_residual_norm": 1.3949066041726876e-12, "computed_residual_norm": 1.3949066041726876e-12, "relative_residual": 1.445182822195809e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.23652698348738, "operations_count": 29610, "algorithm_type": "algebraic_multigrid", "matrix_size": "450×450", "implementation": "basic_amg", "nnz": 8458, "actual_sparsity_ratio": 0.0417679012345679, "target_sparsity_ratio": 0.02, "b_norm": 9652.111710359719, "solution_norm": 21.03480256027138, "true_solution_norm": 21.03480256027138, "theoretical_flops": 29610, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 9652.111710359719, "final_residual": 1.3949066041726876e-12, "num_levels": 5, "level_sizes": [450, 135, 40, 12, 3], "avg_coarsening_ratio": 0.2865740740740741, "coarsening_ratios": [0.3, 0.2962962962962963, 0.3, 0.25], "setup_work": 9870, "solve_work": 29610, "total_nnz_all_levels": 9870, "hierarchy_efficiency": 1.1669425396074722, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈4,050, k≤15]", "theoretical_space_complexity": "O(nnz) [N=450, memory≈8,325 elements]", "theoretical_memory_mb": 0.08926963806152344, "efficiency_ratio": 0.1731289950284091}, {"input_size": 500, "algorithm_name": "AMG-basic", "timestamp": 1753656818.134589, "execution_time_ms": 26.377567369490862, "setup_time_ms": 5.480813793838024, "cleanup_time_ms": 25.93027614057064, "total_time_ms": 57.78865730389953, "baseline_memory_mb": 434.13671875, "peak_memory_mb": 434.390625, "memory_increment_mb": 0.25390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.6453916262939129e-09, "relative_solution_error": 7.127842816997961e-11, "final_residual_norm": 8.44188212963451e-07, "computed_residual_norm": 8.44188212963451e-07, "relative_residual": 7.174137318287866e-11, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 24070, "algorithm_type": "algebraic_multigrid", "matrix_size": "500×500", "implementation": "basic_amg", "nnz": 10376, "actual_sparsity_ratio": 0.041504, "target_sparsity_ratio": 0.02, "b_norm": 11767.104189816646, "solution_norm": 23.084005477431763, "true_solution_norm": 23.084005477366905, "theoretical_flops": 24070, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 11767.104189816646, "final_residual": 8.44188212963451e-07, "num_levels": 5, "level_sizes": [500, 150, 45, 13, 3], "avg_coarsening_ratio": 0.2799145299145299, "coarsening_ratios": [0.3, 0.3, 0.28888888888888886, 0.23076923076923078], "setup_work": 12035, "solve_work": 24070, "total_nnz_all_levels": 12035, "hierarchy_efficiency": 1.159888203546646, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=500, nnz≈5,000, k≤15]", "theoretical_space_complexity": "O(nnz) [N=500, memory≈10,000 elements]", "theoretical_memory_mb": 0.10777091979980469, "efficiency_ratio": 0.4244516225961538}, {"input_size": 550, "algorithm_name": "AMG-basic", "timestamp": 1753656818.5333478, "execution_time_ms": 52.55205109715462, "setup_time_ms": 6.7409370094537735, "cleanup_time_ms": 25.87456302717328, "total_time_ms": 85.16755113378167, "baseline_memory_mb": 434.390625, "peak_memory_mb": 434.90625, "memory_increment_mb": 0.515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.4104924936412217e-15, "relative_solution_error": 1.4981769595020737e-16, "final_residual_norm": 1.8668090594765794e-12, "computed_residual_norm": 1.8668090594765794e-12, "relative_residual": 1.4611489832305312e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.231042410481848, "operations_count": 43842, "algorithm_type": "algebraic_multigrid", "matrix_size": "550×550", "implementation": "basic_amg", "nnz": 12504, "actual_sparsity_ratio": 0.04133553719008264, "target_sparsity_ratio": 0.02, "b_norm": 12776.308787822258, "solution_norm": 22.76428343134255, "true_solution_norm": 22.76428343134255, "theoretical_flops": 43842, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 12776.308787822258, "final_residual": 1.8668090594765794e-12, "num_levels": 5, "level_sizes": [550, 165, 49, 14, 4], "avg_coarsening_ratio": 0.2920995670995671, "coarsening_ratios": [0.3, 0.296969696969697, 0.2857142857142857, 0.2857142857142857], "setup_work": 14614, "solve_work": 43842, "total_nnz_all_levels": 14614, "hierarchy_efficiency": 1.1687460012795905, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈6,050, k≤15]", "theoretical_space_complexity": "O(nnz) [N=550, memory≈11,825 elements]", "theoretical_memory_mb": 0.1279888153076172, "efficiency_ratio": 0.24822073271780304}, {"input_size": 600, "algorithm_name": "AMG-basic", "timestamp": 1753656819.1472814, "execution_time_ms": 59.249668940901756, "setup_time_ms": 8.088013157248497, "cleanup_time_ms": 26.195733807981014, "total_time_ms": 93.53341590613127, "baseline_memory_mb": 434.90625, "peak_memory_mb": 435.1640625, "memory_increment_mb": 0.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 4.185307998456295e-15, "relative_solution_error": 1.774450210765958e-16, "final_residual_norm": 2.4114408262639846e-12, "computed_residual_norm": 2.4114408262639846e-12, "relative_residual": 1.6707037702404164e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.16403753140943, "operations_count": 51630, "algorithm_type": "algebraic_multigrid", "matrix_size": "600×600", "implementation": "basic_amg", "nnz": 14828, "actual_sparsity_ratio": 0.041188888888888886, "target_sparsity_ratio": 0.02, "b_norm": 14433.68279414953, "solution_norm": 23.586505685328127, "true_solution_norm": 23.586505685328124, "theoretical_flops": 51630, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 14433.68279414953, "final_residual": 2.4114408262639846e-12, "num_levels": 5, "level_sizes": [600, 180, 54, 16, 4], "avg_coarsening_ratio": 0.2865740740740741, "coarsening_ratios": [0.3, 0.3, 0.2962962962962963, 0.25], "setup_work": 17210, "solve_work": 51630, "total_nnz_all_levels": 17210, "hierarchy_efficiency": 1.160642028594551, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=600, nnz≈7,200, k≤15]", "theoretical_space_complexity": "O(nnz) [N=600, memory≈13,800 elements]", "theoretical_memory_mb": 0.14992332458496094, "efficiency_ratio": 0.5815207741477273}, {"input_size": 650, "algorithm_name": "AMG-basic", "timestamp": 1753656819.8344278, "execution_time_ms": 63.09987399727106, "setup_time_ms": 9.537707082927227, "cleanup_time_ms": 26.57801704481244, "total_time_ms": 99.21559812501073, "baseline_memory_mb": 435.1640625, "peak_memory_mb": 435.84765625, "memory_increment_mb": 0.68359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 4.848716493328378e-15, "relative_solution_error": 1.9563774601260892e-16, "final_residual_norm": 3.1664158296886315e-12, "computed_residual_norm": 3.1664158296886315e-12, "relative_residual": 1.927146298305705e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.09264480008116, "operations_count": 60069, "algorithm_type": "algebraic_multigrid", "matrix_size": "650×650", "implementation": "basic_amg", "nnz": 17334, "actual_sparsity_ratio": 0.04102721893491124, "target_sparsity_ratio": 0.02, "b_norm": 16430.59394334752, "solution_norm": 24.784156391864567, "true_solution_norm": 24.784156391864567, "theoretical_flops": 60069, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 16430.59394334752, "final_residual": 3.1664158296886315e-12, "num_levels": 5, "level_sizes": [650, 195, 58, 17, 5], "avg_coarsening_ratio": 0.29616424819264575, "coarsening_ratios": [0.3, 0.29743589743589743, 0.29310344827586204, 0.29411764705882354], "setup_work": 20023, "solve_work": 60069, "total_nnz_all_levels": 20023, "hierarchy_efficiency": 1.1551286488981194, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈8,450, k≤15]", "theoretical_space_complexity": "O(nnz) [N=650, memory≈15,925 elements]", "theoretical_memory_mb": 0.17357444763183594, "efficiency_ratio": 0.2539146205357143}, {"input_size": 700, "algorithm_name": "AMG-basic", "timestamp": 1753656820.5510323, "execution_time_ms": 35.918350610882044, "setup_time_ms": 11.156109161674976, "cleanup_time_ms": 25.74037527665496, "total_time_ms": 72.81483504921198, "baseline_memory_mb": 435.84765625, "peak_memory_mb": 436.33203125, "memory_increment_mb": 0.484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.0572949427136111e-09, "relative_solution_error": 4.011405202056155e-11, "final_residual_norm": 7.616371297898493e-07, "computed_residual_norm": 7.616371297898493e-07, "relative_residual": 4.04939612007651e-11, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 46360, "algorithm_type": "algebraic_multigrid", "matrix_size": "700×700", "implementation": "basic_amg", "nnz": 20092, "actual_sparsity_ratio": 0.04100408163265306, "target_sparsity_ratio": 0.02, "b_norm": 18808.65954342493, "solution_norm": 26.35722120944161, "true_solution_norm": 26.35722120945712, "theoretical_flops": 46360, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 18808.65954342493, "final_residual": 7.616371297898493e-07, "num_levels": 5, "level_sizes": [700, 210, 63, 18, 5], "avg_coarsening_ratio": 0.29087301587301584, "coarsening_ratios": [0.3, 0.3, 0.2857142857142857, 0.2777777777777778], "setup_work": 23180, "solve_work": 46360, "total_nnz_all_levels": 23180, "hierarchy_efficiency": 1.1536930121441369, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=700, nnz≈9,800, k≤15]", "theoretical_space_complexity": "O(nnz) [N=700, memory≈18,200 elements]", "theoretical_memory_mb": 0.1989421844482422, "efficiency_ratio": 0.41071934853830644}, {"input_size": 750, "algorithm_name": "AMG-basic", "timestamp": 1753656821.0343928, "execution_time_ms": 71.01386301219463, "setup_time_ms": 12.65881909057498, "cleanup_time_ms": 26.26793598756194, "total_time_ms": 109.94061809033155, "baseline_memory_mb": 436.33203125, "peak_memory_mb": 436.76953125, "memory_increment_mb": 0.4375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 5.4565357093870165e-15, "relative_solution_error": 1.931192779079536e-16, "final_residual_norm": 3.8973069380876835e-12, "computed_residual_norm": 3.8973069380876835e-12, "relative_residual": 1.8025042466053789e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.126079441681465, "operations_count": 79284, "algorithm_type": "algebraic_multigrid", "matrix_size": "750×750", "implementation": "basic_amg", "nnz": 23006, "actual_sparsity_ratio": 0.04089955555555556, "target_sparsity_ratio": 0.02, "b_norm": 21621.62416775109, "solution_norm": 28.254743744370067, "true_solution_norm": 28.254743744370067, "theoretical_flops": 79284, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 21621.62416775109, "final_residual": 3.8973069380876835e-12, "num_levels": 5, "level_sizes": [750, 225, 67, 20, 6], "avg_coarsening_ratio": 0.29907131011608623, "coarsening_ratios": [0.3, 0.29777777777777775, 0.29850746268656714, 0.3], "setup_work": 26428, "solve_work": 79284, "total_nnz_all_levels": 26428, "hierarchy_efficiency": 1.1487438059636617, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈11,250, k≤15]", "theoretical_space_complexity": "O(nnz) [N=750, memory≈20,625 elements]", "theoretical_memory_mb": 0.2260265350341797, "efficiency_ratio": 0.516632080078125}, {"input_size": 800, "algorithm_name": "AMG-basic", "timestamp": 1753656821.8067024, "execution_time_ms": 40.93028558418155, "setup_time_ms": 14.835035894066095, "cleanup_time_ms": 26.576987002044916, "total_time_ms": 82.34230848029256, "baseline_memory_mb": 436.76953125, "peak_memory_mb": 437.53125, "memory_increment_mb": 0.76171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.608416214814904e-10, "relative_solution_error": 2.2594454518314266e-11, "final_residual_norm": 5.408199290223858e-07, "computed_residual_norm": 5.408199290223858e-07, "relative_residual": 2.265111886407887e-11, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 59842, "algorithm_type": "algebraic_multigrid", "matrix_size": "800×800", "implementation": "basic_amg", "nnz": 26106, "actual_sparsity_ratio": 0.040790625, "target_sparsity_ratio": 0.02, "b_norm": 23876.08012953574, "solution_norm": 29.247956437563207, "true_solution_norm": 29.247956437533624, "theoretical_flops": 59842, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 23876.08012953574, "final_residual": 5.408199290223858e-07, "num_levels": 5, "level_sizes": [800, 240, 72, 21, 6], "avg_coarsening_ratio": 0.29434523809523805, "coarsening_ratios": [0.3, 0.3, 0.2916666666666667, 0.2857142857142857], "setup_work": 29921, "solve_work": 59842, "total_nnz_all_levels": 29921, "hierarchy_efficiency": 1.146134988125335, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=800, nnz≈12,800, k≤15]", "theoretical_space_complexity": "O(nnz) [N=800, memory≈23,200 elements]", "theoretical_memory_mb": 0.25482749938964844, "efficiency_ratio": 0.33454276842948716}, {"input_size": 850, "algorithm_name": "AMG-basic", "timestamp": 1753656822.3385293, "execution_time_ms": 80.30589073896408, "setup_time_ms": 16.473617870360613, "cleanup_time_ms": 26.147938799113035, "total_time_ms": 122.92744740843773, "baseline_memory_mb": 437.53125, "peak_memory_mb": 438.2890625, "memory_increment_mb": 0.7578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.040840206655916e-15, "relative_solution_error": 2.1240168429836648e-16, "final_residual_norm": 5.104454511020274e-12, "computed_residual_norm": 5.104454511020274e-12, "relative_residual": 2.070076871156043e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.05687807747638, "operations_count": 100797, "algorithm_type": "algebraic_multigrid", "matrix_size": "850×850", "implementation": "basic_amg", "nnz": 29442, "actual_sparsity_ratio": 0.04075017301038062, "target_sparsity_ratio": 0.02, "b_norm": 24658.284830599892, "solution_norm": 28.440641733189747, "true_solution_norm": 28.44064173318975, "theoretical_flops": 100797, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 24658.284830599892, "final_residual": 5.104454511020274e-12, "num_levels": 5, "level_sizes": [850, 255, 76, 22, 6], "avg_coarsening_ratio": 0.2900600431560184, "coarsening_ratios": [0.3, 0.2980392156862745, 0.2894736842105263, 0.2727272727272727], "setup_work": 33599, "solve_work": 100797, "total_nnz_all_levels": 33599, "hierarchy_efficiency": 1.1411928537463487, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈14,450, k≤15]", "theoretical_space_complexity": "O(nnz) [N=850, memory≈25,925 elements]", "theoretical_memory_mb": 0.28534507751464844, "efficiency_ratio": 0.376537834246134}, {"input_size": 900, "algorithm_name": "AMG-basic", "timestamp": 1753656823.1837037, "execution_time_ms": 85.14118725433946, "setup_time_ms": 18.403392285108566, "cleanup_time_ms": 26.669981889426708, "total_time_ms": 130.21456142887473, "baseline_memory_mb": 438.2890625, "peak_memory_mb": 439.30859375, "memory_increment_mb": 1.01953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 5.8999105739586665e-15, "relative_solution_error": 1.9284429541626404e-16, "final_residual_norm": 5.321965410458414e-12, "computed_residual_norm": 5.321965410458414e-12, "relative_residual": 1.8957084915603624e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.100875027597677, "operations_count": 112914, "algorithm_type": "algebraic_multigrid", "matrix_size": "900×900", "implementation": "basic_amg", "nnz": 32946, "actual_sparsity_ratio": 0.04067407407407407, "target_sparsity_ratio": 0.02, "b_norm": 28073.754135467796, "solution_norm": 30.594166974052378, "true_solution_norm": 30.59416697405238, "theoretical_flops": 112914, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 28073.754135467796, "final_residual": 5.321965410458414e-12, "num_levels": 5, "level_sizes": [900, 270, 81, 24, 7], "avg_coarsening_ratio": 0.2969907407407407, "coarsening_ratios": [0.3, 0.3, 0.2962962962962963, 0.2916666666666667], "setup_work": 37638, "solve_work": 112914, "total_nnz_all_levels": 37638, "hierarchy_efficiency": 1.1424148606811146, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=900, nnz≈16,200, k≤15]", "theoretical_space_complexity": "O(nnz) [N=900, memory≈28,800 elements]", "theoretical_memory_mb": 0.3175792694091797, "efficiency_ratio": 0.3114953753591954}, {"input_size": 950, "algorithm_name": "AMG-basic", "timestamp": 1753656824.0770898, "execution_time_ms": 48.68774162605405, "setup_time_ms": 20.927161909639835, "cleanup_time_ms": 26.366550009697676, "total_time_ms": 95.98145354539156, "baseline_memory_mb": 439.30859375, "peak_memory_mb": 439.87890625, "memory_increment_mb": 0.5703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.459957415655658e-10, "relative_solution_error": 1.1822234044777136e-11, "final_residual_norm": 3.356634506397332e-07, "computed_residual_norm": 3.356634506397332e-07, "relative_residual": 1.183415739810243e-11, "converged": true, "iterations_performed": 2, "max_iterations": 15, "iteration_efficiency": 0.13333333333333333, "convergence_rate": null, "operations_count": 83736, "algorithm_type": "algebraic_multigrid", "matrix_size": "950×950", "implementation": "basic_amg", "nnz": 36614, "actual_sparsity_ratio": 0.04056952908587257, "target_sparsity_ratio": 0.02, "b_norm": 28363.950161213488, "solution_norm": 29.266527819975337, "true_solution_norm": 29.266527819961482, "theoretical_flops": 83736, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 28363.950161213488, "final_residual": 3.356634506397332e-07, "num_levels": 5, "level_sizes": [950, 285, 85, 25, 7], "avg_coarsening_ratio": 0.2930908152734778, "coarsening_ratios": [0.3, 0.2982456140350877, 0.29411764705882354, 0.28], "setup_work": 41868, "solve_work": 83736, "total_nnz_all_levels": 41868, "hierarchy_efficiency": 1.1434970229966679, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈18,050, k≤15]", "theoretical_space_complexity": "O(nnz) [N=950, memory≈31,825 elements]", "theoretical_memory_mb": 0.3515300750732422, "efficiency_ratio": 0.6163815014982876}, {"input_size": 1000, "algorithm_name": "AMG-basic", "timestamp": 1753656824.6942503, "execution_time_ms": 95.03813236951828, "setup_time_ms": 22.144249640405178, "cleanup_time_ms": 26.77134331315756, "total_time_ms": 143.95372532308102, "baseline_memory_mb": 439.87890625, "peak_memory_mb": 440.6484375, "memory_increment_mb": 0.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.488195458812928e-15, "relative_solution_error": 2.034429505735185e-16, "final_residual_norm": 6.567248626815519e-12, "computed_residual_norm": 6.567248626815519e-12, "relative_residual": 2.018677758668569e-16, "converged": true, "iterations_performed": 3, "max_iterations": 15, "iteration_efficiency": 0.2, "convergence_rate": 18.069451769387438, "operations_count": 138831, "algorithm_type": "algebraic_multigrid", "matrix_size": "1000×1000", "implementation": "basic_amg", "nnz": 40538, "actual_sparsity_ratio": 0.040538, "target_sparsity_ratio": 0.02, "b_norm": 32532.426726428028, "solution_norm": 31.891964998159416, "true_solution_norm": 31.891964998159416, "theoretical_flops": 138831, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 3, "initial_residual": 32532.426726428028, "final_residual": 6.567248626815519e-12, "num_levels": 5, "level_sizes": [1000, 300, 90, 27, 8], "avg_coarsening_ratio": 0.29907407407407405, "coarsening_ratios": [0.3, 0.3, 0.3, 0.2962962962962963], "setup_work": 46277, "solve_work": 138831, "total_nnz_all_levels": 46277, "hierarchy_efficiency": 1.1415708717746311, "max_levels": 4, "coarsening_factor": 0.3}, "theoretical_time_complexity": "O(k*nnz) [N=1000, nnz≈20,000, k≤15]", "theoretical_space_complexity": "O(nnz) [N=1000, memory≈35,000 elements]", "theoretical_memory_mb": 0.38719749450683594, "efficiency_ratio": 0.5031601959073604}]