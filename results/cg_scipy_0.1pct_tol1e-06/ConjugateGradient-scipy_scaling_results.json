[{"input_size": 50, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656353.538099, "execution_time_ms": 0.1683681271970272, "setup_time_ms": 2.0484840497374535, "cleanup_time_ms": 46.0244189016521, "total_time_ms": 48.24127107858658, "baseline_memory_mb": 429.42578125, "peak_memory_mb": 430.57421875, "memory_increment_mb": 1.1484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.0288533645198593e-15, "relative_solution_error": 1.5166871769469518e-16, "final_residual_norm": 0.0009435547635009801, "computed_residual_norm": 4.314984300722186e-14, "relative_residual": 1.2717282162878854e-16, "converged": false, "iterations_performed": 3, "max_iterations": 50, "iteration_efficiency": 0.06, "convergence_rate": 13.426027369568015, "operations_count": 1224, "algorithm_type": "conjugate_gradient", "matrix_size": "50×50", "implementation": "custom_cg", "nnz": 54, "actual_sparsity_ratio": 0.0216, "target_sparsity_ratio": 0.001, "b_norm": 339.3008227274709, "solution_norm": 6.783556821459466, "true_solution_norm": 6.783556821459465, "theoretical_flops": 1224, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 4, "initial_residual": 339.3008227274709, "final_residual": 3.7245465409216866e-17}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈2, k≤50]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈252 elements]", "theoretical_memory_mb": 0.002124786376953125, "efficiency_ratio": 0.0018501541241496599}, {"input_size": 150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656353.7749026, "execution_time_ms": 0.17294855788350105, "setup_time_ms": 1.3917731121182442, "cleanup_time_ms": 37.200281862169504, "total_time_ms": 38.76500353217125, "baseline_memory_mb": 430.57421875, "peak_memory_mb": 432.01953125, "memory_increment_mb": 1.4453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.013692219502107e-09, "relative_solution_error": 8.099215958083547e-11, "final_residual_norm": 3.4673618654249816e-05, "computed_residual_norm": 1.530356580830162e-07, "relative_residual": 8.145152464789684e-11, "converged": false, "iterations_performed": 4, "max_iterations": 150, "iteration_efficiency": 0.02666666666666667, "convergence_rate": 5.833796204102894, "operations_count": 5152, "algorithm_type": "conjugate_gradient", "matrix_size": "150×150", "implementation": "custom_cg", "nnz": 194, "actual_sparsity_ratio": 0.008622222222222222, "target_sparsity_ratio": 0.001, "b_norm": 1878.8556597874283, "solution_norm": 12.51593024248694, "true_solution_norm": 12.51593024248694, "theoretical_flops": 5152, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 1878.8556597874283, "final_residual": 1.5303566098276757e-07}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈22, k≤150]", "theoretical_space_complexity": "O(nnz+N) [N=150, memory≈772 elements]", "theoretical_memory_mb": 0.006549835205078125, "efficiency_ratio": 0.004531777871621622}, {"input_size": 250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656354.0478492, "execution_time_ms": 0.17953934147953987, "setup_time_ms": 1.8810699693858624, "cleanup_time_ms": 37.84873802214861, "total_time_ms": 39.90934733301401, "baseline_memory_mb": 432.01953125, "peak_memory_mb": 432.6171875, "memory_increment_mb": 0.59765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.120391673175908e-09, "relative_solution_error": 6.635965468260958e-11, "final_residual_norm": 8.932887414606864e-05, "computed_residual_norm": 2.8198488567953184e-07, "relative_residual": 6.67306689659973e-11, "converged": false, "iterations_performed": 4, "max_iterations": 250, "iteration_efficiency": 0.016, "convergence_rate": 5.860632504222656, "operations_count": 8976, "algorithm_type": "conjugate_gradient", "matrix_size": "250×250", "implementation": "custom_cg", "nnz": 372, "actual_sparsity_ratio": 0.005952, "target_sparsity_ratio": 0.001, "b_norm": 4225.7164516546, "solution_norm": 16.883627236076038, "true_solution_norm": 16.88362723607604, "theoretical_flops": 8976, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 4225.7164516546, "final_residual": 2.819848188496769e-07}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈62, k≤250]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈1,312 elements]", "theoretical_memory_mb": 0.011203765869140625, "efficiency_ratio": 0.018746170343137254}, {"input_size": 350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656354.3246872, "execution_time_ms": 0.19026808440685272, "setup_time_ms": 2.697463147342205, "cleanup_time_ms": 39.381281938403845, "total_time_ms": 42.2690131701529, "baseline_memory_mb": 432.6171875, "peak_memory_mb": 432.875, "memory_increment_mb": 0.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.806073756379098e-10, "relative_solution_error": 3.761658541226847e-11, "final_residual_norm": 8.872325791192428e-05, "computed_residual_norm": 2.395223479738645e-07, "relative_residual": 3.778090041841534e-11, "converged": false, "iterations_performed": 4, "max_iterations": 350, "iteration_efficiency": 0.011428571428571429, "convergence_rate": 5.995850433630776, "operations_count": 13152, "algorithm_type": "conjugate_gradient", "matrix_size": "350×350", "implementation": "custom_cg", "nnz": 594, "actual_sparsity_ratio": 0.004848979591836735, "target_sparsity_ratio": 0.001, "b_norm": 6339.773412523418, "solution_norm": 18.09327901984247, "true_solution_norm": 18.093279019842477, "theoretical_flops": 13152, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 6339.773412523418, "final_residual": 2.395223866217809e-07}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈122, k≤350]", "theoretical_space_complexity": "O(nnz+N) [N=350, memory≈1,872 elements]", "theoretical_memory_mb": 0.016086578369140625, "efficiency_ratio": 0.062396425189393936}, {"input_size": 450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656354.5943165, "execution_time_ms": 0.1968342810869217, "setup_time_ms": 4.465077072381973, "cleanup_time_ms": 38.36398897692561, "total_time_ms": 43.02590033039451, "baseline_memory_mb": 433.0859375, "peak_memory_mb": 433.0859375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.541839696661971e-10, "relative_solution_error": 1.637099355471988e-11, "final_residual_norm": 9.211547008022218e-05, "computed_residual_norm": 1.6001365219280106e-07, "relative_residual": 1.642093120824846e-11, "converged": false, "iterations_performed": 4, "max_iterations": 450, "iteration_efficiency": 0.008888888888888889, "convergence_rate": 6.17182710167254, "operations_count": 17632, "algorithm_type": "conjugate_gradient", "matrix_size": "450×450", "implementation": "custom_cg", "nnz": 854, "actual_sparsity_ratio": 0.004217283950617284, "target_sparsity_ratio": 0.001, "b_norm": 9744.493181508733, "solution_norm": 21.634848763597688, "true_solution_norm": 21.634848763597688, "theoretical_flops": 17632, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 9744.493181508733, "final_residual": 1.600135935770996e-07}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈202, k≤450]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈2,452 elements]", "theoretical_memory_mb": 0.021198272705078125, "efficiency_ratio": 0.0}, {"input_size": 550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656354.8918748, "execution_time_ms": 0.20323488861322403, "setup_time_ms": 6.7084599286317825, "cleanup_time_ms": 36.6636379621923, "total_time_ms": 43.5753327794373, "baseline_memory_mb": 433.0859375, "peak_memory_mb": 433.08984375, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.610293194160497e-10, "relative_solution_error": 1.5612757522196487e-11, "final_residual_norm": 8.82477049563204e-05, "computed_residual_norm": 1.9950062681130062e-07, "relative_residual": 1.5670890823265236e-11, "converged": false, "iterations_performed": 4, "max_iterations": 550, "iteration_efficiency": 0.007272727272727273, "convergence_rate": 6.210255974595798, "operations_count": 22432, "algorithm_type": "conjugate_gradient", "matrix_size": "550×550", "implementation": "custom_cg", "nnz": 1154, "actual_sparsity_ratio": 0.003814876033057851, "target_sparsity_ratio": 0.001, "b_norm": 12730.650035230865, "solution_norm": 23.12399452196566, "true_solution_norm": 23.123994521965656, "theoretical_flops": 22432, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 12730.650035230865, "final_residual": 1.9950075672548665e-07}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈302, k≤550]", "theoretical_space_complexity": "O(nnz+N) [N=550, memory≈3,052 elements]", "theoretical_memory_mb": 0.026538848876953125, "efficiency_ratio": 6.7939453125}, {"input_size": 650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656355.1685941, "execution_time_ms": 0.20522531121969223, "setup_time_ms": 8.882306981831789, "cleanup_time_ms": 35.970734898000956, "total_time_ms": 45.05826719105244, "baseline_memory_mb": 433.08984375, "peak_memory_mb": 433.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.2019563467398266e-10, "relative_solution_error": 4.7356800782927616e-12, "final_residual_norm": 5.3024387727819814e-05, "computed_residual_norm": 7.838964895149187e-08, "relative_residual": 4.747146434922341e-12, "converged": false, "iterations_performed": 4, "max_iterations": 650, "iteration_efficiency": 0.006153846153846154, "convergence_rate": 6.508577755457509, "operations_count": 27552, "algorithm_type": "conjugate_gradient", "matrix_size": "650×650", "implementation": "custom_cg", "nnz": 1494, "actual_sparsity_ratio": 0.003536094674556213, "target_sparsity_ratio": 0.001, "b_norm": 16513.00418601354, "solution_norm": 25.380860338292496, "true_solution_norm": 25.380860338292496, "theoretical_flops": 27552, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 16513.00418601354, "final_residual": 7.838973860559368e-08}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈422, k≤650]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈3,672 elements]", "theoretical_memory_mb": 0.032108306884765625, "efficiency_ratio": 0.0}, {"input_size": 750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656355.437749, "execution_time_ms": 0.21683229133486748, "setup_time_ms": 11.056337971240282, "cleanup_time_ms": 36.467268131673336, "total_time_ms": 47.740438394248486, "baseline_memory_mb": 433.08984375, "peak_memory_mb": 433.09375, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.859113954231082e-10, "relative_solution_error": 1.0852610689818343e-11, "final_residual_norm": 0.00010452860128370108, "computed_residual_norm": 2.1526021146918294e-07, "relative_residual": 1.0883433667428655e-11, "converged": false, "iterations_performed": 4, "max_iterations": 750, "iteration_efficiency": 0.005333333333333333, "convergence_rate": 6.296887294080836, "operations_count": 32960, "algorithm_type": "conjugate_gradient", "matrix_size": "750×750", "implementation": "custom_cg", "nnz": 1870, "actual_sparsity_ratio": 0.0033244444444444445, "target_sparsity_ratio": 0.001, "b_norm": 19778.70385826873, "solution_norm": 26.344941654577493, "true_solution_norm": 26.34494165457749, "theoretical_flops": 32960, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 19778.70385826873, "final_residual": 2.1526006836024774e-07}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈562, k≤750]", "theoretical_space_complexity": "O(nnz+N) [N=750, memory≈4,312 elements]", "theoretical_memory_mb": 0.037906646728515625, "efficiency_ratio": 9.7041015625}, {"input_size": 850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656355.7131262, "execution_time_ms": 0.21823886781930923, "setup_time_ms": 13.540644198656082, "cleanup_time_ms": 35.85729980841279, "total_time_ms": 49.61618287488818, "baseline_memory_mb": 433.09375, "peak_memory_mb": 433.09375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.550704571473915e-10, "relative_solution_error": 5.269553597457775e-12, "final_residual_norm": 7.264863483301432e-05, "computed_residual_norm": 1.3237463450048685e-07, "relative_residual": 5.286498590110944e-12, "converged": false, "iterations_performed": 4, "max_iterations": 850, "iteration_efficiency": 0.004705882352941176, "convergence_rate": 6.497446578068863, "operations_count": 38736, "algorithm_type": "conjugate_gradient", "matrix_size": "850×850", "implementation": "custom_cg", "nnz": 2292, "actual_sparsity_ratio": 0.003172318339100346, "target_sparsity_ratio": 0.001, "b_norm": 25040.134267340985, "solution_norm": 29.427626890862854, "true_solution_norm": 29.427626890862854, "theoretical_flops": 38736, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 25040.134267340985, "final_residual": 1.3237455798217674e-07}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈722, k≤850]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈4,972 elements]", "theoretical_memory_mb": 0.043933868408203125, "efficiency_ratio": 0.0}, {"input_size": 950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656355.9881735, "execution_time_ms": 0.2226008102297783, "setup_time_ms": 17.549340147525072, "cleanup_time_ms": 65.36508770659566, "total_time_ms": 83.13702866435051, "baseline_memory_mb": 433.09375, "peak_memory_mb": 433.09765625, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.348317147789876e-11, "relative_solution_error": 1.9519729871914983e-12, "final_residual_norm": 4.917519854676917e-05, "computed_residual_norm": 6.04619727126411e-08, "relative_residual": 1.955024771032536e-12, "converged": false, "iterations_performed": 4, "max_iterations": 950, "iteration_efficiency": 0.004210526315789474, "convergence_rate": 6.737828497152031, "operations_count": 44816, "algorithm_type": "conjugate_gradient", "matrix_size": "950×950", "implementation": "custom_cg", "nnz": 2752, "actual_sparsity_ratio": 0.0030493074792243766, "target_sparsity_ratio": 0.001, "b_norm": 30926.448405412502, "solution_norm": 32.522566600288066, "true_solution_norm": 32.52256660028807, "theoretical_flops": 44816, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 30926.448405412502, "final_residual": 6.046211891061556e-08}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈902, k≤950]", "theoretical_space_complexity": "O(nnz+N) [N=950, memory≈5,652 elements]", "theoretical_memory_mb": 0.050189971923828125, "efficiency_ratio": 12.8486328125}, {"input_size": 1050, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656356.29796, "execution_time_ms": 0.2134302631020546, "setup_time_ms": 20.510839764028788, "cleanup_time_ms": 24.647529236972332, "total_time_ms": 45.371799264103174, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.0261872676229003e-10, "relative_solution_error": 3.1428078420956418e-12, "final_residual_norm": 7.529915877094791e-05, "computed_residual_norm": 1.0805370209224958e-07, "relative_residual": 3.148286890033561e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1050, "iteration_efficiency": 0.0038095238095238095, "convergence_rate": 6.61875370719871, "operations_count": 51216, "algorithm_type": "conjugate_gradient", "matrix_size": "1050×1050", "implementation": "custom_cg", "nnz": 3252, "actual_sparsity_ratio": 0.002949659863945578, "target_sparsity_ratio": 0.001, "b_norm": 34321.42808659274, "solution_norm": 32.651925258613105, "true_solution_norm": 32.651925258613105, "theoretical_flops": 51216, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 34321.42808659274, "final_residual": 1.0805383565186101e-07}, "theoretical_time_complexity": "O(k*nnz) [N=1050, nnz≈1,102, k≤1050]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈6,352 elements]", "theoretical_memory_mb": 0.056674957275390625, "efficiency_ratio": 0.0}, {"input_size": 1150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656356.535692, "execution_time_ms": 0.21336674690246582, "setup_time_ms": 24.906138889491558, "cleanup_time_ms": 24.697755929082632, "total_time_ms": 49.817261565476656, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 7.888732067467123e-11, "relative_solution_error": 2.3767055899738025e-12, "final_residual_norm": 6.538862528590132e-05, "computed_residual_norm": 9.09699772916931e-08, "relative_residual": 2.380836744959719e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1150, "iteration_efficiency": 0.0034782608695652175, "convergence_rate": 6.687504510862373, "operations_count": 57936, "algorithm_type": "conjugate_gradient", "matrix_size": "1150×1150", "implementation": "custom_cg", "nnz": 3792, "actual_sparsity_ratio": 0.002867296786389414, "target_sparsity_ratio": 0.001, "b_norm": 38209.24617543745, "solution_norm": 33.191877448961094, "true_solution_norm": 33.191877448961094, "theoretical_flops": 57936, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 38209.24617543745, "final_residual": 9.097013999431119e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1150, nnz≈1,322, k≤1150]", "theoretical_space_complexity": "O(nnz+N) [N=1150, memory≈7,072 elements]", "theoretical_memory_mb": 0.06338882446289062, "efficiency_ratio": 0.0}, {"input_size": 1250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656356.7441926, "execution_time_ms": 0.22627590224146843, "setup_time_ms": 28.912650886923075, "cleanup_time_ms": 24.75599804893136, "total_time_ms": 53.8949248380959, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.277366511865655e-11, "relative_solution_error": 1.7861734515219005e-12, "final_residual_norm": 5.919522754803733e-05, "computed_residual_norm": 7.868568788534566e-08, "relative_residual": 1.7893438079935068e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1250, "iteration_efficiency": 0.0032, "convergence_rate": 6.7605641810196255, "operations_count": 64992, "algorithm_type": "conjugate_gradient", "matrix_size": "1250×1250", "implementation": "custom_cg", "nnz": 4374, "actual_sparsity_ratio": 0.00279936, "target_sparsity_ratio": 0.001, "b_norm": 43974.60540217836, "solution_norm": 35.14421573401539, "true_solution_norm": 35.14421573401539, "theoretical_flops": 64992, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 43974.60540217836, "final_residual": 7.868575819745638e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1250, nnz≈1,562, k≤1250]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈7,812 elements]", "theoretical_memory_mb": 0.07033157348632812, "efficiency_ratio": 0.0}, {"input_size": 1350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656356.9570918, "execution_time_ms": 0.2693571150302887, "setup_time_ms": 33.36371900513768, "cleanup_time_ms": 29.105149675160646, "total_time_ms": 62.73822579532862, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 9.12516542328907e-11, "relative_solution_error": 2.4646630878348186e-12, "final_residual_norm": 9.063054228617349e-05, "computed_residual_norm": 1.2348315587499197e-07, "relative_residual": 2.468114167391638e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1350, "iteration_efficiency": 0.002962962962962963, "convergence_rate": 6.667260179650921, "operations_count": 72304, "algorithm_type": "conjugate_gradient", "matrix_size": "1350×1350", "implementation": "custom_cg", "nnz": 4988, "actual_sparsity_ratio": 0.002736899862825789, "target_sparsity_ratio": 0.001, "b_norm": 50031.37922322772, "solution_norm": 37.02398704443387, "true_solution_norm": 37.02398704443387, "theoretical_flops": 72304, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 50031.37922322772, "final_residual": 1.2348303542920524e-07}, "theoretical_time_complexity": "O(k*nnz) [N=1350, nnz≈1,822, k≤1350]", "theoretical_space_complexity": "O(nnz+N) [N=1350, memory≈8,572 elements]", "theoretical_memory_mb": 0.07750320434570312, "efficiency_ratio": 0.0}, {"input_size": 1450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656357.2006407, "execution_time_ms": 0.2639695070683956, "setup_time_ms": 41.02094192057848, "cleanup_time_ms": 27.09527499973774, "total_time_ms": 68.38018642738461, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.3675734751340504e-11, "relative_solution_error": 1.1329435876422444e-12, "final_residual_norm": 5.7101516666133285e-05, "computed_residual_norm": 6.347128507184512e-08, "relative_residual": 1.1342861314356794e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1450, "iteration_efficiency": 0.002758620689655172, "convergence_rate": 6.877752464729332, "operations_count": 80032, "algorithm_type": "conjugate_gradient", "matrix_size": "1450×1450", "implementation": "custom_cg", "nnz": 5654, "actual_sparsity_ratio": 0.002689179548156956, "target_sparsity_ratio": 0.001, "b_norm": 55957.031751334885, "solution_norm": 38.55067033146246, "true_solution_norm": 38.550670331462456, "theoretical_flops": 80032, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 55957.031751334885, "final_residual": 6.347140884153716e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1450, nnz≈2,102, k≤1450]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈9,352 elements]", "theoretical_memory_mb": 0.08490371704101562, "efficiency_ratio": 0.0}, {"input_size": 1550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656357.4408472, "execution_time_ms": 0.24622464552521706, "setup_time_ms": 46.84047866612673, "cleanup_time_ms": 24.600558914244175, "total_time_ms": 71.68726222589612, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.00178052626015e-11, "relative_solution_error": 1.012284293627222e-12, "final_residual_norm": 6.103943749479064e-05, "computed_residual_norm": 6.214869240103767e-08, "relative_residual": 1.0132383398417244e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1550, "iteration_efficiency": 0.0025806451612903226, "convergence_rate": 6.898207260740753, "operations_count": 88016, "algorithm_type": "conjugate_gradient", "matrix_size": "1550×1550", "implementation": "custom_cg", "nnz": 6352, "actual_sparsity_ratio": 0.0026439125910509885, "target_sparsity_ratio": 0.001, "b_norm": 61336.69636972656, "solution_norm": 39.53218035144012, "true_solution_norm": 39.53218035144012, "theoretical_flops": 88016, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 61336.69636972656, "final_residual": 6.214887553669046e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1550, nnz≈2,402, k≤1550]", "theoretical_space_complexity": "O(nnz+N) [N=1550, memory≈10,152 elements]", "theoretical_memory_mb": 0.09253311157226562, "efficiency_ratio": 0.0}, {"input_size": 1650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656357.676635, "execution_time_ms": 0.24697687476873398, "setup_time_ms": 52.99855209887028, "cleanup_time_ms": 24.740831926465034, "total_time_ms": 77.98636090010405, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 8.480136000784471e-11, "relative_solution_error": 2.0891591242363352e-12, "final_residual_norm": 0.00010526313669210506, "computed_residual_norm": 1.402626956423233e-07, "relative_residual": 2.0921628996675803e-12, "converged": false, "iterations_performed": 4, "max_iterations": 1650, "iteration_efficiency": 0.0024242424242424242, "convergence_rate": 6.706674958018442, "operations_count": 96288, "algorithm_type": "conjugate_gradient", "matrix_size": "1650×1650", "implementation": "custom_cg", "nnz": 7086, "actual_sparsity_ratio": 0.002602754820936639, "target_sparsity_ratio": 0.001, "b_norm": 67041.95723220661, "solution_norm": 40.591144553837054, "true_solution_norm": 40.591144553837054, "theoretical_flops": 96288, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 67041.95723220661, "final_residual": 1.402635252667782e-07}, "theoretical_time_complexity": "O(k*nnz) [N=1650, nnz≈2,722, k≤1650]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈10,972 elements]", "theoretical_memory_mb": 0.10039138793945312, "efficiency_ratio": 0.0}, {"input_size": 1750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656357.9183137, "execution_time_ms": 0.2597804181277752, "setup_time_ms": 56.88146594911814, "cleanup_time_ms": 25.223345961421728, "total_time_ms": 82.36459232866764, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.201377685386594e-11, "relative_solution_error": 7.90562820929946e-13, "final_residual_norm": 5.469440257259846e-05, "computed_residual_norm": 5.6129142032164605e-08, "relative_residual": 7.912882750496932e-13, "converged": false, "iterations_performed": 4, "max_iterations": 1750, "iteration_efficiency": 0.002285714285714286, "convergence_rate": 6.963684675608401, "operations_count": 104976, "algorithm_type": "conjugate_gradient", "matrix_size": "1750×1750", "implementation": "custom_cg", "nnz": 7872, "actual_sparsity_ratio": 0.002570448979591837, "target_sparsity_ratio": 0.001, "b_norm": 70933.87302957279, "solution_norm": 40.494918311751434, "true_solution_norm": 40.494918311751434, "theoretical_flops": 104976, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 70933.87302957279, "final_residual": 5.612892204595341e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1750, nnz≈3,062, k≤1750]", "theoretical_space_complexity": "O(nnz+N) [N=1750, memory≈11,812 elements]", "theoretical_memory_mb": 0.10847854614257812, "efficiency_ratio": 0.0}, {"input_size": 1850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656358.1655686, "execution_time_ms": 0.25946488603949547, "setup_time_ms": 72.24282482638955, "cleanup_time_ms": 24.79580696672201, "total_time_ms": 97.29809667915106, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.001597355904147e-11, "relative_solution_error": 8.883483047401978e-13, "final_residual_norm": 7.06962417546088e-05, "computed_residual_norm": 7.417640043318914e-08, "relative_residual": 8.892496230915997e-13, "converged": false, "iterations_performed": 4, "max_iterations": 1850, "iteration_efficiency": 0.002162162162162162, "convergence_rate": 6.928672773108907, "operations_count": 113888, "algorithm_type": "conjugate_gradient", "matrix_size": "1850×1850", "implementation": "custom_cg", "nnz": 8686, "actual_sparsity_ratio": 0.0025379108838568297, "target_sparsity_ratio": 0.001, "b_norm": 83414.59867624637, "solution_norm": 45.045364915447614, "true_solution_norm": 45.045364915447614, "theoretical_flops": 113888, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 83414.59867624637, "final_residual": 7.41766918506513e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1850, nnz≈3,422, k≤1850]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈12,672 elements]", "theoretical_memory_mb": 0.11679458618164062, "efficiency_ratio": 0.0}, {"input_size": 1950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656358.44097, "execution_time_ms": 0.28248904272913933, "setup_time_ms": 84.38508305698633, "cleanup_time_ms": 25.96254786476493, "total_time_ms": 110.6301199644804, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.7010621251327494e-11, "relative_solution_error": 8.084648173318841e-13, "final_residual_norm": 6.807518567136575e-05, "computed_residual_norm": 7.23213680688393e-08, "relative_residual": 8.093637989847922e-13, "converged": false, "iterations_performed": 4, "max_iterations": 1950, "iteration_efficiency": 0.0020512820512820513, "convergence_rate": 6.957995668393767, "operations_count": 123152, "algorithm_type": "conjugate_gradient", "matrix_size": "1950×1950", "implementation": "custom_cg", "nnz": 9544, "actual_sparsity_ratio": 0.0025099276791584486, "target_sparsity_ratio": 0.001, "b_norm": 89355.82263446182, "solution_norm": 45.778889146309275, "true_solution_norm": 45.77888914630927, "theoretical_flops": 123152, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 5, "initial_residual": 89355.82263446182, "final_residual": 7.232171647254656e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1950, nnz≈3,802, k≤1950]", "theoretical_space_complexity": "O(nnz+N) [N=1950, memory≈13,552 elements]", "theoretical_memory_mb": 0.12533950805664062, "efficiency_ratio": 0.0}]