input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_solution_error,custom_relative_solution_error,custom_final_residual_norm,custom_computed_residual_norm,custom_relative_residual,custom_converged,custom_iterations_performed,custom_max_iterations,custom_iteration_efficiency,custom_convergence_rate,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_b_norm,custom_solution_norm,custom_true_solution_norm,custom_theoretical_flops,custom_storage_format,custom_tolerance,custom_residual_history_length,custom_initial_residual,custom_final_residual,custom_num_levels,custom_level_sizes,custom_avg_coarsening_ratio,custom_coarsening_ratios,custom_setup_work,custom_solve_work,custom_total_nnz_all_levels,custom_hierarchy_efficiency,custom_max_levels,custom_coarsening_factor
50,AMG-basic,1753656807.1192055,3.8686,1.5704,27.4283,32.8674,428.41,430.74,2.32,0.00,,,,"O(k*nnz) [N=50, nnz≈25, k≤10]","O(nnz) [N=50, memory≈287 elements]",0.00,0.0011,True,9.053417253116895e-11,1.3031484376969566e-11,4.657041417573871e-09,4.657041417573871e-09,1.3319253506272922e-11,True,2,10,0.2,,274,algebraic_multigrid,50×50,basic_amg,100,0.04,0.01,349.64732936275755,6.947341523964542,6.94734152397629,274,CSR,1e-06,2,349.64732936275755,4.657041417573871e-09,3,"[50, 12, 3]",0.245,"[0.24, 0.25]",137,274,137,1.37,3,0.25
100,AMG-basic,1753656807.4174066,5.9943,0.9942,26.6508,33.6392,430.74,430.86,0.12,0.00,,,,"O(k*nnz) [N=100, nnz≈100, k≤10]","O(nnz) [N=100, memory≈650 elements]",0.01,0.0521,True,4.3933084597211523e-10,4.091663299043139e-11,4.448580960544149e-08,4.448580960544149e-08,4.1011000463551225e-11,True,2,10,0.2,,734,algebraic_multigrid,100×100,basic_amg,292,0.0292,0.01,1084.7287094343997,10.73721892200682,10.73721892206662,734,CSR,1e-06,2,1084.7287094343997,4.448580960544149e-08,3,"[100, 25, 6]",0.245,"[0.25, 0.24]",367,734,367,1.2568493150684932,3,0.25
150,AMG-basic,1753656807.6489184,7.9913,1.2210,25.9055,35.1178,430.86,430.86,0.00,0.00,,,,"O(k*nnz) [N=150, nnz≈225, k≤10]","O(nnz) [N=150, memory≈1,087 elements]",0.01,0.0000,True,2.896775801590219e-10,2.4242832564779973e-11,4.388574458950841e-08,4.388574458950841e-08,2.4274953514768134e-11,True,2,10,0.2,,1460,algebraic_multigrid,150×150,basic_amg,594,0.0264,0.01,1807.861117542807,11.948998921046627,11.948998921019896,1460,CSR,1e-06,2,1807.861117542807,4.388574458950841e-08,3,"[150, 37, 9]",0.24495495495495495,"[0.24666666666666667, 0.24324324324324326]",730,1460,730,1.228956228956229,3,0.25
200,AMG-basic,1753656807.9019136,11.0971,1.5366,26.3842,39.0179,430.86,431.09,0.24,0.00,,,,"O(k*nnz) [N=200, nnz≈400, k≤10]","O(nnz) [N=200, memory≈1,600 elements]",0.02,0.0657,True,9.783839605895963e-10,7.124037264505793e-11,1.9759045452632511e-07,1.9759045452632511e-07,7.120102694227675e-11,True,2,10,0.2,,2382,algebraic_multigrid,200×200,basic_amg,990,0.02475,0.01,2775.106806907621,13.733560399243045,13.733560399300753,2382,CSR,1e-06,2,2775.106806907621,1.9759045452632511e-07,4,"[200, 50, 12, 3]",0.24666666666666667,"[0.25, 0.24, 0.25]",1191,2382,1191,1.2030303030303031,3,0.25
250,AMG-basic,1753656808.177874,13.3642,1.7945,26.8618,42.0205,431.11,431.25,0.14,0.00,,,,"O(k*nnz) [N=250, nnz≈625, k≤10]","O(nnz) [N=250, memory≈2,187 elements]",0.02,0.1502,True,3.8794489282382465e-10,2.474216492819534e-11,9.813577357761e-08,9.813577357761e-08,2.477661640972374e-11,True,2,10,0.2,,3504,algebraic_multigrid,250×250,basic_amg,1482,0.023712,0.01,3960.822250898472,15.679504762375641,15.679504762404024,3504,CSR,1e-06,2,3960.822250898472,9.813577357761e-08,4,"[250, 62, 15, 3]",0.2299784946236559,"[0.248, 0.24193548387096775, 0.2]",1752,3504,1752,1.1821862348178138,3,0.25
300,AMG-basic,1753656808.4899867,15.2973,2.2516,25.4225,42.9713,431.25,431.57,0.32,0.00,,,,"O(k*nnz) [N=300, nnz≈900, k≤10]","O(nnz) [N=300, memory≈2,850 elements]",0.03,0.0904,True,6.682457699415073e-10,3.743443835221379e-11,2.0362139516516123e-07,2.0362139516516123e-07,3.7644611006651945e-11,True,2,10,0.2,,4890,algebraic_multigrid,300×300,basic_amg,2080,0.02311111111111111,0.01,5409.04500591547,17.851096459710433,17.85109645973862,4890,CSR,1e-06,2,5409.04500591547,2.0362139516516123e-07,4,"[300, 75, 18, 4]",0.2374074074074074,"[0.25, 0.24, 0.2222222222222222]",2445,4890,2445,1.1754807692307692,3,0.25
350,AMG-basic,1753656808.8021019,17.2390,2.6576,25.1858,45.0824,431.57,431.80,0.23,0.00,,,,"O(k*nnz) [N=350, nnz≈1,225, k≤10]","O(nnz) [N=350, memory≈3,587 elements]",0.04,0.1606,True,5.218975466058281e-10,2.94117963924944e-11,1.8508455876715224e-07,1.8508455876715224e-07,2.9487808724813575e-11,True,2,10,0.2,,6446,algebraic_multigrid,350×350,basic_amg,2784,0.0227265306122449,0.01,6276.646748980238,17.744497467613055,17.74449746765591,6446,CSR,1e-06,2,6276.646748980238,1.8508455876715224e-07,4,"[350, 87, 21, 5]",0.24268199233716473,"[0.24857142857142858, 0.2413793103448276, 0.23809523809523808]",3223,6446,3223,1.1576867816091954,3,0.25
400,AMG-basic,1753656809.1222093,19.7549,3.4059,26.3029,49.4636,431.80,432.15,0.36,0.00,,,,"O(k*nnz) [N=400, nnz≈1,600, k≤10]","O(nnz) [N=400, memory≈4,400 elements]",0.05,0.1266,True,3.865151439258596e-10,1.960037094869967e-11,1.5670515784617132e-07,1.5670515784617132e-07,1.968631692007221e-11,True,2,10,0.2,,8326,algebraic_multigrid,400×400,basic_amg,3584,0.0224,0.01,7960.105411408591,19.71978718858238,19.719787188594093,8326,CSR,1e-06,2,7960.105411408591,1.5670515784617132e-07,4,"[400, 100, 25, 6]",0.24666666666666667,"[0.25, 0.25, 0.24]",4163,8326,4163,1.1615513392857142,3,0.25
450,AMG-basic,1753656809.4664035,21.8930,4.6855,25.4543,52.0328,432.15,432.15,0.00,0.00,,,,"O(k*nnz) [N=450, nnz≈2,025, k≤10]","O(nnz) [N=450, memory≈5,287 elements]",0.05,0.0000,True,3.0602768908694256e-10,1.4218863207348514e-11,1.3929951321484937e-07,1.3929951321484937e-07,1.423142579590161e-11,True,2,10,0.2,,10214,algebraic_multigrid,450×450,basic_amg,4462,0.022034567901234568,0.01,9788.162845564291,21.52265512539787,21.52265512539589,10214,CSR,1e-06,2,9788.162845564291,1.3929951321484937e-07,4,"[450, 112, 28, 7]",0.24962962962962965,"[0.24888888888888888, 0.25, 0.25]",5107,10214,5107,1.1445540116539668,3,0.25
500,AMG-basic,1753656809.8338544,26.1244,5.4795,36.7217,68.3257,432.15,432.17,0.02,0.00,,,,"O(k*nnz) [N=500, nnz≈2,500, k≤10]","O(nnz) [N=500, memory≈6,250 elements]",0.06,3.3206,True,2.879373199691769e-10,1.2134214862924293e-11,1.458542909849727e-07,1.458542909849727e-07,1.2174114441769737e-11,True,2,10,0.2,,12434,algebraic_multigrid,500×500,basic_amg,5472,0.021888,0.01,11980.689986332183,23.729373776690444,23.729373776704765,12434,CSR,1e-06,2,11980.689986332183,1.458542909849727e-07,4,"[500, 125, 31, 7]",0.24126881720430107,"[0.25, 0.248, 0.22580645161290322]",6217,12434,6217,1.1361476608187135,3,0.25
550,AMG-basic,1753656810.2589042,25.8806,6.7082,25.7258,58.3146,432.17,432.17,0.00,0.00,,,,"O(k*nnz) [N=550, nnz≈3,025, k≤10]","O(nnz) [N=550, memory≈7,287 elements]",0.08,0.0000,True,2.793340432198222e-10,1.1872205242053216e-11,1.5563099713807226e-07,1.5563099713807226e-07,1.1909915403931312e-11,True,2,10,0.2,,15026,algebraic_multigrid,550×550,basic_amg,6556,0.021672727272727274,0.01,13067.34698440431,23.528404161204104,23.528404161206474,15026,CSR,1e-06,2,13067.34698440431,1.5563099713807226e-07,4,"[550, 137, 34, 8]",0.2441867364065732,"[0.24909090909090909, 0.24817518248175183, 0.23529411764705882]",7513,15026,7513,1.145973154362416,3,0.25
600,AMG-basic,1753656810.653367,28.2198,7.8898,25.7128,61.8224,432.17,432.18,0.01,0.00,,,,"O(k*nnz) [N=600, nnz≈3,600, k≤10]","O(nnz) [N=600, memory≈8,400 elements]",0.09,11.2800,True,1.684089177346004e-10,7.090336553005708e-12,1.0226343736981004e-07,1.0226343736981004e-07,7.1020974621904846e-12,True,2,10,0.2,,17596,algebraic_multigrid,600×600,basic_amg,7752,0.021533333333333335,0.01,14399.047311619002,23.751893365792284,23.751893365796455,17596,CSR,1e-06,2,14399.047311619002,1.0226343736981004e-07,4,"[600, 150, 37, 9]",0.24663663663663668,"[0.25, 0.24666666666666667, 0.24324324324324326]",8798,17596,8798,1.1349329205366356,3,0.25
650,AMG-basic,1753656811.0685265,30.0078,9.2079,25.6270,64.8427,432.18,432.20,0.02,0.00,,,,"O(k*nnz) [N=650, nnz≈4,225, k≤10]","O(nnz) [N=650, memory≈9,587 elements]",0.10,6.4670,True,2.182075046873184e-10,8.258052924915914e-12,1.436955558010116e-07,1.436955558010116e-07,8.285913617293314e-12,True,2,10,0.2,,20528,algebraic_multigrid,650×650,basic_amg,9044,0.021405917159763314,0.01,17342.14987483195,26.423602109512,26.423602109517876,20528,CSR,1e-06,2,17342.14987483195,1.436955558010116e-07,4,"[650, 162, 40, 10]",0.2487147831592276,"[0.24923076923076923, 0.24691358024691357, 0.25]",10264,20528,10264,1.1348960636886334,3,0.25
700,AMG-basic,1753656811.504609,32.6630,10.4725,25.7660,68.9016,432.20,432.20,0.01,0.00,,,,"O(k*nnz) [N=700, nnz≈4,900, k≤10]","O(nnz) [N=700, memory≈10,850 elements]",0.11,14.6980,True,1.4152401698336639e-10,5.376021866974379e-12,1.0021883397397453e-07,1.0021883397397453e-07,5.384946880794547e-12,True,2,10,0.2,,23596,algebraic_multigrid,700×700,basic_amg,10444,0.021314285714285713,0.01,18610.92341159497,26.325044891046687,26.325044891049895,23596,CSR,1e-06,2,18610.92341159497,1.0021883397397453e-07,4,"[700, 175, 43, 10]",0.24275747508305648,"[0.25, 0.24571428571428572, 0.23255813953488372]",11798,23596,11798,1.1296438146304097,3,0.25
750,AMG-basic,1753656811.9579802,35.1851,11.1656,25.4310,71.7817,432.20,432.22,0.02,0.00,,,,"O(k*nnz) [N=750, nnz≈5,625, k≤10]","O(nnz) [N=750, memory≈12,187 elements]",0.13,8.2859,True,1.84969816299881e-10,6.510191367048128e-12,1.4058717556547987e-07,1.4058717556547987e-07,6.53258271816384e-12,True,2,10,0.2,,26872,algebraic_multigrid,750×750,basic_amg,11930,0.021208888888888888,0.01,21520.917779514275,28.412347021951312,28.412347021951003,26872,CSR,1e-06,2,21520.917779514275,1.4058717556547987e-07,4,"[750, 187, 46, 11]",0.24481769097625874,"[0.24933333333333332, 0.24598930481283424, 0.2391304347826087]",13436,26872,13436,1.1262363788767813,3,0.25
800,AMG-basic,1753656812.4430382,37.0949,13.5256,26.3806,77.0011,432.22,432.23,0.01,0.00,,,,"O(k*nnz) [N=800, nnz≈6,400, k≤10]","O(nnz) [N=800, memory≈13,600 elements]",0.14,12.3703,True,1.2934563820462166e-10,4.523139035084618e-12,1.0459501928695355e-07,1.0459501928695355e-07,4.525912734914447e-12,True,2,10,0.2,,30324,algebraic_multigrid,800×800,basic_amg,13532,0.02114375,0.01,23110.259833352866,28.596432079865526,28.59643207987346,30324,CSR,1e-06,2,23110.259833352866,1.0459501928695355e-07,4,"[800, 200, 50, 12]",0.24666666666666667,"[0.25, 0.25, 0.24]",15162,30324,15162,1.1204552172627844,3,0.25
850,AMG-basic,1753656812.9397101,38.6150,15.2993,25.7033,79.6176,432.23,432.23,0.00,0.00,,,,"O(k*nnz) [N=850, nnz≈7,225, k≤10]","O(nnz) [N=850, memory≈15,087 elements]",0.16,0.0000,True,1.2435578665311165e-10,4.097720497913389e-12,1.0689021693563727e-07,1.0689021693563727e-07,4.102280677482105e-12,True,2,10,0.2,,34144,algebraic_multigrid,850×850,basic_amg,15220,0.02106574394463668,0.01,26056.290473338428,30.347552185766396,30.3475521857665,34144,CSR,1e-06,2,26056.290473338428,1.0689021693563727e-07,4,"[850, 212, 53, 13]",0.2482315945246023,"[0.24941176470588236, 0.25, 0.24528301886792453]",17072,34144,17072,1.121681997371879,3,0.25
900,AMG-basic,1753656813.4477503,41.2553,15.8252,26.3495,83.4301,432.23,432.24,0.01,0.00,,,,"O(k*nnz) [N=900, nnz≈8,100, k≤10]","O(nnz) [N=900, memory≈16,650 elements]",0.18,22.8523,True,2.1230360851695112e-10,6.808010389469158e-12,1.937575096140446e-07,1.937575096140446e-07,6.83583974518637e-12,True,2,10,0.2,,37954,algebraic_multigrid,900×900,basic_amg,16986,0.020970370370370372,0.01,28344.361020236593,31.184383743787496,31.18438374379524,37954,CSR,1e-06,2,28344.361020236593,1.937575096140446e-07,4,"[900, 225, 56, 14]",0.24962962962962965,"[0.25, 0.24888888888888888, 0.25]",18977,37954,18977,1.1172141763805488,3,0.25
950,AMG-basic,1753656813.9803236,43.5923,18.0679,26.0961,87.7562,432.24,432.25,0.01,0.00,,,,"O(k*nnz) [N=950, nnz≈9,025, k≤10]","O(nnz) [N=950, memory≈18,287 elements]",0.20,16.7770,True,1.8085083081527286e-10,5.661574553536262e-12,1.741969173685808e-07,1.741969173685808e-07,5.683255897024621e-12,True,2,10,0.2,,42248,algebraic_multigrid,950×950,basic_amg,18882,0.020921883656509696,0.01,30650.90161781715,31.943557239269115,31.943557239268728,42248,CSR,1e-06,2,30650.90161781715,1.741969173685808e-07,4,"[950, 237, 59, 14]",0.24523565582769055,"[0.24947368421052632, 0.2489451476793249, 0.23728813559322035]",21124,42248,21124,1.118737421883275,3,0.25
1000,AMG-basic,1753656814.5323656,46.8738,20.1340,28.5623,95.5701,432.25,432.26,0.01,0.00,,,,"O(k*nnz) [N=1000, nnz≈10,000, k≤10]","O(nnz) [N=1000, memory≈20,000 elements]",0.22,27.5886,True,1.4941158996428886e-10,5.004916043180291e-12,1.514191311123778e-07,1.514191311123778e-07,5.021661109207927e-12,True,2,10,0.2,,46518,algebraic_multigrid,1000×1000,basic_amg,20854,0.020854,0.01,30153.195888653136,29.852966298575176,29.852966298581055,46518,CSR,1e-06,2,30153.195888653136,1.514191311123778e-07,4,"[1000, 250, 62, 15]",0.24664516129032257,"[0.25, 0.248, 0.24193548387096775]",23259,46518,23259,1.1153255970077682,3,0.25
