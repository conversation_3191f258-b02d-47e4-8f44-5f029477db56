[{"input_size": 50, "algorithm_name": "AMG-basic", "timestamp": 1753656807.1192055, "execution_time_ms": 3.868642169982195, "setup_time_ms": 1.570443157106638, "cleanup_time_ms": 27.428340166807175, "total_time_ms": 32.86742549389601, "baseline_memory_mb": 428.4140625, "peak_memory_mb": 430.73828125, "memory_increment_mb": 2.32421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 9.053417253116895e-11, "relative_solution_error": 1.3031484376969566e-11, "final_residual_norm": 4.657041417573871e-09, "computed_residual_norm": 4.657041417573871e-09, "relative_residual": 1.3319253506272922e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 274, "algorithm_type": "algebraic_multigrid", "matrix_size": "50×50", "implementation": "basic_amg", "nnz": 100, "actual_sparsity_ratio": 0.04, "target_sparsity_ratio": 0.01, "b_norm": 349.64732936275755, "solution_norm": 6.947341523964542, "true_solution_norm": 6.94734152397629, "theoretical_flops": 274, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 349.64732936275755, "final_residual": 4.657041417573871e-09, "num_levels": 3, "level_sizes": [50, 12, 3], "avg_coarsening_ratio": 0.245, "coarsening_ratios": [0.24, 0.25], "setup_work": 137, "solve_work": 274, "total_nnz_all_levels": 137, "hierarchy_efficiency": 1.37, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈25, k≤10]", "theoretical_space_complexity": "O(nnz) [N=50, memory≈287 elements]", "theoretical_memory_mb": 0.002628326416015625, "efficiency_ratio": 0.001130842962184874}, {"input_size": 100, "algorithm_name": "AMG-basic", "timestamp": 1753656807.4174066, "execution_time_ms": 5.994278937578201, "setup_time_ms": 0.9941509924829006, "cleanup_time_ms": 26.65077894926071, "total_time_ms": 33.639208879321814, "baseline_memory_mb": 430.73828125, "peak_memory_mb": 430.85546875, "memory_increment_mb": 0.1171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 4.3933084597211523e-10, "relative_solution_error": 4.091663299043139e-11, "final_residual_norm": 4.448580960544149e-08, "computed_residual_norm": 4.448580960544149e-08, "relative_residual": 4.1011000463551225e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 734, "algorithm_type": "algebraic_multigrid", "matrix_size": "100×100", "implementation": "basic_amg", "nnz": 292, "actual_sparsity_ratio": 0.0292, "target_sparsity_ratio": 0.01, "b_norm": 1084.7287094343997, "solution_norm": 10.73721892200682, "true_solution_norm": 10.73721892206662, "theoretical_flops": 734, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 1084.7287094343997, "final_residual": 4.448580960544149e-08, "num_levels": 3, "level_sizes": [100, 25, 6], "avg_coarsening_ratio": 0.245, "coarsening_ratios": [0.25, 0.24], "setup_work": 367, "solve_work": 734, "total_nnz_all_levels": 367, "hierarchy_efficiency": 1.2568493150684932, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=100, nnz≈100, k≤10]", "theoretical_space_complexity": "O(nnz) [N=100, memory≈650 elements]", "theoretical_memory_mb": 0.0061092376708984375, "efficiency_ratio": 0.05213216145833333}, {"input_size": 150, "algorithm_name": "AMG-basic", "timestamp": 1753656807.6489184, "execution_time_ms": 7.991311699151993, "setup_time_ms": 1.2210030108690262, "cleanup_time_ms": 25.905488058924675, "total_time_ms": 35.117802768945694, "baseline_memory_mb": 430.85546875, "peak_memory_mb": 430.85546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.896775801590219e-10, "relative_solution_error": 2.4242832564779973e-11, "final_residual_norm": 4.388574458950841e-08, "computed_residual_norm": 4.388574458950841e-08, "relative_residual": 2.4274953514768134e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 1460, "algorithm_type": "algebraic_multigrid", "matrix_size": "150×150", "implementation": "basic_amg", "nnz": 594, "actual_sparsity_ratio": 0.0264, "target_sparsity_ratio": 0.01, "b_norm": 1807.861117542807, "solution_norm": 11.948998921046627, "true_solution_norm": 11.948998921019896, "theoretical_flops": 1460, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 1807.861117542807, "final_residual": 4.388574458950841e-08, "num_levels": 3, "level_sizes": [150, 37, 9], "avg_coarsening_ratio": 0.24495495495495495, "coarsening_ratios": [0.24666666666666667, 0.24324324324324326], "setup_work": 730, "solve_work": 1460, "total_nnz_all_levels": 730, "hierarchy_efficiency": 1.228956228956229, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈225, k≤10]", "theoretical_space_complexity": "O(nnz) [N=150, memory≈1,087 elements]", "theoretical_memory_mb": 0.010448455810546875, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "AMG-basic", "timestamp": 1753656807.9019136, "execution_time_ms": 11.097086686640978, "setup_time_ms": 1.5366203151643276, "cleanup_time_ms": 26.38419670984149, "total_time_ms": 39.017903711646795, "baseline_memory_mb": 430.85546875, "peak_memory_mb": 431.09375, "memory_increment_mb": 0.23828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 9.783839605895963e-10, "relative_solution_error": 7.124037264505793e-11, "final_residual_norm": 1.9759045452632511e-07, "computed_residual_norm": 1.9759045452632511e-07, "relative_residual": 7.120102694227675e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 2382, "algorithm_type": "algebraic_multigrid", "matrix_size": "200×200", "implementation": "basic_amg", "nnz": 990, "actual_sparsity_ratio": 0.02475, "target_sparsity_ratio": 0.01, "b_norm": 2775.106806907621, "solution_norm": 13.733560399243045, "true_solution_norm": 13.733560399300753, "theoretical_flops": 2382, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 2775.106806907621, "final_residual": 1.9759045452632511e-07, "num_levels": 4, "level_sizes": [200, 50, 12, 3], "avg_coarsening_ratio": 0.24666666666666667, "coarsening_ratios": [0.25, 0.24, 0.25], "setup_work": 1191, "solve_work": 2382, "total_nnz_all_levels": 1191, "hierarchy_efficiency": 1.2030303030303031, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=200, nnz≈400, k≤10]", "theoretical_space_complexity": "O(nnz) [N=200, memory≈1,600 elements]", "theoretical_memory_mb": 0.015645980834960938, "efficiency_ratio": 0.0656618212090164}, {"input_size": 250, "algorithm_name": "AMG-basic", "timestamp": 1753656808.177874, "execution_time_ms": 13.364170212298632, "setup_time_ms": 1.7944881692528725, "cleanup_time_ms": 26.861791964620352, "total_time_ms": 42.020450346171856, "baseline_memory_mb": 431.109375, "peak_memory_mb": 431.25390625, "memory_increment_mb": 0.14453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.8794489282382465e-10, "relative_solution_error": 2.474216492819534e-11, "final_residual_norm": 9.813577357761e-08, "computed_residual_norm": 9.813577357761e-08, "relative_residual": 2.477661640972374e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 3504, "algorithm_type": "algebraic_multigrid", "matrix_size": "250×250", "implementation": "basic_amg", "nnz": 1482, "actual_sparsity_ratio": 0.023712, "target_sparsity_ratio": 0.01, "b_norm": 3960.822250898472, "solution_norm": 15.679504762375641, "true_solution_norm": 15.679504762404024, "theoretical_flops": 3504, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 3960.822250898472, "final_residual": 9.813577357761e-08, "num_levels": 4, "level_sizes": [250, 62, 15, 3], "avg_coarsening_ratio": 0.2299784946236559, "coarsening_ratios": [0.248, 0.24193548387096775, 0.2], "setup_work": 1752, "solve_work": 3504, "total_nnz_all_levels": 1752, "hierarchy_efficiency": 1.1821862348178138, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈625, k≤10]", "theoretical_space_complexity": "O(nnz) [N=250, memory≈2,187 elements]", "theoretical_memory_mb": 0.021701812744140625, "efficiency_ratio": 0.15015308277027026}, {"input_size": 300, "algorithm_name": "AMG-basic", "timestamp": 1753656808.4899867, "execution_time_ms": 15.29725082218647, "setup_time_ms": 2.2515500895678997, "cleanup_time_ms": 25.42246924713254, "total_time_ms": 42.97127015888691, "baseline_memory_mb": 431.25390625, "peak_memory_mb": 431.5703125, "memory_increment_mb": 0.31640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 6.682457699415073e-10, "relative_solution_error": 3.743443835221379e-11, "final_residual_norm": 2.0362139516516123e-07, "computed_residual_norm": 2.0362139516516123e-07, "relative_residual": 3.7644611006651945e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 4890, "algorithm_type": "algebraic_multigrid", "matrix_size": "300×300", "implementation": "basic_amg", "nnz": 2080, "actual_sparsity_ratio": 0.02311111111111111, "target_sparsity_ratio": 0.01, "b_norm": 5409.04500591547, "solution_norm": 17.851096459710433, "true_solution_norm": 17.85109645973862, "theoretical_flops": 4890, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 5409.04500591547, "final_residual": 2.0362139516516123e-07, "num_levels": 4, "level_sizes": [300, 75, 18, 4], "avg_coarsening_ratio": 0.2374074074074074, "coarsening_ratios": [0.25, 0.24, 0.2222222222222222], "setup_work": 2445, "solve_work": 4890, "total_nnz_all_levels": 2445, "hierarchy_efficiency": 1.1754807692307692, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=300, nnz≈900, k≤10]", "theoretical_space_complexity": "O(nnz) [N=300, memory≈2,850 elements]", "theoretical_memory_mb": 0.028615951538085938, "efficiency_ratio": 0.09044053819444445}, {"input_size": 350, "algorithm_name": "AMG-basic", "timestamp": 1753656808.8021019, "execution_time_ms": 17.238991614431143, "setup_time_ms": 2.657562028616667, "cleanup_time_ms": 25.185801088809967, "total_time_ms": 45.08235473185778, "baseline_memory_mb": 431.5703125, "peak_memory_mb": 431.796875, "memory_increment_mb": 0.2265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 5.218975466058281e-10, "relative_solution_error": 2.94117963924944e-11, "final_residual_norm": 1.8508455876715224e-07, "computed_residual_norm": 1.8508455876715224e-07, "relative_residual": 2.9487808724813575e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 6446, "algorithm_type": "algebraic_multigrid", "matrix_size": "350×350", "implementation": "basic_amg", "nnz": 2784, "actual_sparsity_ratio": 0.0227265306122449, "target_sparsity_ratio": 0.01, "b_norm": 6276.646748980238, "solution_norm": 17.744497467613055, "true_solution_norm": 17.74449746765591, "theoretical_flops": 6446, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 6276.646748980238, "final_residual": 1.8508455876715224e-07, "num_levels": 4, "level_sizes": [350, 87, 21, 5], "avg_coarsening_ratio": 0.24268199233716473, "coarsening_ratios": [0.24857142857142858, 0.2413793103448276, 0.23809523809523808], "setup_work": 3223, "solve_work": 6446, "total_nnz_all_levels": 3223, "hierarchy_efficiency": 1.1576867816091954, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈1,225, k≤10]", "theoretical_space_complexity": "O(nnz) [N=350, memory≈3,587 elements]", "theoretical_memory_mb": 0.036388397216796875, "efficiency_ratio": 0.16061085668103448}, {"input_size": 400, "algorithm_name": "AMG-basic", "timestamp": 1753656809.1222093, "execution_time_ms": 19.754917453974485, "setup_time_ms": 3.405860159546137, "cleanup_time_ms": 26.302862912416458, "total_time_ms": 49.46364052593708, "baseline_memory_mb": 431.796875, "peak_memory_mb": 432.15234375, "memory_increment_mb": 0.35546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.865151439258596e-10, "relative_solution_error": 1.960037094869967e-11, "final_residual_norm": 1.5670515784617132e-07, "computed_residual_norm": 1.5670515784617132e-07, "relative_residual": 1.968631692007221e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 8326, "algorithm_type": "algebraic_multigrid", "matrix_size": "400×400", "implementation": "basic_amg", "nnz": 3584, "actual_sparsity_ratio": 0.0224, "target_sparsity_ratio": 0.01, "b_norm": 7960.105411408591, "solution_norm": 19.71978718858238, "true_solution_norm": 19.719787188594093, "theoretical_flops": 8326, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 7960.105411408591, "final_residual": 1.5670515784617132e-07, "num_levels": 4, "level_sizes": [400, 100, 25, 6], "avg_coarsening_ratio": 0.24666666666666667, "coarsening_ratios": [0.25, 0.25, 0.24], "setup_work": 4163, "solve_work": 8326, "total_nnz_all_levels": 4163, "hierarchy_efficiency": 1.1615513392857142, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=400, nnz≈1,600, k≤10]", "theoretical_space_complexity": "O(nnz) [N=400, memory≈4,400 elements]", "theoretical_memory_mb": 0.04501914978027344, "efficiency_ratio": 0.12664727850274726}, {"input_size": 450, "algorithm_name": "AMG-basic", "timestamp": 1753656809.4664035, "execution_time_ms": 21.893040370196104, "setup_time_ms": 4.685461055487394, "cleanup_time_ms": 25.454338639974594, "total_time_ms": 52.03284006565809, "baseline_memory_mb": 432.15234375, "peak_memory_mb": 432.15234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 3.0602768908694256e-10, "relative_solution_error": 1.4218863207348514e-11, "final_residual_norm": 1.3929951321484937e-07, "computed_residual_norm": 1.3929951321484937e-07, "relative_residual": 1.423142579590161e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 10214, "algorithm_type": "algebraic_multigrid", "matrix_size": "450×450", "implementation": "basic_amg", "nnz": 4462, "actual_sparsity_ratio": 0.022034567901234568, "target_sparsity_ratio": 0.01, "b_norm": 9788.162845564291, "solution_norm": 21.52265512539787, "true_solution_norm": 21.52265512539589, "theoretical_flops": 10214, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 9788.162845564291, "final_residual": 1.3929951321484937e-07, "num_levels": 4, "level_sizes": [450, 112, 28, 7], "avg_coarsening_ratio": 0.24962962962962965, "coarsening_ratios": [0.24888888888888888, 0.25, 0.25], "setup_work": 5107, "solve_work": 10214, "total_nnz_all_levels": 5107, "hierarchy_efficiency": 1.1445540116539668, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈2,025, k≤10]", "theoretical_space_complexity": "O(nnz) [N=450, memory≈5,287 elements]", "theoretical_memory_mb": 0.054508209228515625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "AMG-basic", "timestamp": 1753656809.8338544, "execution_time_ms": 26.124423928558826, "setup_time_ms": 5.4795402102172375, "cleanup_time_ms": 36.72174783423543, "total_time_ms": 68.3257119730115, "baseline_memory_mb": 432.15234375, "peak_memory_mb": 432.171875, "memory_increment_mb": 0.01953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.879373199691769e-10, "relative_solution_error": 1.2134214862924293e-11, "final_residual_norm": 1.458542909849727e-07, "computed_residual_norm": 1.458542909849727e-07, "relative_residual": 1.2174114441769737e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 12434, "algorithm_type": "algebraic_multigrid", "matrix_size": "500×500", "implementation": "basic_amg", "nnz": 5472, "actual_sparsity_ratio": 0.021888, "target_sparsity_ratio": 0.01, "b_norm": 11980.689986332183, "solution_norm": 23.729373776690444, "true_solution_norm": 23.729373776704765, "theoretical_flops": 12434, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 11980.689986332183, "final_residual": 1.458542909849727e-07, "num_levels": 4, "level_sizes": [500, 125, 31, 7], "avg_coarsening_ratio": 0.24126881720430107, "coarsening_ratios": [0.25, 0.248, 0.22580645161290322], "setup_work": 6217, "solve_work": 12434, "total_nnz_all_levels": 6217, "hierarchy_efficiency": 1.1361476608187135, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=500, nnz≈2,500, k≤10]", "theoretical_space_complexity": "O(nnz) [N=500, memory≈6,250 elements]", "theoretical_memory_mb": 0.06485557556152344, "efficiency_ratio": 3.32060546875}, {"input_size": 550, "algorithm_name": "AMG-basic", "timestamp": 1753656810.2589042, "execution_time_ms": 25.88062882423401, "setup_time_ms": 6.708184257149696, "cleanup_time_ms": 25.725819170475006, "total_time_ms": 58.31463225185871, "baseline_memory_mb": 432.171875, "peak_memory_mb": 432.171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.793340432198222e-10, "relative_solution_error": 1.1872205242053216e-11, "final_residual_norm": 1.5563099713807226e-07, "computed_residual_norm": 1.5563099713807226e-07, "relative_residual": 1.1909915403931312e-11, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 15026, "algorithm_type": "algebraic_multigrid", "matrix_size": "550×550", "implementation": "basic_amg", "nnz": 6556, "actual_sparsity_ratio": 0.021672727272727274, "target_sparsity_ratio": 0.01, "b_norm": 13067.34698440431, "solution_norm": 23.528404161204104, "true_solution_norm": 23.528404161206474, "theoretical_flops": 15026, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 13067.34698440431, "final_residual": 1.5563099713807226e-07, "num_levels": 4, "level_sizes": [550, 137, 34, 8], "avg_coarsening_ratio": 0.2441867364065732, "coarsening_ratios": [0.24909090909090909, 0.24817518248175183, 0.23529411764705882], "setup_work": 7513, "solve_work": 15026, "total_nnz_all_levels": 7513, "hierarchy_efficiency": 1.145973154362416, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈3,025, k≤10]", "theoretical_space_complexity": "O(nnz) [N=550, memory≈7,287 elements]", "theoretical_memory_mb": 0.07606124877929688, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "AMG-basic", "timestamp": 1753656810.653367, "execution_time_ms": 28.219810407608747, "setup_time_ms": 7.889778818935156, "cleanup_time_ms": 25.712830014526844, "total_time_ms": 61.82241924107075, "baseline_memory_mb": 432.171875, "peak_memory_mb": 432.1796875, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.684089177346004e-10, "relative_solution_error": 7.090336553005708e-12, "final_residual_norm": 1.0226343736981004e-07, "computed_residual_norm": 1.0226343736981004e-07, "relative_residual": 7.1020974621904846e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 17596, "algorithm_type": "algebraic_multigrid", "matrix_size": "600×600", "implementation": "basic_amg", "nnz": 7752, "actual_sparsity_ratio": 0.021533333333333335, "target_sparsity_ratio": 0.01, "b_norm": 14399.047311619002, "solution_norm": 23.751893365792284, "true_solution_norm": 23.751893365796455, "theoretical_flops": 17596, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 14399.047311619002, "final_residual": 1.0226343736981004e-07, "num_levels": 4, "level_sizes": [600, 150, 37, 9], "avg_coarsening_ratio": 0.24663663663663668, "coarsening_ratios": [0.25, 0.24666666666666667, 0.24324324324324326], "setup_work": 8798, "solve_work": 17596, "total_nnz_all_levels": 8798, "hierarchy_efficiency": 1.1349329205366356, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=600, nnz≈3,600, k≤10]", "theoretical_space_complexity": "O(nnz) [N=600, memory≈8,400 elements]", "theoretical_memory_mb": 0.08812522888183594, "efficiency_ratio": 11.280029296875}, {"input_size": 650, "algorithm_name": "AMG-basic", "timestamp": 1753656811.0685265, "execution_time_ms": 30.007806327193975, "setup_time_ms": 9.20790433883667, "cleanup_time_ms": 25.626963935792446, "total_time_ms": 64.84267460182309, "baseline_memory_mb": 432.1796875, "peak_memory_mb": 432.1953125, "memory_increment_mb": 0.015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.182075046873184e-10, "relative_solution_error": 8.258052924915914e-12, "final_residual_norm": 1.436955558010116e-07, "computed_residual_norm": 1.436955558010116e-07, "relative_residual": 8.285913617293314e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 20528, "algorithm_type": "algebraic_multigrid", "matrix_size": "650×650", "implementation": "basic_amg", "nnz": 9044, "actual_sparsity_ratio": 0.021405917159763314, "target_sparsity_ratio": 0.01, "b_norm": 17342.14987483195, "solution_norm": 26.423602109512, "true_solution_norm": 26.423602109517876, "theoretical_flops": 20528, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 17342.14987483195, "final_residual": 1.436955558010116e-07, "num_levels": 4, "level_sizes": [650, 162, 40, 10], "avg_coarsening_ratio": 0.2487147831592276, "coarsening_ratios": [0.24923076923076923, 0.24691358024691357, 0.25], "setup_work": 10264, "solve_work": 20528, "total_nnz_all_levels": 10264, "hierarchy_efficiency": 1.1348960636886334, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈4,225, k≤10]", "theoretical_space_complexity": "O(nnz) [N=650, memory≈9,587 elements]", "theoretical_memory_mb": 0.10104751586914062, "efficiency_ratio": 6.467041015625}, {"input_size": 700, "algorithm_name": "AMG-basic", "timestamp": 1753656811.504609, "execution_time_ms": 32.663048803806305, "setup_time_ms": 10.472545865923166, "cleanup_time_ms": 25.765973143279552, "total_time_ms": 68.90156781300902, "baseline_memory_mb": 432.1953125, "peak_memory_mb": 432.203125, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.4152401698336639e-10, "relative_solution_error": 5.376021866974379e-12, "final_residual_norm": 1.0021883397397453e-07, "computed_residual_norm": 1.0021883397397453e-07, "relative_residual": 5.384946880794547e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 23596, "algorithm_type": "algebraic_multigrid", "matrix_size": "700×700", "implementation": "basic_amg", "nnz": 10444, "actual_sparsity_ratio": 0.021314285714285713, "target_sparsity_ratio": 0.01, "b_norm": 18610.92341159497, "solution_norm": 26.325044891046687, "true_solution_norm": 26.325044891049895, "theoretical_flops": 23596, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 18610.92341159497, "final_residual": 1.0021883397397453e-07, "num_levels": 4, "level_sizes": [700, 175, 43, 10], "avg_coarsening_ratio": 0.24275747508305648, "coarsening_ratios": [0.25, 0.24571428571428572, 0.23255813953488372], "setup_work": 11798, "solve_work": 23596, "total_nnz_all_levels": 11798, "hierarchy_efficiency": 1.1296438146304097, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=700, nnz≈4,900, k≤10]", "theoretical_space_complexity": "O(nnz) [N=700, memory≈10,850 elements]", "theoretical_memory_mb": 0.11482810974121094, "efficiency_ratio": 14.697998046875}, {"input_size": 750, "algorithm_name": "AMG-basic", "timestamp": 1753656811.9579802, "execution_time_ms": 35.18513832241297, "setup_time_ms": 11.165560688823462, "cleanup_time_ms": 25.43096197769046, "total_time_ms": 71.78166098892689, "baseline_memory_mb": 432.203125, "peak_memory_mb": 432.21875, "memory_increment_mb": 0.015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.84969816299881e-10, "relative_solution_error": 6.510191367048128e-12, "final_residual_norm": 1.4058717556547987e-07, "computed_residual_norm": 1.4058717556547987e-07, "relative_residual": 6.53258271816384e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 26872, "algorithm_type": "algebraic_multigrid", "matrix_size": "750×750", "implementation": "basic_amg", "nnz": 11930, "actual_sparsity_ratio": 0.021208888888888888, "target_sparsity_ratio": 0.01, "b_norm": 21520.917779514275, "solution_norm": 28.412347021951312, "true_solution_norm": 28.412347021951003, "theoretical_flops": 26872, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 21520.917779514275, "final_residual": 1.4058717556547987e-07, "num_levels": 4, "level_sizes": [750, 187, 46, 11], "avg_coarsening_ratio": 0.24481769097625874, "coarsening_ratios": [0.24933333333333332, 0.24598930481283424, 0.2391304347826087], "setup_work": 13436, "solve_work": 26872, "total_nnz_all_levels": 13436, "hierarchy_efficiency": 1.1262363788767813, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈5,625, k≤10]", "theoretical_space_complexity": "O(nnz) [N=750, memory≈12,187 elements]", "theoretical_memory_mb": 0.12946701049804688, "efficiency_ratio": 8.285888671875}, {"input_size": 800, "algorithm_name": "AMG-basic", "timestamp": 1753656812.4430382, "execution_time_ms": 37.09485875442624, "setup_time_ms": 13.52560892701149, "cleanup_time_ms": 26.38059388846159, "total_time_ms": 77.00106156989932, "baseline_memory_mb": 432.21875, "peak_memory_mb": 432.23046875, "memory_increment_mb": 0.01171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.2934563820462166e-10, "relative_solution_error": 4.523139035084618e-12, "final_residual_norm": 1.0459501928695355e-07, "computed_residual_norm": 1.0459501928695355e-07, "relative_residual": 4.525912734914447e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 30324, "algorithm_type": "algebraic_multigrid", "matrix_size": "800×800", "implementation": "basic_amg", "nnz": 13532, "actual_sparsity_ratio": 0.02114375, "target_sparsity_ratio": 0.01, "b_norm": 23110.259833352866, "solution_norm": 28.596432079865526, "true_solution_norm": 28.59643207987346, "theoretical_flops": 30324, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 23110.259833352866, "final_residual": 1.0459501928695355e-07, "num_levels": 4, "level_sizes": [800, 200, 50, 12], "avg_coarsening_ratio": 0.24666666666666667, "coarsening_ratios": [0.25, 0.25, 0.24], "setup_work": 15162, "solve_work": 30324, "total_nnz_all_levels": 15162, "hierarchy_efficiency": 1.1204552172627844, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=800, nnz≈6,400, k≤10]", "theoretical_space_complexity": "O(nnz) [N=800, memory≈13,600 elements]", "theoretical_memory_mb": 0.14496421813964844, "efficiency_ratio": 12.370279947916666}, {"input_size": 850, "algorithm_name": "AMG-basic", "timestamp": 1753656812.9397101, "execution_time_ms": 38.61498944461346, "setup_time_ms": 15.299326740205288, "cleanup_time_ms": 25.70328302681446, "total_time_ms": 79.6175992116332, "baseline_memory_mb": 432.23046875, "peak_memory_mb": 432.23046875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.2435578665311165e-10, "relative_solution_error": 4.097720497913389e-12, "final_residual_norm": 1.0689021693563727e-07, "computed_residual_norm": 1.0689021693563727e-07, "relative_residual": 4.102280677482105e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 34144, "algorithm_type": "algebraic_multigrid", "matrix_size": "850×850", "implementation": "basic_amg", "nnz": 15220, "actual_sparsity_ratio": 0.02106574394463668, "target_sparsity_ratio": 0.01, "b_norm": 26056.290473338428, "solution_norm": 30.347552185766396, "true_solution_norm": 30.3475521857665, "theoretical_flops": 34144, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 26056.290473338428, "final_residual": 1.0689021693563727e-07, "num_levels": 4, "level_sizes": [850, 212, 53, 13], "avg_coarsening_ratio": 0.2482315945246023, "coarsening_ratios": [0.24941176470588236, 0.25, 0.24528301886792453], "setup_work": 17072, "solve_work": 34144, "total_nnz_all_levels": 17072, "hierarchy_efficiency": 1.121681997371879, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈7,225, k≤10]", "theoretical_space_complexity": "O(nnz) [N=850, memory≈15,087 elements]", "theoretical_memory_mb": 0.16131973266601562, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "AMG-basic", "timestamp": 1753656813.4477503, "execution_time_ms": 41.25533923506737, "setup_time_ms": 15.825188253074884, "cleanup_time_ms": 26.349533814936876, "total_time_ms": 83.43006130307913, "baseline_memory_mb": 432.23046875, "peak_memory_mb": 432.23828125, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 2.1230360851695112e-10, "relative_solution_error": 6.808010389469158e-12, "final_residual_norm": 1.937575096140446e-07, "computed_residual_norm": 1.937575096140446e-07, "relative_residual": 6.83583974518637e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 37954, "algorithm_type": "algebraic_multigrid", "matrix_size": "900×900", "implementation": "basic_amg", "nnz": 16986, "actual_sparsity_ratio": 0.020970370370370372, "target_sparsity_ratio": 0.01, "b_norm": 28344.361020236593, "solution_norm": 31.184383743787496, "true_solution_norm": 31.18438374379524, "theoretical_flops": 37954, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 28344.361020236593, "final_residual": 1.937575096140446e-07, "num_levels": 4, "level_sizes": [900, 225, 56, 14], "avg_coarsening_ratio": 0.24962962962962965, "coarsening_ratios": [0.25, 0.24888888888888888, 0.25], "setup_work": 18977, "solve_work": 37954, "total_nnz_all_levels": 18977, "hierarchy_efficiency": 1.1172141763805488, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=900, nnz≈8,100, k≤10]", "theoretical_space_complexity": "O(nnz) [N=900, memory≈16,650 elements]", "theoretical_memory_mb": 0.17853355407714844, "efficiency_ratio": 22.852294921875}, {"input_size": 950, "algorithm_name": "AMG-basic", "timestamp": 1753656813.9803236, "execution_time_ms": 43.59225081279874, "setup_time_ms": 18.067888915538788, "cleanup_time_ms": 26.096087880432606, "total_time_ms": 87.75622760877013, "baseline_memory_mb": 432.23828125, "peak_memory_mb": 432.25, "memory_increment_mb": 0.01171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.8085083081527286e-10, "relative_solution_error": 5.661574553536262e-12, "final_residual_norm": 1.741969173685808e-07, "computed_residual_norm": 1.741969173685808e-07, "relative_residual": 5.683255897024621e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 42248, "algorithm_type": "algebraic_multigrid", "matrix_size": "950×950", "implementation": "basic_amg", "nnz": 18882, "actual_sparsity_ratio": 0.020921883656509696, "target_sparsity_ratio": 0.01, "b_norm": 30650.90161781715, "solution_norm": 31.943557239269115, "true_solution_norm": 31.943557239268728, "theoretical_flops": 42248, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 30650.90161781715, "final_residual": 1.741969173685808e-07, "num_levels": 4, "level_sizes": [950, 237, 59, 14], "avg_coarsening_ratio": 0.24523565582769055, "coarsening_ratios": [0.24947368421052632, 0.2489451476793249, 0.23728813559322035], "setup_work": 21124, "solve_work": 42248, "total_nnz_all_levels": 21124, "hierarchy_efficiency": 1.118737421883275, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈9,025, k≤10]", "theoretical_space_complexity": "O(nnz) [N=950, memory≈18,287 elements]", "theoretical_memory_mb": 0.19660568237304688, "efficiency_ratio": 16.777018229166668}, {"input_size": 1000, "algorithm_name": "AMG-basic", "timestamp": 1753656814.5323656, "execution_time_ms": 46.87378220260143, "setup_time_ms": 20.134015008807182, "cleanup_time_ms": 28.562307823449373, "total_time_ms": 95.57010503485799, "baseline_memory_mb": 432.25, "peak_memory_mb": 432.2578125, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "solution_error": 1.4941158996428886e-10, "relative_solution_error": 5.004916043180291e-12, "final_residual_norm": 1.514191311123778e-07, "computed_residual_norm": 1.514191311123778e-07, "relative_residual": 5.021661109207927e-12, "converged": true, "iterations_performed": 2, "max_iterations": 10, "iteration_efficiency": 0.2, "convergence_rate": null, "operations_count": 46518, "algorithm_type": "algebraic_multigrid", "matrix_size": "1000×1000", "implementation": "basic_amg", "nnz": 20854, "actual_sparsity_ratio": 0.020854, "target_sparsity_ratio": 0.01, "b_norm": 30153.195888653136, "solution_norm": 29.852966298575176, "true_solution_norm": 29.852966298581055, "theoretical_flops": 46518, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 2, "initial_residual": 30153.195888653136, "final_residual": 1.514191311123778e-07, "num_levels": 4, "level_sizes": [1000, 250, 62, 15], "avg_coarsening_ratio": 0.24664516129032257, "coarsening_ratios": [0.25, 0.248, 0.24193548387096775], "setup_work": 23259, "solve_work": 46518, "total_nnz_all_levels": 23259, "hierarchy_efficiency": 1.1153255970077682, "max_levels": 3, "coarsening_factor": 0.25}, "theoretical_time_complexity": "O(k*nnz) [N=1000, nnz≈10,000, k≤10]", "theoretical_space_complexity": "O(nnz) [N=1000, memory≈20,000 elements]", "theoretical_memory_mb": 0.21553611755371094, "efficiency_ratio": 27.588623046875}]