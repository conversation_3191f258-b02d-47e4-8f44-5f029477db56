[{"input_size": 16, "algorithm_name": "AES-256-CBC", "timestamp": 1753702861.9899194, "execution_time_ms": 0.09793993085622787, "setup_time_ms": 0.01833168789744377, "cleanup_time_ms": 24.09316273406148, "total_time_ms": 24.20943435281515, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 1, "actual_blocks": 2, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 1.0, "output_size_bytes": 32, "expansion_ratio": 2.0, "throughput_mbps": 2.41, "bytes_per_second": 2523667, "blocks_per_second": 315458, "encryption_time_ms": 0.00633997842669487, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 9.1552734375e-05, "efficiency_ratio": 0.0}, {"input_size": 32, "algorithm_name": "AES-256-CBC", "timestamp": 1753702862.1583009, "execution_time_ms": 0.0981396995484829, "setup_time_ms": 0.021245796233415604, "cleanup_time_ms": 23.985919076949358, "total_time_ms": 24.105304572731256, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 2, "actual_blocks": 3, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.5, "output_size_bytes": 48, "expansion_ratio": 1.5, "throughput_mbps": 5.4, "bytes_per_second": 5664782, "blocks_per_second": 531073, "encryption_time_ms": 0.005648937076330185, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0001373291015625, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "AES-256-CBC", "timestamp": 1753702862.3288689, "execution_time_ms": 0.09910659864544868, "setup_time_ms": 0.020207837224006653, "cleanup_time_ms": 24.18668707832694, "total_time_ms": 24.306001514196396, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 4, "actual_blocks": 5, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.25, "output_size_bytes": 80, "expansion_ratio": 1.25, "throughput_mbps": 10.31, "bytes_per_second": 10805798, "blocks_per_second": 844203, "encryption_time_ms": 0.005922745913267136, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "AES-256-CBC", "timestamp": 1753702862.496902, "execution_time_ms": 0.10025547817349434, "setup_time_ms": 0.018569640815258026, "cleanup_time_ms": 24.105112999677658, "total_time_ms": 24.22393811866641, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 8, "actual_blocks": 9, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.125, "output_size_bytes": 144, "expansion_ratio": 1.125, "throughput_mbps": 17.79, "bytes_per_second": 18658560, "blocks_per_second": 1311930, "encryption_time_ms": 0.006860122084617615, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-256-CBC", "timestamp": 1753702862.6638062, "execution_time_ms": 0.10748514905571938, "setup_time_ms": 0.018823891878128052, "cleanup_time_ms": 25.368678849190474, "total_time_ms": 25.49498789012432, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 16, "actual_blocks": 17, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0625, "output_size_bytes": 272, "expansion_ratio": 1.0625, "throughput_mbps": 37.37, "bytes_per_second": 39181513, "blocks_per_second": 2601897, "encryption_time_ms": 0.0065336935222148895, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-CBC", "timestamp": 1753702862.8405552, "execution_time_ms": 0.11381115764379501, "setup_time_ms": 0.024342909455299377, "cleanup_time_ms": 24.39195103943348, "total_time_ms": 24.530105106532574, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 32, "actual_blocks": 33, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0312, "output_size_bytes": 528, "expansion_ratio": 1.0312, "throughput_mbps": 63.58, "bytes_per_second": 66669392, "blocks_per_second": 4297050, "encryption_time_ms": 0.007679685950279236, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.0190945, "execution_time_ms": 0.10349564254283905, "setup_time_ms": 0.022771302610635757, "cleanup_time_ms": 23.545220959931612, "total_time_ms": 23.671487905085087, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 64, "actual_blocks": 65, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0156, "output_size_bytes": 1040, "expansion_ratio": 1.0156, "throughput_mbps": 134.49, "bytes_per_second": 141026310, "blocks_per_second": 8951865, "encryption_time_ms": 0.007261056452989578, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.187056, "execution_time_ms": 0.10467348620295525, "setup_time_ms": 0.02552289515733719, "cleanup_time_ms": 23.809432052075863, "total_time_ms": 23.939628433436155, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 128, "actual_blocks": 129, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0078, "output_size_bytes": 2064, "expansion_ratio": 1.0078, "throughput_mbps": 196.84, "bytes_per_second": 206403534, "blocks_per_second": 13001003, "encryption_time_ms": 0.009922310709953308, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0059051513671875, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.3531687, "execution_time_ms": 0.10464489459991455, "setup_time_ms": 0.029695220291614532, "cleanup_time_ms": 23.534557782113552, "total_time_ms": 23.66889789700508, "baseline_memory_mb": 420.90234375, "peak_memory_mb": 420.90234375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 256, "actual_blocks": 257, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0039, "output_size_bytes": 4112, "expansion_ratio": 1.0039, "throughput_mbps": 343.5, "bytes_per_second": 360185619, "blocks_per_second": 22599537, "encryption_time_ms": 0.0113719142973423, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0117645263671875, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.5204172, "execution_time_ms": 0.11373991146683693, "setup_time_ms": 0.043979380279779434, "cleanup_time_ms": 23.964475840330124, "total_time_ms": 24.12219513207674, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 512, "actual_blocks": 513, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.002, "output_size_bytes": 8208, "expansion_ratio": 1.002, "throughput_mbps": 468.74, "bytes_per_second": 491511679, "blocks_per_second": 30779478, "encryption_time_ms": 0.016666948795318604, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=512]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0234832763671875, "efficiency_ratio": 0.0}, {"input_size": 16384, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.7018495, "execution_time_ms": 0.13300292193889618, "setup_time_ms": 0.05808193236589432, "cleanup_time_ms": 24.36455711722374, "total_time_ms": 24.55564197152853, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16384, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 1024, "actual_blocks": 1025, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.001, "output_size_bytes": 16400, "expansion_ratio": 1.001, "throughput_mbps": 529.53, "bytes_per_second": 555256321, "blocks_per_second": 34737410, "encryption_time_ms": 0.029507093131542206, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1024]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0469207763671875, "efficiency_ratio": 0.0}, {"input_size": 32768, "algorithm_name": "AES-256-CBC", "timestamp": 1753702863.8708704, "execution_time_ms": 0.1491648145020008, "setup_time_ms": 0.1365160569548607, "cleanup_time_ms": 23.749978747218847, "total_time_ms": 24.03565961867571, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32768, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 2048, "actual_blocks": 2049, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0005, "output_size_bytes": 32784, "expansion_ratio": 1.0005, "throughput_mbps": 681.45, "bytes_per_second": 714548580, "blocks_per_second": 44681092, "encryption_time_ms": 0.04585832357406616, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2048]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0937957763671875, "efficiency_ratio": 0.0}, {"input_size": 65536, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.040283, "execution_time_ms": 0.19991714507341385, "setup_time_ms": 0.17203111201524734, "cleanup_time_ms": 23.646058049052954, "total_time_ms": 24.018006306141615, "baseline_memory_mb": 420.921875, "peak_memory_mb": 420.921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 65536, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 4096, "actual_blocks": 4097, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0002, "output_size_bytes": 65552, "expansion_ratio": 1.0002, "throughput_mbps": 710.23, "bytes_per_second": 744725542, "blocks_per_second": 46556710, "encryption_time_ms": 0.08800020441412926, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4096]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.1875457763671875, "efficiency_ratio": 0.0}, {"input_size": 131072, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.2075522, "execution_time_ms": 0.2998986281454563, "setup_time_ms": 0.33481186255812645, "cleanup_time_ms": 23.980451747775078, "total_time_ms": 24.61516223847866, "baseline_memory_mb": 420.921875, "peak_memory_mb": 421.4140625, "memory_increment_mb": 0.4921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 131072, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 8192, "actual_blocks": 8193, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 131088, "expansion_ratio": 1.0001, "throughput_mbps": 733.93, "bytes_per_second": 769578776, "blocks_per_second": 48104544, "encryption_time_ms": 0.17031654715538025, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8192]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.3750457763671875, "efficiency_ratio": 0.7619977678571429}, {"input_size": 262144, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.378551, "execution_time_ms": 0.5089459009468555, "setup_time_ms": 0.6187078543007374, "cleanup_time_ms": 26.37067623436451, "total_time_ms": 27.498329989612103, "baseline_memory_mb": 421.4140625, "peak_memory_mb": 421.921875, "memory_increment_mb": 0.5078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 262144, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 16384, "actual_blocks": 16385, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0001, "output_size_bytes": 262160, "expansion_ratio": 1.0001, "throughput_mbps": 720.86, "bytes_per_second": 755871892, "blocks_per_second": 47244876, "encryption_time_ms": 0.34681009128689766, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16384]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.7500457763671875, "efficiency_ratio": 1.4770132211538463}, {"input_size": 524288, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.5578992, "execution_time_ms": 0.8719774894416332, "setup_time_ms": 1.1866460554301739, "cleanup_time_ms": 24.44355795159936, "total_time_ms": 26.502181496471167, "baseline_memory_mb": 421.921875, "peak_memory_mb": 423.2109375, "memory_increment_mb": 1.2890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 524288, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 32768, "actual_blocks": 32769, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 524304, "expansion_ratio": 1.0, "throughput_mbps": 783.29, "bytes_per_second": 821334346, "blocks_per_second": 51334963, "encryption_time_ms": 0.6383368745446205, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32768]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 1.5000457763671875, "efficiency_ratio": 1.163671875}, {"input_size": 1048576, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.7352855, "execution_time_ms": 1.7482906579971313, "setup_time_ms": 2.368480898439884, "cleanup_time_ms": 24.301128927618265, "total_time_ms": 28.41790048405528, "baseline_memory_mb": 423.2109375, "peak_memory_mb": 425.7890625, "memory_increment_mb": 2.578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1048576, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 65536, "actual_blocks": 65537, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 1048592, "expansion_ratio": 1.0, "throughput_mbps": 767.09, "bytes_per_second": 804354119, "blocks_per_second": 50272899, "encryption_time_ms": 1.303624827414751, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=65536]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 3.0000457763671875, "efficiency_ratio": 1.1636541193181817}, {"input_size": 2097152, "algorithm_name": "AES-256-CBC", "timestamp": 1753702864.9197156, "execution_time_ms": 3.7272932939231396, "setup_time_ms": 5.655650049448013, "cleanup_time_ms": 24.227285757660866, "total_time_ms": 33.61022910103202, "baseline_memory_mb": 420.921875, "peak_memory_mb": 430.65234375, "memory_increment_mb": 9.73046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2097152, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 131072, "actual_blocks": 131073, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0, "output_size_bytes": 2097168, "expansion_ratio": 1.0, "throughput_mbps": 765.72, "bytes_per_second": 802917507, "blocks_per_second": 50182727, "encryption_time_ms": 2.611914649605751, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=131072]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 6.0000457763671875, "efficiency_ratio": 0.6166245358289844}]