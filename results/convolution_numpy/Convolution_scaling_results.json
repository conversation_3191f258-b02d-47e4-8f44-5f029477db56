[{"input_size": 50, "algorithm_name": "Convolution", "timestamp": 1753652253.047267, "execution_time_ms": 8.713772241026163, "setup_time_ms": 0.07581524550914764, "cleanup_time_ms": 29.65177781879902, "total_time_ms": 38.44136530533433, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 460.58984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "50×50", "kernel_size": "5×5", "output_size": "46×46", "convolution_type": "numpy", "input_elements": 2500, "kernel_elements": 25, "output_elements": 2116, "theoretical_operations": 52900, "operations_per_output": 25, "reduction_ratio": 0.8464}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.03814697265625, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "Convolution", "timestamp": 1753652253.3273523, "execution_time_ms": 241.68561352416873, "setup_time_ms": 1.4774249866604805, "cleanup_time_ms": 29.94060516357422, "total_time_ms": 273.10364367440343, "baseline_memory_mb": 460.58984375, "peak_memory_mb": 461.84765625, "memory_increment_mb": 1.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "250×250", "kernel_size": "5×5", "output_size": "246×246", "convolution_type": "numpy", "input_elements": 62500, "kernel_elements": 25, "output_elements": 60516, "theoretical_operations": 1512900, "operations_per_output": 25, "reduction_ratio": 0.968256}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.95367431640625, "efficiency_ratio": 0.758200698757764}, {"input_size": 450, "algorithm_name": "Convolution", "timestamp": 1753652255.4691193, "execution_time_ms": 796.0476045496762, "setup_time_ms": 4.165931139141321, "cleanup_time_ms": 30.301266815513372, "total_time_ms": 830.5148025043309, "baseline_memory_mb": 461.84765625, "peak_memory_mb": 464.94140625, "memory_increment_mb": 3.09375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "450×450", "kernel_size": "5×5", "output_size": "446×446", "convolution_type": "numpy", "input_elements": 202500, "kernel_elements": 25, "output_elements": 198916, "theoretical_operations": 4972900, "operations_per_output": 25, "reduction_ratio": 0.9823012345679012}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 3.08990478515625, "efficiency_ratio": 0.9987571022727273}, {"input_size": 650, "algorithm_name": "Convolution", "timestamp": 1753652262.059005, "execution_time_ms": 1658.1182367168367, "setup_time_ms": 8.295041974633932, "cleanup_time_ms": 30.84853757172823, "total_time_ms": 1697.2618162631989, "baseline_memory_mb": 464.94140625, "peak_memory_mb": 469.83984375, "memory_increment_mb": 4.8984375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "650×650", "kernel_size": "5×5", "output_size": "646×646", "convolution_type": "numpy", "input_elements": 422500, "kernel_elements": 25, "output_elements": 417316, "theoretical_operations": 10432900, "operations_per_output": 25, "reduction_ratio": 0.9877301775147929}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 6.44683837890625, "efficiency_ratio": 1.3161009768740033}, {"input_size": 850, "algorithm_name": "Convolution", "timestamp": 1753652275.5998561, "execution_time_ms": 2862.593909725547, "setup_time_ms": 14.14019288495183, "cleanup_time_ms": 33.02515996620059, "total_time_ms": 2909.7592625766993, "baseline_memory_mb": 469.83984375, "peak_memory_mb": 476.79296875, "memory_increment_mb": 6.953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "850×850", "kernel_size": "5×5", "output_size": "846×846", "convolution_type": "numpy", "input_elements": 722500, "kernel_elements": 25, "output_elements": 715716, "theoretical_operations": 17892900, "operations_per_output": 25, "reduction_ratio": 0.9906103806228373}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 11.02447509765625, "efficiency_ratio": 1.5855424859550562}, {"input_size": 1050, "algorithm_name": "Convolution", "timestamp": 1753652298.7804124, "execution_time_ms": 4371.623514965177, "setup_time_ms": 21.75393933430314, "cleanup_time_ms": 32.73088904097676, "total_time_ms": 4426.1083433404565, "baseline_memory_mb": 476.79296875, "peak_memory_mb": 485.53515625, "memory_increment_mb": 8.7421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1050×1050", "kernel_size": "5×5", "output_size": "1046×1046", "convolution_type": "numpy", "input_elements": 1102500, "kernel_elements": 25, "output_elements": 1094116, "theoretical_operations": 27352900, "operations_per_output": 25, "reduction_ratio": 0.9923954648526077}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 16.82281494140625, "efficiency_ratio": 1.9243255697050938}, {"input_size": 1250, "algorithm_name": "Convolution", "timestamp": 1753652334.0868862, "execution_time_ms": 6199.113733228296, "setup_time_ms": 30.65782319754362, "cleanup_time_ms": 35.04415927454829, "total_time_ms": 6264.815715700388, "baseline_memory_mb": 485.53515625, "peak_memory_mb": 496.07421875, "memory_increment_mb": 10.5390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1250×1250", "kernel_size": "5×5", "output_size": "1246×1246", "convolution_type": "numpy", "input_elements": 1562500, "kernel_elements": 25, "output_elements": 1552516, "theoretical_operations": 38812900, "operations_per_output": 25, "reduction_ratio": 0.99361024}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 23.84185791015625, "efficiency_ratio": 2.2622370737583397}, {"input_size": 1450, "algorithm_name": "Convolution", "timestamp": 1753652383.9202406, "execution_time_ms": 8350.574846379459, "setup_time_ms": 41.20529210194945, "cleanup_time_ms": 35.758697893470526, "total_time_ms": 8427.538836374879, "baseline_memory_mb": 496.07421875, "peak_memory_mb": 508.40234375, "memory_increment_mb": 12.328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1450×1450", "kernel_size": "5×5", "output_size": "1446×1446", "convolution_type": "numpy", "input_elements": 2102500, "kernel_elements": 25, "output_elements": 2090916, "theoretical_operations": 52272900, "operations_per_output": 25, "reduction_ratio": 0.994490368608799}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 32.08160400390625, "efficiency_ratio": 2.6023100839670468}, {"input_size": 1650, "algorithm_name": "Convolution", "timestamp": 1753652451.046717, "execution_time_ms": 10748.204739857465, "setup_time_ms": 53.38514503091574, "cleanup_time_ms": 38.01478259265423, "total_time_ms": 10839.604667481035, "baseline_memory_mb": 508.40234375, "peak_memory_mb": 522.49609375, "memory_increment_mb": 14.09375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1650×1650", "kernel_size": "5×5", "output_size": "1646×1646", "convolution_type": "numpy", "input_elements": 2722500, "kernel_elements": 25, "output_elements": 2709316, "theoretical_operations": 67732900, "operations_per_output": 25, "reduction_ratio": 0.9951573921028466}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 41.54205322265625, "efficiency_ratio": 2.947551448170732}, {"input_size": 1850, "algorithm_name": "Convolution", "timestamp": 1753652537.4138823, "execution_time_ms": 13530.098571069539, "setup_time_ms": 67.05763982608914, "cleanup_time_ms": 39.39489088952541, "total_time_ms": 13636.551101785153, "baseline_memory_mb": 522.49609375, "peak_memory_mb": 538.56640625, "memory_increment_mb": 16.0703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1850×1850", "kernel_size": "5×5", "output_size": "1846×1846", "convolution_type": "numpy", "input_elements": 3422500, "kernel_elements": 25, "output_elements": 3407716, "theoretical_operations": 85192900, "operations_per_output": 25, "reduction_ratio": 0.9956803506208911}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 52.22320556640625, "efficiency_ratio": 3.2496695734078758}, {"input_size": 2050, "algorithm_name": "Convolution", "timestamp": 1753652645.6564364, "execution_time_ms": 16612.89695231244, "setup_time_ms": 90.40164295583963, "cleanup_time_ms": 40.66334618255496, "total_time_ms": 16743.961941450834, "baseline_memory_mb": 460.59375, "peak_memory_mb": 556.32421875, "memory_increment_mb": 95.73046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2050×2050", "kernel_size": "5×5", "output_size": "2046×2046", "convolution_type": "numpy", "input_elements": 4202500, "kernel_elements": 25, "output_elements": 4186116, "theoretical_operations": 104652900, "operations_per_output": 25, "reduction_ratio": 0.9961013682331945}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 64.12506103515625, "efficiency_ratio": 0.669850068347819}, {"input_size": 2250, "algorithm_name": "Convolution", "timestamp": 1753652778.5898473, "execution_time_ms": 20014.305956009775, "setup_time_ms": 108.86465106159449, "cleanup_time_ms": 42.58956713601947, "total_time_ms": 20165.76017420739, "baseline_memory_mb": 460.59375, "peak_memory_mb": 537.70703125, "memory_increment_mb": 77.11328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2250×2250", "kernel_size": "5×5", "output_size": "2246×2246", "convolution_type": "numpy", "input_elements": 5062500, "kernel_elements": 25, "output_elements": 5044516, "theoretical_operations": 126112900, "operations_per_output": 25, "reduction_ratio": 0.9964476049382716}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 77.24761962890625, "efficiency_ratio": 1.0017420913327593}, {"input_size": 2450, "algorithm_name": "Convolution", "timestamp": 1753652938.7980921, "execution_time_ms": 23710.554498247802, "setup_time_ms": 128.8204831071198, "cleanup_time_ms": 44.06798304989934, "total_time_ms": 23883.44296440482, "baseline_memory_mb": 460.59375, "peak_memory_mb": 552.0390625, "memory_increment_mb": 91.4453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2450×2450", "kernel_size": "5×5", "output_size": "2446×2446", "convolution_type": "numpy", "input_elements": 6002500, "kernel_elements": 25, "output_elements": 5982916, "theoretical_operations": 149572900, "operations_per_output": 25, "reduction_ratio": 0.9967373594335693}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 91.59088134765625, "efficiency_ratio": 1.001591867791542}, {"input_size": 2650, "algorithm_name": "Convolution", "timestamp": 1753653128.8984177, "execution_time_ms": 27723.660470638424, "setup_time_ms": 150.87453369051218, "cleanup_time_ms": 43.77199616283178, "total_time_ms": 27918.307000491768, "baseline_memory_mb": 460.59375, "peak_memory_mb": 567.58984375, "memory_increment_mb": 106.99609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2650×2650", "kernel_size": "5×5", "output_size": "2646×2646", "convolution_type": "numpy", "input_elements": 7022500, "kernel_elements": 25, "output_elements": 7001316, "theoretical_operations": 175032900, "operations_per_output": 25, "reduction_ratio": 0.9969834104663582}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 107.15484619140625, "efficiency_ratio": 1.001483721842941}, {"input_size": 2850, "algorithm_name": "Convolution", "timestamp": 1753653350.861424, "execution_time_ms": 32155.92272588983, "setup_time_ms": 174.34273567050695, "cleanup_time_ms": 43.954113963991404, "total_time_ms": 32374.21957552433, "baseline_memory_mb": 460.59375, "peak_memory_mb": 584.36328125, "memory_increment_mb": 123.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "2850×2850", "kernel_size": "5×5", "output_size": "2846×2846", "convolution_type": "numpy", "input_elements": 8122500, "kernel_elements": 25, "output_elements": 8099716, "theoretical_operations": 202492900, "operations_per_output": 25, "reduction_ratio": 0.9971949522930132}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 123.93951416015625, "efficiency_ratio": 1.0013733825153859}, {"input_size": 3050, "algorithm_name": "Convolution", "timestamp": 1753653608.1684837, "execution_time_ms": 36756.42769224942, "setup_time_ms": 199.99131234362721, "cleanup_time_ms": 44.092976953834295, "total_time_ms": 37000.51198154688, "baseline_memory_mb": 460.59375, "peak_memory_mb": 602.35546875, "memory_increment_mb": 141.76171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3050×3050", "kernel_size": "5×5", "output_size": "3046×3046", "convolution_type": "numpy", "input_elements": 9302500, "kernel_elements": 25, "output_elements": 9278116, "theoretical_operations": 231952900, "operations_per_output": 25, "reduction_ratio": 0.9973787691480784}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 141.94488525390625, "efficiency_ratio": 1.0012920731035242}, {"input_size": 3250, "algorithm_name": "Convolution", "timestamp": 1753653903.0072913, "execution_time_ms": 41823.94398255274, "setup_time_ms": 226.80330090224743, "cleanup_time_ms": 44.17407000437379, "total_time_ms": 42094.92135345936, "baseline_memory_mb": 460.59375, "peak_memory_mb": 621.5703125, "memory_increment_mb": 160.9765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3250×3250", "kernel_size": "5×5", "output_size": "3246×3246", "convolution_type": "numpy", "input_elements": 10562500, "kernel_elements": 25, "output_elements": 10536516, "theoretical_operations": 263412900, "operations_per_output": 25, "reduction_ratio": 0.9975399763313609}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 161.17095947265625, "efficiency_ratio": 1.0012076104100947}, {"input_size": 3450, "algorithm_name": "Convolution", "timestamp": 1753654237.735131, "execution_time_ms": 47042.85016190261, "setup_time_ms": 255.48421079292893, "cleanup_time_ms": 43.40409114956856, "total_time_ms": 47341.738463845104, "baseline_memory_mb": 460.59375, "peak_memory_mb": 642.0078125, "memory_increment_mb": 181.4140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3450×3450", "kernel_size": "5×5", "output_size": "3446×3446", "convolution_type": "numpy", "input_elements": 11902500, "kernel_elements": 25, "output_elements": 11874916, "theoretical_operations": 296872900, "operations_per_output": 25, "reduction_ratio": 0.9976825036756983}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 181.61773681640625, "efficiency_ratio": 1.001122704125576}, {"input_size": 3650, "algorithm_name": "Convolution", "timestamp": 1753654614.2912784, "execution_time_ms": 52752.153251413256, "setup_time_ms": 285.18131328746676, "cleanup_time_ms": 43.93802536651492, "total_time_ms": 53081.27259006724, "baseline_memory_mb": 460.59375, "peak_memory_mb": 663.66015625, "memory_increment_mb": 203.06640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3650×3650", "kernel_size": "5×5", "output_size": "3646×3646", "convolution_type": "numpy", "input_elements": 13322500, "kernel_elements": 25, "output_elements": 13293316, "theoretical_operations": 332332900, "operations_per_output": 25, "reduction_ratio": 0.997809420153875}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 203.28521728515625, "efficiency_ratio": 1.0010775343849188}, {"input_size": 3850, "algorithm_name": "Convolution", "timestamp": 1753655036.5842865, "execution_time_ms": 58613.501027878374, "setup_time_ms": 317.52712884917855, "cleanup_time_ms": 44.055432081222534, "total_time_ms": 58975.083588808775, "baseline_memory_mb": 460.59375, "peak_memory_mb": 686.5390625, "memory_increment_mb": 225.9453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "3850×3850", "kernel_size": "5×5", "output_size": "3846×3846", "convolution_type": "numpy", "input_elements": 14822500, "kernel_elements": 25, "output_elements": 14791716, "theoretical_operations": 369792900, "operations_per_output": 25, "reduction_ratio": 0.9979231573621185}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 226.17340087890625, "efficiency_ratio": 1.0010094848898725}, {"input_size": 4050, "algorithm_name": "Convolution", "timestamp": 1753655506.9537835, "execution_time_ms": 65134.05160717666, "setup_time_ms": 350.94007663428783, "cleanup_time_ms": 43.55695378035307, "total_time_ms": 65528.5486375913, "baseline_memory_mb": 460.59375, "peak_memory_mb": 710.6328125, "memory_increment_mb": 250.0390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4050×4050", "kernel_size": "5×5", "output_size": "4046×4046", "convolution_type": "numpy", "input_elements": 16402500, "kernel_elements": 25, "output_elements": 16370116, "theoretical_operations": 409252900, "operations_per_output": 25, "reduction_ratio": 0.9980256668190824}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 250.28228759765625, "efficiency_ratio": 1.0009727483986877}, {"input_size": 4250, "algorithm_name": "Convolution", "timestamp": 1753656029.0358496, "execution_time_ms": 71884.62827093899, "setup_time_ms": 386.5059819072485, "cleanup_time_ms": 43.65088231861591, "total_time_ms": 72314.78513516486, "baseline_memory_mb": 460.59375, "peak_memory_mb": 735.94921875, "memory_increment_mb": 275.35546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4250×4250", "kernel_size": "5×5", "output_size": "4246×4246", "convolution_type": "numpy", "input_elements": 18062500, "kernel_elements": 25, "output_elements": 18028516, "theoretical_operations": 450712900, "operations_per_output": 25, "reduction_ratio": 0.9981185328719723}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 275.61187744140625, "efficiency_ratio": 1.0009311915705552}, {"input_size": 4450, "algorithm_name": "Convolution", "timestamp": 1753656604.601836, "execution_time_ms": 78788.15260697156, "setup_time_ms": 423.4704449772835, "cleanup_time_ms": 43.952371925115585, "total_time_ms": 79255.57542387396, "baseline_memory_mb": 460.59375, "peak_memory_mb": 762.48828125, "memory_increment_mb": 301.89453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4450×4450", "kernel_size": "5×5", "output_size": "4446×4446", "convolution_type": "numpy", "input_elements": 19802500, "kernel_elements": 25, "output_elements": 19766916, "theoretical_operations": 494172900, "operations_per_output": 25, "reduction_ratio": 0.9982030551698018}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 302.16217041015625, "efficiency_ratio": 1.0008865319919777}, {"input_size": 4650, "algorithm_name": "Convolution", "timestamp": 1753657235.9327064, "execution_time_ms": 85864.40753871575, "setup_time_ms": 466.83146711438894, "cleanup_time_ms": 43.81432291120291, "total_time_ms": 86375.05332874134, "baseline_memory_mb": 460.59375, "peak_memory_mb": 790.24609375, "memory_increment_mb": 329.65234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4650×4650", "kernel_size": "5×5", "output_size": "4646×4646", "convolution_type": "numpy", "input_elements": 21622500, "kernel_elements": 25, "output_elements": 21585316, "theoretical_operations": 539632900, "operations_per_output": 25, "reduction_ratio": 0.9982803098624118}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 329.93316650390625, "efficiency_ratio": 1.0008518754962021}, {"input_size": 4850, "algorithm_name": "Convolution", "timestamp": 1753657923.3802824, "execution_time_ms": 93551.18314428255, "setup_time_ms": 502.99123767763376, "cleanup_time_ms": 43.79261517897248, "total_time_ms": 94097.96699713916, "baseline_memory_mb": 460.59375, "peak_memory_mb": 819.2265625, "memory_increment_mb": 358.6328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "4850×4850", "kernel_size": "5×5", "output_size": "4846×4846", "convolution_type": "numpy", "input_elements": 23522500, "kernel_elements": 25, "output_elements": 23483716, "theoretical_operations": 587092900, "operations_per_output": 25, "reduction_ratio": 0.9983511956637262}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 358.92486572265625, "efficiency_ratio": 1.000814351650147}, {"input_size": 5050, "algorithm_name": "Convolution", "timestamp": 1753658673.4373724, "execution_time_ms": 101110.45370195061, "setup_time_ms": 545.1689893379807, "cleanup_time_ms": 44.06011896207929, "total_time_ms": 101699.68281025067, "baseline_memory_mb": 460.59375, "peak_memory_mb": 849.42578125, "memory_increment_mb": 388.83203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5050×5050", "kernel_size": "5×5", "output_size": "5046×5046", "convolution_type": "numpy", "input_elements": 25502500, "kernel_elements": 25, "output_elements": 25462116, "theoretical_operations": 636552900, "operations_per_output": 25, "reduction_ratio": 0.99841646897363}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 389.13726806640625, "efficiency_ratio": 1.000785009443345}, {"input_size": 5250, "algorithm_name": "Convolution", "timestamp": 1753659486.3758233, "execution_time_ms": 109863.95346755162, "setup_time_ms": 589.1547817736864, "cleanup_time_ms": 43.85600797832012, "total_time_ms": 110496.96425730363, "baseline_memory_mb": 460.59375, "peak_memory_mb": 880.8515625, "memory_increment_mb": 420.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5250×5250", "kernel_size": "5×5", "output_size": "5246×5246", "convolution_type": "numpy", "input_elements": 27562500, "kernel_elements": 25, "output_elements": 27520516, "theoretical_operations": 688012900, "operations_per_output": 25, "reduction_ratio": 0.9984767709750567}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 420.57037353515625, "efficiency_ratio": 1.000743736406224}, {"input_size": 5450, "algorithm_name": "Convolution", "timestamp": 1753660365.9703696, "execution_time_ms": 118868.8282306306, "setup_time_ms": 635.6525290757418, "cleanup_time_ms": 44.17434195056558, "total_time_ms": 119548.65510165691, "baseline_memory_mb": 460.59375, "peak_memory_mb": 913.48828125, "memory_increment_mb": 452.89453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5450×5450", "kernel_size": "5×5", "output_size": "5446×5446", "convolution_type": "numpy", "input_elements": 29702500, "kernel_elements": 25, "output_elements": 29658916, "theoretical_operations": 741472900, "operations_per_output": 25, "reduction_ratio": 0.9985326487669388}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 453.22418212890625, "efficiency_ratio": 1.000727875600521}, {"input_size": 5650, "algorithm_name": "Convolution", "timestamp": 1753661316.97883, "execution_time_ms": 126520.33823356032, "setup_time_ms": 682.1676660329103, "cleanup_time_ms": 44.13234023377299, "total_time_ms": 127246.638239827, "baseline_memory_mb": 460.59375, "peak_memory_mb": 947.3515625, "memory_increment_mb": 486.7578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5650×5650", "kernel_size": "5×5", "output_size": "5646×5646", "convolution_type": "numpy", "input_elements": 31922500, "kernel_elements": 25, "output_elements": 31877316, "theoretical_operations": 796932900, "operations_per_output": 25, "reduction_ratio": 0.9985845720103376}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 487.09869384765625, "efficiency_ratio": 1.0007003099670972}, {"input_size": 5850, "algorithm_name": "Convolution", "timestamp": 1753662332.4855247, "execution_time_ms": 136087.64368547127, "setup_time_ms": 732.2866362519562, "cleanup_time_ms": 44.205366633832455, "total_time_ms": 136864.13568835706, "baseline_memory_mb": 460.59375, "peak_memory_mb": 982.43359375, "memory_increment_mb": 521.83984375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "5850×5850", "kernel_size": "5×5", "output_size": "5846×5846", "convolution_type": "numpy", "input_elements": 34222500, "kernel_elements": 25, "output_elements": 34175716, "theoretical_operations": 854392900, "operations_per_output": 25, "reduction_ratio": 0.9986329461611513}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 522.1939086914062, "efficiency_ratio": 1.0006784934988135}, {"input_size": 6050, "algorithm_name": "Convolution", "timestamp": 1753663423.1630158, "execution_time_ms": 146063.98393111303, "setup_time_ms": 782.3144188150764, "cleanup_time_ms": 44.314198195934296, "total_time_ms": 146890.61254812405, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1018.73828125, "memory_increment_mb": 558.14453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6050×6050", "kernel_size": "5×5", "output_size": "6046×6046", "convolution_type": "numpy", "input_elements": 36602500, "kernel_elements": 25, "output_elements": 36554116, "theoretical_operations": 913852900, "operations_per_output": 25, "reduction_ratio": 0.9986781230790247}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 558.5098266601562, "efficiency_ratio": 1.0006544817510585}, {"input_size": 6250, "algorithm_name": "Convolution", "timestamp": 1753664593.1325407, "execution_time_ms": 155064.8762261495, "setup_time_ms": 836.1627222038805, "cleanup_time_ms": 44.5803739130497, "total_time_ms": 155945.61932226643, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1056.26171875, "memory_increment_mb": 595.66796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6250×6250", "kernel_size": "5×5", "output_size": "6246×6246", "convolution_type": "numpy", "input_elements": 39062500, "kernel_elements": 25, "output_elements": 39012516, "theoretical_operations": 975312900, "operations_per_output": 25, "reduction_ratio": 0.9987204096}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 596.0464477539062, "efficiency_ratio": 1.0006353858588375}, {"input_size": 6450, "algorithm_name": "Convolution", "timestamp": 1753665834.9766107, "execution_time_ms": 165537.54510097206, "setup_time_ms": 889.2203350551426, "cleanup_time_ms": 44.17395684868097, "total_time_ms": 166470.93939287588, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1095.0078125, "memory_increment_mb": 634.4140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6450×6450", "kernel_size": "5×5", "output_size": "6446×6446", "convolution_type": "numpy", "input_elements": 41602500, "kernel_elements": 25, "output_elements": 41550916, "theoretical_operations": 1038772900, "operations_per_output": 25, "reduction_ratio": 0.9987600745147527}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 634.8037719726562, "efficiency_ratio": 1.0006142825257065}, {"input_size": 6650, "algorithm_name": "Convolution", "timestamp": 1753667158.1119037, "execution_time_ms": 175128.11392042786, "setup_time_ms": 945.7652936689556, "cleanup_time_ms": 44.1056452691555, "total_time_ms": 176117.98485936597, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1134.9765625, "memory_increment_mb": 674.3828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6650×6650", "kernel_size": "5×5", "output_size": "6646×6646", "convolution_type": "numpy", "input_elements": 44222500, "kernel_elements": 25, "output_elements": 44169316, "theoretical_operations": 1104232900, "operations_per_output": 25, "reduction_ratio": 0.9987973542879756}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 674.7817993164062, "efficiency_ratio": 1.0005916325401698}, {"input_size": 6850, "algorithm_name": "Convolution", "timestamp": 1753668561.4092784, "execution_time_ms": 186332.77939166874, "setup_time_ms": 992.2851347364485, "cleanup_time_ms": 44.416069984436035, "total_time_ms": 187369.48059638962, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1176.16015625, "memory_increment_mb": 715.56640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "6850×6850", "kernel_size": "5×5", "output_size": "6846×6846", "convolution_type": "numpy", "input_elements": 46922500, "kernel_elements": 25, "output_elements": 46867716, "theoretical_operations": 1171692900, "operations_per_output": 25, "reduction_ratio": 0.9988324577761202}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 715.9805297851562, "efficiency_ratio": 1.0005787352949205}, {"input_size": 7050, "algorithm_name": "Convolution", "timestamp": 1753670054.6121316, "execution_time_ms": 197781.56944792718, "setup_time_ms": 1063.0682092159986, "cleanup_time_ms": 44.512918684631586, "total_time_ms": 198889.1505758278, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1218.5703125, "memory_increment_mb": 757.9765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7050×7050", "kernel_size": "5×5", "output_size": "7046×7046", "convolution_type": "numpy", "input_elements": 49702500, "kernel_elements": 25, "output_elements": 49646116, "theoretical_operations": 1241152900, "operations_per_output": 25, "reduction_ratio": 0.998865570142347}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 758.3999633789062, "efficiency_ratio": 1.0005585936292143}, {"input_size": 7250, "algorithm_name": "Convolution", "timestamp": 1753671637.1402292, "execution_time_ms": 208676.2095442973, "setup_time_ms": 1122.5647437386215, "cleanup_time_ms": 44.481062330305576, "total_time_ms": 209843.25535036623, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1262.1953125, "memory_increment_mb": 801.6015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7250×7250", "kernel_size": "5×5", "output_size": "7246×7246", "convolution_type": "numpy", "input_elements": 52562500, "kernel_elements": 25, "output_elements": 52504516, "theoretical_operations": 1312612900, "operations_per_output": 25, "reduction_ratio": 0.9988968561236623}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 802.0401000976562, "efficiency_ratio": 1.0005470767750109}, {"input_size": 7450, "algorithm_name": "Convolution", "timestamp": 1753673308.8546803, "execution_time_ms": 220595.47850983217, "setup_time_ms": 1186.9501802138984, "cleanup_time_ms": 44.42232986912131, "total_time_ms": 221826.8510199152, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1307.04296875, "memory_increment_mb": 846.44921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7450×7450", "kernel_size": "5×5", "output_size": "7446×7446", "convolution_type": "numpy", "input_elements": 55502500, "kernel_elements": 25, "output_elements": 55442916, "theoretical_operations": 1386072900, "operations_per_output": 25, "reduction_ratio": 0.9989264627719472}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 846.9009399414062, "efficiency_ratio": 1.0005336660267385}, {"input_size": 7650, "algorithm_name": "Convolution", "timestamp": 1753675077.3362348, "execution_time_ms": 231775.56771421805, "setup_time_ms": 1251.2314589694142, "cleanup_time_ms": 44.54306699335575, "total_time_ms": 233071.34224018082, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1353.11328125, "memory_increment_mb": 892.51953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7650×7650", "kernel_size": "5×5", "output_size": "7646×7646", "convolution_type": "numpy", "input_elements": 58522500, "kernel_elements": 25, "output_elements": 58461316, "theoretical_operations": 1461532900, "operations_per_output": 25, "reduction_ratio": 0.9989545217651331}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 892.9824829101562, "efficiency_ratio": 1.0005187019935664}, {"input_size": 7850, "algorithm_name": "Convolution", "timestamp": 1753676931.1201952, "execution_time_ms": 243342.76397190988, "setup_time_ms": 1316.8986602686346, "cleanup_time_ms": 44.6737608872354, "total_time_ms": 244704.33639306575, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1400.40234375, "memory_increment_mb": 939.80859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "7850×7850", "kernel_size": "5×5", "output_size": "7846×7846", "convolution_type": "numpy", "input_elements": 61622500, "kernel_elements": 25, "output_elements": 61559716, "theoretical_operations": 1538992900, "operations_per_output": 25, "reduction_ratio": 0.998981151365167}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 940.2847290039062, "efficiency_ratio": 1.0005066300277234}, {"input_size": 8050, "algorithm_name": "Convolution", "timestamp": 1753678880.819276, "execution_time_ms": 256440.50948573276, "setup_time_ms": 1384.548737667501, "cleanup_time_ms": 44.470562133938074, "total_time_ms": 257869.5287855342, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1448.9140625, "memory_increment_mb": 988.3203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8050×8050", "kernel_size": "5×5", "output_size": "8046×8046", "convolution_type": "numpy", "input_elements": 64802500, "kernel_elements": 25, "output_elements": 64738116, "theoretical_operations": 1618452900, "operations_per_output": 25, "reduction_ratio": 0.9990064580841789}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 988.8076782226562, "efficiency_ratio": 1.0004931252717284}, {"input_size": 8250, "algorithm_name": "Convolution", "timestamp": 1753680935.6702304, "execution_time_ms": 270477.9583774507, "setup_time_ms": 1455.6817850098014, "cleanup_time_ms": 44.61694601923227, "total_time_ms": 271978.25710847974, "baseline_memory_mb": 460.59375, "peak_memory_mb": 1498.6484375, "memory_increment_mb": 1038.0546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8250×8250", "kernel_size": "5×5", "output_size": "8246×8246", "convolution_type": "numpy", "input_elements": 68062500, "kernel_elements": 25, "output_elements": 67996516, "theoretical_operations": 1699912900, "operations_per_output": 25, "reduction_ratio": 0.9990305381083563}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1038.5513305664062, "efficiency_ratio": 1.0004784363216954}, {"input_size": 8450, "algorithm_name": "Convolution", "timestamp": 1753683100.3377702, "execution_time_ms": 284055.2787036635, "setup_time_ms": 1526.1554839089513, "cleanup_time_ms": 44.86894514411688, "total_time_ms": 285626.30313271657, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1549.60546875, "memory_increment_mb": 1089.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8450×8450", "kernel_size": "5×5", "output_size": "8446×8446", "convolution_type": "numpy", "input_elements": 71402500, "kernel_elements": 25, "output_elements": 71334916, "theoretical_operations": 1783372900, "operations_per_output": 25, "reduction_ratio": 0.9990534785196596}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1089.5156860351562, "efficiency_ratio": 1.0004663635369064}, {"input_size": 8650, "algorithm_name": "Convolution", "timestamp": 1753685374.337904, "execution_time_ms": 296312.23684353754, "setup_time_ms": 1599.2934359237552, "cleanup_time_ms": 44.79354294016957, "total_time_ms": 297956.32382240146, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1601.7734375, "memory_increment_mb": 1141.17578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8650×8650", "kernel_size": "5×5", "output_size": "8646×8646", "convolution_type": "numpy", "input_elements": 74822500, "kernel_elements": 25, "output_elements": 74753316, "theoretical_operations": 1868832900, "operations_per_output": 25, "reduction_ratio": 0.9990753583480905}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1141.7007446289062, "efficiency_ratio": 1.0004600197336218}, {"input_size": 8850, "algorithm_name": "Convolution", "timestamp": 1753687748.74361, "execution_time_ms": 311641.29732158035, "setup_time_ms": 1673.4003531746566, "cleanup_time_ms": 44.69138337299228, "total_time_ms": 313359.389058128, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1655.16796875, "memory_increment_mb": 1194.5703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "8850×8850", "kernel_size": "5×5", "output_size": "8846×8846", "convolution_type": "numpy", "input_elements": 78322500, "kernel_elements": 25, "output_elements": 78251716, "theoretical_operations": 1956292900, "operations_per_output": 25, "reduction_ratio": 0.9990962494813113}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1195.1065063476562, "efficiency_ratio": 1.000448859177267}, {"input_size": 9050, "algorithm_name": "Convolution", "timestamp": 1753690242.3653295, "execution_time_ms": 326264.2247471027, "setup_time_ms": 1749.0111612714827, "cleanup_time_ms": 44.84662739560008, "total_time_ms": 328058.0825357698, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1709.78125, "memory_increment_mb": 1249.18359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9050×9050", "kernel_size": "5×5", "output_size": "9046×9046", "convolution_type": "numpy", "input_elements": 81902500, "kernel_elements": 25, "output_elements": 81830116, "theoretical_operations": 2045752900, "operations_per_output": 25, "reduction_ratio": 0.9991162174536797}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1249.7329711914062, "efficiency_ratio": 1.00043978919044}, {"input_size": 9250, "algorithm_name": "Convolution", "timestamp": 1753692854.0713272, "execution_time_ms": 340404.51176892966, "setup_time_ms": 1830.4744130000472, "cleanup_time_ms": 44.802882708609104, "total_time_ms": 342279.7890646383, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1765.6171875, "memory_increment_mb": 1305.01953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9250×9250", "kernel_size": "5×5", "output_size": "9246×9246", "convolution_type": "numpy", "input_elements": 85562500, "kernel_elements": 25, "output_elements": 85488516, "theoretical_operations": 2137212900, "operations_per_output": 25, "reduction_ratio": 0.9991353221329438}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1305.5801391601562, "efficiency_ratio": 1.0004295781762127}, {"input_size": 9450, "algorithm_name": "Convolution", "timestamp": 1753695578.4231455, "execution_time_ms": 352802.66411975026, "setup_time_ms": 1907.0443948730826, "cleanup_time_ms": 44.99229183420539, "total_time_ms": 354754.70080645755, "baseline_memory_mb": 460.59765625, "peak_memory_mb": 1822.671875, "memory_increment_mb": 1362.07421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9450×9450", "kernel_size": "5×5", "output_size": "9446×9446", "convolution_type": "numpy", "input_elements": 89302500, "kernel_elements": 25, "output_elements": 89226916, "theoretical_operations": 2230672900, "operations_per_output": 25, "reduction_ratio": 0.9991536183197559}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1362.6480102539062, "efficiency_ratio": 1.0004212630237086}, {"input_size": 9650, "algorithm_name": "Convolution", "timestamp": 1753698411.3315802, "execution_time_ms": 371571.6336798854, "setup_time_ms": 1991.8037177994847, "cleanup_time_ms": 44.932270888239145, "total_time_ms": 373608.36966857314, "baseline_memory_mb": 460.6015625, "peak_memory_mb": 1880.953125, "memory_increment_mb": 1420.3515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9650×9650", "kernel_size": "5×5", "output_size": "9646×9646", "convolution_type": "numpy", "input_elements": 93122500, "kernel_elements": 25, "output_elements": 93045316, "theoretical_operations": 2326132900, "operations_per_output": 25, "reduction_ratio": 0.9991711562726516}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1420.9365844726562, "efficiency_ratio": 1.0004118853304365}, {"input_size": 9850, "algorithm_name": "Convolution", "timestamp": 1753701382.204841, "execution_time_ms": 386769.8302413337, "setup_time_ms": 2074.77018609643, "cleanup_time_ms": 46.02724779397249, "total_time_ms": 388890.6276752241, "baseline_memory_mb": 460.6015625, "peak_memory_mb": 1940.453125, "memory_increment_mb": 1479.8515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "9850×9850", "kernel_size": "5×5", "output_size": "9846×9846", "convolution_type": "numpy", "input_elements": 97022500, "kernel_elements": 25, "output_elements": 96943716, "theoretical_operations": 2423592900, "operations_per_output": 25, "reduction_ratio": 0.9991879821690844}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1480.4458618164062, "efficiency_ratio": 1.0004015938702677}]