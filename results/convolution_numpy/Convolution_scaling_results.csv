input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_input_size,custom_kernel_size,custom_output_size,custom_convolution_type,custom_input_elements,custom_kernel_elements,custom_output_elements,custom_theoretical_operations,custom_operations_per_output,custom_reduction_ratio
50,Convolution,1753652253.047267,8.7138,0.0758,29.6518,38.4414,460.59,460.59,0.00,0.00,,,,O(N² × K²),O(N²),0.04,0.0000,50×50,5×5,46×46,numpy,2500,25,2116,52900,25,0.8464
250,Convolution,1753652253.3273523,241.6856,1.4774,29.9406,273.1036,460.59,461.85,1.26,0.00,,,,O(N² × K²),O(N²),0.95,0.7582,250×250,5×5,246×246,numpy,62500,25,60516,1512900,25,0.968256
450,Convolution,1753652255.4691193,796.0476,4.1659,30.3013,830.5148,461.85,464.94,3.09,0.00,,,,O(N² × K²),O(N²),3.09,0.9988,450×450,5×5,446×446,numpy,202500,25,198916,4972900,25,0.9823012345679012
650,Convolution,1753652262.059005,1658.1182,8.2950,30.8485,1697.2618,464.94,469.84,4.90,0.00,,,,O(N² × K²),O(N²),6.45,1.3161,650×650,5×5,646×646,numpy,422500,25,417316,10432900,25,0.9877301775147929
850,Convolution,1753652275.5998561,2862.5939,14.1402,33.0252,2909.7593,469.84,476.79,6.95,0.00,,,,O(N² × K²),O(N²),11.02,1.5855,850×850,5×5,846×846,numpy,722500,25,715716,17892900,25,0.9906103806228373
1050,Convolution,1753652298.7804124,4371.6235,21.7539,32.7309,4426.1083,476.79,485.54,8.74,0.00,,,,O(N² × K²),O(N²),16.82,1.9243,1050×1050,5×5,1046×1046,numpy,1102500,25,1094116,27352900,25,0.9923954648526077
1250,Convolution,1753652334.0868862,6199.1137,30.6578,35.0442,6264.8157,485.54,496.07,10.54,0.00,,,,O(N² × K²),O(N²),23.84,2.2622,1250×1250,5×5,1246×1246,numpy,1562500,25,1552516,38812900,25,0.99361024
1450,Convolution,1753652383.9202406,8350.5748,41.2053,35.7587,8427.5388,496.07,508.40,12.33,0.00,,,,O(N² × K²),O(N²),32.08,2.6023,1450×1450,5×5,1446×1446,numpy,2102500,25,2090916,52272900,25,0.994490368608799
1650,Convolution,1753652451.046717,10748.2047,53.3851,38.0148,10839.6047,508.40,522.50,14.09,0.00,,,,O(N² × K²),O(N²),41.54,2.9476,1650×1650,5×5,1646×1646,numpy,2722500,25,2709316,67732900,25,0.9951573921028466
1850,Convolution,1753652537.4138823,13530.0986,67.0576,39.3949,13636.5511,522.50,538.57,16.07,0.00,,,,O(N² × K²),O(N²),52.22,3.2497,1850×1850,5×5,1846×1846,numpy,3422500,25,3407716,85192900,25,0.9956803506208911
2050,Convolution,1753652645.6564364,16612.8970,90.4016,40.6633,16743.9619,460.59,556.32,95.73,0.00,,,,O(N² × K²),O(N²),64.13,0.6699,2050×2050,5×5,2046×2046,numpy,4202500,25,4186116,104652900,25,0.9961013682331945
2250,Convolution,1753652778.5898473,20014.3060,108.8647,42.5896,20165.7602,460.59,537.71,77.11,0.00,,,,O(N² × K²),O(N²),77.25,1.0017,2250×2250,5×5,2246×2246,numpy,5062500,25,5044516,126112900,25,0.9964476049382716
2450,Convolution,1753652938.7980921,23710.5545,128.8205,44.0680,23883.4430,460.59,552.04,91.45,0.00,,,,O(N² × K²),O(N²),91.59,1.0016,2450×2450,5×5,2446×2446,numpy,6002500,25,5982916,149572900,25,0.9967373594335693
2650,Convolution,1753653128.8984177,27723.6605,150.8745,43.7720,27918.3070,460.59,567.59,107.00,0.00,,,,O(N² × K²),O(N²),107.15,1.0015,2650×2650,5×5,2646×2646,numpy,7022500,25,7001316,175032900,25,0.9969834104663582
2850,Convolution,1753653350.861424,32155.9227,174.3427,43.9541,32374.2196,460.59,584.36,123.77,0.00,,,,O(N² × K²),O(N²),123.94,1.0014,2850×2850,5×5,2846×2846,numpy,8122500,25,8099716,202492900,25,0.9971949522930132
3050,Convolution,1753653608.1684837,36756.4277,199.9913,44.0930,37000.5120,460.59,602.36,141.76,0.00,,,,O(N² × K²),O(N²),141.94,1.0013,3050×3050,5×5,3046×3046,numpy,9302500,25,9278116,231952900,25,0.9973787691480784
3250,Convolution,1753653903.0072913,41823.9440,226.8033,44.1741,42094.9214,460.59,621.57,160.98,0.00,,,,O(N² × K²),O(N²),161.17,1.0012,3250×3250,5×5,3246×3246,numpy,10562500,25,10536516,263412900,25,0.9975399763313609
3450,Convolution,1753654237.735131,47042.8502,255.4842,43.4041,47341.7385,460.59,642.01,181.41,0.00,,,,O(N² × K²),O(N²),181.62,1.0011,3450×3450,5×5,3446×3446,numpy,11902500,25,11874916,296872900,25,0.9976825036756983
3650,Convolution,1753654614.2912784,52752.1533,285.1813,43.9380,53081.2726,460.59,663.66,203.07,0.00,,,,O(N² × K²),O(N²),203.29,1.0011,3650×3650,5×5,3646×3646,numpy,13322500,25,13293316,332332900,25,0.997809420153875
3850,Convolution,1753655036.5842865,58613.5010,317.5271,44.0554,58975.0836,460.59,686.54,225.95,0.00,,,,O(N² × K²),O(N²),226.17,1.0010,3850×3850,5×5,3846×3846,numpy,14822500,25,14791716,369792900,25,0.9979231573621185
4050,Convolution,1753655506.9537835,65134.0516,350.9401,43.5570,65528.5486,460.59,710.63,250.04,0.00,,,,O(N² × K²),O(N²),250.28,1.0010,4050×4050,5×5,4046×4046,numpy,16402500,25,16370116,409252900,25,0.9980256668190824
4250,Convolution,1753656029.0358496,71884.6283,386.5060,43.6509,72314.7851,460.59,735.95,275.36,0.00,,,,O(N² × K²),O(N²),275.61,1.0009,4250×4250,5×5,4246×4246,numpy,18062500,25,18028516,450712900,25,0.9981185328719723
4450,Convolution,1753656604.601836,78788.1526,423.4704,43.9524,79255.5754,460.59,762.49,301.89,0.00,,,,O(N² × K²),O(N²),302.16,1.0009,4450×4450,5×5,4446×4446,numpy,19802500,25,19766916,494172900,25,0.9982030551698018
4650,Convolution,1753657235.9327064,85864.4075,466.8315,43.8143,86375.0533,460.59,790.25,329.65,0.00,,,,O(N² × K²),O(N²),329.93,1.0009,4650×4650,5×5,4646×4646,numpy,21622500,25,21585316,539632900,25,0.9982803098624118
4850,Convolution,1753657923.3802824,93551.1831,502.9912,43.7926,94097.9670,460.59,819.23,358.63,0.00,,,,O(N² × K²),O(N²),358.92,1.0008,4850×4850,5×5,4846×4846,numpy,23522500,25,23483716,587092900,25,0.9983511956637262
5050,Convolution,1753658673.4373724,101110.4537,545.1690,44.0601,101699.6828,460.59,849.43,388.83,0.00,,,,O(N² × K²),O(N²),389.14,1.0008,5050×5050,5×5,5046×5046,numpy,25502500,25,25462116,636552900,25,0.99841646897363
5250,Convolution,1753659486.3758233,109863.9535,589.1548,43.8560,110496.9643,460.59,880.85,420.26,0.00,,,,O(N² × K²),O(N²),420.57,1.0007,5250×5250,5×5,5246×5246,numpy,27562500,25,27520516,688012900,25,0.9984767709750567
5450,Convolution,1753660365.9703696,118868.8282,635.6525,44.1743,119548.6551,460.59,913.49,452.89,0.00,,,,O(N² × K²),O(N²),453.22,1.0007,5450×5450,5×5,5446×5446,numpy,29702500,25,29658916,741472900,25,0.9985326487669388
5650,Convolution,1753661316.97883,126520.3382,682.1677,44.1323,127246.6382,460.59,947.35,486.76,0.00,,,,O(N² × K²),O(N²),487.10,1.0007,5650×5650,5×5,5646×5646,numpy,31922500,25,31877316,796932900,25,0.9985845720103376
5850,Convolution,1753662332.4855247,136087.6437,732.2866,44.2054,136864.1357,460.59,982.43,521.84,0.00,,,,O(N² × K²),O(N²),522.19,1.0007,5850×5850,5×5,5846×5846,numpy,34222500,25,34175716,854392900,25,0.9986329461611513
6050,Convolution,1753663423.1630158,146063.9839,782.3144,44.3142,146890.6125,460.59,1018.74,558.14,0.00,,,,O(N² × K²),O(N²),558.51,1.0007,6050×6050,5×5,6046×6046,numpy,36602500,25,36554116,913852900,25,0.9986781230790247
6250,Convolution,1753664593.1325407,155064.8762,836.1627,44.5804,155945.6193,460.59,1056.26,595.67,0.00,,,,O(N² × K²),O(N²),596.05,1.0006,6250×6250,5×5,6246×6246,numpy,39062500,25,39012516,975312900,25,0.9987204096
6450,Convolution,1753665834.9766107,165537.5451,889.2203,44.1740,166470.9394,460.59,1095.01,634.41,0.00,,,,O(N² × K²),O(N²),634.80,1.0006,6450×6450,5×5,6446×6446,numpy,41602500,25,41550916,1038772900,25,0.9987600745147527
6650,Convolution,1753667158.1119037,175128.1139,945.7653,44.1056,176117.9849,460.59,1134.98,674.38,0.00,,,,O(N² × K²),O(N²),674.78,1.0006,6650×6650,5×5,6646×6646,numpy,44222500,25,44169316,1104232900,25,0.9987973542879756
6850,Convolution,1753668561.4092784,186332.7794,992.2851,44.4161,187369.4806,460.59,1176.16,715.57,0.00,,,,O(N² × K²),O(N²),715.98,1.0006,6850×6850,5×5,6846×6846,numpy,46922500,25,46867716,1171692900,25,0.9988324577761202
7050,Convolution,1753670054.6121316,197781.5694,1063.0682,44.5129,198889.1506,460.59,1218.57,757.98,0.00,,,,O(N² × K²),O(N²),758.40,1.0006,7050×7050,5×5,7046×7046,numpy,49702500,25,49646116,1241152900,25,0.998865570142347
7250,Convolution,1753671637.1402292,208676.2095,1122.5647,44.4811,209843.2554,460.59,1262.20,801.60,0.00,,,,O(N² × K²),O(N²),802.04,1.0005,7250×7250,5×5,7246×7246,numpy,52562500,25,52504516,1312612900,25,0.9988968561236623
7450,Convolution,1753673308.8546803,220595.4785,1186.9502,44.4223,221826.8510,460.59,1307.04,846.45,0.00,,,,O(N² × K²),O(N²),846.90,1.0005,7450×7450,5×5,7446×7446,numpy,55502500,25,55442916,1386072900,25,0.9989264627719472
7650,Convolution,1753675077.3362348,231775.5677,1251.2315,44.5431,233071.3422,460.59,1353.11,892.52,0.00,,,,O(N² × K²),O(N²),892.98,1.0005,7650×7650,5×5,7646×7646,numpy,58522500,25,58461316,1461532900,25,0.9989545217651331
7850,Convolution,1753676931.1201952,243342.7640,1316.8987,44.6738,244704.3364,460.59,1400.40,939.81,0.00,,,,O(N² × K²),O(N²),940.28,1.0005,7850×7850,5×5,7846×7846,numpy,61622500,25,61559716,1538992900,25,0.998981151365167
8050,Convolution,1753678880.819276,256440.5095,1384.5487,44.4706,257869.5288,460.59,1448.91,988.32,0.00,,,,O(N² × K²),O(N²),988.81,1.0005,8050×8050,5×5,8046×8046,numpy,64802500,25,64738116,1618452900,25,0.9990064580841789
8250,Convolution,1753680935.6702304,270477.9584,1455.6818,44.6169,271978.2571,460.59,1498.65,1038.05,0.00,,,,O(N² × K²),O(N²),1038.55,1.0005,8250×8250,5×5,8246×8246,numpy,68062500,25,67996516,1699912900,25,0.9990305381083563
8450,Convolution,1753683100.3377702,284055.2787,1526.1555,44.8689,285626.3031,460.60,1549.61,1089.01,0.00,,,,O(N² × K²),O(N²),1089.52,1.0005,8450×8450,5×5,8446×8446,numpy,71402500,25,71334916,1783372900,25,0.9990534785196596
8650,Convolution,1753685374.337904,296312.2368,1599.2934,44.7935,297956.3238,460.60,1601.77,1141.18,0.00,,,,O(N² × K²),O(N²),1141.70,1.0005,8650×8650,5×5,8646×8646,numpy,74822500,25,74753316,1868832900,25,0.9990753583480905
8850,Convolution,1753687748.74361,311641.2973,1673.4004,44.6914,313359.3891,460.60,1655.17,1194.57,0.00,,,,O(N² × K²),O(N²),1195.11,1.0004,8850×8850,5×5,8846×8846,numpy,78322500,25,78251716,1956292900,25,0.9990962494813113
9050,Convolution,1753690242.3653295,326264.2247,1749.0112,44.8466,328058.0825,460.60,1709.78,1249.18,0.00,,,,O(N² × K²),O(N²),1249.73,1.0004,9050×9050,5×5,9046×9046,numpy,81902500,25,81830116,2045752900,25,0.9991162174536797
9250,Convolution,1753692854.0713272,340404.5118,1830.4744,44.8029,342279.7891,460.60,1765.62,1305.02,0.00,,,,O(N² × K²),O(N²),1305.58,1.0004,9250×9250,5×5,9246×9246,numpy,85562500,25,85488516,2137212900,25,0.9991353221329438
9450,Convolution,1753695578.4231455,352802.6641,1907.0444,44.9923,354754.7008,460.60,1822.67,1362.07,0.00,,,,O(N² × K²),O(N²),1362.65,1.0004,9450×9450,5×5,9446×9446,numpy,89302500,25,89226916,2230672900,25,0.9991536183197559
9650,Convolution,1753698411.3315802,371571.6337,1991.8037,44.9323,373608.3697,460.60,1880.95,1420.35,0.00,,,,O(N² × K²),O(N²),1420.94,1.0004,9650×9650,5×5,9646×9646,numpy,93122500,25,93045316,2326132900,25,0.9991711562726516
9850,Convolution,1753701382.204841,386769.8302,2074.7702,46.0272,388890.6277,460.60,1940.45,1479.85,0.00,,,,O(N² × K²),O(N²),1480.45,1.0004,9850×9850,5×5,9846×9846,numpy,97022500,25,96943716,2423592900,25,0.9991879821690844
