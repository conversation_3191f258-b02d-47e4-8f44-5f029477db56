[{"input_size": 100, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657423.6863832, "execution_time_ms": 0.11415006592869759, "setup_time_ms": 0.06043212488293648, "cleanup_time_ms": 98.3375059440732, "total_time_ms": 98.51208813488483, "baseline_memory_mb": 650.21875, "peak_memory_mb": 650.37890625, "memory_increment_mb": 0.16015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 100, "compression_ratio": 1.0, "theoretical_operations": 400, "operations_per_sample": 4.0, "coefficient_efficiency": 1.0}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.009527439024390244}, {"input_size": 400, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657424.412727, "execution_time_ms": 0.24890592321753502, "setup_time_ms": 0.06548967212438583, "cleanup_time_ms": 100.83261085674167, "total_time_ms": 101.14700645208359, "baseline_memory_mb": 650.37890625, "peak_memory_mb": 650.37890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 400, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 1, "total_coefficients": 676, "compression_ratio": 0.591715976331361, "theoretical_operations": 1600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.69}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657425.1513321, "execution_time_ms": 0.3122396767139435, "setup_time_ms": 0.07962994277477264, "cleanup_time_ms": 99.56053271889687, "total_time_ms": 99.95240233838558, "baseline_memory_mb": 650.37890625, "peak_memory_mb": 650.37890625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 900, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 1548, "compression_ratio": 0.5813953488372093, "theoretical_operations": 3600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.72}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657425.8523893, "execution_time_ms": 0.3216906450688839, "setup_time_ms": 0.10314397513866425, "cleanup_time_ms": 99.20667205005884, "total_time_ms": 99.63150667026639, "baseline_memory_mb": 650.37890625, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.01953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 1600, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 2487, "compression_ratio": 0.6433453960595095, "theoretical_operations": 6400, "operations_per_sample": 4.0, "coefficient_efficiency": 1.554375}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 1.25}, {"input_size": 2500, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657426.5509167, "execution_time_ms": 0.3479054197669029, "setup_time_ms": 0.1357519067823887, "cleanup_time_ms": 97.900016233325, "total_time_ms": 98.3836735598743, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 2500, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 3508, "compression_ratio": 0.7126567844925884, "theoretical_operations": 10000, "operations_per_sample": 4.0, "coefficient_efficiency": 1.4032}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.03814697265625, "efficiency_ratio": 0.0}, {"input_size": 3600, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657427.242844, "execution_time_ms": 0.42321551591157913, "setup_time_ms": 0.13517402112483978, "cleanup_time_ms": 100.7210467942059, "total_time_ms": 101.27943633124232, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 3600, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 5143, "compression_ratio": 0.699980556095664, "theoretical_operations": 14400, "operations_per_sample": 4.0, "coefficient_efficiency": 1.428611111111111}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 4900, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657427.936305, "execution_time_ms": 0.444992259144783, "setup_time_ms": 0.17607398331165314, "cleanup_time_ms": 97.89731772616506, "total_time_ms": 98.51838396862149, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 4900, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 6568, "compression_ratio": 0.7460414129110841, "theoretical_operations": 19600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.3404081632653062}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.07476806640625, "efficiency_ratio": 0.0}, {"input_size": 6400, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657428.6437068, "execution_time_ms": 0.470819603651762, "setup_time_ms": 0.19513489678502083, "cleanup_time_ms": 97.49687928706408, "total_time_ms": 98.16283378750086, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 6400, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 8446, "compression_ratio": 0.7577551503670377, "theoretical_operations": 25600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.3196875}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.09765625, "efficiency_ratio": 0.0}, {"input_size": 8100, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657429.3364131, "execution_time_ms": 0.5157249048352242, "setup_time_ms": 0.23491866886615753, "cleanup_time_ms": 97.03862387686968, "total_time_ms": 97.78926745057106, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 8100, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 10255, "compression_ratio": 0.7898586055582643, "theoretical_operations": 32400, "operations_per_sample": 4.0, "coefficient_efficiency": 1.2660493827160493}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.12359619140625, "efficiency_ratio": 0.0}, {"input_size": 10000, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657430.031176, "execution_time_ms": 0.5537030287086964, "setup_time_ms": 0.30611269176006317, "cleanup_time_ms": 97.90921164676547, "total_time_ms": 98.76902736723423, "baseline_memory_mb": 650.3984375, "peak_memory_mb": 650.3984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 10000, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 3, "total_coefficients": 12423, "compression_ratio": 0.8049585446349513, "theoretical_operations": 40000, "operations_per_sample": 4.0, "coefficient_efficiency": 1.2423}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.152587890625, "efficiency_ratio": 0.0}]