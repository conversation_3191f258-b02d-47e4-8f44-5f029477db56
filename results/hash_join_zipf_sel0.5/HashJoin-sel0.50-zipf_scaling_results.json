[{"input_size": 100, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655094.2823834, "execution_time_ms": 0.12792004272341728, "setup_time_ms": 0.19975611940026283, "cleanup_time_ms": 22.44363771751523, "total_time_ms": 22.77131387963891, "baseline_memory_mb": 413.2265625, "peak_memory_mb": 412.9765625, "memory_increment_mb": -0.25, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 100, "left_table_size": 100, "right_table_size": 100, "result_size": 112, "hash_operations": 200, "hash_collisions": 34, "collision_rate": 0.17, "hash_efficiency": 1.0, "comparisons": 112, "memory_accesses": 312, "access_efficiency": 0.641, "build_time_ms": 0.041, "probe_time_ms": 0.045, "build_time_ratio": 0.475, "probe_time_ratio": 0.525, "actual_selectivity": 0.6364, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8636, "unique_left_keys": 66, "unique_right_keys": 61, "matching_keys": 42, "join_ratio": 0.0112, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=100, |S|=100]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=100, est_result=500]", "theoretical_memory_mb": 0.03662109375, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655094.4419246, "execution_time_ms": 0.23110629990696907, "setup_time_ms": 0.31044427305459976, "cleanup_time_ms": 22.10739580914378, "total_time_ms": 22.64894638210535, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 200, "left_table_size": 200, "right_table_size": 200, "result_size": 233, "hash_operations": 400, "hash_collisions": 74, "collision_rate": 0.185, "hash_efficiency": 1.0, "comparisons": 233, "memory_accesses": 633, "access_efficiency": 0.632, "build_time_ms": 0.07, "probe_time_ms": 0.098, "build_time_ratio": 0.419, "probe_time_ratio": 0.581, "actual_selectivity": 0.6667, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8333, "unique_left_keys": 126, "unique_right_keys": 123, "matching_keys": 84, "join_ratio": 0.005825, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=200, |S|=200]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=200, est_result=2000]", "theoretical_memory_mb": 0.13427734375, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655094.6020906, "execution_time_ms": 0.32732365652918816, "setup_time_ms": 0.4029008559882641, "cleanup_time_ms": 22.476100828498602, "total_time_ms": 23.206325341016054, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 300, "left_table_size": 300, "right_table_size": 300, "result_size": 362, "hash_operations": 600, "hash_collisions": 122, "collision_rate": 0.2033, "hash_efficiency": 1.0, "comparisons": 362, "memory_accesses": 962, "access_efficiency": 0.624, "build_time_ms": 0.106, "probe_time_ms": 0.144, "build_time_ratio": 0.424, "probe_time_ratio": 0.576, "actual_selectivity": 0.7022, "expected_selectivity": 0.5, "selectivity_accuracy": 0.7978, "unique_left_keys": 178, "unique_right_keys": 186, "matching_keys": 125, "join_ratio": 0.004022, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=300]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=300, est_result=4500]", "theoretical_memory_mb": 0.29296875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655094.763099, "execution_time_ms": 0.469317939132452, "setup_time_ms": 0.5357591435313225, "cleanup_time_ms": 22.771708201617002, "total_time_ms": 23.776785284280777, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 400, "left_table_size": 400, "right_table_size": 400, "result_size": 483, "hash_operations": 800, "hash_collisions": 160, "collision_rate": 0.2, "hash_efficiency": 1.0, "comparisons": 483, "memory_accesses": 1283, "access_efficiency": 0.624, "build_time_ms": 0.156, "probe_time_ms": 0.223, "build_time_ratio": 0.413, "probe_time_ratio": 0.587, "actual_selectivity": 0.6708, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8292, "unique_left_keys": 240, "unique_right_keys": 239, "matching_keys": 161, "join_ratio": 0.003019, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=400, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=8000]", "theoretical_memory_mb": 0.5126953125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655094.9252734, "execution_time_ms": 0.5590882152318954, "setup_time_ms": 0.6489288061857224, "cleanup_time_ms": 22.473766934126616, "total_time_ms": 23.681783955544233, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 500, "left_table_size": 500, "right_table_size": 500, "result_size": 614, "hash_operations": 1000, "hash_collisions": 200, "collision_rate": 0.2, "hash_efficiency": 1.0, "comparisons": 614, "memory_accesses": 1614, "access_efficiency": 0.62, "build_time_ms": 0.176, "probe_time_ms": 0.27, "build_time_ratio": 0.395, "probe_time_ratio": 0.605, "actual_selectivity": 0.6733, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8267, "unique_left_keys": 300, "unique_right_keys": 311, "matching_keys": 202, "join_ratio": 0.002456, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=500, |S|=500]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=500, est_result=12500]", "theoretical_memory_mb": 0.79345703125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655095.0882037, "execution_time_ms": 0.674085970968008, "setup_time_ms": 0.7617203518748283, "cleanup_time_ms": 22.61479292064905, "total_time_ms": 24.050599243491888, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 600, "left_table_size": 600, "right_table_size": 600, "result_size": 714, "hash_operations": 1200, "hash_collisions": 238, "collision_rate": 0.1983, "hash_efficiency": 1.0, "comparisons": 714, "memory_accesses": 1914, "access_efficiency": 0.627, "build_time_ms": 0.221, "probe_time_ms": 0.321, "build_time_ratio": 0.408, "probe_time_ratio": 0.592, "actual_selectivity": 0.616, "expected_selectivity": 0.5, "selectivity_accuracy": 0.884, "unique_left_keys": 362, "unique_right_keys": 351, "matching_keys": 223, "join_ratio": 0.001983, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=600]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=600, est_result=18000]", "theoretical_memory_mb": 1.13525390625, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655095.2541413, "execution_time_ms": 0.7834972813725471, "setup_time_ms": 0.9247171692550182, "cleanup_time_ms": 22.234822157770395, "total_time_ms": 23.94303660839796, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 700, "left_table_size": 700, "right_table_size": 700, "result_size": 865, "hash_operations": 1400, "hash_collisions": 271, "collision_rate": 0.1936, "hash_efficiency": 1.0, "comparisons": 865, "memory_accesses": 2265, "access_efficiency": 0.618, "build_time_ms": 0.254, "probe_time_ms": 0.363, "build_time_ratio": 0.412, "probe_time_ratio": 0.588, "actual_selectivity": 0.676, "expected_selectivity": 0.5, "selectivity_accuracy": 0.824, "unique_left_keys": 429, "unique_right_keys": 414, "matching_keys": 290, "join_ratio": 0.001765, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=700, |S|=700]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=700, est_result=24500]", "theoretical_memory_mb": 1.5380859375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655095.4181793, "execution_time_ms": 0.8986706845462322, "setup_time_ms": 1.0076630860567093, "cleanup_time_ms": 22.78565475717187, "total_time_ms": 24.69198852777481, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 800, "left_table_size": 800, "right_table_size": 800, "result_size": 964, "hash_operations": 1600, "hash_collisions": 317, "collision_rate": 0.1981, "hash_efficiency": 1.0, "comparisons": 964, "memory_accesses": 2564, "access_efficiency": 0.624, "build_time_ms": 0.281, "probe_time_ms": 0.443, "build_time_ratio": 0.388, "probe_time_ratio": 0.612, "actual_selectivity": 0.6749, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8251, "unique_left_keys": 483, "unique_right_keys": 501, "matching_keys": 326, "join_ratio": 0.001506, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=800, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=32000]", "theoretical_memory_mb": 2.001953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655095.5836434, "execution_time_ms": 1.0055066086351871, "setup_time_ms": 1.1498890817165375, "cleanup_time_ms": 22.990167140960693, "total_time_ms": 25.145562831312418, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 900, "left_table_size": 900, "right_table_size": 900, "result_size": 1154, "hash_operations": 1800, "hash_collisions": 336, "collision_rate": 0.1867, "hash_efficiency": 1.0, "comparisons": 1154, "memory_accesses": 2954, "access_efficiency": 0.609, "build_time_ms": 0.314, "probe_time_ms": 0.519, "build_time_ratio": 0.377, "probe_time_ratio": 0.623, "actual_selectivity": 0.6649, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8351, "unique_left_keys": 564, "unique_right_keys": 533, "matching_keys": 375, "join_ratio": 0.001425, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=900]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=900, est_result=40500]", "theoretical_memory_mb": 2.52685546875, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.50-zipf", "timestamp": 1753655095.7506378, "execution_time_ms": 1.1203549802303314, "setup_time_ms": 1.2482921592891216, "cleanup_time_ms": 22.809257730841637, "total_time_ms": 25.17790487036109, "baseline_memory_mb": 412.9765625, "peak_memory_mb": 412.9765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "zipf", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 1000, "left_table_size": 1000, "right_table_size": 1000, "result_size": 1250, "hash_operations": 2000, "hash_collisions": 386, "collision_rate": 0.193, "hash_efficiency": 1.0, "comparisons": 1250, "memory_accesses": 3250, "access_efficiency": 0.615, "build_time_ms": 0.349, "probe_time_ms": 0.545, "build_time_ratio": 0.39, "probe_time_ratio": 0.61, "actual_selectivity": 0.6906, "expected_selectivity": 0.5, "selectivity_accuracy": 0.8094, "unique_left_keys": 614, "unique_right_keys": 618, "matching_keys": 424, "join_ratio": 0.00125, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1000, |S|=1000]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=1000, est_result=50000]", "theoretical_memory_mb": 3.11279296875, "efficiency_ratio": 0.0}]