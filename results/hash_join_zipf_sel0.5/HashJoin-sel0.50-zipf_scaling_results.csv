input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_join_selectivity,custom_data_distribution,custom_left_table_ratio,custom_right_table_ratio,custom_input_size,custom_left_table_size,custom_right_table_size,custom_result_size,custom_hash_operations,custom_hash_collisions,custom_collision_rate,custom_hash_efficiency,custom_comparisons,custom_memory_accesses,custom_access_efficiency,custom_build_time_ms,custom_probe_time_ms,custom_build_time_ratio,custom_probe_time_ratio,custom_actual_selectivity,custom_expected_selectivity,custom_selectivity_accuracy,custom_unique_left_keys,custom_unique_right_keys,custom_matching_keys,custom_join_ratio,custom_algorithm_type
100,HashJoin-sel0.50-zipf,1753655094.2823834,0.1279,0.1998,22.4436,22.7713,413.23,412.98,-0.25,0.00,,,,"O(|R|+|S|) [|R|=100, |S|=100]","O(min(|R|,|S|)+result) [min=100, est_result=500]",0.04,0.0000,0.5,zipf,1.0,1.0,100,100,100,112,200,34,0.17,1.0,112,312,0.641,0.041,0.045,0.475,0.525,0.6364,0.5,0.8636,66,61,42,0.0112,hash_based_join
200,HashJoin-sel0.50-zipf,1753655094.4419246,0.2311,0.3104,22.1074,22.6489,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=200, |S|=200]","O(min(|R|,|S|)+result) [min=200, est_result=2000]",0.13,0.0000,0.5,zipf,1.0,1.0,200,200,200,233,400,74,0.185,1.0,233,633,0.632,0.07,0.098,0.419,0.581,0.6667,0.5,0.8333,126,123,84,0.005825,hash_based_join
300,HashJoin-sel0.50-zipf,1753655094.6020906,0.3273,0.4029,22.4761,23.2063,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=300, |S|=300]","O(min(|R|,|S|)+result) [min=300, est_result=4500]",0.29,0.0000,0.5,zipf,1.0,1.0,300,300,300,362,600,122,0.2033,1.0,362,962,0.624,0.106,0.144,0.424,0.576,0.7022,0.5,0.7978,178,186,125,0.004022,hash_based_join
400,HashJoin-sel0.50-zipf,1753655094.763099,0.4693,0.5358,22.7717,23.7768,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=400, |S|=400]","O(min(|R|,|S|)+result) [min=400, est_result=8000]",0.51,0.0000,0.5,zipf,1.0,1.0,400,400,400,483,800,160,0.2,1.0,483,1283,0.624,0.156,0.223,0.413,0.587,0.6708,0.5,0.8292,240,239,161,0.003019,hash_based_join
500,HashJoin-sel0.50-zipf,1753655094.9252734,0.5591,0.6489,22.4738,23.6818,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=500, |S|=500]","O(min(|R|,|S|)+result) [min=500, est_result=12500]",0.79,0.0000,0.5,zipf,1.0,1.0,500,500,500,614,1000,200,0.2,1.0,614,1614,0.62,0.176,0.27,0.395,0.605,0.6733,0.5,0.8267,300,311,202,0.002456,hash_based_join
600,HashJoin-sel0.50-zipf,1753655095.0882037,0.6741,0.7617,22.6148,24.0506,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=600, |S|=600]","O(min(|R|,|S|)+result) [min=600, est_result=18000]",1.14,0.0000,0.5,zipf,1.0,1.0,600,600,600,714,1200,238,0.1983,1.0,714,1914,0.627,0.221,0.321,0.408,0.592,0.616,0.5,0.884,362,351,223,0.001983,hash_based_join
700,HashJoin-sel0.50-zipf,1753655095.2541413,0.7835,0.9247,22.2348,23.9430,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=700, |S|=700]","O(min(|R|,|S|)+result) [min=700, est_result=24500]",1.54,0.0000,0.5,zipf,1.0,1.0,700,700,700,865,1400,271,0.1936,1.0,865,2265,0.618,0.254,0.363,0.412,0.588,0.676,0.5,0.824,429,414,290,0.001765,hash_based_join
800,HashJoin-sel0.50-zipf,1753655095.4181793,0.8987,1.0077,22.7857,24.6920,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=800, |S|=800]","O(min(|R|,|S|)+result) [min=800, est_result=32000]",2.00,0.0000,0.5,zipf,1.0,1.0,800,800,800,964,1600,317,0.1981,1.0,964,2564,0.624,0.281,0.443,0.388,0.612,0.6749,0.5,0.8251,483,501,326,0.001506,hash_based_join
900,HashJoin-sel0.50-zipf,1753655095.5836434,1.0055,1.1499,22.9902,25.1456,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=900, |S|=900]","O(min(|R|,|S|)+result) [min=900, est_result=40500]",2.53,0.0000,0.5,zipf,1.0,1.0,900,900,900,1154,1800,336,0.1867,1.0,1154,2954,0.609,0.314,0.519,0.377,0.623,0.6649,0.5,0.8351,564,533,375,0.001425,hash_based_join
1000,HashJoin-sel0.50-zipf,1753655095.7506378,1.1204,1.2483,22.8093,25.1779,412.98,412.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1000, |S|=1000]","O(min(|R|,|S|)+result) [min=1000, est_result=50000]",3.11,0.0000,0.5,zipf,1.0,1.0,1000,1000,1000,1250,2000,386,0.193,1.0,1250,3250,0.615,0.349,0.545,0.39,0.61,0.6906,0.5,0.8094,614,618,424,0.00125,hash_based_join
