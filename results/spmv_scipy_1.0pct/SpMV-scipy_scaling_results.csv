input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_max_error,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_vector_norm,custom_result_norm,custom_matrix_frobenius_norm,custom_theoretical_flops,custom_storage_format,custom_memory_efficiency
50,SpMV-scipy,1753651098.2227123,0.0535,0.4137,27.3611,27.8283,434.70,434.70,0.00,0.00,,,,"O(nnz) [N=50, nnz≈25]","O(nnz+N) [N=50, memory≈125 elements]",0.00,0.0000,True,2.7755575615628914e-17,50,spmv_scipy,50×50,scipy_sparse,25,0.01,0.01,6.94734152397629,1.5660434575735314,2.9451157279834925,50,CSR,0.01
250,SpMV-scipy,1753651098.4176831,0.0587,1.1299,27.2563,28.4448,434.70,434.70,0.00,0.00,,,,"O(nnz) [N=250, nnz≈625]","O(nnz+N) [N=250, memory≈1,125 elements]",0.01,0.0000,True,0.0,1250,spmv_scipy,250×250,scipy_sparse,625,0.01,0.01,15.679504762404024,14.729385456741946,14.133665063679404,1250,CSR,0.01
450,SpMV-scipy,1753651098.61072,0.0632,3.6669,27.2747,31.0049,434.70,434.70,0.00,0.00,,,,"O(nnz) [N=450, nnz≈2,025]","O(nnz+N) [N=450, memory≈2,925 elements]",0.03,0.0000,True,0.0,4050,spmv_scipy,450×450,scipy_sparse,2025,0.01,0.01,21.52265512539589,28.013396049932744,26.404470273371356,4050,CSR,0.01
650,SpMV-scipy,1753651098.8066564,0.0652,8.1623,27.2905,35.5180,434.70,434.70,0.00,0.00,,,,"O(nnz) [N=650, nnz≈4,225]","O(nnz+N) [N=650, memory≈5,525 elements]",0.06,0.0000,True,0.0,8450,spmv_scipy,650×650,scipy_sparse,4225,0.01,0.01,26.423602109517876,38.62495281244622,37.678774740897595,8450,CSR,0.01
850,SpMV-scipy,1753651099.0084643,0.0691,14.5793,27.2949,41.9433,434.70,436.32,1.62,0.00,,,,"O(nnz) [N=850, nnz≈7,225]","O(nnz+N) [N=850, memory≈8,925 elements]",0.10,0.0609,True,0.0,14450,spmv_scipy,850×850,scipy_sparse,7225,0.01,0.01,30.3475521857665,53.76254167786289,49.602351349901916,14450,CSR,0.01
1050,SpMV-scipy,1753651099.2173214,0.0753,22.2980,38.1593,60.5327,436.32,439.16,2.83,0.00,,,,"O(nnz) [N=1050, nnz≈11,025]","O(nnz+N) [N=1050, memory≈13,125 elements]",0.15,0.0516,True,0.0,22050,spmv_scipy,1050×1050,scipy_sparse,11025,0.01,0.01,32.696215921148806,59.413775388784764,60.9404137102669,22050,CSR,0.01
1250,SpMV-scipy,1753651099.4461927,0.0809,56.4596,38.3130,94.8535,439.16,442.70,3.54,0.00,,,,"O(nnz) [N=1250, nnz≈15,625]","O(nnz+N) [N=1250, memory≈18,125 elements]",0.20,0.0572,True,0.0,31250,spmv_scipy,1250×1250,scipy_sparse,15625,0.01,0.01,35.184101745416434,72.39213406594719,72.39715730835846,31250,CSR,0.01
1450,SpMV-scipy,1753651099.722565,0.0872,65.9782,38.1714,104.2368,442.70,447.27,4.57,0.00,,,,"O(nnz) [N=1450, nnz≈21,025]","O(nnz+N) [N=1450, memory≈23,925 elements]",0.27,0.0586,True,0.0,42050,spmv_scipy,1450×1450,scipy_sparse,21025,0.01,0.01,37.25757957886495,84.49403746068958,83.49513628285756,42050,CSR,0.01
1650,SpMV-scipy,1753651100.0114589,0.0949,81.5838,38.1590,119.8377,447.27,452.11,4.83,0.00,,,,"O(nnz) [N=1650, nnz≈27,225]","O(nnz+N) [N=1650, memory≈30,525 elements]",0.34,0.0710,True,0.0,54450,spmv_scipy,1650×1650,scipy_sparse,27225,0.01,0.01,41.228438796304985,96.78176072352461,95.11586260309858,54450,CSR,0.01
1850,SpMV-scipy,1753651100.3177052,0.1038,98.3049,38.2030,136.6117,452.11,457.45,5.35,0.00,,,,"O(nnz) [N=1850, nnz≈34,225]","O(nnz+N) [N=1850, memory≈37,925 elements]",0.43,0.0798,True,0.0,68450,spmv_scipy,1850×1850,scipy_sparse,34225,0.01,0.01,43.61794274396044,106.49544593733054,106.40145650087746,68450,CSR,0.01
2050,SpMV-scipy,1753651100.6414802,0.1105,108.9010,38.2428,147.2543,457.45,457.67,0.22,0.00,,,,"O(nnz) [N=2050, nnz≈42,025]","O(nnz+N) [N=2050, memory≈46,125 elements]",0.52,2.3773,True,0.0,84050,spmv_scipy,2050×2050,scipy_sparse,42025,0.01,0.01,44.81429839947863,113.7176045561937,118.72147810213035,84050,CSR,0.01
2250,SpMV-scipy,1753651100.9768403,0.1189,138.1049,38.2377,176.4615,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=2250, nnz≈50,625]","O(nnz+N) [N=2250, memory≈55,125 elements]",0.62,0.0000,True,0.0,101250,spmv_scipy,2250×2250,scipy_sparse,50625,0.01,0.01,47.18640745460617,132.0258649156784,130.46724465746473,101250,CSR,0.01
2450,SpMV-scipy,1753651101.3417873,0.1293,171.9350,38.3148,210.3790,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=2450, nnz≈60,025]","O(nnz+N) [N=2450, memory≈64,925 elements]",0.73,0.0000,True,0.0,120050,spmv_scipy,2450×2450,scipy_sparse,60025,0.01,0.01,48.36453735506269,137.4452169374988,141.55046534813977,120050,CSR,0.01
2650,SpMV-scipy,1753651101.7409174,0.2020,235.1514,59.1985,294.5518,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=2650, nnz≈70,225]","O(nnz+N) [N=2650, memory≈75,525 elements]",0.85,0.0000,True,0.0,140450,spmv_scipy,2650×2650,scipy_sparse,70225,0.01,0.01,51.66518434623715,152.55678008571797,153.2434503382266,140450,CSR,0.01
2850,SpMV-scipy,1753651102.3319485,0.2122,235.1975,54.2443,289.6540,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=2850, nnz≈81,225]","O(nnz+N) [N=2850, memory≈86,925 elements]",0.98,0.0000,True,0.0,162450,spmv_scipy,2850×2850,scipy_sparse,81225,0.01,0.01,54.39801079402592,165.7127034803238,164.70694618385718,162450,CSR,0.01
3050,SpMV-scipy,1753651102.9071636,0.2251,279.0813,43.8922,323.1986,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=3050, nnz≈93,025]","O(nnz+N) [N=3050, memory≈99,125 elements]",1.12,0.0000,True,0.0,186050,spmv_scipy,3050×3050,scipy_sparse,93025,0.01,0.01,53.3251578092968,170.21665966281304,176.1946430481548,186050,CSR,0.01
3250,SpMV-scipy,1753651103.513289,0.2507,342.7130,44.4158,387.3794,457.67,457.67,0.00,0.00,,,,"O(nnz) [N=3250, nnz≈105,625]","O(nnz+N) [N=3250, memory≈112,125 elements]",1.27,0.0000,True,0.0,211250,spmv_scipy,3250×3250,scipy_sparse,105625,0.01,0.01,58.16550147455768,192.32156007360484,187.82343633637447,211250,CSR,0.01
3450,SpMV-scipy,1753651104.1738837,0.2725,387.5152,44.0397,431.8275,457.67,457.68,0.00,0.00,,,,"O(nnz) [N=3450, nnz≈119,025]","O(nnz+N) [N=3450, memory≈125,925 elements]",1.43,365.5527,True,0.0,238050,spmv_scipy,3450×3450,scipy_sparse,119025,0.01,0.01,58.90424943218702,200.27059179834473,199.47359396007053,238050,CSR,0.01
3650,SpMV-scipy,1753651104.8785326,0.2879,444.2166,43.8688,488.3733,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=3650, nnz≈133,225]","O(nnz+N) [N=3650, memory≈140,525 elements]",1.59,0.0000,True,0.0,266450,spmv_scipy,3650×3650,scipy_sparse,133225,0.01,0.01,60.88224852195641,216.08615343273166,210.50100073994568,266450,CSR,0.01
3850,SpMV-scipy,1753651105.6416066,0.3102,502.7822,43.0643,546.1567,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=3850, nnz≈148,225]","O(nnz+N) [N=3850, memory≈155,925 elements]",1.77,0.0000,True,0.0,296450,spmv_scipy,3850×3850,scipy_sparse,148225,0.01,0.01,61.82485321181183,217.61421182563322,222.32807443069876,296450,CSR,0.01
4050,SpMV-scipy,1753651106.4788985,0.2427,569.0162,39.2159,608.4749,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=4050, nnz≈164,025]","O(nnz+N) [N=4050, memory≈172,125 elements]",1.95,0.0000,True,0.0,328050,spmv_scipy,4050×4050,scipy_sparse,164025,0.01,0.01,62.55898254730146,228.80794302901015,233.49853618422094,328050,CSR,0.01
4250,SpMV-scipy,1753651107.2817714,0.2562,649.0693,39.1032,688.4287,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=4250, nnz≈180,625]","O(nnz+N) [N=4250, memory≈189,125 elements]",2.15,0.0000,True,0.0,361250,spmv_scipy,4250×4250,scipy_sparse,180625,0.01,0.01,65.37160402655253,247.55410646470514,246.07385468329335,361250,CSR,0.01
4450,SpMV-scipy,1753651108.164605,0.2737,726.7572,39.0350,766.0659,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=4450, nnz≈198,025]","O(nnz+N) [N=4450, memory≈206,925 elements]",2.35,0.0000,True,0.0,396050,spmv_scipy,4450×4450,scipy_sparse,198025,0.01,0.01,66.93278260674327,258.5205619202809,256.96706211801995,396050,CSR,0.01
4650,SpMV-scipy,1753651109.1252255,0.2994,808.4433,39.4959,848.2387,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=4650, nnz≈216,225]","O(nnz+N) [N=4650, memory≈225,525 elements]",2.56,0.0000,True,0.0,432450,spmv_scipy,4650×4650,scipy_sparse,216225,0.01,0.01,68.06284948064402,270.04971635443553,268.3795802233839,432450,CSR,0.01
4850,SpMV-scipy,1753651110.1682487,0.3093,890.7869,39.0050,930.1011,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=4850, nnz≈235,225]","O(nnz+N) [N=4850, memory≈244,925 elements]",2.78,0.0000,True,0.0,470450,spmv_scipy,4850×4850,scipy_sparse,235225,0.01,0.01,70.53714542254133,284.64650667748316,280.24698794561516,470450,CSR,0.01
5050,SpMV-scipy,1753651111.292721,0.3327,973.6104,38.9671,1012.9102,457.68,457.68,0.01,0.00,,,,"O(nnz) [N=5050, nnz≈255,025]","O(nnz+N) [N=5050, memory≈265,125 elements]",3.01,385.9014,True,0.0,510050,spmv_scipy,5050×5050,scipy_sparse,255025,0.01,0.01,69.65679338360282,290.68526732860306,291.8099377088267,510050,CSR,0.01
5250,SpMV-scipy,1753651112.4998727,0.3554,1059.4839,39.0620,1098.9013,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=5250, nnz≈275,625]","O(nnz+N) [N=5250, memory≈286,125 elements]",3.25,0.0000,True,0.0,551250,spmv_scipy,5250×5250,scipy_sparse,275625,0.01,0.01,73.02852900935734,308.95411666700477,303.6339318642585,551250,CSR,0.01
5450,SpMV-scipy,1753651113.793394,0.3655,1147.6545,39.0833,1187.1033,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=5450, nnz≈297,025]","O(nnz+N) [N=5450, memory≈307,925 elements]",3.50,0.0000,True,0.0,594050,spmv_scipy,5450×5450,scipy_sparse,297025,0.01,0.01,73.8982827875474,319.07977619623625,314.9656549611526,594050,CSR,0.01
5650,SpMV-scipy,1753651115.1752374,0.4079,1245.1326,39.0060,1284.5465,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=5650, nnz≈319,225]","O(nnz+N) [N=5650, memory≈330,525 elements]",3.76,0.0000,True,0.0,638450,spmv_scipy,5650×5650,scipy_sparse,319225,0.01,0.01,74.4080928220569,319.14181680573523,326.49014844817884,638450,CSR,0.01
5850,SpMV-scipy,1753651116.6542304,0.4158,1330.9457,39.0042,1370.3657,457.68,457.68,0.00,0.00,,,,"O(nnz) [N=5850, nnz≈342,225]","O(nnz+N) [N=5850, memory≈353,925 elements]",4.03,0.0000,True,0.0,684450,spmv_scipy,5850×5850,scipy_sparse,342225,0.01,0.01,75.1493693438263,337.7536768735092,337.5790648328052,684450,CSR,0.01
6050,SpMV-scipy,1753651118.2190533,0.4544,1447.6020,38.8860,1486.9424,457.68,457.69,0.00,0.00,,,,"O(nnz) [N=6050, nnz≈366,025]","O(nnz+N) [N=6050, memory≈378,125 elements]",4.30,1101.8809,True,0.0,732050,spmv_scipy,6050×6050,scipy_sparse,366025,0.01,0.01,78.8405577107323,355.0882574320424,349.1095711936271,732050,CSR,0.01
6250,SpMV-scipy,1753651119.9008615,0.4820,1565.3591,39.0731,1604.9142,457.69,457.69,0.00,0.00,,,,"O(nnz) [N=6250, nnz≈390,625]","O(nnz+N) [N=6250, memory≈403,125 elements]",4.59,0.0000,True,0.0,781250,spmv_scipy,6250×6250,scipy_sparse,390625,0.01,0.01,78.24445017303194,356.92797892036845,360.40497292440193,781250,CSR,0.01
6450,SpMV-scipy,1753651121.7007706,0.5075,1682.9312,38.9209,1722.3597,457.69,457.69,0.00,0.00,,,,"O(nnz) [N=6450, nnz≈416,025]","O(nnz+N) [N=6450, memory≈428,925 elements]",4.88,0.0000,True,0.0,832050,spmv_scipy,6450×6450,scipy_sparse,416025,0.01,0.01,78.94423292277045,366.7301665760261,371.70959361050495,832050,CSR,0.01
6650,SpMV-scipy,1753651123.6177027,0.5304,1801.6426,38.9162,1841.0891,457.69,457.69,0.00,0.00,,,,"O(nnz) [N=6650, nnz≈442,225]","O(nnz+N) [N=6650, memory≈455,525 elements]",5.19,1328.0527,True,0.0,884450,spmv_scipy,6650×6650,scipy_sparse,442225,0.01,0.01,81.41749574089216,377.9450773163402,383.79018904583774,884450,CSR,0.01
6850,SpMV-scipy,1753651125.6538103,0.5515,1925.2784,38.9145,1964.7444,457.69,457.70,0.00,0.00,,,,"O(nnz) [N=6850, nnz≈469,225]","O(nnz+N) [N=6850, memory≈482,925 elements]",5.50,1408.1309,True,0.0,938450,spmv_scipy,6850×6850,scipy_sparse,469225,0.01,0.01,83.67830559870188,399.2738469110678,395.66813895704286,938450,CSR,0.01
7050,SpMV-scipy,1753651127.8138485,0.5881,2051.3399,71.3767,2123.3048,457.70,457.70,0.00,0.00,,,,"O(nnz) [N=7050, nnz≈497,025]","O(nnz+N) [N=7050, memory≈511,125 elements]",5.82,0.0000,True,0.0,994050,spmv_scipy,7050×7050,scipy_sparse,497025,0.01,0.01,83.40612415586374,399.62767623186704,407.0734643712129,994050,CSR,0.01
7250,SpMV-scipy,1753651130.1413894,0.6195,2364.6968,38.9161,2404.2324,457.70,457.70,0.00,0.00,,,,"O(nnz) [N=7250, nnz≈525,625]","O(nnz+N) [N=7250, memory≈540,125 elements]",6.15,0.0000,True,0.0,1051250,spmv_scipy,7250×7250,scipy_sparse,525625,0.01,0.01,85.16277394474639,420.35691001692703,418.78485002183095,1051250,CSR,0.01
7450,SpMV-scipy,1753651132.7571735,0.6703,2284.8486,38.7105,2324.2293,457.70,457.70,0.00,0.00,,,,"O(nnz) [N=7450, nnz≈555,025]","O(nnz+N) [N=7450, memory≈569,925 elements]",6.49,0.0000,True,0.0,1110050,spmv_scipy,7450×7450,scipy_sparse,555025,0.01,0.01,86.80710955262319,429.42145304243127,429.95138324973334,1110050,CSR,0.01
7650,SpMV-scipy,1753651135.275723,0.6743,2411.0057,38.9761,2450.6561,457.70,457.70,0.00,0.00,,,,"O(nnz) [N=7650, nnz≈585,225]","O(nnz+N) [N=7650, memory≈600,525 elements]",6.84,0.0000,True,0.0,1170450,spmv_scipy,7650×7650,scipy_sparse,585225,0.01,0.01,86.83612223185568,435.19262288226,441.74665835319087,1170450,CSR,0.01
7850,SpMV-scipy,1753651137.921012,0.7221,2541.2096,38.6158,2580.5474,457.70,457.70,0.00,0.00,,,,"O(nnz) [N=7850, nnz≈616,225]","O(nnz+N) [N=7850, memory≈631,925 elements]",7.20,0.0000,True,0.0,1232450,spmv_scipy,7850×7850,scipy_sparse,616225,0.01,0.01,88.51185148185178,453.8284882203482,453.6854450456949,1232450,CSR,0.01
8050,SpMV-scipy,1753651140.695896,0.7710,2658.6570,38.7001,2698.1282,457.70,457.98,0.29,0.00,,,,"O(nnz) [N=8050, nnz≈648,025]","O(nnz+N) [N=8050, memory≈664,125 elements]",7.57,26.1867,True,0.0,1296050,spmv_scipy,8050×8050,scipy_sparse,648025,0.01,0.01,90.05362974902677,475.4113457206467,464.572207020507,1296050,CSR,0.01
8250,SpMV-scipy,1753651143.5888999,0.7918,2805.1581,38.6632,2844.6130,457.98,459.28,1.30,0.00,,,,"O(nnz) [N=8250, nnz≈680,625]","O(nnz+N) [N=8250, memory≈697,125 elements]",7.95,6.1274,True,0.0,1361250,spmv_scipy,8250×8250,scipy_sparse,680625,0.01,0.01,90.61365484405506,469.5514094333707,476.1201653986126,1361250,CSR,0.01
8450,SpMV-scipy,1753651146.6288524,0.8529,2969.3534,38.6092,3008.8155,459.28,460.65,1.37,0.00,,,,"O(nnz) [N=8450, nnz≈714,025]","O(nnz+N) [N=8450, memory≈730,925 elements]",8.33,6.0947,True,0.0,1428050,spmv_scipy,8450×8450,scipy_sparse,714025,0.01,0.01,92.67484338439145,490.2727247391462,487.4088357384165,1428050,CSR,0.01
8650,SpMV-scipy,1753651149.833258,0.8788,3132.2220,38.6648,3171.7656,460.65,462.05,1.40,0.00,,,,"O(nnz) [N=8650, nnz≈748,225]","O(nnz+N) [N=8650, memory≈765,525 elements]",8.73,6.2237,True,0.0,1496450,spmv_scipy,8650×8650,scipy_sparse,748225,0.01,0.01,92.69701746300113,496.5246276741079,499.0556330653468,1496450,CSR,0.01
8850,SpMV-scipy,1753651153.2008355,0.9265,3310.4794,38.7517,3350.1575,462.05,463.48,1.43,0.00,,,,"O(nnz) [N=8850, nnz≈783,225]","O(nnz+N) [N=8850, memory≈800,925 elements]",9.13,6.3701,True,0.0,1566450,spmv_scipy,8850×8850,scipy_sparse,783225,0.01,0.01,93.84027054607637,504.32339957354793,511.174888255716,1566450,CSR,0.01
9050,SpMV-scipy,1753651156.7479498,0.9618,3478.0652,38.5978,3517.6248,463.48,465.17,1.69,0.00,,,,"O(nnz) [N=9050, nnz≈819,025]","O(nnz+N) [N=9050, memory≈837,125 elements]",9.55,5.6567,True,0.0,1638050,spmv_scipy,9050×9050,scipy_sparse,819025,0.01,0.01,95.8079403328875,524.1698457462254,523.0774533650352,1638050,CSR,0.01
9250,SpMV-scipy,1753651160.469487,1.0269,3666.9749,38.7359,3706.7377,465.17,466.71,1.54,0.00,,,,"O(nnz) [N=9250, nnz≈855,625]","O(nnz+N) [N=9250, memory≈874,125 elements]",9.97,6.4933,True,0.0,1711250,spmv_scipy,9250×9250,scipy_sparse,855625,0.01,0.01,94.91153377964336,533.3003180475911,533.6386722086679,1711250,CSR,0.01
9450,SpMV-scipy,1753651164.387083,1.0147,3818.2439,38.5762,3857.8347,466.71,468.25,1.54,0.00,,,,"O(nnz) [N=9450, nnz≈893,025]","O(nnz+N) [N=9450, memory≈911,925 elements]",10.40,6.7403,True,0.0,1786050,spmv_scipy,9450×9450,scipy_sparse,893025,0.01,0.01,97.75355635040941,548.6722216903863,545.5405534135814,1786050,CSR,0.01
9650,SpMV-scipy,1753651168.44148,1.0742,3982.4283,38.7119,4022.2144,468.25,469.82,1.57,0.00,,,,"O(nnz) [N=9650, nnz≈931,225]","O(nnz+N) [N=9650, memory≈950,525 elements]",10.84,6.8866,True,0.0,1862450,spmv_scipy,9650×9650,scipy_sparse,931225,0.01,0.01,98.02711513255286,553.5518677232726,557.0554561245317,1862450,CSR,0.01
9850,SpMV-scipy,1753651172.6613488,1.1441,4177.2546,38.5958,4216.9946,469.82,471.43,1.60,0.00,,,,"O(nnz) [N=9850, nnz≈970,225]","O(nnz+N) [N=9850, memory≈989,925 elements]",11.29,7.0501,True,0.0,1940450,spmv_scipy,9850×9850,scipy_sparse,970225,0.01,0.01,99.01229756083126,567.7999429217895,568.5628751257899,1940450,CSR,0.01
