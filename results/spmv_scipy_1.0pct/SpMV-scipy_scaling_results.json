[{"input_size": 50, "algorithm_name": "SpMV-scipy", "timestamp": 1753651098.2227123, "execution_time_ms": 0.05349284037947655, "setup_time_ms": 0.41371211409568787, "cleanup_time_ms": 27.361088898032904, "total_time_ms": 27.828293852508068, "baseline_memory_mb": 434.69921875, "peak_memory_mb": 434.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 2.7755575615628914e-17, "operations_count": 50, "algorithm_type": "spmv_scipy", "matrix_size": "50×50", "implementation": "scipy_sparse", "nnz": 25, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 6.94734152397629, "result_norm": 1.5660434575735314, "matrix_frobenius_norm": 2.9451157279834925, "theoretical_flops": 50, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=50, nnz≈25]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈125 elements]", "theoretical_memory_mb": 0.00124359130859375, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651098.4176831, "execution_time_ms": 0.0586668960750103, "setup_time_ms": 1.129866112023592, "cleanup_time_ms": 27.256289962679148, "total_time_ms": 28.44482297077775, "baseline_memory_mb": 434.69921875, "peak_memory_mb": 434.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1250, "algorithm_type": "spmv_scipy", "matrix_size": "250×250", "implementation": "scipy_sparse", "nnz": 625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 15.679504762404024, "result_norm": 14.729385456741946, "matrix_frobenius_norm": 14.133665063679404, "theoretical_flops": 1250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=250, nnz≈625]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈1,125 elements]", "theoretical_memory_mb": 0.01192474365234375, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651098.61072, "execution_time_ms": 0.06320644170045853, "setup_time_ms": 3.6669461987912655, "cleanup_time_ms": 27.27471385151148, "total_time_ms": 31.004866492003202, "baseline_memory_mb": 434.69921875, "peak_memory_mb": 434.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 4050, "algorithm_type": "spmv_scipy", "matrix_size": "450×450", "implementation": "scipy_sparse", "nnz": 2025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 21.52265512539589, "result_norm": 28.013396049932744, "matrix_frobenius_norm": 26.404470273371356, "theoretical_flops": 4050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=450, nnz≈2,025]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈2,925 elements]", "theoretical_memory_mb": 0.03176116943359375, "efficiency_ratio": 0.0}, {"input_size": 650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651098.8066564, "execution_time_ms": 0.06517637521028519, "setup_time_ms": 8.162285201251507, "cleanup_time_ms": 27.29051001369953, "total_time_ms": 35.51797159016132, "baseline_memory_mb": 434.69921875, "peak_memory_mb": 434.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 8450, "algorithm_type": "spmv_scipy", "matrix_size": "650×650", "implementation": "scipy_sparse", "nnz": 4225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 26.423602109517876, "result_norm": 38.62495281244622, "matrix_frobenius_norm": 37.678774740897595, "theoretical_flops": 8450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=650, nnz≈4,225]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈5,525 elements]", "theoretical_memory_mb": 0.06075286865234375, "efficiency_ratio": 0.0}, {"input_size": 850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651099.0084643, "execution_time_ms": 0.06909724324941635, "setup_time_ms": 14.579328708350658, "cleanup_time_ms": 27.294905856251717, "total_time_ms": 41.94333180785179, "baseline_memory_mb": 434.69921875, "peak_memory_mb": 436.32421875, "memory_increment_mb": 1.625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 14450, "algorithm_type": "spmv_scipy", "matrix_size": "850×850", "implementation": "scipy_sparse", "nnz": 7225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 30.3475521857665, "result_norm": 53.76254167786289, "matrix_frobenius_norm": 49.602351349901916, "theoretical_flops": 14450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=850, nnz≈7,225]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈8,925 elements]", "theoretical_memory_mb": 0.09889984130859375, "efficiency_ratio": 0.060861440805288464}, {"input_size": 1050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651099.2173214, "execution_time_ms": 0.07532946765422821, "setup_time_ms": 22.298031020909548, "cleanup_time_ms": 38.15932618454099, "total_time_ms": 60.53268667310476, "baseline_memory_mb": 436.32421875, "peak_memory_mb": 439.15625, "memory_increment_mb": 2.83203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 22050, "algorithm_type": "spmv_scipy", "matrix_size": "1050×1050", "implementation": "scipy_sparse", "nnz": 11025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 32.696215921148806, "result_norm": 59.413775388784764, "matrix_frobenius_norm": 60.9404137102669, "theoretical_flops": 22050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=1050, nnz≈11,025]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈13,125 elements]", "theoretical_memory_mb": 0.14620208740234375, "efficiency_ratio": 0.05162446120689655}, {"input_size": 1250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651099.4461927, "execution_time_ms": 0.08085109293460846, "setup_time_ms": 56.4596327021718, "cleanup_time_ms": 38.312985096126795, "total_time_ms": 94.8534688912332, "baseline_memory_mb": 439.15625, "peak_memory_mb": 442.69921875, "memory_increment_mb": 3.54296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 31250, "algorithm_type": "spmv_scipy", "matrix_size": "1250×1250", "implementation": "scipy_sparse", "nnz": 15625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 35.184101745416434, "result_norm": 72.39213406594719, "matrix_frobenius_norm": 72.39715730835846, "theoretical_flops": 31250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=1250, nnz≈15,625]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈18,125 elements]", "theoretical_memory_mb": 0.20265960693359375, "efficiency_ratio": 0.057200506477398015}, {"input_size": 1450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651099.722565, "execution_time_ms": 0.08715428411960602, "setup_time_ms": 65.97823835909367, "cleanup_time_ms": 38.171421736478806, "total_time_ms": 104.23681437969208, "baseline_memory_mb": 442.69921875, "peak_memory_mb": 447.2734375, "memory_increment_mb": 4.57421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 42050, "algorithm_type": "spmv_scipy", "matrix_size": "1450×1450", "implementation": "scipy_sparse", "nnz": 21025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 37.25757957886495, "result_norm": 84.49403746068958, "matrix_frobenius_norm": 83.49513628285756, "theoretical_flops": 42050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=1450, nnz≈21,025]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈23,925 elements]", "theoretical_memory_mb": 0.26827239990234375, "efficiency_ratio": 0.05864879109735269}, {"input_size": 1650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651100.0114589, "execution_time_ms": 0.09485865011811256, "setup_time_ms": 81.5838472917676, "cleanup_time_ms": 38.15895318984985, "total_time_ms": 119.83765913173556, "baseline_memory_mb": 447.2734375, "peak_memory_mb": 452.10546875, "memory_increment_mb": 4.83203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 54450, "algorithm_type": "spmv_scipy", "matrix_size": "1650×1650", "implementation": "scipy_sparse", "nnz": 27225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 41.228438796304985, "result_norm": 96.78176072352461, "matrix_frobenius_norm": 95.11586260309858, "theoretical_flops": 54450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=1650, nnz≈27,225]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈30,525 elements]", "theoretical_memory_mb": 0.34304046630859375, "efficiency_ratio": 0.07099301485448666}, {"input_size": 1850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651100.3177052, "execution_time_ms": 0.10375939309597015, "setup_time_ms": 98.3049152418971, "cleanup_time_ms": 38.203011732548475, "total_time_ms": 136.61168636754155, "baseline_memory_mb": 452.10546875, "peak_memory_mb": 457.453125, "memory_increment_mb": 5.34765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 68450, "algorithm_type": "spmv_scipy", "matrix_size": "1850×1850", "implementation": "scipy_sparse", "nnz": 34225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 43.61794274396044, "result_norm": 106.49544593733054, "matrix_frobenius_norm": 106.40145650087746, "theoretical_flops": 68450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=1850, nnz≈34,225]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈37,925 elements]", "theoretical_memory_mb": 0.42696380615234375, "efficiency_ratio": 0.07984129611029948}, {"input_size": 2050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651100.6414802, "execution_time_ms": 0.11048857122659683, "setup_time_ms": 108.90099918469787, "cleanup_time_ms": 38.2427703589201, "total_time_ms": 147.25425811484456, "baseline_memory_mb": 457.453125, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.21875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 84050, "algorithm_type": "spmv_scipy", "matrix_size": "2050×2050", "implementation": "scipy_sparse", "nnz": 42025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 44.81429839947863, "result_norm": 113.7176045561937, "matrix_frobenius_norm": 118.72147810213035, "theoretical_flops": 84050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=2050, nnz≈42,025]", "theoretical_space_complexity": "O(nnz+N) [N=2050, memory≈46,125 elements]", "theoretical_memory_mb": 0.5200424194335938, "efficiency_ratio": 2.3773367745535716}, {"input_size": 2250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651100.9768403, "execution_time_ms": 0.11894023045897484, "setup_time_ms": 138.1048932671547, "cleanup_time_ms": 38.23765320703387, "total_time_ms": 176.46148670464754, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 101250, "algorithm_type": "spmv_scipy", "matrix_size": "2250×2250", "implementation": "scipy_sparse", "nnz": 50625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 47.18640745460617, "result_norm": 132.0258649156784, "matrix_frobenius_norm": 130.46724465746473, "theoretical_flops": 101250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=2250, nnz≈50,625]", "theoretical_space_complexity": "O(nnz+N) [N=2250, memory≈55,125 elements]", "theoretical_memory_mb": 0.6222763061523438, "efficiency_ratio": 0.0}, {"input_size": 2450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651101.3417873, "execution_time_ms": 0.12925313785672188, "setup_time_ms": 171.93499999120831, "cleanup_time_ms": 38.314796052873135, "total_time_ms": 210.37904918193817, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 120050, "algorithm_type": "spmv_scipy", "matrix_size": "2450×2450", "implementation": "scipy_sparse", "nnz": 60025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 48.36453735506269, "result_norm": 137.4452169374988, "matrix_frobenius_norm": 141.55046534813977, "theoretical_flops": 120050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=2450, nnz≈60,025]", "theoretical_space_complexity": "O(nnz+N) [N=2450, memory≈64,925 elements]", "theoretical_memory_mb": 0.7336654663085938, "efficiency_ratio": 0.0}, {"input_size": 2650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651101.7409174, "execution_time_ms": 0.2019607461988926, "setup_time_ms": 235.1513602770865, "cleanup_time_ms": 59.19850617647171, "total_time_ms": 294.5518271997571, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 140450, "algorithm_type": "spmv_scipy", "matrix_size": "2650×2650", "implementation": "scipy_sparse", "nnz": 70225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 51.66518434623715, "result_norm": 152.55678008571797, "matrix_frobenius_norm": 153.2434503382266, "theoretical_flops": 140450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=2650, nnz≈70,225]", "theoretical_space_complexity": "O(nnz+N) [N=2650, memory≈75,525 elements]", "theoretical_memory_mb": 0.8542098999023438, "efficiency_ratio": 0.0}, {"input_size": 2850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651102.3319485, "execution_time_ms": 0.21215295419096947, "setup_time_ms": 235.1975329220295, "cleanup_time_ms": 54.24429615959525, "total_time_ms": 289.6539820358157, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 162450, "algorithm_type": "spmv_scipy", "matrix_size": "2850×2850", "implementation": "scipy_sparse", "nnz": 81225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 54.39801079402592, "result_norm": 165.7127034803238, "matrix_frobenius_norm": 164.70694618385718, "theoretical_flops": 162450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=2850, nnz≈81,225]", "theoretical_space_complexity": "O(nnz+N) [N=2850, memory≈86,925 elements]", "theoretical_memory_mb": 0.9839096069335938, "efficiency_ratio": 0.0}, {"input_size": 3050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651102.9071636, "execution_time_ms": 0.225124042481184, "setup_time_ms": 279.0812738239765, "cleanup_time_ms": 43.8922462053597, "total_time_ms": 323.1986440718174, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 186050, "algorithm_type": "spmv_scipy", "matrix_size": "3050×3050", "implementation": "scipy_sparse", "nnz": 93025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 53.3251578092968, "result_norm": 170.21665966281304, "matrix_frobenius_norm": 176.1946430481548, "theoretical_flops": 186050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=3050, nnz≈93,025]", "theoretical_space_complexity": "O(nnz+N) [N=3050, memory≈99,125 elements]", "theoretical_memory_mb": 1.1227645874023438, "efficiency_ratio": 0.0}, {"input_size": 3250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651103.513289, "execution_time_ms": 0.25065261870622635, "setup_time_ms": 342.7129709161818, "cleanup_time_ms": 44.415750075131655, "total_time_ms": 387.3793736100197, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.671875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 211250, "algorithm_type": "spmv_scipy", "matrix_size": "3250×3250", "implementation": "scipy_sparse", "nnz": 105625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 58.16550147455768, "result_norm": 192.32156007360484, "matrix_frobenius_norm": 187.82343633637447, "theoretical_flops": 211250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=3250, nnz≈105,625]", "theoretical_space_complexity": "O(nnz+N) [N=3250, memory≈112,125 elements]", "theoretical_memory_mb": 1.2707748413085938, "efficiency_ratio": 0.0}, {"input_size": 3450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651104.1738837, "execution_time_ms": 0.2725093625485897, "setup_time_ms": 387.51520682126284, "cleanup_time_ms": 44.039747677743435, "total_time_ms": 431.82746386155486, "baseline_memory_mb": 457.671875, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 238050, "algorithm_type": "spmv_scipy", "matrix_size": "3450×3450", "implementation": "scipy_sparse", "nnz": 119025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 58.90424943218702, "result_norm": 200.27059179834473, "matrix_frobenius_norm": 199.47359396007053, "theoretical_flops": 238050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=3450, nnz≈119,025]", "theoretical_space_complexity": "O(nnz+N) [N=3450, memory≈125,925 elements]", "theoretical_memory_mb": 1.4279403686523438, "efficiency_ratio": 365.552734375}, {"input_size": 3650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651104.8785326, "execution_time_ms": 0.2878584899008274, "setup_time_ms": 444.21663181856275, "cleanup_time_ms": 43.86880388483405, "total_time_ms": 488.3732941932976, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 266450, "algorithm_type": "spmv_scipy", "matrix_size": "3650×3650", "implementation": "scipy_sparse", "nnz": 133225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 60.88224852195641, "result_norm": 216.08615343273166, "matrix_frobenius_norm": 210.50100073994568, "theoretical_flops": 266450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=3650, nnz≈133,225]", "theoretical_space_complexity": "O(nnz+N) [N=3650, memory≈140,525 elements]", "theoretical_memory_mb": 1.5942611694335938, "efficiency_ratio": 0.0}, {"input_size": 3850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651105.6416066, "execution_time_ms": 0.3101661801338196, "setup_time_ms": 502.78217205777764, "cleanup_time_ms": 43.06431394070387, "total_time_ms": 546.1566521786153, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 296450, "algorithm_type": "spmv_scipy", "matrix_size": "3850×3850", "implementation": "scipy_sparse", "nnz": 148225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 61.82485321181183, "result_norm": 217.61421182563322, "matrix_frobenius_norm": 222.32807443069876, "theoretical_flops": 296450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=3850, nnz≈148,225]", "theoretical_space_complexity": "O(nnz+N) [N=3850, memory≈155,925 elements]", "theoretical_memory_mb": 1.7697372436523438, "efficiency_ratio": 0.0}, {"input_size": 4050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651106.4788985, "execution_time_ms": 0.24274401366710663, "setup_time_ms": 569.0162088721991, "cleanup_time_ms": 39.21591490507126, "total_time_ms": 608.4748677909374, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 328050, "algorithm_type": "spmv_scipy", "matrix_size": "4050×4050", "implementation": "scipy_sparse", "nnz": 164025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 62.55898254730146, "result_norm": 228.80794302901015, "matrix_frobenius_norm": 233.49853618422094, "theoretical_flops": 328050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=4050, nnz≈164,025]", "theoretical_space_complexity": "O(nnz+N) [N=4050, memory≈172,125 elements]", "theoretical_memory_mb": 1.9543685913085938, "efficiency_ratio": 0.0}, {"input_size": 4250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651107.2817714, "execution_time_ms": 0.2562071196734905, "setup_time_ms": 649.0692729130387, "cleanup_time_ms": 39.103247225284576, "total_time_ms": 688.4287272579968, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 361250, "algorithm_type": "spmv_scipy", "matrix_size": "4250×4250", "implementation": "scipy_sparse", "nnz": 180625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 65.37160402655253, "result_norm": 247.55410646470514, "matrix_frobenius_norm": 246.07385468329335, "theoretical_flops": 361250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=4250, nnz≈180,625]", "theoretical_space_complexity": "O(nnz+N) [N=4250, memory≈189,125 elements]", "theoretical_memory_mb": 2.1481552124023438, "efficiency_ratio": 0.0}, {"input_size": 4450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651108.164605, "execution_time_ms": 0.273748766630888, "setup_time_ms": 726.7572111450136, "cleanup_time_ms": 39.03495520353317, "total_time_ms": 766.0659151151776, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 396050, "algorithm_type": "spmv_scipy", "matrix_size": "4450×4450", "implementation": "scipy_sparse", "nnz": 198025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 66.93278260674327, "result_norm": 258.5205619202809, "matrix_frobenius_norm": 256.96706211801995, "theoretical_flops": 396050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=4450, nnz≈198,025]", "theoretical_space_complexity": "O(nnz+N) [N=4450, memory≈206,925 elements]", "theoretical_memory_mb": 2.3510971069335938, "efficiency_ratio": 0.0}, {"input_size": 4650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651109.1252255, "execution_time_ms": 0.2994246780872345, "setup_time_ms": 808.4433320909739, "cleanup_time_ms": 39.49593985453248, "total_time_ms": 848.2386966235936, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 432450, "algorithm_type": "spmv_scipy", "matrix_size": "4650×4650", "implementation": "scipy_sparse", "nnz": 216225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 68.06284948064402, "result_norm": 270.04971635443553, "matrix_frobenius_norm": 268.3795802233839, "theoretical_flops": 432450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=4650, nnz≈216,225]", "theoretical_space_complexity": "O(nnz+N) [N=4650, memory≈225,525 elements]", "theoretical_memory_mb": 2.5631942749023438, "efficiency_ratio": 0.0}, {"input_size": 4850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651110.1682487, "execution_time_ms": 0.30926279723644257, "setup_time_ms": 890.7868559472263, "cleanup_time_ms": 39.00502622127533, "total_time_ms": 930.1011449657381, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.67578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 470450, "algorithm_type": "spmv_scipy", "matrix_size": "4850×4850", "implementation": "scipy_sparse", "nnz": 235225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 70.53714542254133, "result_norm": 284.64650667748316, "matrix_frobenius_norm": 280.24698794561516, "theoretical_flops": 470450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=4850, nnz≈235,225]", "theoretical_space_complexity": "O(nnz+N) [N=4850, memory≈244,925 elements]", "theoretical_memory_mb": 2.7844467163085938, "efficiency_ratio": 0.0}, {"input_size": 5050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651111.292721, "execution_time_ms": 0.332706980407238, "setup_time_ms": 973.6103811301291, "cleanup_time_ms": 38.967086002230644, "total_time_ms": 1012.910174112767, "baseline_memory_mb": 457.67578125, "peak_memory_mb": 457.68359375, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 510050, "algorithm_type": "spmv_scipy", "matrix_size": "5050×5050", "implementation": "scipy_sparse", "nnz": 255025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 69.65679338360282, "result_norm": 290.68526732860306, "matrix_frobenius_norm": 291.8099377088267, "theoretical_flops": 510050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=5050, nnz≈255,025]", "theoretical_space_complexity": "O(nnz+N) [N=5050, memory≈265,125 elements]", "theoretical_memory_mb": 3.0148544311523438, "efficiency_ratio": 385.9013671875}, {"input_size": 5250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651112.4998727, "execution_time_ms": 0.35538654774427414, "setup_time_ms": 1059.4838592223823, "cleanup_time_ms": 39.06202083453536, "total_time_ms": 1098.901266604662, "baseline_memory_mb": 457.68359375, "peak_memory_mb": 457.68359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 551250, "algorithm_type": "spmv_scipy", "matrix_size": "5250×5250", "implementation": "scipy_sparse", "nnz": 275625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 73.02852900935734, "result_norm": 308.95411666700477, "matrix_frobenius_norm": 303.6339318642585, "theoretical_flops": 551250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=5250, nnz≈275,625]", "theoretical_space_complexity": "O(nnz+N) [N=5250, memory≈286,125 elements]", "theoretical_memory_mb": 3.2544174194335938, "efficiency_ratio": 0.0}, {"input_size": 5450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651113.793394, "execution_time_ms": 0.365467369556427, "setup_time_ms": 1147.654461208731, "cleanup_time_ms": 39.08334206789732, "total_time_ms": 1187.1032706461847, "baseline_memory_mb": 457.68359375, "peak_memory_mb": 457.68359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 594050, "algorithm_type": "spmv_scipy", "matrix_size": "5450×5450", "implementation": "scipy_sparse", "nnz": 297025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 73.8982827875474, "result_norm": 319.07977619623625, "matrix_frobenius_norm": 314.9656549611526, "theoretical_flops": 594050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=5450, nnz≈297,025]", "theoretical_space_complexity": "O(nnz+N) [N=5450, memory≈307,925 elements]", "theoretical_memory_mb": 3.5031356811523438, "efficiency_ratio": 0.0}, {"input_size": 5650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651115.1752374, "execution_time_ms": 0.40794191882014275, "setup_time_ms": 1245.1326409354806, "cleanup_time_ms": 39.00595894083381, "total_time_ms": 1284.5465417951345, "baseline_memory_mb": 457.68359375, "peak_memory_mb": 457.68359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 638450, "algorithm_type": "spmv_scipy", "matrix_size": "5650×5650", "implementation": "scipy_sparse", "nnz": 319225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 74.4080928220569, "result_norm": 319.14181680573523, "matrix_frobenius_norm": 326.49014844817884, "theoretical_flops": 638450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=5650, nnz≈319,225]", "theoretical_space_complexity": "O(nnz+N) [N=5650, memory≈330,525 elements]", "theoretical_memory_mb": 3.7610092163085938, "efficiency_ratio": 0.0}, {"input_size": 5850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651116.6542304, "execution_time_ms": 0.4158196970820427, "setup_time_ms": 1330.9456519782543, "cleanup_time_ms": 39.00424111634493, "total_time_ms": 1370.3657127916813, "baseline_memory_mb": 457.68359375, "peak_memory_mb": 457.68359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 684450, "algorithm_type": "spmv_scipy", "matrix_size": "5850×5850", "implementation": "scipy_sparse", "nnz": 342225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 75.1493693438263, "result_norm": 337.7536768735092, "matrix_frobenius_norm": 337.5790648328052, "theoretical_flops": 684450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=5850, nnz≈342,225]", "theoretical_space_complexity": "O(nnz+N) [N=5850, memory≈353,925 elements]", "theoretical_memory_mb": 4.028038024902344, "efficiency_ratio": 0.0}, {"input_size": 6050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651118.2190533, "execution_time_ms": 0.4544343799352646, "setup_time_ms": 1447.6020182482898, "cleanup_time_ms": 38.88595197349787, "total_time_ms": 1486.942404601723, "baseline_memory_mb": 457.68359375, "peak_memory_mb": 457.6875, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 732050, "algorithm_type": "spmv_scipy", "matrix_size": "6050×6050", "implementation": "scipy_sparse", "nnz": 366025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 78.8405577107323, "result_norm": 355.0882574320424, "matrix_frobenius_norm": 349.1095711936271, "theoretical_flops": 732050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=6050, nnz≈366,025]", "theoretical_space_complexity": "O(nnz+N) [N=6050, memory≈378,125 elements]", "theoretical_memory_mb": 4.304222106933594, "efficiency_ratio": 1101.880859375}, {"input_size": 6250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651119.9008615, "execution_time_ms": 0.48201726749539375, "setup_time_ms": 1565.3590569272637, "cleanup_time_ms": 39.0730882063508, "total_time_ms": 1604.91416240111, "baseline_memory_mb": 457.6875, "peak_memory_mb": 457.6875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 781250, "algorithm_type": "spmv_scipy", "matrix_size": "6250×6250", "implementation": "scipy_sparse", "nnz": 390625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 78.24445017303194, "result_norm": 356.92797892036845, "matrix_frobenius_norm": 360.40497292440193, "theoretical_flops": 781250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=6250, nnz≈390,625]", "theoretical_space_complexity": "O(nnz+N) [N=6250, memory≈403,125 elements]", "theoretical_memory_mb": 4.589561462402344, "efficiency_ratio": 0.0}, {"input_size": 6450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651121.7007706, "execution_time_ms": 0.5075421184301376, "setup_time_ms": 1682.9312471672893, "cleanup_time_ms": 38.920922204852104, "total_time_ms": 1722.3597114905715, "baseline_memory_mb": 457.6875, "peak_memory_mb": 457.6875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 832050, "algorithm_type": "spmv_scipy", "matrix_size": "6450×6450", "implementation": "scipy_sparse", "nnz": 416025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 78.94423292277045, "result_norm": 366.7301665760261, "matrix_frobenius_norm": 371.70959361050495, "theoretical_flops": 832050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=6450, nnz≈416,025]", "theoretical_space_complexity": "O(nnz+N) [N=6450, memory≈428,925 elements]", "theoretical_memory_mb": 4.884056091308594, "efficiency_ratio": 0.0}, {"input_size": 6650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651123.6177027, "execution_time_ms": 0.5303803831338882, "setup_time_ms": 1801.6426130197942, "cleanup_time_ms": 38.916150107979774, "total_time_ms": 1841.089143510908, "baseline_memory_mb": 457.6875, "peak_memory_mb": 457.69140625, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 884450, "algorithm_type": "spmv_scipy", "matrix_size": "6650×6650", "implementation": "scipy_sparse", "nnz": 442225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 81.41749574089216, "result_norm": 377.9450773163402, "matrix_frobenius_norm": 383.79018904583774, "theoretical_flops": 884450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=6650, nnz≈442,225]", "theoretical_space_complexity": "O(nnz+N) [N=6650, memory≈455,525 elements]", "theoretical_memory_mb": 5.187705993652344, "efficiency_ratio": 1328.052734375}, {"input_size": 6850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651125.6538103, "execution_time_ms": 0.5514658056199551, "setup_time_ms": 1925.2784359268844, "cleanup_time_ms": 38.914499804377556, "total_time_ms": 1964.744401536882, "baseline_memory_mb": 457.69140625, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 938450, "algorithm_type": "spmv_scipy", "matrix_size": "6850×6850", "implementation": "scipy_sparse", "nnz": 469225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 83.67830559870188, "result_norm": 399.2738469110678, "matrix_frobenius_norm": 395.66813895704286, "theoretical_flops": 938450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=6850, nnz≈469,225]", "theoretical_space_complexity": "O(nnz+N) [N=6850, memory≈482,925 elements]", "theoretical_memory_mb": 5.500511169433594, "efficiency_ratio": 1408.130859375}, {"input_size": 7050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651127.8138485, "execution_time_ms": 0.5881191231310368, "setup_time_ms": 2051.339943893254, "cleanup_time_ms": 71.37674978002906, "total_time_ms": 2123.304812796414, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 994050, "algorithm_type": "spmv_scipy", "matrix_size": "7050×7050", "implementation": "scipy_sparse", "nnz": 497025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 83.40612415586374, "result_norm": 399.62767623186704, "matrix_frobenius_norm": 407.0734643712129, "theoretical_flops": 994050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=7050, nnz≈497,025]", "theoretical_space_complexity": "O(nnz+N) [N=7050, memory≈511,125 elements]", "theoretical_memory_mb": 5.822471618652344, "efficiency_ratio": 0.0}, {"input_size": 7250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651130.1413894, "execution_time_ms": 0.619489885866642, "setup_time_ms": 2364.6967969834805, "cleanup_time_ms": 38.916067220270634, "total_time_ms": 2404.2323540896177, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1051250, "algorithm_type": "spmv_scipy", "matrix_size": "7250×7250", "implementation": "scipy_sparse", "nnz": 525625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 85.16277394474639, "result_norm": 420.35691001692703, "matrix_frobenius_norm": 418.78485002183095, "theoretical_flops": 1051250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=7250, nnz≈525,625]", "theoretical_space_complexity": "O(nnz+N) [N=7250, memory≈540,125 elements]", "theoretical_memory_mb": 6.153587341308594, "efficiency_ratio": 0.0}, {"input_size": 7450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651132.7571735, "execution_time_ms": 0.6702789105474949, "setup_time_ms": 2284.8485531285405, "cleanup_time_ms": 38.71047403663397, "total_time_ms": 2324.229306075722, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1110050, "algorithm_type": "spmv_scipy", "matrix_size": "7450×7450", "implementation": "scipy_sparse", "nnz": 555025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 86.80710955262319, "result_norm": 429.42145304243127, "matrix_frobenius_norm": 429.95138324973334, "theoretical_flops": 1110050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=7450, nnz≈555,025]", "theoretical_space_complexity": "O(nnz+N) [N=7450, memory≈569,925 elements]", "theoretical_memory_mb": 6.493858337402344, "efficiency_ratio": 0.0}, {"input_size": 7650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651135.275723, "execution_time_ms": 0.6742788478732109, "setup_time_ms": 2411.00573213771, "cleanup_time_ms": 38.97607512772083, "total_time_ms": 2450.656086113304, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1170450, "algorithm_type": "spmv_scipy", "matrix_size": "7650×7650", "implementation": "scipy_sparse", "nnz": 585225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 86.83612223185568, "result_norm": 435.19262288226, "matrix_frobenius_norm": 441.74665835319087, "theoretical_flops": 1170450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=7650, nnz≈585,225]", "theoretical_space_complexity": "O(nnz+N) [N=7650, memory≈600,525 elements]", "theoretical_memory_mb": 6.843284606933594, "efficiency_ratio": 0.0}, {"input_size": 7850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651137.921012, "execution_time_ms": 0.7220984436571598, "setup_time_ms": 2541.2095868960023, "cleanup_time_ms": 38.61576411873102, "total_time_ms": 2580.5474494583905, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1232450, "algorithm_type": "spmv_scipy", "matrix_size": "7850×7850", "implementation": "scipy_sparse", "nnz": 616225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 88.51185148185178, "result_norm": 453.8284882203482, "matrix_frobenius_norm": 453.6854450456949, "theoretical_flops": 1232450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=7850, nnz≈616,225]", "theoretical_space_complexity": "O(nnz+N) [N=7850, memory≈631,925 elements]", "theoretical_memory_mb": 7.201866149902344, "efficiency_ratio": 0.0}, {"input_size": 8050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651140.695896, "execution_time_ms": 0.7709815166890621, "setup_time_ms": 2658.6570367217064, "cleanup_time_ms": 38.700147066265345, "total_time_ms": 2698.128165304661, "baseline_memory_mb": 457.6953125, "peak_memory_mb": 457.984375, "memory_increment_mb": 0.2890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1296050, "algorithm_type": "spmv_scipy", "matrix_size": "8050×8050", "implementation": "scipy_sparse", "nnz": 648025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 90.05362974902677, "result_norm": 475.4113457206467, "matrix_frobenius_norm": 464.572207020507, "theoretical_flops": 1296050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=8050, nnz≈648,025]", "theoretical_space_complexity": "O(nnz+N) [N=8050, memory≈664,125 elements]", "theoretical_memory_mb": 7.569602966308594, "efficiency_ratio": 26.18673458614865}, {"input_size": 8250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651143.5888999, "execution_time_ms": 0.7917726412415504, "setup_time_ms": 2805.1580651663244, "cleanup_time_ms": 38.6631959117949, "total_time_ms": 2844.613033719361, "baseline_memory_mb": 457.984375, "peak_memory_mb": 459.28125, "memory_increment_mb": 1.296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1361250, "algorithm_type": "spmv_scipy", "matrix_size": "8250×8250", "implementation": "scipy_sparse", "nnz": 680625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 90.61365484405506, "result_norm": 469.5514094333707, "matrix_frobenius_norm": 476.1201653986126, "theoretical_flops": 1361250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=8250, nnz≈680,625]", "theoretical_space_complexity": "O(nnz+N) [N=8250, memory≈697,125 elements]", "theoretical_memory_mb": 7.946495056152344, "efficiency_ratio": 6.127417874623494}, {"input_size": 8450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651146.6288524, "execution_time_ms": 0.8529012091457844, "setup_time_ms": 2969.353436026722, "cleanup_time_ms": 38.60916011035442, "total_time_ms": 3008.8154973462224, "baseline_memory_mb": 459.28125, "peak_memory_mb": 460.6484375, "memory_increment_mb": 1.3671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1428050, "algorithm_type": "spmv_scipy", "matrix_size": "8450×8450", "implementation": "scipy_sparse", "nnz": 714025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 92.67484338439145, "result_norm": 490.2727247391462, "matrix_frobenius_norm": 487.4088357384165, "theoretical_flops": 1428050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=8450, nnz≈714,025]", "theoretical_space_complexity": "O(nnz+N) [N=8450, memory≈730,925 elements]", "theoretical_memory_mb": 8.332542419433594, "efficiency_ratio": 6.094659598214286}, {"input_size": 8650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651149.833258, "execution_time_ms": 0.8787727914750576, "setup_time_ms": 3132.221963722259, "cleanup_time_ms": 38.664828054606915, "total_time_ms": 3171.765564568341, "baseline_memory_mb": 460.6484375, "peak_memory_mb": 462.05078125, "memory_increment_mb": 1.40234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1496450, "algorithm_type": "spmv_scipy", "matrix_size": "8650×8650", "implementation": "scipy_sparse", "nnz": 748225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 92.69701746300113, "result_norm": 496.5246276741079, "matrix_frobenius_norm": 499.0556330653468, "theoretical_flops": 1496450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=8650, nnz≈748,225]", "theoretical_space_complexity": "O(nnz+N) [N=8650, memory≈765,525 elements]", "theoretical_memory_mb": 8.727745056152344, "efficiency_ratio": 6.223684496866295}, {"input_size": 8850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651153.2008355, "execution_time_ms": 0.9264573454856873, "setup_time_ms": 3310.4794030077755, "cleanup_time_ms": 38.75165106728673, "total_time_ms": 3350.157511420548, "baseline_memory_mb": 462.05078125, "peak_memory_mb": 463.484375, "memory_increment_mb": 1.43359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1566450, "algorithm_type": "spmv_scipy", "matrix_size": "8850×8850", "implementation": "scipy_sparse", "nnz": 783225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 93.84027054607637, "result_norm": 504.32339957354793, "matrix_frobenius_norm": 511.174888255716, "theoretical_flops": 1566450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=8850, nnz≈783,225]", "theoretical_space_complexity": "O(nnz+N) [N=8850, memory≈800,925 elements]", "theoretical_memory_mb": 9.132102966308594, "efficiency_ratio": 6.370077273501362}, {"input_size": 9050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651156.7479498, "execution_time_ms": 0.9618131443858147, "setup_time_ms": 3478.065175935626, "cleanup_time_ms": 38.59779005870223, "total_time_ms": 3517.624779138714, "baseline_memory_mb": 463.484375, "peak_memory_mb": 465.171875, "memory_increment_mb": 1.6875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1638050, "algorithm_type": "spmv_scipy", "matrix_size": "9050×9050", "implementation": "scipy_sparse", "nnz": 819025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 95.8079403328875, "result_norm": 524.1698457462254, "matrix_frobenius_norm": 523.0774533650352, "theoretical_flops": 1638050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=9050, nnz≈819,025]", "theoretical_space_complexity": "O(nnz+N) [N=9050, memory≈837,125 elements]", "theoretical_memory_mb": 9.545616149902344, "efficiency_ratio": 5.656661422164352}, {"input_size": 9250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651160.469487, "execution_time_ms": 1.0268811136484146, "setup_time_ms": 3666.9748816639185, "cleanup_time_ms": 38.73589402064681, "total_time_ms": 3706.7376567982137, "baseline_memory_mb": 465.171875, "peak_memory_mb": 466.70703125, "memory_increment_mb": 1.53515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1711250, "algorithm_type": "spmv_scipy", "matrix_size": "9250×9250", "implementation": "scipy_sparse", "nnz": 855625, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 94.91153377964336, "result_norm": 533.3003180475911, "matrix_frobenius_norm": 533.6386722086679, "theoretical_flops": 1711250, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=9250, nnz≈855,625]", "theoretical_space_complexity": "O(nnz+N) [N=9250, memory≈874,125 elements]", "theoretical_memory_mb": 9.968284606933594, "efficiency_ratio": 6.493335520038168}, {"input_size": 9450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651164.387083, "execution_time_ms": 1.0146871209144592, "setup_time_ms": 3818.2438728399575, "cleanup_time_ms": 38.57618384063244, "total_time_ms": 3857.8347438015044, "baseline_memory_mb": 466.70703125, "peak_memory_mb": 468.25, "memory_increment_mb": 1.54296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1786050, "algorithm_type": "spmv_scipy", "matrix_size": "9450×9450", "implementation": "scipy_sparse", "nnz": 893025, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 97.75355635040941, "result_norm": 548.6722216903863, "matrix_frobenius_norm": 545.5405534135814, "theoretical_flops": 1786050, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=9450, nnz≈893,025]", "theoretical_space_complexity": "O(nnz+N) [N=9450, memory≈911,925 elements]", "theoretical_memory_mb": 10.400108337402344, "efficiency_ratio": 6.740323378164557}, {"input_size": 9650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651168.44148, "execution_time_ms": 1.0741823352873325, "setup_time_ms": 3982.42830298841, "cleanup_time_ms": 38.711918983608484, "total_time_ms": 4022.214404307306, "baseline_memory_mb": 468.25, "peak_memory_mb": 469.82421875, "memory_increment_mb": 1.57421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1862450, "algorithm_type": "spmv_scipy", "matrix_size": "9650×9650", "implementation": "scipy_sparse", "nnz": 931225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 98.02711513255286, "result_norm": 553.5518677232726, "matrix_frobenius_norm": 557.0554561245317, "theoretical_flops": 1862450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=9650, nnz≈931,225]", "theoretical_space_complexity": "O(nnz+N) [N=9650, memory≈950,525 elements]", "theoretical_memory_mb": 10.841087341308594, "efficiency_ratio": 6.886646053039702}, {"input_size": 9850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651172.6613488, "execution_time_ms": 1.1441371403634548, "setup_time_ms": 4177.254613954574, "cleanup_time_ms": 38.59582869336009, "total_time_ms": 4216.994579788297, "baseline_memory_mb": 469.82421875, "peak_memory_mb": 471.42578125, "memory_increment_mb": 1.6015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1940450, "algorithm_type": "spmv_scipy", "matrix_size": "9850×9850", "implementation": "scipy_sparse", "nnz": 970225, "actual_sparsity_ratio": 0.01, "target_sparsity_ratio": 0.01, "vector_norm": 99.01229756083126, "result_norm": 567.7999429217895, "matrix_frobenius_norm": 568.5628751257899, "theoretical_flops": 1940450, "storage_format": "CSR", "memory_efficiency": 0.01}, "theoretical_time_complexity": "O(nnz) [N=9850, nnz≈970,225]", "theoretical_space_complexity": "O(nnz+N) [N=9850, memory≈989,925 elements]", "theoretical_memory_mb": 11.291221618652344, "efficiency_ratio": 7.050128620426829}]