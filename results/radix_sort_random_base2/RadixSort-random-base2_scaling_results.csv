input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-random-base2,1753654422.3953545,1.3895,0.0915,22.3621,23.8431,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=13, n=100, k=2]","O(n+k) [n=100, k=2]",0.00,0.0000,random,2,100,9981,4,2800,800,0.286,4300,1414,0,True,100,1.0,False,0.999,non_comparative
200,RadixSort-random-base2,1753654422.5709403,2.9031,0.1557,22.7817,25.8404,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=14, n=200, k=2]","O(n+k) [n=200, k=2]",0.00,0.0000,random,2,200,19961,5,6000,2000,0.333,9200,3015,0,True,198,0.99,False,0.999,non_comparative
300,RadixSort-random-base2,1753654422.7538238,4.3693,0.2266,22.3280,26.9239,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=14, n=300, k=2]","O(n+k) [n=300, k=2]",0.00,0.0000,random,2,300,29968,5,9000,3000,0.333,13800,4515,0,True,297,0.99,False,1.0,non_comparative
400,RadixSort-random-base2,1753654422.9488,6.1623,0.2848,22.1257,28.5728,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=15, n=400, k=2]","O(n+k) [n=400, k=2]",0.01,0.0000,random,2,400,39921,5,12800,4000,0.312,19600,6416,0,True,395,0.988,False,0.993,non_comparative
500,RadixSort-random-base2,1753654423.1568956,7.7913,0.3429,25.1825,33.3167,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=15, n=500, k=2]","O(n+k) [n=500, k=2]",0.01,0.0000,random,2,500,49972,5,16000,5000,0.312,24500,8016,0,True,493,0.986,False,0.997,non_comparative
600,RadixSort-random-base2,1753654423.379456,10.0205,0.4607,28.8750,39.3563,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=15, n=600, k=2]","O(n+k) [n=600, k=2]",0.01,0.0000,random,2,600,59936,5,19200,6000,0.312,29400,9616,0,True,593,0.988,False,0.998,non_comparative
700,RadixSort-random-base2,1753654423.6499093,12.1158,0.5004,23.2660,35.8822,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=16, n=700, k=2]","O(n+k) [n=700, k=2]",0.01,0.0000,random,2,700,69828,5,23800,7000,0.294,36400,11917,0,True,698,0.997,False,1.0,non_comparative
800,RadixSort-random-base2,1753654423.9171758,13.3210,0.5912,24.0029,37.9150,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=16, n=800, k=2]","O(n+k) [n=800, k=2]",0.01,0.0000,random,2,800,79998,5,27200,8000,0.294,41600,13617,0,True,796,0.995,False,1.0,non_comparative
900,RadixSort-random-base2,1753654424.1866229,15.1773,0.5980,22.3480,38.1234,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=16, n=900, k=2]","O(n+k) [n=900, k=2]",0.01,0.0000,random,2,900,89734,5,30600,9000,0.294,46800,15317,0,True,894,0.993,False,1.0,non_comparative
1000,RadixSort-random-base2,1753654424.4684484,16.5500,0.6557,22.2172,39.4229,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=16, n=1000, k=2]","O(n+k) [n=1000, k=2]",0.02,0.0000,random,2,1000,99944,5,34000,10000,0.294,52000,17017,0,True,994,0.994,False,1.0,non_comparative
