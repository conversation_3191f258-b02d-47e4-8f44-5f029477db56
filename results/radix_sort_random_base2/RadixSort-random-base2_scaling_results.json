[{"input_size": 100, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654422.3953545, "execution_time_ms": 1.389543991535902, "setup_time_ms": 0.09146006777882576, "cleanup_time_ms": 22.362116258591413, "total_time_ms": 23.84312031790614, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 100, "max_number": 9981, "num_digits": 4, "actual_digit_extractions": 2800, "theoretical_digit_extractions": 800, "digit_efficiency": 0.286, "array_accesses": 4300, "bucket_operations": 1414, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 0.999, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=13, n=100, k=2]", "theoretical_space_complexity": "O(n+k) [n=100, k=2]", "theoretical_memory_mb": 0.0015411376953125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654422.5709403, "execution_time_ms": 2.90306368842721, "setup_time_ms": 0.1556691713631153, "cleanup_time_ms": 22.78170920908451, "total_time_ms": 25.840442068874836, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 200, "max_number": 19961, "num_digits": 5, "actual_digit_extractions": 6000, "theoretical_digit_extractions": 2000, "digit_efficiency": 0.333, "array_accesses": 9200, "bucket_operations": 3015, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 198, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 0.999, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=14, n=200, k=2]", "theoretical_space_complexity": "O(n+k) [n=200, k=2]", "theoretical_memory_mb": 0.0030670166015625, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654422.7538238, "execution_time_ms": 4.36926344409585, "setup_time_ms": 0.22664666175842285, "cleanup_time_ms": 22.327990271151066, "total_time_ms": 26.92390037700534, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 300, "max_number": 29968, "num_digits": 5, "actual_digit_extractions": 9000, "theoretical_digit_extractions": 3000, "digit_efficiency": 0.333, "array_accesses": 13800, "bucket_operations": 4515, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 297, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 1.0, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=14, n=300, k=2]", "theoretical_space_complexity": "O(n+k) [n=300, k=2]", "theoretical_memory_mb": 0.0045928955078125, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654422.9488, "execution_time_ms": 6.162269972264767, "setup_time_ms": 0.2847597934305668, "cleanup_time_ms": 22.125738207250834, "total_time_ms": 28.572767972946167, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 400, "max_number": 39921, "num_digits": 5, "actual_digit_extractions": 12800, "theoretical_digit_extractions": 4000, "digit_efficiency": 0.312, "array_accesses": 19600, "bucket_operations": 6416, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 395, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 0.993, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=15, n=400, k=2]", "theoretical_space_complexity": "O(n+k) [n=400, k=2]", "theoretical_memory_mb": 0.0061187744140625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654423.1568956, "execution_time_ms": 7.791341561824083, "setup_time_ms": 0.34286174923181534, "cleanup_time_ms": 25.182468816637993, "total_time_ms": 33.31667212769389, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 500, "max_number": 49972, "num_digits": 5, "actual_digit_extractions": 16000, "theoretical_digit_extractions": 5000, "digit_efficiency": 0.312, "array_accesses": 24500, "bucket_operations": 8016, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 493, "uniqueness_ratio": 0.986, "was_already_sorted": false, "digit_distribution_entropy": 0.997, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=15, n=500, k=2]", "theoretical_space_complexity": "O(n+k) [n=500, k=2]", "theoretical_memory_mb": 0.0076446533203125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654423.379456, "execution_time_ms": 10.020548570901155, "setup_time_ms": 0.4606652073562145, "cleanup_time_ms": 28.87504082173109, "total_time_ms": 39.35625459998846, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 600, "max_number": 59936, "num_digits": 5, "actual_digit_extractions": 19200, "theoretical_digit_extractions": 6000, "digit_efficiency": 0.312, "array_accesses": 29400, "bucket_operations": 9616, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 593, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 0.998, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=15, n=600, k=2]", "theoretical_space_complexity": "O(n+k) [n=600, k=2]", "theoretical_memory_mb": 0.0091705322265625, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654423.6499093, "execution_time_ms": 12.115794233977795, "setup_time_ms": 0.5004452541470528, "cleanup_time_ms": 23.265965282917023, "total_time_ms": 35.88220477104187, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 700, "max_number": 69828, "num_digits": 5, "actual_digit_extractions": 23800, "theoretical_digit_extractions": 7000, "digit_efficiency": 0.294, "array_accesses": 36400, "bucket_operations": 11917, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 698, "uniqueness_ratio": 0.997, "was_already_sorted": false, "digit_distribution_entropy": 1.0, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=16, n=700, k=2]", "theoretical_space_complexity": "O(n+k) [n=700, k=2]", "theoretical_memory_mb": 0.0106964111328125, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654423.9171758, "execution_time_ms": 13.32095516845584, "setup_time_ms": 0.5911537446081638, "cleanup_time_ms": 24.002919904887676, "total_time_ms": 37.91502881795168, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 800, "max_number": 79998, "num_digits": 5, "actual_digit_extractions": 27200, "theoretical_digit_extractions": 8000, "digit_efficiency": 0.294, "array_accesses": 41600, "bucket_operations": 13617, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 796, "uniqueness_ratio": 0.995, "was_already_sorted": false, "digit_distribution_entropy": 1.0, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=16, n=800, k=2]", "theoretical_space_complexity": "O(n+k) [n=800, k=2]", "theoretical_memory_mb": 0.0122222900390625, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654424.1866229, "execution_time_ms": 15.17730625346303, "setup_time_ms": 0.5980110727250576, "cleanup_time_ms": 22.3480430431664, "total_time_ms": 38.12336036935449, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 900, "max_number": 89734, "num_digits": 5, "actual_digit_extractions": 30600, "theoretical_digit_extractions": 9000, "digit_efficiency": 0.294, "array_accesses": 46800, "bucket_operations": 15317, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 894, "uniqueness_ratio": 0.993, "was_already_sorted": false, "digit_distribution_entropy": 1.0, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=16, n=900, k=2]", "theoretical_space_complexity": "O(n+k) [n=900, k=2]", "theoretical_memory_mb": 0.0137481689453125, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-random-base2", "timestamp": 1753654424.4684484, "execution_time_ms": 16.549967974424362, "setup_time_ms": 0.6556902080774307, "cleanup_time_ms": 22.217242047190666, "total_time_ms": 39.42290022969246, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 2, "input_size": 1000, "max_number": 99944, "num_digits": 5, "actual_digit_extractions": 34000, "theoretical_digit_extractions": 10000, "digit_efficiency": 0.294, "array_accesses": 52000, "bucket_operations": 17017, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 994, "uniqueness_ratio": 0.994, "was_already_sorted": false, "digit_distribution_entropy": 1.0, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=16, n=1000, k=2]", "theoretical_space_complexity": "O(n+k) [n=1000, k=2]", "theoretical_memory_mb": 0.0152740478515625, "efficiency_ratio": 0.0}]