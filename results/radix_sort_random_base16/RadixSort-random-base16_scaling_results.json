[{"input_size": 100, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654424.7627077, "execution_time_ms": 0.4536398686468601, "setup_time_ms": 0.08885329589247704, "cleanup_time_ms": 23.230633698403835, "total_time_ms": 23.773126862943172, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 100, "max_number": 9981, "num_digits": 4, "actual_digit_extractions": 800, "theoretical_digit_extractions": 800, "digit_efficiency": 1.0, "array_accesses": 1300, "bucket_operations": 460, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 100, "uniqueness_ratio": 1.0, "was_already_sorted": false, "digit_distribution_entropy": 3.926, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=100, k=16]", "theoretical_space_complexity": "O(n+k) [n=100, k=16]", "theoretical_memory_mb": 0.00164794921875, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654424.9291751, "execution_time_ms": 0.8253131061792374, "setup_time_ms": 0.18315808847546577, "cleanup_time_ms": 22.192788776010275, "total_time_ms": 23.201259970664978, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 200, "max_number": 19961, "num_digits": 5, "actual_digit_extractions": 1600, "theoretical_digit_extractions": 2000, "digit_efficiency": 1.25, "array_accesses": 2600, "bucket_operations": 860, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 198, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 3.953, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=200, k=16]", "theoretical_space_complexity": "O(n+k) [n=200, k=16]", "theoretical_memory_mb": 0.003173828125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.0946345, "execution_time_ms": 1.2560833245515823, "setup_time_ms": 0.20993268117308617, "cleanup_time_ms": 23.70896702632308, "total_time_ms": 25.17498303204775, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 300, "max_number": 29968, "num_digits": 5, "actual_digit_extractions": 2400, "theoretical_digit_extractions": 3000, "digit_efficiency": 1.25, "array_accesses": 3900, "bucket_operations": 1260, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 297, "uniqueness_ratio": 0.99, "was_already_sorted": false, "digit_distribution_entropy": 3.975, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=300, k=16]", "theoretical_space_complexity": "O(n+k) [n=300, k=16]", "theoretical_memory_mb": 0.00469970703125, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.265693, "execution_time_ms": 1.6120254062116146, "setup_time_ms": 0.2925689332187176, "cleanup_time_ms": 22.654718719422817, "total_time_ms": 24.55931305885315, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 400, "max_number": 39921, "num_digits": 5, "actual_digit_extractions": 3200, "theoretical_digit_extractions": 4000, "digit_efficiency": 1.25, "array_accesses": 5200, "bucket_operations": 1660, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 395, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 3.969, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=400, k=16]", "theoretical_space_complexity": "O(n+k) [n=400, k=16]", "theoretical_memory_mb": 0.0062255859375, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.4369924, "execution_time_ms": 2.0475375466048717, "setup_time_ms": 0.34504616633057594, "cleanup_time_ms": 22.328845225274563, "total_time_ms": 24.72142893821001, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 500, "max_number": 49972, "num_digits": 5, "actual_digit_extractions": 4000, "theoretical_digit_extractions": 5000, "digit_efficiency": 1.25, "array_accesses": 6500, "bucket_operations": 2060, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 493, "uniqueness_ratio": 0.986, "was_already_sorted": false, "digit_distribution_entropy": 3.981, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=500, k=16]", "theoretical_space_complexity": "O(n+k) [n=500, k=16]", "theoretical_memory_mb": 0.00775146484375, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.611872, "execution_time_ms": 2.4630882777273655, "setup_time_ms": 0.4017692990601063, "cleanup_time_ms": 22.954158950597048, "total_time_ms": 25.81901652738452, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 600, "max_number": 59936, "num_digits": 5, "actual_digit_extractions": 4800, "theoretical_digit_extractions": 6000, "digit_efficiency": 1.25, "array_accesses": 7800, "bucket_operations": 2460, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 593, "uniqueness_ratio": 0.988, "was_already_sorted": false, "digit_distribution_entropy": 3.988, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=3, n=600, k=16]", "theoretical_space_complexity": "O(n+k) [n=600, k=16]", "theoretical_memory_mb": 0.00927734375, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.7925172, "execution_time_ms": 3.442978300154209, "setup_time_ms": 0.4983041435480118, "cleanup_time_ms": 22.23886363208294, "total_time_ms": 26.18014607578516, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 700, "max_number": 69828, "num_digits": 5, "actual_digit_extractions": 7000, "theoretical_digit_extractions": 7000, "digit_efficiency": 1.0, "array_accesses": 11200, "bucket_operations": 3575, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 698, "uniqueness_ratio": 0.997, "was_already_sorted": false, "digit_distribution_entropy": 3.977, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=700, k=16]", "theoretical_space_complexity": "O(n+k) [n=700, k=16]", "theoretical_memory_mb": 0.01080322265625, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654425.979362, "execution_time_ms": 3.935816790908575, "setup_time_ms": 0.5479976534843445, "cleanup_time_ms": 22.151299752295017, "total_time_ms": 26.635114196687937, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 800, "max_number": 79998, "num_digits": 5, "actual_digit_extractions": 8000, "theoretical_digit_extractions": 8000, "digit_efficiency": 1.0, "array_accesses": 12800, "bucket_operations": 4075, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 796, "uniqueness_ratio": 0.995, "was_already_sorted": false, "digit_distribution_entropy": 3.983, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=800, k=16]", "theoretical_space_complexity": "O(n+k) [n=800, k=16]", "theoretical_memory_mb": 0.0123291015625, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654426.1695282, "execution_time_ms": 4.506432358175516, "setup_time_ms": 0.6462926976382732, "cleanup_time_ms": 22.41852693259716, "total_time_ms": 27.57125198841095, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 900, "max_number": 89734, "num_digits": 5, "actual_digit_extractions": 9000, "theoretical_digit_extractions": 9000, "digit_efficiency": 1.0, "array_accesses": 14400, "bucket_operations": 4575, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 894, "uniqueness_ratio": 0.993, "was_already_sorted": false, "digit_distribution_entropy": 3.986, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=900, k=16]", "theoretical_space_complexity": "O(n+k) [n=900, k=16]", "theoretical_memory_mb": 0.01385498046875, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "RadixSort-random-base16", "timestamp": 1753654426.364104, "execution_time_ms": 5.022987630218267, "setup_time_ms": 0.6570820696651936, "cleanup_time_ms": 22.660610731691122, "total_time_ms": 28.340680431574583, "baseline_memory_mb": 411.640625, "peak_memory_mb": 411.640625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "radix_base": 16, "input_size": 1000, "max_number": 99944, "num_digits": 5, "actual_digit_extractions": 10000, "theoretical_digit_extractions": 10000, "digit_efficiency": 1.0, "array_accesses": 16000, "bucket_operations": 5075, "comparisons_used": 0, "correctness_verified": true, "unique_elements": 994, "uniqueness_ratio": 0.994, "was_already_sorted": false, "digit_distribution_entropy": 3.989, "algorithm_type": "non_comparative"}, "theoretical_time_complexity": "O(d*(n+k)) [d=4, n=1000, k=16]", "theoretical_space_complexity": "O(n+k) [n=1000, k=16]", "theoretical_memory_mb": 0.015380859375, "efficiency_ratio": 0.0}]