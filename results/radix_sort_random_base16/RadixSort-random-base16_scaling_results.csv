input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_radix_base,custom_input_size,custom_max_number,custom_num_digits,custom_actual_digit_extractions,custom_theoretical_digit_extractions,custom_digit_efficiency,custom_array_accesses,custom_bucket_operations,custom_comparisons_used,custom_correctness_verified,custom_unique_elements,custom_uniqueness_ratio,custom_was_already_sorted,custom_digit_distribution_entropy,custom_algorithm_type
100,RadixSort-random-base16,1753654424.7627077,0.4536,0.0889,23.2306,23.7731,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=100, k=16]","O(n+k) [n=100, k=16]",0.00,0.0000,random,16,100,9981,4,800,800,1.0,1300,460,0,True,100,1.0,False,3.926,non_comparative
200,RadixSort-random-base16,1753654424.9291751,0.8253,0.1832,22.1928,23.2013,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=200, k=16]","O(n+k) [n=200, k=16]",0.00,0.0000,random,16,200,19961,5,1600,2000,1.25,2600,860,0,True,198,0.99,False,3.953,non_comparative
300,RadixSort-random-base16,1753654425.0946345,1.2561,0.2099,23.7090,25.1750,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=300, k=16]","O(n+k) [n=300, k=16]",0.00,0.0000,random,16,300,29968,5,2400,3000,1.25,3900,1260,0,True,297,0.99,False,3.975,non_comparative
400,RadixSort-random-base16,1753654425.265693,1.6120,0.2926,22.6547,24.5593,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=400, k=16]","O(n+k) [n=400, k=16]",0.01,0.0000,random,16,400,39921,5,3200,4000,1.25,5200,1660,0,True,395,0.988,False,3.969,non_comparative
500,RadixSort-random-base16,1753654425.4369924,2.0475,0.3450,22.3288,24.7214,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=500, k=16]","O(n+k) [n=500, k=16]",0.01,0.0000,random,16,500,49972,5,4000,5000,1.25,6500,2060,0,True,493,0.986,False,3.981,non_comparative
600,RadixSort-random-base16,1753654425.611872,2.4631,0.4018,22.9542,25.8190,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=3, n=600, k=16]","O(n+k) [n=600, k=16]",0.01,0.0000,random,16,600,59936,5,4800,6000,1.25,7800,2460,0,True,593,0.988,False,3.988,non_comparative
700,RadixSort-random-base16,1753654425.7925172,3.4430,0.4983,22.2389,26.1801,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=700, k=16]","O(n+k) [n=700, k=16]",0.01,0.0000,random,16,700,69828,5,7000,7000,1.0,11200,3575,0,True,698,0.997,False,3.977,non_comparative
800,RadixSort-random-base16,1753654425.979362,3.9358,0.5480,22.1513,26.6351,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=800, k=16]","O(n+k) [n=800, k=16]",0.01,0.0000,random,16,800,79998,5,8000,8000,1.0,12800,4075,0,True,796,0.995,False,3.983,non_comparative
900,RadixSort-random-base16,1753654426.1695282,4.5064,0.6463,22.4185,27.5713,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=900, k=16]","O(n+k) [n=900, k=16]",0.01,0.0000,random,16,900,89734,5,9000,9000,1.0,14400,4575,0,True,894,0.993,False,3.986,non_comparative
1000,RadixSort-random-base16,1753654426.364104,5.0230,0.6571,22.6606,28.3407,411.64,411.64,0.00,0.00,,,,"O(d*(n+k)) [d=4, n=1000, k=16]","O(n+k) [n=1000, k=16]",0.02,0.0000,random,16,1000,99944,5,10000,10000,1.0,16000,5075,0,True,994,0.994,False,3.989,non_comparative
