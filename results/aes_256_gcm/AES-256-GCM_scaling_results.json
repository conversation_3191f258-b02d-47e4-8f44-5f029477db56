[{"input_size": 16, "algorithm_name": "AES-256-GCM", "timestamp": 1753702871.3521497, "execution_time_ms": 0.10133646428585052, "setup_time_ms": 0.018046703189611435, "cleanup_time_ms": 24.463642854243517, "total_time_ms": 24.58302602171898, "baseline_memory_mb": 420.92578125, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 1, "actual_blocks": 1, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 16, "expansion_ratio": 1.0, "throughput_mbps": 1.63, "bytes_per_second": 1712506, "blocks_per_second": 107031, "encryption_time_ms": 0.00934302806854248, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 9.1552734375e-05, "efficiency_ratio": 0.0234375}, {"input_size": 32, "algorithm_name": "AES-256-GCM", "timestamp": 1753702871.5213072, "execution_time_ms": 0.10629342868924141, "setup_time_ms": 0.019222963601350784, "cleanup_time_ms": 25.80789104104042, "total_time_ms": 25.933407433331013, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 2, "actual_blocks": 2, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 32, "expansion_ratio": 1.0, "throughput_mbps": 3.09, "bytes_per_second": 3241026, "blocks_per_second": 202564, "encryption_time_ms": 0.009873416274785995, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0001373291015625, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "AES-256-GCM", "timestamp": 1753702871.6919339, "execution_time_ms": 0.11341702193021774, "setup_time_ms": 0.01971423625946045, "cleanup_time_ms": 26.132164988666773, "total_time_ms": 26.26529624685645, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 4, "actual_blocks": 4, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 64, "expansion_ratio": 1.0, "throughput_mbps": 6.0, "bytes_per_second": 6290688, "blocks_per_second": 393168, "encryption_time_ms": 0.010173767805099487, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "AES-256-GCM", "timestamp": 1753702871.8738632, "execution_time_ms": 0.10636840015649796, "setup_time_ms": 0.020380131900310516, "cleanup_time_ms": 23.831721860915422, "total_time_ms": 23.95847039297223, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 8, "actual_blocks": 8, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 128, "expansion_ratio": 1.0, "throughput_mbps": 11.67, "bytes_per_second": 12238553, "blocks_per_second": 764909, "encryption_time_ms": 0.010458752512931824, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.0434124, "execution_time_ms": 0.10632043704390526, "setup_time_ms": 0.020467210561037064, "cleanup_time_ms": 23.401054088026285, "total_time_ms": 23.527841735631227, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 16, "actual_blocks": 16, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 256, "expansion_ratio": 1.0, "throughput_mbps": 22.9, "bytes_per_second": 24007852, "blocks_per_second": 1500490, "encryption_time_ms": 0.010663177818059921, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.210949, "execution_time_ms": 0.1056937500834465, "setup_time_ms": 0.020517967641353607, "cleanup_time_ms": 23.487484082579613, "total_time_ms": 23.613695800304413, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 32, "actual_blocks": 32, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 512, "expansion_ratio": 1.0, "throughput_mbps": 39.87, "bytes_per_second": 41806525, "blocks_per_second": 2612907, "encryption_time_ms": 0.012246891856193542, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.377868, "execution_time_ms": 0.11023422703146935, "setup_time_ms": 0.022800173610448837, "cleanup_time_ms": 27.82169869169593, "total_time_ms": 27.954733092337847, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 64, "actual_blocks": 64, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 1024, "expansion_ratio": 1.0, "throughput_mbps": 71.44, "bytes_per_second": 74913921, "blocks_per_second": 4682120, "encryption_time_ms": 0.013669021427631378, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.556327, "execution_time_ms": 0.10854033753275871, "setup_time_ms": 0.026404857635498047, "cleanup_time_ms": 24.453476071357727, "total_time_ms": 24.588421266525984, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 128, "actual_blocks": 128, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 2048, "expansion_ratio": 1.0, "throughput_mbps": 141.42, "bytes_per_second": 148287080, "blocks_per_second": 9267942, "encryption_time_ms": 0.013811048120260239, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0059051513671875, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.7272282, "execution_time_ms": 0.11337064206600189, "setup_time_ms": 0.028233975172042847, "cleanup_time_ms": 23.69192475453019, "total_time_ms": 23.833529371768236, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 256, "actual_blocks": 256, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 4096, "expansion_ratio": 1.0, "throughput_mbps": 227.88, "bytes_per_second": 238946349, "blocks_per_second": 14934146, "encryption_time_ms": 0.017141923308372498, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0117645263671875, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "AES-256-GCM", "timestamp": 1753702872.897186, "execution_time_ms": 0.12050699442625046, "setup_time_ms": 0.039628706872463226, "cleanup_time_ms": 24.34982405975461, "total_time_ms": 24.509959761053324, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 512, "actual_blocks": 512, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 8192, "expansion_ratio": 1.0, "throughput_mbps": 461.79, "bytes_per_second": 484219703, "blocks_per_second": 30263731, "encryption_time_ms": 0.016917940229177475, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=512]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0234832763671875, "efficiency_ratio": 0.0}, {"input_size": 16384, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.0707757, "execution_time_ms": 0.11835899204015732, "setup_time_ms": 0.05963677540421486, "cleanup_time_ms": 24.750257842242718, "total_time_ms": 24.92825360968709, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16384, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 1024, "actual_blocks": 1024, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 16384, "expansion_ratio": 1.0, "throughput_mbps": 709.65, "bytes_per_second": 744123090, "blocks_per_second": 46507693, "encryption_time_ms": 0.022017862647771835, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=1024]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0469207763671875, "efficiency_ratio": 0.0}, {"input_size": 32768, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.239921, "execution_time_ms": 0.130366999655962, "setup_time_ms": 0.09747128933668137, "cleanup_time_ms": 23.9250878803432, "total_time_ms": 24.152926169335842, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32768, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 2048, "actual_blocks": 2048, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 32768, "expansion_ratio": 1.0, "throughput_mbps": 1026.04, "bytes_per_second": 1075875977, "blocks_per_second": 67242248, "encryption_time_ms": 0.030457042157649994, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=2048]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0937957763671875, "efficiency_ratio": 0.0}, {"input_size": 65536, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.410024, "execution_time_ms": 0.15248935669660568, "setup_time_ms": 0.17349515110254288, "cleanup_time_ms": 23.762819822877645, "total_time_ms": 24.088804330676794, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 420.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 65536, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 4096, "actual_blocks": 4096, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 65536, "expansion_ratio": 1.0, "throughput_mbps": 1311.69, "bytes_per_second": 1375410347, "blocks_per_second": 85963146, "encryption_time_ms": 0.04764832556247711, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4096]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.1875457763671875, "efficiency_ratio": 0.0}, {"input_size": 131072, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.5788894, "execution_time_ms": 0.18729520961642265, "setup_time_ms": 0.3257701173424721, "cleanup_time_ms": 23.389064241200686, "total_time_ms": 23.90212956815958, "baseline_memory_mb": 420.9296875, "peak_memory_mb": 421.18359375, "memory_increment_mb": 0.25390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 131072, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 8192, "actual_blocks": 8192, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 131072, "expansion_ratio": 1.0, "throughput_mbps": 1509.97, "bytes_per_second": 1583321483, "blocks_per_second": 98957592, "encryption_time_ms": 0.08278293535113335, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8192]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.3750457763671875, "efficiency_ratio": 1.4771033653846153}, {"input_size": 262144, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.7461762, "execution_time_ms": 0.2595006488263607, "setup_time_ms": 0.6159213371574879, "cleanup_time_ms": 23.749976884573698, "total_time_ms": 24.625398870557547, "baseline_memory_mb": 421.18359375, "peak_memory_mb": 421.69921875, "memory_increment_mb": 0.515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 262144, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 16384, "actual_blocks": 16384, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 262144, "expansion_ratio": 1.0, "throughput_mbps": 1659.52, "bytes_per_second": 1740131536, "blocks_per_second": 108758221, "encryption_time_ms": 0.15064608305692673, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16384]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.7500457763671875, "efficiency_ratio": 1.4546342329545454}, {"input_size": 524288, "algorithm_name": "AES-256-GCM", "timestamp": 1753702873.9141536, "execution_time_ms": 0.4096358083188534, "setup_time_ms": 1.1892588809132576, "cleanup_time_ms": 23.888312745839357, "total_time_ms": 25.48720743507147, "baseline_memory_mb": 421.69921875, "peak_memory_mb": 422.73046875, "memory_increment_mb": 1.03125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 524288, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 32768, "actual_blocks": 32768, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 524288, "expansion_ratio": 1.0, "throughput_mbps": 1721.11, "bytes_per_second": 1804711431, "blocks_per_second": 112794464, "encryption_time_ms": 0.29051071032881737, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32768]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 1.5000457763671875, "efficiency_ratio": 1.45458984375}, {"input_size": 1048576, "algorithm_name": "AES-256-GCM", "timestamp": 1753702874.084818, "execution_time_ms": 0.7507570087909698, "setup_time_ms": 2.366230823099613, "cleanup_time_ms": 24.262723978608847, "total_time_ms": 27.37971181049943, "baseline_memory_mb": 422.73046875, "peak_memory_mb": 424.79296875, "memory_increment_mb": 2.0625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1048576, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 65536, "actual_blocks": 65536, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 1048576, "expansion_ratio": 1.0, "throughput_mbps": 1800.65, "bytes_per_second": 1888118064, "blocks_per_second": 118007379, "encryption_time_ms": 0.5553551018238068, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=65536]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 3.0000457763671875, "efficiency_ratio": 1.4545676491477273}, {"input_size": 2097152, "algorithm_name": "AES-256-GCM", "timestamp": 1753702874.2605047, "execution_time_ms": 1.669416856020689, "setup_time_ms": 5.327852908521891, "cleanup_time_ms": 23.948609363287687, "total_time_ms": 30.945879127830267, "baseline_memory_mb": 420.9921875, "peak_memory_mb": 428.66015625, "memory_increment_mb": 7.66796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2097152, "key_size_bits": 256, "encryption_mode": "GCM", "padding_algorithm": "PKCS7", "theoretical_blocks": 131072, "actual_blocks": 131072, "block_size_bytes": 16, "padding_overhead_bytes": 0, "padding_ratio": 0.0, "output_size_bytes": 2097152, "expansion_ratio": 1.0, "throughput_mbps": 1725.41, "bytes_per_second": 1809224582, "blocks_per_second": 113076536, "encryption_time_ms": 1.1591440998017788, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=131072]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 6.0000457763671875, "efficiency_ratio": 0.7824817721599593}]