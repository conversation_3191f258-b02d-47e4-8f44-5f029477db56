[{"input_size": 50, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656363.3547864, "execution_time_ms": 0.1750652678310871, "setup_time_ms": 0.8891741745173931, "cleanup_time_ms": 27.94275199994445, "total_time_ms": 29.00699144229293, "baseline_memory_mb": 462.1015625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.5848468010728282e-08, "relative_solution_error": 2.217264468232433e-09, "final_residual_norm": 2.8168113274194995e-05, "computed_residual_norm": 8.546321537575294e-07, "relative_residual": 2.2819346315292803e-09, "converged": false, "iterations_performed": 6, "max_iterations": 50, "iteration_efficiency": 0.12, "convergence_rate": 3.2890233255129937, "operations_count": 5304, "algorithm_type": "conjugate_gradient", "matrix_size": "50×50", "implementation": "custom_cg", "nnz": 292, "actual_sparsity_ratio": 0.1168, "target_sparsity_ratio": 0.05, "b_norm": 374.52087450234364, "solution_norm": 7.147757174570348, "true_solution_norm": 7.147757174570349, "theoretical_flops": 5304, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 374.52087450234364, "final_residual": 8.546321667806973e-07}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈125, k≤50]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈375 elements]", "theoretical_memory_mb": 0.00353240966796875, "efficiency_ratio": 0.0904296875}, {"input_size": 150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656363.539711, "execution_time_ms": 0.1979166641831398, "setup_time_ms": 1.2595150619745255, "cleanup_time_ms": 25.364906061440706, "total_time_ms": 26.82233778759837, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.6165010185845413e-09, "relative_solution_error": 1.4014976008212685e-10, "final_residual_norm": 1.1964510371971886e-05, "computed_residual_norm": 2.5593402720426394e-07, "relative_residual": 1.407844073294152e-10, "converged": false, "iterations_performed": 6, "max_iterations": 150, "iteration_efficiency": 0.04, "convergence_rate": 3.7689544020859636, "operations_count": 33264, "algorithm_type": "conjugate_gradient", "matrix_size": "150×150", "implementation": "custom_cg", "nnz": 2322, "actual_sparsity_ratio": 0.1032, "target_sparsity_ratio": 0.05, "b_norm": 1817.9145834340536, "solution_norm": 11.53409765123595, "true_solution_norm": 11.53409765123595, "theoretical_flops": 33264, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 1817.9145834340536, "final_residual": 2.5593400025256034e-07}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈1,125, k≤150]", "theoretical_space_complexity": "O(nnz+N) [N=150, memory≈1,875 elements]", "theoretical_memory_mb": 0.01917266845703125, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656363.7198567, "execution_time_ms": 0.23047151044011116, "setup_time_ms": 1.9850200042128563, "cleanup_time_ms": 25.70204809308052, "total_time_ms": 27.917539607733488, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.4285826071007695e-09, "relative_solution_error": 9.106778014797486e-11, "final_residual_norm": 1.7834974037301122e-05, "computed_residual_norm": 3.77697149037163e-07, "relative_residual": 9.156850933761581e-11, "converged": false, "iterations_performed": 6, "max_iterations": 250, "iteration_efficiency": 0.024, "convergence_rate": 3.8234319538065, "operations_count": 84816, "algorithm_type": "conjugate_gradient", "matrix_size": "250×250", "implementation": "custom_cg", "nnz": 6318, "actual_sparsity_ratio": 0.101088, "target_sparsity_ratio": 0.05, "b_norm": 4124.749346356425, "solution_norm": 15.687025694262932, "true_solution_norm": 15.687025694262934, "theoretical_flops": 84816, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 4124.749346356425, "final_residual": 3.776971417581406e-07}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈3,125, k≤250]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈4,375 elements]", "theoretical_memory_mb": 0.04625701904296875, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656363.901251, "execution_time_ms": 0.27480563148856163, "setup_time_ms": 3.0372971668839455, "cleanup_time_ms": 25.141671299934387, "total_time_ms": 28.453774098306894, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.557078446494938e-10, "relative_solution_error": 3.340971688718538e-11, "final_residual_norm": 1.4825938027422651e-05, "computed_residual_norm": 2.424717973650921e-07, "relative_residual": 3.360237077880264e-11, "converged": false, "iterations_performed": 6, "max_iterations": 350, "iteration_efficiency": 0.017142857142857144, "convergence_rate": 3.970362780639614, "operations_count": 159888, "algorithm_type": "conjugate_gradient", "matrix_size": "350×350", "implementation": "custom_cg", "nnz": 12274, "actual_sparsity_ratio": 0.10019591836734694, "target_sparsity_ratio": 0.05, "b_norm": 7215.913393767156, "solution_norm": 19.62626163111837, "true_solution_norm": 19.626261631118368, "theoretical_flops": 159888, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 7215.913393767156, "final_residual": 2.424718949188947e-07}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈6,125, k≤350]", "theoretical_space_complexity": "O(nnz+N) [N=350, memory≈7,875 elements]", "theoretical_memory_mb": 0.08478546142578125, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656364.0830305, "execution_time_ms": 0.355359073728323, "setup_time_ms": 4.964983090758324, "cleanup_time_ms": 24.912970140576363, "total_time_ms": 30.23331230506301, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.8217632863033924e-10, "relative_solution_error": 1.3394188070494368e-11, "final_residual_norm": 5.854519403279004e-06, "computed_residual_norm": 1.3511502142621184e-07, "relative_residual": 1.358890983713519e-11, "converged": false, "iterations_performed": 6, "max_iterations": 450, "iteration_efficiency": 0.013333333333333334, "convergence_rate": 4.184452224084712, "operations_count": 258672, "algorithm_type": "conjugate_gradient", "matrix_size": "450×450", "implementation": "custom_cg", "nnz": 20206, "actual_sparsity_ratio": 0.09978271604938271, "target_sparsity_ratio": 0.05, "b_norm": 9943.036126192794, "solution_norm": 21.067072311156842, "true_solution_norm": 21.067072311156846, "theoretical_flops": 258672, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 9943.036126192794, "final_residual": 1.3511545318734834e-07}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈10,125, k≤450]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈12,375 elements]", "theoretical_memory_mb": 0.13475799560546875, "efficiency_ratio": 0.0}, {"input_size": 550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656364.285243, "execution_time_ms": 0.4109845496714115, "setup_time_ms": 7.851270958781242, "cleanup_time_ms": 25.024950969964266, "total_time_ms": 33.28720647841692, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.7132119286176443e-10, "relative_solution_error": 1.2070753345242868e-11, "final_residual_norm": 1.2079111374358298e-05, "computed_residual_norm": 1.5765023597733654e-07, "relative_residual": 1.2131814099273262e-11, "converged": false, "iterations_performed": 6, "max_iterations": 550, "iteration_efficiency": 0.01090909090909091, "convergence_rate": 4.141965277222165, "operations_count": 379488, "algorithm_type": "conjugate_gradient", "matrix_size": "550×550", "implementation": "custom_cg", "nnz": 29974, "actual_sparsity_ratio": 0.09908760330578513, "target_sparsity_ratio": 0.05, "b_norm": 12994.778413788945, "solution_norm": 22.477569137695387, "true_solution_norm": 22.477569137695387, "theoretical_flops": 379488, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 12994.778413788945, "final_residual": 1.5764994711405762e-07}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈15,125, k≤550]", "theoretical_space_complexity": "O(nnz+N) [N=550, memory≈17,875 elements]", "theoretical_memory_mb": 0.19617462158203125, "efficiency_ratio": 0.0}, {"input_size": 650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656364.473875, "execution_time_ms": 0.4869019612669945, "setup_time_ms": 10.573584120720625, "cleanup_time_ms": 24.940032046288252, "total_time_ms": 36.00051812827587, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.5154685817604362e-10, "relative_solution_error": 6.1554145311338986e-12, "final_residual_norm": 9.007764089565139e-06, "computed_residual_norm": 1.0371587017312778e-07, "relative_residual": 6.170724261638923e-12, "converged": false, "iterations_performed": 6, "max_iterations": 650, "iteration_efficiency": 0.009230769230769232, "convergence_rate": 4.243650788233856, "operations_count": 525120, "algorithm_type": "conjugate_gradient", "matrix_size": "650×650", "implementation": "custom_cg", "nnz": 41810, "actual_sparsity_ratio": 0.09895857988165681, "target_sparsity_ratio": 0.05, "b_norm": 16807.730466566205, "solution_norm": 24.620089745300543, "true_solution_norm": 24.62008974530054, "theoretical_flops": 525120, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 16807.730466566205, "final_residual": 1.0371584061785233e-07}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈21,125, k≤650]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈24,375 elements]", "theoretical_memory_mb": 0.26903533935546875, "efficiency_ratio": 0.0}, {"input_size": 750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656364.6655538, "execution_time_ms": 0.5898258648812771, "setup_time_ms": 13.93473381176591, "cleanup_time_ms": 25.822171010077, "total_time_ms": 40.346730686724186, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.1131967018567765e-10, "relative_solution_error": 3.9851240208372146e-12, "final_residual_norm": 8.288433406550697e-06, "computed_residual_norm": 8.769764285716016e-08, "relative_residual": 3.98692958588867e-12, "converged": false, "iterations_performed": 6, "max_iterations": 750, "iteration_efficiency": 0.008, "convergence_rate": 4.307010494496871, "operations_count": 694488, "algorithm_type": "conjugate_gradient", "matrix_size": "750×750", "implementation": "custom_cg", "nnz": 55624, "actual_sparsity_ratio": 0.0988871111111111, "target_sparsity_ratio": 0.05, "b_norm": 21996.28585555085, "solution_norm": 27.933803215060557, "true_solution_norm": 27.93380321506056, "theoretical_flops": 694488, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 21996.28585555085, "final_residual": 8.769709553133421e-08}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈28,125, k≤750]", "theoretical_space_complexity": "O(nnz+N) [N=750, memory≈31,875 elements]", "theoretical_memory_mb": 0.35334014892578125, "efficiency_ratio": 0.0}, {"input_size": 850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656364.864448, "execution_time_ms": 0.7596718147397041, "setup_time_ms": 18.306797835975885, "cleanup_time_ms": 25.099398102611303, "total_time_ms": 44.16586775332689, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.377068357828527e-10, "relative_solution_error": 4.75704660423733e-12, "final_residual_norm": 1.0450446915307765e-05, "computed_residual_norm": 1.2303623678715674e-07, "relative_residual": 4.762965588452e-12, "converged": false, "iterations_performed": 6, "max_iterations": 850, "iteration_efficiency": 0.007058823529411765, "convergence_rate": 4.3055383699842515, "operations_count": 885432, "algorithm_type": "conjugate_gradient", "matrix_size": "850×850", "implementation": "custom_cg", "nnz": 71236, "actual_sparsity_ratio": 0.09859653979238754, "target_sparsity_ratio": 0.05, "b_norm": 25831.855070602025, "solution_norm": 28.947968611489028, "true_solution_norm": 28.94796861148903, "theoretical_flops": 885432, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 25831.855070602025, "final_residual": 1.2303581194979956e-07}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈36,125, k≤850]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈40,375 elements]", "theoretical_memory_mb": 0.44908905029296875, "efficiency_ratio": 0.0}, {"input_size": 950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656365.0702446, "execution_time_ms": 0.9068894200026989, "setup_time_ms": 22.59396528825164, "cleanup_time_ms": 27.825744822621346, "total_time_ms": 51.32659953087568, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 7.747911283687917e-11, "relative_solution_error": 2.4946904065785484e-12, "final_residual_norm": 7.510949955038091e-06, "computed_residual_norm": 7.745407975856966e-08, "relative_residual": 2.49842385214525e-12, "converged": false, "iterations_performed": 6, "max_iterations": 950, "iteration_efficiency": 0.00631578947368421, "convergence_rate": 4.391706990952165, "operations_count": 1102368, "algorithm_type": "conjugate_gradient", "matrix_size": "950×950", "implementation": "custom_cg", "nnz": 89014, "actual_sparsity_ratio": 0.09863047091412742, "target_sparsity_ratio": 0.05, "b_norm": 31001.176878800758, "solution_norm": 31.05760643988737, "true_solution_norm": 31.05760643988737, "theoretical_flops": 1102368, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 31001.176878800758, "final_residual": 7.745502323628907e-08}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈45,125, k≤950]", "theoretical_space_complexity": "O(nnz+N) [N=950, memory≈49,875 elements]", "theoretical_memory_mb": 0.5562820434570312, "efficiency_ratio": 0.0}, {"input_size": 1050, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656365.2846224, "execution_time_ms": 1.035382691770792, "setup_time_ms": 28.603693936020136, "cleanup_time_ms": 27.9380539432168, "total_time_ms": 57.57713057100773, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 9.582533390448464e-11, "relative_solution_error": 2.9012400220113083e-12, "final_residual_norm": 9.457530669094506e-06, "computed_residual_norm": 1.0617500141695368e-07, "relative_residual": 2.9137911817429353e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1050, "iteration_efficiency": 0.005714285714285714, "convergence_rate": 4.358247709515876, "operations_count": 1338144, "algorithm_type": "conjugate_gradient", "matrix_size": "1050×1050", "implementation": "custom_cg", "nnz": 108362, "actual_sparsity_ratio": 0.09828752834467121, "target_sparsity_ratio": 0.05, "b_norm": 36438.78191485336, "solution_norm": 33.02909555137495, "true_solution_norm": 33.029095551374944, "theoretical_flops": 1338144, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 36438.78191485336, "final_residual": 1.0617454916641621e-07}, "theoretical_time_complexity": "O(k*nnz) [N=1050, nnz≈55,125, k≤1050]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈60,375 elements]", "theoretical_memory_mb": 0.6749191284179688, "efficiency_ratio": 0.0}, {"input_size": 1150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656365.5279586, "execution_time_ms": 1.2536469846963882, "setup_time_ms": 35.21612798795104, "cleanup_time_ms": 25.28152521699667, "total_time_ms": 61.7513001896441, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.7680570615069586e-11, "relative_solution_error": 1.4048338936856597e-12, "final_residual_norm": 6.862371161797219e-06, "computed_residual_norm": 5.76676403072078e-08, "relative_residual": 1.4070500694827172e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1150, "iteration_efficiency": 0.0052173913043478265, "convergence_rate": 4.468975310472926, "operations_count": 1601352, "algorithm_type": "conjugate_gradient", "matrix_size": "1150×1150", "implementation": "custom_cg", "nnz": 129996, "actual_sparsity_ratio": 0.09829565217391305, "target_sparsity_ratio": 0.05, "b_norm": 40984.781961887486, "solution_norm": 33.94036179606754, "true_solution_norm": 33.94036179606755, "theoretical_flops": 1601352, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 40984.781961887486, "final_residual": 5.766757858749516e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1150, nnz≈66,125, k≤1150]", "theoretical_space_complexity": "O(nnz+N) [N=1150, memory≈71,875 elements]", "theoretical_memory_mb": 0.8050003051757812, "efficiency_ratio": 0.0}, {"input_size": 1250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656365.7629833, "execution_time_ms": 1.405700296163559, "setup_time_ms": 40.73038697242737, "cleanup_time_ms": 26.187175884842873, "total_time_ms": 68.3232631534338, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 5.857889971858932e-11, "relative_solution_error": 1.7121477417455473e-12, "final_residual_norm": 7.65477534072998e-06, "computed_residual_norm": 7.69698403736586e-08, "relative_residual": 1.7137104682129248e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1250, "iteration_efficiency": 0.0048, "convergence_rate": 4.446788224021642, "operations_count": 1886976, "algorithm_type": "conjugate_gradient", "matrix_size": "1250×1250", "implementation": "custom_cg", "nnz": 153498, "actual_sparsity_ratio": 0.09823872, "target_sparsity_ratio": 0.05, "b_norm": 44914.14495117344, "solution_norm": 34.213694467083606, "true_solution_norm": 34.213694467083606, "theoretical_flops": 1886976, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 44914.14495117344, "final_residual": 7.697080489460266e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1250, nnz≈78,125, k≤1250]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈84,375 elements]", "theoretical_memory_mb": 0.9465255737304688, "efficiency_ratio": 0.0}, {"input_size": 1350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656366.0060594, "execution_time_ms": 1.5952986665070057, "setup_time_ms": 47.867449931800365, "cleanup_time_ms": 26.739528868347406, "total_time_ms": 76.20227746665478, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.5411552481473483e-11, "relative_solution_error": 1.1958960143917674e-12, "final_residual_norm": 7.64704814322475e-06, "computed_residual_norm": 6.44499752845249e-08, "relative_residual": 1.1976285331595854e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1350, "iteration_efficiency": 0.0044444444444444444, "convergence_rate": 4.496862410479885, "operations_count": 2194008, "algorithm_type": "conjugate_gradient", "matrix_size": "1350×1350", "implementation": "custom_cg", "nnz": 178784, "actual_sparsity_ratio": 0.09809821673525378, "target_sparsity_ratio": 0.05, "b_norm": 53814.66247676388, "solution_norm": 37.97282701420307, "true_solution_norm": 37.97282701420306, "theoretical_flops": 2194008, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 53814.66247676388, "final_residual": 6.445108681042873e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1350, nnz≈91,125, k≤1350]", "theoretical_space_complexity": "O(nnz+N) [N=1350, memory≈97,875 elements]", "theoretical_memory_mb": 1.0994949340820312, "efficiency_ratio": 0.0}, {"input_size": 1450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656366.2587988, "execution_time_ms": 1.7929652705788612, "setup_time_ms": 53.57369082048535, "cleanup_time_ms": 28.039532247930765, "total_time_ms": 83.40618833899498, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 5.743885280901008e-11, "relative_solution_error": 1.4960005037325253e-12, "final_residual_norm": 9.653548794283846e-06, "computed_residual_norm": 8.759200791600439e-08, "relative_residual": 1.4982701851624734e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1450, "iteration_efficiency": 0.004137931034482759, "convergence_rate": 4.4557070946030946, "operations_count": 2526096, "algorithm_type": "conjugate_gradient", "matrix_size": "1450×1450", "implementation": "custom_cg", "nnz": 206158, "actual_sparsity_ratio": 0.09805374554102259, "target_sparsity_ratio": 0.05, "b_norm": 58462.09100563918, "solution_norm": 38.39494215790701, "true_solution_norm": 38.394942157907025, "theoretical_flops": 2526096, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 58462.09100563918, "final_residual": 8.759194783052049e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1450, nnz≈105,125, k≤1450]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈112,375 elements]", "theoretical_memory_mb": 1.2639083862304688, "efficiency_ratio": 0.0}, {"input_size": 1550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656366.5220714, "execution_time_ms": 2.062859106808901, "setup_time_ms": 66.65811687707901, "cleanup_time_ms": 26.178135070949793, "total_time_ms": 94.8991110548377, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 8.303297376722169e-11, "relative_solution_error": 2.094906837945514e-12, "final_residual_norm": 1.3743979338480073e-05, "computed_residual_norm": 1.3535535947581116e-07, "relative_residual": 2.0979256274015e-12, "converged": false, "iterations_performed": 6, "max_iterations": 1550, "iteration_efficiency": 0.003870967741935484, "convergence_rate": 4.397599941414353, "operations_count": 2883984, "algorithm_type": "conjugate_gradient", "matrix_size": "1550×1550", "implementation": "custom_cg", "nnz": 235682, "actual_sparsity_ratio": 0.09809864724245577, "target_sparsity_ratio": 0.05, "b_norm": 64518.66439300945, "solution_norm": 39.635640241001155, "true_solution_norm": 39.63564024100115, "theoretical_flops": 2883984, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 64518.66439300945, "final_residual": 1.353564445607336e-07}, "theoretical_time_complexity": "O(k*nnz) [N=1550, nnz≈120,125, k≤1550]", "theoretical_space_complexity": "O(nnz+N) [N=1550, memory≈127,875 elements]", "theoretical_memory_mb": 1.4397659301757812, "efficiency_ratio": 0.0}, {"input_size": 1650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656366.8120418, "execution_time_ms": 2.273887861520052, "setup_time_ms": 72.29040283709764, "cleanup_time_ms": 26.30086801946163, "total_time_ms": 100.86515871807933, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.0038347901062184e-11, "relative_solution_error": 7.442517419905016e-13, "final_residual_norm": 6.289190406189688e-06, "computed_residual_norm": 5.2116037658247697e-08, "relative_residual": 7.450406762845305e-13, "converged": false, "iterations_performed": 6, "max_iterations": 1650, "iteration_efficiency": 0.0036363636363636364, "convergence_rate": 4.584574053534416, "operations_count": 3262800, "algorithm_type": "conjugate_gradient", "matrix_size": "1650×1650", "implementation": "custom_cg", "nnz": 266950, "actual_sparsity_ratio": 0.09805325987144169, "target_sparsity_ratio": 0.05, "b_norm": 69950.59372885115, "solution_norm": 40.36046703864556, "true_solution_norm": 40.36046703864557, "theoretical_flops": 3262800, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 69950.59372885115, "final_residual": 5.211504535224582e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1650, nnz≈136,125, k≤1650]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈144,375 elements]", "theoretical_memory_mb": 1.6270675659179688, "efficiency_ratio": 0.0}, {"input_size": 1750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656367.095796, "execution_time_ms": 2.7222564443945885, "setup_time_ms": 82.16177625581622, "cleanup_time_ms": 26.393359061330557, "total_time_ms": 111.27739176154137, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.867566885679558e-11, "relative_solution_error": 6.932927820011644e-13, "final_residual_norm": 6.5545993324484205e-06, "computed_residual_norm": 5.2816285192704994e-08, "relative_residual": 6.949534015567753e-13, "converged": false, "iterations_performed": 6, "max_iterations": 1750, "iteration_efficiency": 0.0034285714285714284, "convergence_rate": 4.5857010230088, "operations_count": 3665064, "algorithm_type": "conjugate_gradient", "matrix_size": "1750×1750", "implementation": "custom_cg", "nnz": 300172, "actual_sparsity_ratio": 0.0980153469387755, "target_sparsity_ratio": 0.05, "b_norm": 75999.75059391098, "solution_norm": 41.3615569082146, "true_solution_norm": 41.3615569082146, "theoretical_flops": 3665064, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 75999.75059391098, "final_residual": 5.281642176448717e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1750, nnz≈153,125, k≤1750]", "theoretical_space_complexity": "O(nnz+N) [N=1750, memory≈161,875 elements]", "theoretical_memory_mb": 1.8258132934570312, "efficiency_ratio": 0.0}, {"input_size": 1850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656367.4101276, "execution_time_ms": 2.73844413459301, "setup_time_ms": 99.45382410660386, "cleanup_time_ms": 26.229839771986008, "total_time_ms": 128.42210801318288, "baseline_memory_mb": 462.140625, "peak_memory_mb": 462.41796875, "memory_increment_mb": 0.27734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.4523845358219023e-11, "relative_solution_error": 5.697499954041507e-13, "final_residual_norm": 6.683456398229311e-06, "computed_residual_norm": 4.7670643181651714e-08, "relative_residual": 5.699553560085744e-13, "converged": false, "iterations_performed": 6, "max_iterations": 1850, "iteration_efficiency": 0.003243243243243243, "convergence_rate": 4.614983622570047, "operations_count": 4092480, "algorithm_type": "conjugate_gradient", "matrix_size": "1850×1850", "implementation": "custom_cg", "nnz": 335490, "actual_sparsity_ratio": 0.09802483564645727, "target_sparsity_ratio": 0.05, "b_norm": 83639.25819645172, "solution_norm": 43.04316903209995, "true_solution_norm": 43.04316903209995, "theoretical_flops": 4092480, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 83639.25819645172, "final_residual": 4.7670434334498905e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1850, nnz≈171,125, k≤1850]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈180,375 elements]", "theoretical_memory_mb": 2.0360031127929688, "efficiency_ratio": 7.341081646126761}, {"input_size": 1950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656367.729058, "execution_time_ms": 3.0803132802248, "setup_time_ms": 103.84735884144902, "cleanup_time_ms": 26.946659665554762, "total_time_ms": 133.87433178722858, "baseline_memory_mb": 462.41796875, "peak_memory_mb": 465.73828125, "memory_increment_mb": 3.3203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.7112493703364866e-11, "relative_solution_error": 6.195672330710893e-13, "final_residual_norm": 7.107614414547398e-06, "computed_residual_norm": 5.5604775459004625e-08, "relative_residual": 6.206357040218558e-13, "converged": false, "iterations_performed": 6, "max_iterations": 1950, "iteration_efficiency": 0.003076923076923077, "convergence_rate": 4.62648369194482, "operations_count": 4541136, "algorithm_type": "conjugate_gradient", "matrix_size": "1950×1950", "implementation": "custom_cg", "nnz": 372578, "actual_sparsity_ratio": 0.09798238001314924, "target_sparsity_ratio": 0.05, "b_norm": 89593.25913522772, "solution_norm": 43.76037378376653, "true_solution_norm": 43.76037378376653, "theoretical_flops": 4541136, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 7, "initial_residual": 89593.25913522772, "final_residual": 5.560724331396989e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1950, nnz≈190,125, k≤1950]", "theoretical_space_complexity": "O(nnz+N) [N=1950, memory≈199,875 elements]", "theoretical_memory_mb": 2.2576370239257812, "efficiency_ratio": 0.6799471507352941}]