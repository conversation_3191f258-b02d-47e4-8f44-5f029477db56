[{"input_size": 100, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.6271389, "execution_time_ms": 0.20537525415420532, "setup_time_ms": 0.023691914975643158, "cleanup_time_ms": 2.8218762017786503, "total_time_ms": 3.0509433709084988, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 100, "actual_comparisons": 421, "theoretical_comparisons": 664, "comparison_efficiency": 1.578, "array_accesses": 1765, "max_recursion_depth": 7, "theoretical_depth": 7, "correctness_verified": true, "input_inversions": 341, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=100]", "theoretical_space_complexity": "O(n) [n=100]", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.6536536, "execution_time_ms": 0.4069245420396328, "setup_time_ms": 0.023950356990098953, "cleanup_time_ms": 2.833220176398754, "total_time_ms": 3.264095075428486, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 200, "actual_comparisons": 993, "theoretical_comparisons": 1528, "comparison_efficiency": 1.54, "array_accesses": 4081, "max_recursion_depth": 8, "theoretical_depth": 8, "correctness_verified": true, "input_inversions": 1382, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=200]", "theoretical_space_complexity": "O(n) [n=200]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.6774435, "execution_time_ms": 0.6313960999250412, "setup_time_ms": 0.02973293885588646, "cleanup_time_ms": 2.8566070832312107, "total_time_ms": 3.5177361220121384, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 300, "actual_comparisons": 1637, "theoretical_comparisons": 2468, "comparison_efficiency": 1.508, "array_accesses": 6613, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 2557, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=300]", "theoretical_space_complexity": "O(n) [n=300]", "theoretical_memory_mb": 0.00457763671875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.703516, "execution_time_ms": 0.863590557128191, "setup_time_ms": 0.03441516309976578, "cleanup_time_ms": 2.7839262038469315, "total_time_ms": 3.6819319240748882, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 400, "actual_comparisons": 2465, "theoretical_comparisons": 3457, "comparison_efficiency": 1.403, "array_accesses": 9441, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 5524, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=400]", "theoretical_space_complexity": "O(n) [n=400]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.733188, "execution_time_ms": 1.0914691723883152, "setup_time_ms": 0.038430094718933105, "cleanup_time_ms": 2.872686367481947, "total_time_ms": 4.002585634589195, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 500, "actual_comparisons": 3156, "theoretical_comparisons": 4482, "comparison_efficiency": 1.42, "array_accesses": 12132, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 7207, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=500]", "theoretical_space_complexity": "O(n) [n=500]", "theoretical_memory_mb": 0.00762939453125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.766345, "execution_time_ms": 1.3681582175195217, "setup_time_ms": 0.04509417340159416, "cleanup_time_ms": 2.895000856369734, "total_time_ms": 4.30825324729085, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 600, "actual_comparisons": 3864, "theoretical_comparisons": 5537, "comparison_efficiency": 1.433, "array_accesses": 15016, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 9806, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=600]", "theoretical_space_complexity": "O(n) [n=600]", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.8030875, "execution_time_ms": 1.604709681123495, "setup_time_ms": 0.0541098415851593, "cleanup_time_ms": 2.813806291669607, "total_time_ms": 4.472625814378262, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 700, "actual_comparisons": 4747, "theoretical_comparisons": 6615, "comparison_efficiency": 1.394, "array_accesses": 18099, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 14933, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=700]", "theoretical_space_complexity": "O(n) [n=700]", "theoretical_memory_mb": 0.01068115234375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.8439307, "execution_time_ms": 1.8725406378507614, "setup_time_ms": 0.06822915747761726, "cleanup_time_ms": 2.8120209462940693, "total_time_ms": 4.752790741622448, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 800, "actual_comparisons": 5702, "theoretical_comparisons": 7715, "comparison_efficiency": 1.353, "array_accesses": 21254, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 22738, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=800]", "theoretical_space_complexity": "O(n) [n=800]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.889589, "execution_time_ms": 2.1051124669611454, "setup_time_ms": 0.0590016134083271, "cleanup_time_ms": 2.8836559504270554, "total_time_ms": 5.047770030796528, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 900, "actual_comparisons": 6505, "theoretical_comparisons": 8832, "comparison_efficiency": 1.358, "array_accesses": 24257, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 28369, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=900]", "theoretical_space_complexity": "O(n) [n=900]", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.9398992, "execution_time_ms": 2.516056317836046, "setup_time_ms": 0.06204284727573395, "cleanup_time_ms": 2.764460165053606, "total_time_ms": 5.342559330165386, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1000, "actual_comparisons": 7440, "theoretical_comparisons": 9965, "comparison_efficiency": 1.339, "array_accesses": 27392, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 32444, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1000]", "theoretical_space_complexity": "O(n) [n=1000]", "theoretical_memory_mb": 0.0152587890625, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653401.995616, "execution_time_ms": 2.638310194015503, "setup_time_ms": 0.07357634603977203, "cleanup_time_ms": 2.9241428710520267, "total_time_ms": 5.636029411107302, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1100, "actual_comparisons": 8109, "theoretical_comparisons": 11113, "comparison_efficiency": 1.371, "array_accesses": 30413, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 33637, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1100]", "theoretical_space_complexity": "O(n) [n=1100]", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1200, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.056146, "execution_time_ms": 2.8937804512679577, "setup_time_ms": 0.07621897384524345, "cleanup_time_ms": 2.846740186214447, "total_time_ms": 5.816739611327648, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1200, "actual_comparisons": 9204, "theoretical_comparisons": 12274, "comparison_efficiency": 1.334, "array_accesses": 33908, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 39430, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1200]", "theoretical_space_complexity": "O(n) [n=1200]", "theoretical_memory_mb": 0.018310546875, "efficiency_ratio": 0.0}, {"input_size": 1300, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.1227157, "execution_time_ms": 3.186732530593872, "setup_time_ms": 0.08064508438110352, "cleanup_time_ms": 2.772171050310135, "total_time_ms": 6.0395486652851105, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1300, "actual_comparisons": 10215, "theoretical_comparisons": 13447, "comparison_efficiency": 1.316, "array_accesses": 37319, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 46231, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1300]", "theoretical_space_complexity": "O(n) [n=1300]", "theoretical_memory_mb": 0.01983642578125, "efficiency_ratio": 0.0}, {"input_size": 1400, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.1950414, "execution_time_ms": 3.5040514543652534, "setup_time_ms": 0.08836900815367699, "cleanup_time_ms": 2.7739591896533966, "total_time_ms": 6.366379652172327, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1400, "actual_comparisons": 11137, "theoretical_comparisons": 14631, "comparison_efficiency": 1.314, "array_accesses": 40641, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 57060, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1400]", "theoretical_space_complexity": "O(n) [n=1400]", "theoretical_memory_mb": 0.0213623046875, "efficiency_ratio": 0.0}, {"input_size": 1500, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.2743778, "execution_time_ms": 3.7305017933249474, "setup_time_ms": 0.08988892659544945, "cleanup_time_ms": 2.8051859699189663, "total_time_ms": 6.625576689839363, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1500, "actual_comparisons": 12145, "theoretical_comparisons": 15826, "comparison_efficiency": 1.303, "array_accesses": 44049, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 64855, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1500]", "theoretical_space_complexity": "O(n) [n=1500]", "theoretical_memory_mb": 0.02288818359375, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.3604107, "execution_time_ms": 4.055200982838869, "setup_time_ms": 0.09252689778804779, "cleanup_time_ms": 2.773409243673086, "total_time_ms": 6.921137124300003, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1600, "actual_comparisons": 12867, "theoretical_comparisons": 17030, "comparison_efficiency": 1.324, "array_accesses": 47171, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 86626, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1600]", "theoretical_space_complexity": "O(n) [n=1600]", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 1700, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.4542491, "execution_time_ms": 4.21576714143157, "setup_time_ms": 0.09900610893964767, "cleanup_time_ms": 2.8812773525714874, "total_time_ms": 7.196050602942705, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1700, "actual_comparisons": 14067, "theoretical_comparisons": 18243, "comparison_efficiency": 1.297, "array_accesses": 50771, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 99317, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1700]", "theoretical_space_complexity": "O(n) [n=1700]", "theoretical_memory_mb": 0.02593994140625, "efficiency_ratio": 0.0}, {"input_size": 1800, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.554555, "execution_time_ms": 4.466938879340887, "setup_time_ms": 0.10093627497553825, "cleanup_time_ms": 2.7818912640213966, "total_time_ms": 7.349766418337822, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1800, "actual_comparisons": 14998, "theoretical_comparisons": 19464, "comparison_efficiency": 1.298, "array_accesses": 54102, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 102530, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1800]", "theoretical_space_complexity": "O(n) [n=1800]", "theoretical_memory_mb": 0.0274658203125, "efficiency_ratio": 0.0}, {"input_size": 1900, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.6620214, "execution_time_ms": 4.762660339474678, "setup_time_ms": 0.11349422857165337, "cleanup_time_ms": 2.9319780878722668, "total_time_ms": 7.808132655918598, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 1900, "actual_comparisons": 15961, "theoretical_comparisons": 20694, "comparison_efficiency": 1.297, "array_accesses": 57465, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 117519, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1900]", "theoretical_space_complexity": "O(n) [n=1900]", "theoretical_memory_mb": 0.02899169921875, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "MergeSort-nearly_sorted", "timestamp": 1753653402.7798226, "execution_time_ms": 5.012973491102457, "setup_time_ms": 0.10973773896694183, "cleanup_time_ms": 2.7833981439471245, "total_time_ms": 7.906109374016523, "baseline_memory_mb": 57.3203125, "peak_memory_mb": 57.3203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "nearly_sorted", "input_size": 2000, "actual_comparisons": 17337, "theoretical_comparisons": 21931, "comparison_efficiency": 1.265, "array_accesses": 61241, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 125900, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=2000]", "theoretical_space_complexity": "O(n) [n=2000]", "theoretical_memory_mb": 0.030517578125, "efficiency_ratio": 0.0}]