input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_input_size,custom_actual_comparisons,custom_theoretical_comparisons,custom_comparison_efficiency,custom_array_accesses,custom_max_recursion_depth,custom_theoretical_depth,custom_correctness_verified,custom_input_inversions,custom_was_already_sorted,custom_algorithm_type
100,MergeSort-nearly_sorted,1753653401.6271389,0.2054,0.0237,2.8219,3.0509,57.32,57.32,0.00,0.00,,,,O(n log n) [n=100],O(n) [n=100],0.00,0.0000,nearly_sorted,100,421,664,1.578,1765,7,7,True,341,False,divide_and_conquer
200,MergeSort-nearly_sorted,1753653401.6536536,0.4069,0.0240,2.8332,3.2641,57.32,57.32,0.00,0.00,,,,O(n log n) [n=200],O(n) [n=200],0.00,0.0000,nearly_sorted,200,993,1528,1.54,4081,8,8,True,1382,False,divide_and_conquer
300,MergeSort-nearly_sorted,1753653401.6774435,0.6314,0.0297,2.8566,3.5177,57.32,57.32,0.00,0.00,,,,O(n log n) [n=300],O(n) [n=300],0.00,0.0000,nearly_sorted,300,1637,2468,1.508,6613,9,9,True,2557,False,divide_and_conquer
400,MergeSort-nearly_sorted,1753653401.703516,0.8636,0.0344,2.7839,3.6819,57.32,57.32,0.00,0.00,,,,O(n log n) [n=400],O(n) [n=400],0.01,0.0000,nearly_sorted,400,2465,3457,1.403,9441,9,9,True,5524,False,divide_and_conquer
500,MergeSort-nearly_sorted,1753653401.733188,1.0915,0.0384,2.8727,4.0026,57.32,57.32,0.00,0.00,,,,O(n log n) [n=500],O(n) [n=500],0.01,0.0000,nearly_sorted,500,3156,4482,1.42,12132,9,9,True,7207,False,divide_and_conquer
600,MergeSort-nearly_sorted,1753653401.766345,1.3682,0.0451,2.8950,4.3083,57.32,57.32,0.00,0.00,,,,O(n log n) [n=600],O(n) [n=600],0.01,0.0000,nearly_sorted,600,3864,5537,1.433,15016,10,10,True,9806,False,divide_and_conquer
700,MergeSort-nearly_sorted,1753653401.8030875,1.6047,0.0541,2.8138,4.4726,57.32,57.32,0.00,0.00,,,,O(n log n) [n=700],O(n) [n=700],0.01,0.0000,nearly_sorted,700,4747,6615,1.394,18099,10,10,True,14933,False,divide_and_conquer
800,MergeSort-nearly_sorted,1753653401.8439307,1.8725,0.0682,2.8120,4.7528,57.32,57.32,0.00,0.00,,,,O(n log n) [n=800],O(n) [n=800],0.01,0.0000,nearly_sorted,800,5702,7715,1.353,21254,10,10,True,22738,False,divide_and_conquer
900,MergeSort-nearly_sorted,1753653401.889589,2.1051,0.0590,2.8837,5.0478,57.32,57.32,0.00,0.00,,,,O(n log n) [n=900],O(n) [n=900],0.01,0.0000,nearly_sorted,900,6505,8832,1.358,24257,10,10,True,28369,False,divide_and_conquer
1000,MergeSort-nearly_sorted,1753653401.9398992,2.5161,0.0620,2.7645,5.3426,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1000],O(n) [n=1000],0.02,0.0000,nearly_sorted,1000,7440,9965,1.339,27392,10,10,True,32444,False,divide_and_conquer
1100,MergeSort-nearly_sorted,1753653401.995616,2.6383,0.0736,2.9241,5.6360,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1100],O(n) [n=1100],0.02,0.0000,nearly_sorted,1100,8109,11113,1.371,30413,11,11,True,33637,False,divide_and_conquer
1200,MergeSort-nearly_sorted,1753653402.056146,2.8938,0.0762,2.8467,5.8167,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1200],O(n) [n=1200],0.02,0.0000,nearly_sorted,1200,9204,12274,1.334,33908,11,11,True,39430,False,divide_and_conquer
1300,MergeSort-nearly_sorted,1753653402.1227157,3.1867,0.0806,2.7722,6.0395,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1300],O(n) [n=1300],0.02,0.0000,nearly_sorted,1300,10215,13447,1.316,37319,11,11,True,46231,False,divide_and_conquer
1400,MergeSort-nearly_sorted,1753653402.1950414,3.5041,0.0884,2.7740,6.3664,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1400],O(n) [n=1400],0.02,0.0000,nearly_sorted,1400,11137,14631,1.314,40641,11,11,True,57060,False,divide_and_conquer
1500,MergeSort-nearly_sorted,1753653402.2743778,3.7305,0.0899,2.8052,6.6256,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1500],O(n) [n=1500],0.02,0.0000,nearly_sorted,1500,12145,15826,1.303,44049,11,11,True,64855,False,divide_and_conquer
1600,MergeSort-nearly_sorted,1753653402.3604107,4.0552,0.0925,2.7734,6.9211,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1600],O(n) [n=1600],0.02,0.0000,nearly_sorted,1600,12867,17030,1.324,47171,11,11,True,86626,False,divide_and_conquer
1700,MergeSort-nearly_sorted,1753653402.4542491,4.2158,0.0990,2.8813,7.1961,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1700],O(n) [n=1700],0.03,0.0000,nearly_sorted,1700,14067,18243,1.297,50771,11,11,True,99317,False,divide_and_conquer
1800,MergeSort-nearly_sorted,1753653402.554555,4.4669,0.1009,2.7819,7.3498,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1800],O(n) [n=1800],0.03,0.0000,nearly_sorted,1800,14998,19464,1.298,54102,11,11,True,102530,False,divide_and_conquer
1900,MergeSort-nearly_sorted,1753653402.6620214,4.7627,0.1135,2.9320,7.8081,57.32,57.32,0.00,0.00,,,,O(n log n) [n=1900],O(n) [n=1900],0.03,0.0000,nearly_sorted,1900,15961,20694,1.297,57465,11,11,True,117519,False,divide_and_conquer
2000,MergeSort-nearly_sorted,1753653402.7798226,5.0130,0.1097,2.7834,7.9061,57.32,57.32,0.00,0.00,,,,O(n log n) [n=2000],O(n) [n=2000],0.03,0.0000,nearly_sorted,2000,17337,21931,1.265,61241,11,11,True,125900,False,divide_and_conquer
