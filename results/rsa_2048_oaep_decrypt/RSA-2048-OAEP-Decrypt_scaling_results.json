[{"input_size": 16, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705351.7243576, "execution_time_ms": 0.9021180681884289, "setup_time_ms": 4.828550852835178, "cleanup_time_ms": 43.60583098605275, "total_time_ms": 49.33649990707636, "baseline_memory_mb": 416.94921875, "peak_memory_mb": 421.05078125, "memory_increment_mb": 4.1015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 16, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 16.0, "throughput_mbps": 0.01, "bytes_per_second": 15180, "chunks_per_second": 948, "bits_per_second": 121446, "decryption_rate_per_second": 948.8, "decryption_time_ms": 1.0539619252085686, "chunk_processing_time_ms": 1.0539619252085686, "key_utilization": 0.084, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007476806640625, "efficiency_ratio": 0.00018229166666666667}, {"input_size": 32, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705352.0474308, "execution_time_ms": 0.6184468977153301, "setup_time_ms": 0.15912437811493874, "cleanup_time_ms": 39.014528039842844, "total_time_ms": 39.79209931567311, "baseline_memory_mb": 421.05078125, "peak_memory_mb": 421.05078125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 32, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 8.0, "throughput_mbps": 0.05, "bytes_per_second": 52359, "chunks_per_second": 1636, "bits_per_second": 418874, "decryption_rate_per_second": 1636.23, "decryption_time_ms": 0.6111622788012028, "chunk_processing_time_ms": 0.6111622788012028, "key_utilization": 0.168, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705352.3233352, "execution_time_ms": 0.6778961978852749, "setup_time_ms": 0.12709107249975204, "cleanup_time_ms": 37.32140827924013, "total_time_ms": 38.12639554962516, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 64, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 4.0, "throughput_mbps": 0.09, "bytes_per_second": 97454, "chunks_per_second": 1522, "bits_per_second": 779638, "decryption_rate_per_second": 1522.73, "decryption_time_ms": 0.6567141972482204, "chunk_processing_time_ms": 0.6567141972482204, "key_utilization": 0.337, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.00079345703125, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705352.591177, "execution_time_ms": 0.6188362836837769, "setup_time_ms": 0.11974712833762169, "cleanup_time_ms": 35.57316632941365, "total_time_ms": 36.31174974143505, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 128, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 2.0, "throughput_mbps": 0.2, "bytes_per_second": 214416, "chunks_per_second": 1675, "bits_per_second": 1715332, "decryption_rate_per_second": 1675.13, "decryption_time_ms": 0.5969689227640629, "chunk_processing_time_ms": 0.5969689227640629, "key_utilization": 0.674, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008544921875, "efficiency_ratio": 0.0}, {"input_size": 47, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705352.8518195, "execution_time_ms": 0.6482888013124466, "setup_time_ms": 0.1264740712940693, "cleanup_time_ms": 36.57591389492154, "total_time_ms": 37.35067676752806, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 47, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 47, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 5.45, "throughput_mbps": 0.08, "bytes_per_second": 78806, "chunks_per_second": 1676, "bits_per_second": 630454, "decryption_rate_per_second": 1676.74, "decryption_time_ms": 0.5963947623968124, "chunk_processing_time_ms": 0.5963947623968124, "key_utilization": 0.247, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007772445678710938, "efficiency_ratio": 0.0}, {"input_size": 95, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705353.136617, "execution_time_ms": 0.6775292567908764, "setup_time_ms": 0.11966703459620476, "cleanup_time_ms": 39.14113529026508, "total_time_ms": 39.938331581652164, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 95, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 95, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 2.69, "throughput_mbps": 0.15, "bytes_per_second": 152127, "chunks_per_second": 1601, "bits_per_second": 1217022, "decryption_rate_per_second": 1601.34, "decryption_time_ms": 0.6244750693440437, "chunk_processing_time_ms": 0.6244750693440437, "key_utilization": 0.5, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008230209350585938, "efficiency_ratio": 0.0}, {"input_size": 180, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705353.4108145, "execution_time_ms": 0.6228153593838215, "setup_time_ms": 0.12397393584251404, "cleanup_time_ms": 36.315497010946274, "total_time_ms": 37.06228630617261, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 180, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 180, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 1.42, "throughput_mbps": 0.29, "bytes_per_second": 301387, "chunks_per_second": 1674, "bits_per_second": 2411100, "decryption_rate_per_second": 1674.38, "decryption_time_ms": 0.5972376093268394, "chunk_processing_time_ms": 0.5972376093268394, "key_utilization": 0.947, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.000904083251953125, "efficiency_ratio": 0.0}, {"input_size": 190, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705353.6717389, "execution_time_ms": 0.6256538443267345, "setup_time_ms": 0.12363633140921593, "cleanup_time_ms": 35.24696594104171, "total_time_ms": 35.99625611677766, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 190, "ciphertext_size_bytes": 256, "plaintext_size_bytes": 190, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 1, "actual_chunks": 1, "compression_ratio": 1.35, "throughput_mbps": 0.3, "bytes_per_second": 315309, "chunks_per_second": 1659, "bits_per_second": 2522478, "decryption_rate_per_second": 1659.53, "decryption_time_ms": 0.6025820039212704, "chunk_processing_time_ms": 0.6025820039212704, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0009136199951171875, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705353.9301283, "execution_time_ms": 1.138218678534031, "setup_time_ms": 0.14862418174743652, "cleanup_time_ms": 22.971372585743666, "total_time_ms": 24.258215446025133, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 200, "ciphertext_size_bytes": 512, "plaintext_size_bytes": 200, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 2, "actual_chunks": 2, "compression_ratio": 2.56, "throughput_mbps": 0.17, "bytes_per_second": 180215, "chunks_per_second": 1802, "bits_per_second": 1441721, "decryption_rate_per_second": 901.08, "decryption_time_ms": 1.1097840033471584, "chunk_processing_time_ms": 0.5548920016735792, "key_utilization": 0.526, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.00116729736328125, "efficiency_ratio": 0.0}, {"input_size": 380, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705354.1875618, "execution_time_ms": 1.1448283679783344, "setup_time_ms": 0.1997901126742363, "cleanup_time_ms": 23.973451927304268, "total_time_ms": 25.31807040795684, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 380, "ciphertext_size_bytes": 512, "plaintext_size_bytes": 380, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 2, "actual_chunks": 2, "compression_ratio": 1.35, "throughput_mbps": 0.33, "bytes_per_second": 340877, "chunks_per_second": 1794, "bits_per_second": 2727022, "decryption_rate_per_second": 897.05, "decryption_time_ms": 1.1147689074277878, "chunk_processing_time_ms": 0.5573844537138939, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.001338958740234375, "efficiency_ratio": 0.0}, {"input_size": 570, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705354.361637, "execution_time_ms": 1.6881052404642105, "setup_time_ms": 0.16307085752487183, "cleanup_time_ms": 23.34207436069846, "total_time_ms": 25.193250458687544, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 570, "ciphertext_size_bytes": 768, "plaintext_size_bytes": 570, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 3, "actual_chunks": 3, "compression_ratio": 1.35, "throughput_mbps": 0.33, "bytes_per_second": 349253, "chunks_per_second": 1838, "bits_per_second": 2794027, "decryption_rate_per_second": 612.73, "decryption_time_ms": 1.6320529393851757, "chunk_processing_time_ms": 0.5440176464617252, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=3, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0017642974853515625, "efficiency_ratio": 0.0}, {"input_size": 760, "algorithm_name": "RSA-2048-OAEP-Decrypt", "timestamp": 1753705354.5384157, "execution_time_ms": 2.2594358772039413, "setup_time_ms": 0.1873597502708435, "cleanup_time_ms": 23.832819890230894, "total_time_ms": 26.27961551770568, "baseline_memory_mb": 421.08984375, "peak_memory_mb": 421.08984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 760, "ciphertext_size_bytes": 1024, "plaintext_size_bytes": 760, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "OAEP", "max_chunk_size_bytes": 190, "theoretical_chunks": 4, "actual_chunks": 4, "compression_ratio": 1.35, "throughput_mbps": 0.34, "bytes_per_second": 352666, "chunks_per_second": 1856, "bits_per_second": 2821330, "decryption_rate_per_second": 464.03, "decryption_time_ms": 2.155011985450983, "chunk_processing_time_ms": 0.5387529963627458, "key_utilization": 1.0, "correctness_verified": true, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "decryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=4, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.00218963623046875, "efficiency_ratio": 0.0}]