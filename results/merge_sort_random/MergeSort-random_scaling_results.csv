input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_data_type,custom_input_size,custom_actual_comparisons,custom_theoretical_comparisons,custom_comparison_efficiency,custom_array_accesses,custom_max_recursion_depth,custom_theoretical_depth,custom_correctness_verified,custom_input_inversions,custom_was_already_sorted,custom_algorithm_type
100,MergeSort-random,1753653396.9421804,0.2295,0.0616,2.8090,3.1001,56.60,56.60,0.00,0.00,,,,O(n log n) [n=100],O(n) [n=100],0.00,0.0000,random,100,549,664,1.21,1893,7,7,True,2298,False,divide_and_conquer
200,MergeSort-random,1753653396.9652252,0.4571,0.0923,2.7855,3.3349,56.60,56.60,0.00,0.00,,,,O(n log n) [n=200],O(n) [n=200],0.00,0.0000,random,200,1295,1528,1.181,4383,8,8,True,9289,False,divide_and_conquer
300,MergeSort-random,1753653396.9897525,0.7010,0.1287,2.7535,3.5832,56.60,56.60,0.00,0.00,,,,O(n log n) [n=300],O(n) [n=300],0.00,0.0000,random,300,2093,2468,1.179,7069,9,9,True,22335,False,divide_and_conquer
400,MergeSort-random,1753653397.017051,0.9434,0.1642,2.9131,4.0207,56.60,56.60,0.00,0.00,,,,O(n log n) [n=400],O(n) [n=400],0.01,0.0000,random,400,2974,3457,1.163,9950,9,9,True,39272,False,divide_and_conquer
500,MergeSort-random,1753653397.0478323,1.1962,0.2294,2.7609,4.1864,56.60,56.60,0.00,0.00,,,,O(n log n) [n=500],O(n) [n=500],0.01,0.0000,random,500,3853,4482,1.163,12829,9,9,True,61163,False,divide_and_conquer
600,MergeSort-random,1753653397.0831141,1.4500,0.2493,2.7537,4.4530,56.60,56.60,0.00,0.00,,,,O(n log n) [n=600],O(n) [n=600],0.01,0.0000,random,600,4792,5537,1.156,15944,10,10,True,87892,False,divide_and_conquer
700,MergeSort-random,1753653397.1226604,1.6914,0.2796,2.8344,4.8054,56.60,56.60,0.00,0.00,,,,O(n log n) [n=700],O(n) [n=700],0.01,0.0000,random,700,5750,6615,1.151,19102,10,10,True,119724,False,divide_and_conquer
800,MergeSort-random,1753653397.1670442,1.9721,0.3092,2.7677,5.0490,56.60,56.60,0.00,0.00,,,,O(n log n) [n=800],O(n) [n=800],0.01,0.0000,random,800,6725,7715,1.147,22277,10,10,True,153806,False,divide_and_conquer
900,MergeSort-random,1753653397.216805,2.2585,0.4251,2.7646,5.4482,56.60,56.60,0.00,0.00,,,,O(n log n) [n=900],O(n) [n=900],0.01,0.0000,random,900,7719,8832,1.144,25471,10,10,True,191768,False,divide_and_conquer
1000,MergeSort-random,1753653397.2728636,2.4621,0.4168,2.7620,5.6409,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1000],O(n) [n=1000],0.02,0.0000,random,1000,8704,9965,1.145,28656,10,10,True,237493,False,divide_and_conquer
1100,MergeSort-random,1753653397.3341534,2.8263,0.4466,2.8292,6.1020,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1100],O(n) [n=1100],0.02,0.0000,random,1100,9740,11113,1.141,32044,11,11,True,291903,False,divide_and_conquer
1200,MergeSort-random,1753653397.4026315,3.1166,0.5006,2.7802,6.3974,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1200],O(n) [n=1200],0.02,0.0000,random,1200,10764,12274,1.14,35468,11,11,True,351771,False,divide_and_conquer
1300,MergeSort-random,1753653397.4788125,3.3537,0.5165,2.7656,6.6357,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1300],O(n) [n=1300],0.02,0.0000,random,1300,11799,13447,1.14,38903,11,11,True,410845,False,divide_and_conquer
1400,MergeSort-random,1753653397.5620337,3.6480,0.5492,2.7686,6.9657,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1400],O(n) [n=1400],0.02,0.0000,random,1400,12917,14631,1.133,42421,11,11,True,475374,False,divide_and_conquer
1500,MergeSort-random,1753653397.6531703,3.8789,0.5741,2.7695,7.2225,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1500],O(n) [n=1500],0.02,0.0000,random,1500,13929,15826,1.136,45833,11,11,True,554662,False,divide_and_conquer
1600,MergeSort-random,1753653397.753686,4.4489,0.6334,2.8791,7.9614,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1600],O(n) [n=1600],0.02,0.0000,random,1600,15070,17030,1.13,49374,11,11,True,631774,False,divide_and_conquer
1700,MergeSort-random,1753653397.8652647,4.5360,0.8326,2.7746,8.1433,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1700],O(n) [n=1700],0.03,0.0000,random,1700,16165,18243,1.129,52869,11,11,True,705102,False,divide_and_conquer
1800,MergeSort-random,1753653397.9830165,4.7502,0.7509,2.8154,8.3165,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1800],O(n) [n=1800],0.03,0.0000,random,1800,17236,19464,1.129,56340,11,11,True,796573,False,divide_and_conquer
1900,MergeSort-random,1753653398.1093025,5.1008,0.7912,2.7744,8.6665,56.60,56.60,0.00,0.00,,,,O(n log n) [n=1900],O(n) [n=1900],0.03,0.0000,random,1900,18308,20694,1.13,59812,11,11,True,891306,False,divide_and_conquer
2000,MergeSort-random,1753653398.2460794,5.2148,0.8901,2.8151,8.9200,56.60,56.60,0.00,0.00,,,,O(n log n) [n=2000],O(n) [n=2000],0.03,0.0000,random,2000,19428,21931,1.129,63332,11,11,True,988805,False,divide_and_conquer
