[{"input_size": 100, "algorithm_name": "MergeSort-random", "timestamp": 1753653396.9421804, "execution_time_ms": 0.22953450679779053, "setup_time_ms": 0.0616009347140789, "cleanup_time_ms": 2.8089601546525955, "total_time_ms": 3.100095596164465, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 100, "actual_comparisons": 549, "theoretical_comparisons": 664, "comparison_efficiency": 1.21, "array_accesses": 1893, "max_recursion_depth": 7, "theoretical_depth": 7, "correctness_verified": true, "input_inversions": 2298, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=100]", "theoretical_space_complexity": "O(n) [n=100]", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "MergeSort-random", "timestamp": 1753653396.9652252, "execution_time_ms": 0.4571054130792618, "setup_time_ms": 0.09234296157956123, "cleanup_time_ms": 2.7854950167238712, "total_time_ms": 3.3349433913826942, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 200, "actual_comparisons": 1295, "theoretical_comparisons": 1528, "comparison_efficiency": 1.181, "array_accesses": 4383, "max_recursion_depth": 8, "theoretical_depth": 8, "correctness_verified": true, "input_inversions": 9289, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=200]", "theoretical_space_complexity": "O(n) [n=200]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "MergeSort-random", "timestamp": 1753653396.9897525, "execution_time_ms": 0.7009613327682018, "setup_time_ms": 0.1287427730858326, "cleanup_time_ms": 2.7535296976566315, "total_time_ms": 3.583233803510666, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 300, "actual_comparisons": 2093, "theoretical_comparisons": 2468, "comparison_efficiency": 1.179, "array_accesses": 7069, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 22335, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=300]", "theoretical_space_complexity": "O(n) [n=300]", "theoretical_memory_mb": 0.00457763671875, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.017051, "execution_time_ms": 0.943397730588913, "setup_time_ms": 0.16420800238847733, "cleanup_time_ms": 2.9131020419299603, "total_time_ms": 4.0207077749073505, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 400, "actual_comparisons": 2974, "theoretical_comparisons": 3457, "comparison_efficiency": 1.163, "array_accesses": 9950, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 39272, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=400]", "theoretical_space_complexity": "O(n) [n=400]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.0478323, "execution_time_ms": 1.1961856856942177, "setup_time_ms": 0.22937916219234467, "cleanup_time_ms": 2.7608810923993587, "total_time_ms": 4.186445940285921, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 500, "actual_comparisons": 3853, "theoretical_comparisons": 4482, "comparison_efficiency": 1.163, "array_accesses": 12829, "max_recursion_depth": 9, "theoretical_depth": 9, "correctness_verified": true, "input_inversions": 61163, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=500]", "theoretical_space_complexity": "O(n) [n=500]", "theoretical_memory_mb": 0.00762939453125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.0831141, "execution_time_ms": 1.4499793760478497, "setup_time_ms": 0.24932203814387321, "cleanup_time_ms": 2.7536829002201557, "total_time_ms": 4.452984314411879, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 600, "actual_comparisons": 4792, "theoretical_comparisons": 5537, "comparison_efficiency": 1.156, "array_accesses": 15944, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 87892, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=600]", "theoretical_space_complexity": "O(n) [n=600]", "theoretical_memory_mb": 0.0091552734375, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.1226604, "execution_time_ms": 1.6913599334657192, "setup_time_ms": 0.27960818260908127, "cleanup_time_ms": 2.834385260939598, "total_time_ms": 4.805353377014399, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 700, "actual_comparisons": 5750, "theoretical_comparisons": 6615, "comparison_efficiency": 1.151, "array_accesses": 19102, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 119724, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=700]", "theoretical_space_complexity": "O(n) [n=700]", "theoretical_memory_mb": 0.01068115234375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.1670442, "execution_time_ms": 1.9721146672964096, "setup_time_ms": 0.30916091054677963, "cleanup_time_ms": 2.7677440084517, "total_time_ms": 5.0490195862948895, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 800, "actual_comparisons": 6725, "theoretical_comparisons": 7715, "comparison_efficiency": 1.147, "array_accesses": 22277, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 153806, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=800]", "theoretical_space_complexity": "O(n) [n=800]", "theoretical_memory_mb": 0.01220703125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.216805, "execution_time_ms": 2.258497476577759, "setup_time_ms": 0.42506027966737747, "cleanup_time_ms": 2.7646049857139587, "total_time_ms": 5.448162741959095, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 900, "actual_comparisons": 7719, "theoretical_comparisons": 8832, "comparison_efficiency": 1.144, "array_accesses": 25471, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 191768, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=900]", "theoretical_space_complexity": "O(n) [n=900]", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}, {"input_size": 1000, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.2728636, "execution_time_ms": 2.4621392600238323, "setup_time_ms": 0.4167887382209301, "cleanup_time_ms": 2.7620051987469196, "total_time_ms": 5.640933196991682, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1000, "actual_comparisons": 8704, "theoretical_comparisons": 9965, "comparison_efficiency": 1.145, "array_accesses": 28656, "max_recursion_depth": 10, "theoretical_depth": 10, "correctness_verified": true, "input_inversions": 237493, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1000]", "theoretical_space_complexity": "O(n) [n=1000]", "theoretical_memory_mb": 0.0152587890625, "efficiency_ratio": 0.0}, {"input_size": 1100, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.3341534, "execution_time_ms": 2.8262900188565254, "setup_time_ms": 0.4465789534151554, "cleanup_time_ms": 2.8291549533605576, "total_time_ms": 6.102023925632238, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1100, "actual_comparisons": 9740, "theoretical_comparisons": 11113, "comparison_efficiency": 1.141, "array_accesses": 32044, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 291903, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1100]", "theoretical_space_complexity": "O(n) [n=1100]", "theoretical_memory_mb": 0.01678466796875, "efficiency_ratio": 0.0}, {"input_size": 1200, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.4026315, "execution_time_ms": 3.116642963141203, "setup_time_ms": 0.5005709826946259, "cleanup_time_ms": 2.7801552787423134, "total_time_ms": 6.397369224578142, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1200, "actual_comparisons": 10764, "theoretical_comparisons": 12274, "comparison_efficiency": 1.14, "array_accesses": 35468, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 351771, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1200]", "theoretical_space_complexity": "O(n) [n=1200]", "theoretical_memory_mb": 0.018310546875, "efficiency_ratio": 0.0}, {"input_size": 1300, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.4788125, "execution_time_ms": 3.353689983487129, "setup_time_ms": 0.5164500325918198, "cleanup_time_ms": 2.765554003417492, "total_time_ms": 6.635694019496441, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1300, "actual_comparisons": 11799, "theoretical_comparisons": 13447, "comparison_efficiency": 1.14, "array_accesses": 38903, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 410845, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1300]", "theoretical_space_complexity": "O(n) [n=1300]", "theoretical_memory_mb": 0.01983642578125, "efficiency_ratio": 0.0}, {"input_size": 1400, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.5620337, "execution_time_ms": 3.6479691974818707, "setup_time_ms": 0.5492069758474827, "cleanup_time_ms": 2.7685589157044888, "total_time_ms": 6.965735089033842, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1400, "actual_comparisons": 12917, "theoretical_comparisons": 14631, "comparison_efficiency": 1.133, "array_accesses": 42421, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 475374, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1400]", "theoretical_space_complexity": "O(n) [n=1400]", "theoretical_memory_mb": 0.0213623046875, "efficiency_ratio": 0.0}, {"input_size": 1500, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.6531703, "execution_time_ms": 3.878916334360838, "setup_time_ms": 0.5741207860410213, "cleanup_time_ms": 2.769460901618004, "total_time_ms": 7.222498022019863, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1500, "actual_comparisons": 13929, "theoretical_comparisons": 15826, "comparison_efficiency": 1.136, "array_accesses": 45833, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 554662, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1500]", "theoretical_space_complexity": "O(n) [n=1500]", "theoretical_memory_mb": 0.02288818359375, "efficiency_ratio": 0.0}, {"input_size": 1600, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.753686, "execution_time_ms": 4.448878671973944, "setup_time_ms": 0.6333771161735058, "cleanup_time_ms": 2.879127860069275, "total_time_ms": 7.961383648216724, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1600, "actual_comparisons": 15070, "theoretical_comparisons": 17030, "comparison_efficiency": 1.13, "array_accesses": 49374, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 631774, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1600]", "theoretical_space_complexity": "O(n) [n=1600]", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 1700, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.8652647, "execution_time_ms": 4.536010976880789, "setup_time_ms": 0.8326442912220955, "cleanup_time_ms": 2.7746381238102913, "total_time_ms": 8.143293391913176, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1700, "actual_comparisons": 16165, "theoretical_comparisons": 18243, "comparison_efficiency": 1.129, "array_accesses": 52869, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 705102, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1700]", "theoretical_space_complexity": "O(n) [n=1700]", "theoretical_memory_mb": 0.02593994140625, "efficiency_ratio": 0.0}, {"input_size": 1800, "algorithm_name": "MergeSort-random", "timestamp": 1753653397.9830165, "execution_time_ms": 4.750163573771715, "setup_time_ms": 0.750930979847908, "cleanup_time_ms": 2.815369050949812, "total_time_ms": 8.316463604569435, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1800, "actual_comparisons": 17236, "theoretical_comparisons": 19464, "comparison_efficiency": 1.129, "array_accesses": 56340, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 796573, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1800]", "theoretical_space_complexity": "O(n) [n=1800]", "theoretical_memory_mb": 0.0274658203125, "efficiency_ratio": 0.0}, {"input_size": 1900, "algorithm_name": "MergeSort-random", "timestamp": 1753653398.1093025, "execution_time_ms": 5.100822262465954, "setup_time_ms": 0.7912442088127136, "cleanup_time_ms": 2.7744299732148647, "total_time_ms": 8.666496444493532, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 1900, "actual_comparisons": 18308, "theoretical_comparisons": 20694, "comparison_efficiency": 1.13, "array_accesses": 59812, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 891306, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=1900]", "theoretical_space_complexity": "O(n) [n=1900]", "theoretical_memory_mb": 0.02899169921875, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "MergeSort-random", "timestamp": 1753653398.2460794, "execution_time_ms": 5.214840359985828, "setup_time_ms": 0.8900961838662624, "cleanup_time_ms": 2.815085928887129, "total_time_ms": 8.92002247273922, "baseline_memory_mb": 56.59765625, "peak_memory_mb": 56.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"data_type": "random", "input_size": 2000, "actual_comparisons": 19428, "theoretical_comparisons": 21931, "comparison_efficiency": 1.129, "array_accesses": 63332, "max_recursion_depth": 11, "theoretical_depth": 11, "correctness_verified": true, "input_inversions": 988805, "was_already_sorted": false, "algorithm_type": "divide_and_conquer"}, "theoretical_time_complexity": "O(n log n) [n=2000]", "theoretical_space_complexity": "O(n) [n=2000]", "theoretical_memory_mb": 0.030517578125, "efficiency_ratio": 0.0}]