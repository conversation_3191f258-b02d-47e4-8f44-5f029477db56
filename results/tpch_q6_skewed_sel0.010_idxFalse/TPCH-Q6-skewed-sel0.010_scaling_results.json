[{"input_size": 1000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655668.524708, "execution_time_ms": 0.7914157584309578, "setup_time_ms": 4.57640178501606, "cleanup_time_ms": 29.051283840090036, "total_time_ms": 34.419101383537054, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 1000, "total_rows": 1000, "rows_scanned": 1000, "rows_filtered": 17, "qualifying_rows": 17, "scan_ratio": 1.0, "filter_efficiency": 0.017, "actual_selectivity": 0.017, "expected_selectivity": 0.01, "selectivity_accuracy": 0.993, "comparisons": 2843, "arithmetic_operations": 34, "memory_accesses": 1017, "index_lookups": 0, "comparisons_per_row": 2.84, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.983, "index_effectiveness": 0, "scan_time_ms": 0.752, "revenue": 19060.35, "avg_revenue_per_row": 1121.2, "price_variance": 749445552.76, "discount_variance": 0.0, "quantity_variance": 27.36, "effective_scan_size": 1000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=1000]", "theoretical_space_complexity": "O(k) [k≈10]", "theoretical_memory_mb": 0.0006103515625, "efficiency_ratio": 0.0}, {"input_size": 2000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655668.72598, "execution_time_ms": 1.5475633554160595, "setup_time_ms": 9.74947214126587, "cleanup_time_ms": 26.22136566787958, "total_time_ms": 37.51840116456151, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 2000, "total_rows": 2000, "rows_scanned": 2000, "rows_filtered": 35, "qualifying_rows": 35, "scan_ratio": 1.0, "filter_efficiency": 0.0175, "actual_selectivity": 0.0175, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9925, "comparisons": 5717, "arithmetic_operations": 70, "memory_accesses": 2035, "index_lookups": 0, "comparisons_per_row": 2.86, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.983, "index_effectiveness": 0, "scan_time_ms": 1.702, "revenue": 34301.95, "avg_revenue_per_row": 980.06, "price_variance": 527054073.39, "discount_variance": 0.0, "quantity_variance": 43.85, "effective_scan_size": 2000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=2000]", "theoretical_space_complexity": "O(k) [k≈20]", "theoretical_memory_mb": 0.001220703125, "efficiency_ratio": 0.0}, {"input_size": 3000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655668.9337873, "execution_time_ms": 2.275111898779869, "setup_time_ms": 13.462357223033905, "cleanup_time_ms": 25.221582036465406, "total_time_ms": 40.95905115827918, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 3000, "total_rows": 3000, "rows_scanned": 3000, "rows_filtered": 59, "qualifying_rows": 59, "scan_ratio": 1.0, "filter_efficiency": 0.0197, "actual_selectivity": 0.019667, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9903, "comparisons": 8644, "arithmetic_operations": 118, "memory_accesses": 3059, "index_lookups": 0, "comparisons_per_row": 2.88, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.981, "index_effectiveness": 0, "scan_time_ms": 2.198, "revenue": 58854.91, "avg_revenue_per_row": 997.54, "price_variance": 479283850.42, "discount_variance": 0.0, "quantity_variance": 39.32, "effective_scan_size": 3000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=3000]", "theoretical_space_complexity": "O(k) [k≈30]", "theoretical_memory_mb": 0.0018310546875, "efficiency_ratio": 0.0}, {"input_size": 4000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655669.1393805, "execution_time_ms": 3.0549722723662853, "setup_time_ms": 18.253358080983162, "cleanup_time_ms": 25.72774328291416, "total_time_ms": 47.03607363626361, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 4000, "total_rows": 4000, "rows_scanned": 4000, "rows_filtered": 84, "qualifying_rows": 84, "scan_ratio": 1.0, "filter_efficiency": 0.021, "actual_selectivity": 0.021, "expected_selectivity": 0.01, "selectivity_accuracy": 0.989, "comparisons": 11543, "arithmetic_operations": 168, "memory_accesses": 4084, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.979, "index_effectiveness": 0, "scan_time_ms": 2.924, "revenue": 90235.58, "avg_revenue_per_row": 1074.23, "price_variance": 545428747.92, "discount_variance": 0.0, "quantity_variance": 42.62, "effective_scan_size": 4000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=4000]", "theoretical_space_complexity": "O(k) [k≈40]", "theoretical_memory_mb": 0.00244140625, "efficiency_ratio": 0.0}, {"input_size": 5000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655669.3593824, "execution_time_ms": 3.935314156115055, "setup_time_ms": 22.407134994864464, "cleanup_time_ms": 26.803802233189344, "total_time_ms": 53.14625138416886, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 5000, "total_rows": 5000, "rows_scanned": 5000, "rows_filtered": 100, "qualifying_rows": 100, "scan_ratio": 1.0, "filter_efficiency": 0.02, "actual_selectivity": 0.02, "expected_selectivity": 0.01, "selectivity_accuracy": 0.99, "comparisons": 14451, "arithmetic_operations": 200, "memory_accesses": 5100, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 3.923, "revenue": 105958.09, "avg_revenue_per_row": 1059.58, "price_variance": 568258428.77, "discount_variance": 0.0, "quantity_variance": 43.99, "effective_scan_size": 5000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=5000]", "theoretical_space_complexity": "O(k) [k≈50]", "theoretical_memory_mb": 0.0030517578125, "efficiency_ratio": 0.0}, {"input_size": 6000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655669.597726, "execution_time_ms": 4.529300797730684, "setup_time_ms": 28.37587706744671, "cleanup_time_ms": 25.15763184055686, "total_time_ms": 58.06280970573425, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 6000, "total_rows": 6000, "rows_scanned": 6000, "rows_filtered": 125, "qualifying_rows": 125, "scan_ratio": 1.0, "filter_efficiency": 0.0208, "actual_selectivity": 0.020833, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9892, "comparisons": 17357, "arithmetic_operations": 250, "memory_accesses": 6125, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 4.337, "revenue": 145589.42, "avg_revenue_per_row": 1164.72, "price_variance": 658319911.14, "discount_variance": 0.0, "quantity_variance": 46.65, "effective_scan_size": 6000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=6000]", "theoretical_space_complexity": "O(k) [k≈60]", "theoretical_memory_mb": 0.003662109375, "efficiency_ratio": 0.0}, {"input_size": 7000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655669.845735, "execution_time_ms": 5.223635397851467, "setup_time_ms": 31.229469925165176, "cleanup_time_ms": 26.07798995450139, "total_time_ms": 62.531095277518034, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 7000, "total_rows": 7000, "rows_scanned": 7000, "rows_filtered": 143, "qualifying_rows": 143, "scan_ratio": 1.0, "filter_efficiency": 0.0204, "actual_selectivity": 0.020429, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9896, "comparisons": 20214, "arithmetic_operations": 286, "memory_accesses": 7143, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.98, "index_effectiveness": 0, "scan_time_ms": 5.141, "revenue": 174031.93, "avg_revenue_per_row": 1217.01, "price_variance": 713435634.58, "discount_variance": 0.0, "quantity_variance": 44.91, "effective_scan_size": 7000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=7000]", "theoretical_space_complexity": "O(k) [k≈70]", "theoretical_memory_mb": 0.0042724609375, "efficiency_ratio": 0.0}, {"input_size": 8000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655670.0952802, "execution_time_ms": 6.010828912258148, "setup_time_ms": 36.95657476782799, "cleanup_time_ms": 25.76206298545003, "total_time_ms": 68.72946666553617, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.5546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 8000, "total_rows": 8000, "rows_scanned": 8000, "rows_filtered": 177, "qualifying_rows": 177, "scan_ratio": 1.0, "filter_efficiency": 0.0221, "actual_selectivity": 0.022125, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9879, "comparisons": 23074, "arithmetic_operations": 354, "memory_accesses": 8177, "index_lookups": 0, "comparisons_per_row": 2.88, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.978, "index_effectiveness": 0, "scan_time_ms": 5.939, "revenue": 207651.68, "avg_revenue_per_row": 1173.17, "price_variance": 699933889.07, "discount_variance": 0.0, "quantity_variance": 47.04, "effective_scan_size": 8000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=8000]", "theoretical_space_complexity": "O(k) [k≈80]", "theoretical_memory_mb": 0.0048828125, "efficiency_ratio": 0.0}, {"input_size": 9000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655670.3600063, "execution_time_ms": 6.582358945161104, "setup_time_ms": 41.327273938804865, "cleanup_time_ms": 25.602830108255148, "total_time_ms": 73.51246299222112, "baseline_memory_mb": 417.5546875, "peak_memory_mb": 417.79296875, "memory_increment_mb": 0.23828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 9000, "total_rows": 9000, "rows_scanned": 9000, "rows_filtered": 194, "qualifying_rows": 194, "scan_ratio": 1.0, "filter_efficiency": 0.0216, "actual_selectivity": 0.021556, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9884, "comparisons": 26004, "arithmetic_operations": 388, "memory_accesses": 9194, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.979, "index_effectiveness": 0, "scan_time_ms": 6.539, "revenue": 227391.25, "avg_revenue_per_row": 1172.12, "price_variance": 702304908.9, "discount_variance": 0.0, "quantity_variance": 48.08, "effective_scan_size": 9000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=9000]", "theoretical_space_complexity": "O(k) [k≈90]", "theoretical_memory_mb": 0.0054931640625, "efficiency_ratio": 0.02305327868852459}, {"input_size": 10000, "algorithm_name": "TPCH-Q6-skewed-sel0.010", "timestamp": 1753655670.6297114, "execution_time_ms": 7.317704427987337, "setup_time_ms": 44.80617819353938, "cleanup_time_ms": 26.02942008525133, "total_time_ms": 78.15330270677805, "baseline_memory_mb": 417.79296875, "peak_memory_mb": 418.56640625, "memory_increment_mb": 0.7734375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"selectivity_factor": 0.01, "data_distribution": "skewed", "use_indexes": false, "input_size": 10000, "total_rows": 10000, "rows_scanned": 10000, "rows_filtered": 224, "qualifying_rows": 224, "scan_ratio": 1.0, "filter_efficiency": 0.0224, "actual_selectivity": 0.0224, "expected_selectivity": 0.01, "selectivity_accuracy": 0.9876, "comparisons": 28905, "arithmetic_operations": 448, "memory_accesses": 10224, "index_lookups": 0, "comparisons_per_row": 2.89, "arithmetic_per_qualifying": 2.0, "memory_efficiency": 0.978, "index_effectiveness": 0, "scan_time_ms": 7.144, "revenue": 266673.32, "avg_revenue_per_row": 1190.51, "price_variance": 711739343.14, "discount_variance": 0.0, "quantity_variance": 48.65, "effective_scan_size": 10000, "algorithm_type": "analytical_query"}, "theoretical_time_complexity": "O(n) [n=10000]", "theoretical_space_complexity": "O(k) [k≈100]", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.007891414141414142}]