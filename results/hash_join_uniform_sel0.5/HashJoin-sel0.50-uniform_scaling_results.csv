input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_join_selectivity,custom_data_distribution,custom_left_table_ratio,custom_right_table_ratio,custom_input_size,custom_left_table_size,custom_right_table_size,custom_result_size,custom_hash_operations,custom_hash_collisions,custom_collision_rate,custom_hash_efficiency,custom_comparisons,custom_memory_accesses,custom_access_efficiency,custom_build_time_ms,custom_probe_time_ms,custom_build_time_ratio,custom_probe_time_ratio,custom_actual_selectivity,custom_expected_selectivity,custom_selectivity_accuracy,custom_unique_left_keys,custom_unique_right_keys,custom_matching_keys,custom_join_ratio,custom_algorithm_type
100,HashJoin-sel0.50-uniform,1753655088.8983724,0.1305,0.2267,22.8077,23.1649,411.84,411.84,0.00,0.00,,,,"O(|R|+|S|) [|R|=100, |S|=100]","O(min(|R|,|S|)+result) [min=100, est_result=500]",0.04,0.0000,0.5,uniform,1.0,1.0,100,100,100,78,200,19,0.095,1.0,78,278,0.719,0.045,0.04,0.528,0.472,0.5309,0.5,0.9691,81,79,43,0.0078,hash_based_join
200,HashJoin-sel0.50-uniform,1753655089.0612757,0.2246,0.4376,22.4241,23.0863,411.84,411.84,0.00,0.00,,,,"O(|R|+|S|) [|R|=200, |S|=200]","O(min(|R|,|S|)+result) [min=200, est_result=2000]",0.13,0.0000,0.5,uniform,1.0,1.0,200,200,200,144,400,48,0.12,1.0,144,544,0.735,0.077,0.076,0.503,0.497,0.5066,0.5,0.9934,152,151,77,0.0036,hash_based_join
300,HashJoin-sel0.50-uniform,1753655089.2233546,0.3012,0.5964,22.7555,23.6532,411.84,411.91,0.07,0.00,,,,"O(|R|+|S|) [|R|=300, |S|=300]","O(min(|R|,|S|)+result) [min=300, est_result=4500]",0.29,4.4118,0.5,uniform,1.0,1.0,300,300,300,210,600,76,0.1267,1.0,210,810,0.741,0.111,0.107,0.509,0.491,0.5089,0.5,0.9911,224,237,114,0.002333,hash_based_join
400,HashJoin-sel0.50-uniform,1753655089.3856764,0.4012,0.8073,22.4950,23.7035,411.91,411.91,0.00,0.00,,,,"O(|R|+|S|) [|R|=400, |S|=400]","O(min(|R|,|S|)+result) [min=400, est_result=8000]",0.51,0.0000,0.5,uniform,1.0,1.0,400,400,400,268,800,93,0.1163,1.0,268,1068,0.749,0.151,0.147,0.507,0.493,0.4821,0.5,0.9821,307,304,148,0.001675,hash_based_join
500,HashJoin-sel0.50-uniform,1753655089.5470536,0.5400,1.0151,22.9113,24.4665,411.91,411.91,0.00,0.00,,,,"O(|R|+|S|) [|R|=500, |S|=500]","O(min(|R|,|S|)+result) [min=500, est_result=12500]",0.79,0.0000,0.5,uniform,1.0,1.0,500,500,500,369,1000,114,0.114,1.0,369,1369,0.73,0.194,0.219,0.47,0.53,0.4845,0.5,0.9845,386,375,187,0.001476,hash_based_join
600,HashJoin-sel0.50-uniform,1753655089.7106009,0.6118,1.1752,22.1363,23.9233,411.91,411.91,0.00,0.00,,,,"O(|R|+|S|) [|R|=600, |S|=600]","O(min(|R|,|S|)+result) [min=600, est_result=18000]",1.14,0.0000,0.5,uniform,1.0,1.0,600,600,600,409,1200,124,0.1033,1.0,409,1609,0.746,0.226,0.242,0.483,0.517,0.4643,0.5,0.9643,476,445,221,0.001136,hash_based_join
700,HashJoin-sel0.50-uniform,1753655089.8742607,0.6833,1.3728,22.8908,24.9469,411.91,411.91,0.00,0.00,,,,"O(|R|+|S|) [|R|=700, |S|=700]","O(min(|R|,|S|)+result) [min=700, est_result=24500]",1.54,0.0000,0.5,uniform,1.0,1.0,700,700,700,476,1400,139,0.0993,1.0,476,1876,0.746,0.254,0.275,0.481,0.519,0.4617,0.5,0.9617,561,535,259,0.000971,hash_based_join
800,HashJoin-sel0.50-uniform,1753655090.039659,0.7746,1.5687,22.7825,25.1258,411.91,411.91,0.00,0.00,,,,"O(|R|+|S|) [|R|=800, |S|=800]","O(min(|R|,|S|)+result) [min=800, est_result=32000]",2.00,0.0000,0.5,uniform,1.0,1.0,800,800,800,536,1600,157,0.0981,1.0,536,2136,0.749,0.288,0.315,0.477,0.523,0.465,0.5,0.965,643,602,299,0.000838,hash_based_join
900,HashJoin-sel0.50-uniform,1753655090.2070084,1.4519,1.7008,23.0222,26.1749,411.91,411.98,0.07,0.00,,,,"O(|R|+|S|) [|R|=900, |S|=900]","O(min(|R|,|S|)+result) [min=900, est_result=40500]",2.53,35.9375,0.5,uniform,1.0,1.0,900,900,900,619,1800,177,0.0983,1.0,619,2419,0.744,0.343,0.323,0.515,0.485,0.4675,0.5,0.9675,723,671,338,0.000764,hash_based_join
1000,HashJoin-sel0.50-uniform,1753655090.389802,0.9993,1.8760,24.0279,26.9032,411.98,411.98,0.00,0.00,,,,"O(|R|+|S|) [|R|=1000, |S|=1000]","O(min(|R|,|S|)+result) [min=1000, est_result=50000]",3.11,0.0000,0.5,uniform,1.0,1.0,1000,1000,1000,716,2000,194,0.097,1.0,716,2716,0.736,0.402,0.41,0.495,0.505,0.4926,0.5,0.9926,806,768,397,0.000716,hash_based_join
