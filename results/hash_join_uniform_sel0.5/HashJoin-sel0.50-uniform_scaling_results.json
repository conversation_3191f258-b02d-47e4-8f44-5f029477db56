[{"input_size": 100, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655088.8983724, "execution_time_ms": 0.1305186189711094, "setup_time_ms": 0.2266918309032917, "cleanup_time_ms": 22.807657718658447, "total_time_ms": 23.16486816853285, "baseline_memory_mb": 411.84375, "peak_memory_mb": 411.84375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 100, "left_table_size": 100, "right_table_size": 100, "result_size": 78, "hash_operations": 200, "hash_collisions": 19, "collision_rate": 0.095, "hash_efficiency": 1.0, "comparisons": 78, "memory_accesses": 278, "access_efficiency": 0.719, "build_time_ms": 0.045, "probe_time_ms": 0.04, "build_time_ratio": 0.528, "probe_time_ratio": 0.472, "actual_selectivity": 0.5309, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9691, "unique_left_keys": 81, "unique_right_keys": 79, "matching_keys": 43, "join_ratio": 0.0078, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=100, |S|=100]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=100, est_result=500]", "theoretical_memory_mb": 0.03662109375, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.0612757, "execution_time_ms": 0.2246106043457985, "setup_time_ms": 0.4375879652798176, "cleanup_time_ms": 22.424131631851196, "total_time_ms": 23.086330201476812, "baseline_memory_mb": 411.84375, "peak_memory_mb": 411.84375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 200, "left_table_size": 200, "right_table_size": 200, "result_size": 144, "hash_operations": 400, "hash_collisions": 48, "collision_rate": 0.12, "hash_efficiency": 1.0, "comparisons": 144, "memory_accesses": 544, "access_efficiency": 0.735, "build_time_ms": 0.077, "probe_time_ms": 0.076, "build_time_ratio": 0.503, "probe_time_ratio": 0.497, "actual_selectivity": 0.5066, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9934, "unique_left_keys": 152, "unique_right_keys": 151, "matching_keys": 77, "join_ratio": 0.0036, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=200, |S|=200]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=200, est_result=2000]", "theoretical_memory_mb": 0.13427734375, "efficiency_ratio": 0.0}, {"input_size": 300, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.2233546, "execution_time_ms": 0.30124606564641, "setup_time_ms": 0.5963928997516632, "cleanup_time_ms": 22.755546029657125, "total_time_ms": 23.6531849950552, "baseline_memory_mb": 411.84375, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.06640625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 300, "left_table_size": 300, "right_table_size": 300, "result_size": 210, "hash_operations": 600, "hash_collisions": 76, "collision_rate": 0.1267, "hash_efficiency": 1.0, "comparisons": 210, "memory_accesses": 810, "access_efficiency": 0.741, "build_time_ms": 0.111, "probe_time_ms": 0.107, "build_time_ratio": 0.509, "probe_time_ratio": 0.491, "actual_selectivity": 0.5089, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9911, "unique_left_keys": 224, "unique_right_keys": 237, "matching_keys": 114, "join_ratio": 0.002333, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=300, |S|=300]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=300, est_result=4500]", "theoretical_memory_mb": 0.29296875, "efficiency_ratio": 4.411764705882353}, {"input_size": 400, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.3856764, "execution_time_ms": 0.40122028440237045, "setup_time_ms": 0.8073071949183941, "cleanup_time_ms": 22.495009005069733, "total_time_ms": 23.703536484390497, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 400, "left_table_size": 400, "right_table_size": 400, "result_size": 268, "hash_operations": 800, "hash_collisions": 93, "collision_rate": 0.1163, "hash_efficiency": 1.0, "comparisons": 268, "memory_accesses": 1068, "access_efficiency": 0.749, "build_time_ms": 0.151, "probe_time_ms": 0.147, "build_time_ratio": 0.507, "probe_time_ratio": 0.493, "actual_selectivity": 0.4821, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9821, "unique_left_keys": 307, "unique_right_keys": 304, "matching_keys": 148, "join_ratio": 0.001675, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=400, |S|=400]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=400, est_result=8000]", "theoretical_memory_mb": 0.5126953125, "efficiency_ratio": 0.0}, {"input_size": 500, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.5470536, "execution_time_ms": 0.5400123074650764, "setup_time_ms": 1.0151169262826443, "cleanup_time_ms": 22.91132789105177, "total_time_ms": 24.46645712479949, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 500, "left_table_size": 500, "right_table_size": 500, "result_size": 369, "hash_operations": 1000, "hash_collisions": 114, "collision_rate": 0.114, "hash_efficiency": 1.0, "comparisons": 369, "memory_accesses": 1369, "access_efficiency": 0.73, "build_time_ms": 0.194, "probe_time_ms": 0.219, "build_time_ratio": 0.47, "probe_time_ratio": 0.53, "actual_selectivity": 0.4845, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9845, "unique_left_keys": 386, "unique_right_keys": 375, "matching_keys": 187, "join_ratio": 0.001476, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=500, |S|=500]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=500, est_result=12500]", "theoretical_memory_mb": 0.79345703125, "efficiency_ratio": 0.0}, {"input_size": 600, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.7106009, "execution_time_ms": 0.6117595359683037, "setup_time_ms": 1.1752238497138023, "cleanup_time_ms": 22.13627565652132, "total_time_ms": 23.923259042203426, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 600, "left_table_size": 600, "right_table_size": 600, "result_size": 409, "hash_operations": 1200, "hash_collisions": 124, "collision_rate": 0.1033, "hash_efficiency": 1.0, "comparisons": 409, "memory_accesses": 1609, "access_efficiency": 0.746, "build_time_ms": 0.226, "probe_time_ms": 0.242, "build_time_ratio": 0.483, "probe_time_ratio": 0.517, "actual_selectivity": 0.4643, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9643, "unique_left_keys": 476, "unique_right_keys": 445, "matching_keys": 221, "join_ratio": 0.001136, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=600, |S|=600]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=600, est_result=18000]", "theoretical_memory_mb": 1.13525390625, "efficiency_ratio": 0.0}, {"input_size": 700, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655089.8742607, "execution_time_ms": 0.683298334479332, "setup_time_ms": 1.3727820478379726, "cleanup_time_ms": 22.89082296192646, "total_time_ms": 24.946903344243765, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 700, "left_table_size": 700, "right_table_size": 700, "result_size": 476, "hash_operations": 1400, "hash_collisions": 139, "collision_rate": 0.0993, "hash_efficiency": 1.0, "comparisons": 476, "memory_accesses": 1876, "access_efficiency": 0.746, "build_time_ms": 0.254, "probe_time_ms": 0.275, "build_time_ratio": 0.481, "probe_time_ratio": 0.519, "actual_selectivity": 0.4617, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9617, "unique_left_keys": 561, "unique_right_keys": 535, "matching_keys": 259, "join_ratio": 0.000971, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=700, |S|=700]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=700, est_result=24500]", "theoretical_memory_mb": 1.5380859375, "efficiency_ratio": 0.0}, {"input_size": 800, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655090.039659, "execution_time_ms": 0.7745727896690369, "setup_time_ms": 1.5687411651015282, "cleanup_time_ms": 22.782455664128065, "total_time_ms": 25.12576961889863, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.91015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 800, "left_table_size": 800, "right_table_size": 800, "result_size": 536, "hash_operations": 1600, "hash_collisions": 157, "collision_rate": 0.0981, "hash_efficiency": 1.0, "comparisons": 536, "memory_accesses": 2136, "access_efficiency": 0.749, "build_time_ms": 0.288, "probe_time_ms": 0.315, "build_time_ratio": 0.477, "probe_time_ratio": 0.523, "actual_selectivity": 0.465, "expected_selectivity": 0.5, "selectivity_accuracy": 0.965, "unique_left_keys": 643, "unique_right_keys": 602, "matching_keys": 299, "join_ratio": 0.000838, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=800, |S|=800]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=800, est_result=32000]", "theoretical_memory_mb": 2.001953125, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655090.2070084, "execution_time_ms": 1.4518615789711475, "setup_time_ms": 1.7007901333272457, "cleanup_time_ms": 23.022247944027185, "total_time_ms": 26.17489965632558, "baseline_memory_mb": 411.91015625, "peak_memory_mb": 411.98046875, "memory_increment_mb": 0.0703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 900, "left_table_size": 900, "right_table_size": 900, "result_size": 619, "hash_operations": 1800, "hash_collisions": 177, "collision_rate": 0.0983, "hash_efficiency": 1.0, "comparisons": 619, "memory_accesses": 2419, "access_efficiency": 0.744, "build_time_ms": 0.343, "probe_time_ms": 0.323, "build_time_ratio": 0.515, "probe_time_ratio": 0.485, "actual_selectivity": 0.4675, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9675, "unique_left_keys": 723, "unique_right_keys": 671, "matching_keys": 338, "join_ratio": 0.000764, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=900, |S|=900]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=900, est_result=40500]", "theoretical_memory_mb": 2.52685546875, "efficiency_ratio": 35.9375}, {"input_size": 1000, "algorithm_name": "HashJoin-sel0.50-uniform", "timestamp": 1753655090.389802, "execution_time_ms": 0.9993492625653744, "setup_time_ms": 1.8759728409349918, "cleanup_time_ms": 24.02786910533905, "total_time_ms": 26.903191208839417, "baseline_memory_mb": 411.98046875, "peak_memory_mb": 411.98046875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"join_selectivity": 0.5, "data_distribution": "uniform", "left_table_ratio": 1.0, "right_table_ratio": 1.0, "input_size": 1000, "left_table_size": 1000, "right_table_size": 1000, "result_size": 716, "hash_operations": 2000, "hash_collisions": 194, "collision_rate": 0.097, "hash_efficiency": 1.0, "comparisons": 716, "memory_accesses": 2716, "access_efficiency": 0.736, "build_time_ms": 0.402, "probe_time_ms": 0.41, "build_time_ratio": 0.495, "probe_time_ratio": 0.505, "actual_selectivity": 0.4926, "expected_selectivity": 0.5, "selectivity_accuracy": 0.9926, "unique_left_keys": 806, "unique_right_keys": 768, "matching_keys": 397, "join_ratio": 0.000716, "algorithm_type": "hash_based_join"}, "theoretical_time_complexity": "O(|R|+|S|) [|R|=1000, |S|=1000]", "theoretical_space_complexity": "O(min(|R|,|S|)+result) [min=1000, est_result=50000]", "theoretical_memory_mb": 3.11279296875, "efficiency_ratio": 0.0}]