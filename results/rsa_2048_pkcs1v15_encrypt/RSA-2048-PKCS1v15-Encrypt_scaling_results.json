[{"input_size": 16, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705335.9731393, "execution_time_ms": 0.119733065366745, "setup_time_ms": 0.03356626257300377, "cleanup_time_ms": 27.038009837269783, "total_time_ms": 27.191309165209532, "baseline_memory_mb": 420.51953125, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16, "plaintext_size_bytes": 16, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 16.0, "throughput_mbps": 0.24, "bytes_per_second": 251773, "chunks_per_second": 15735, "bits_per_second": 2014185, "encryption_rate_per_second": 15735.82, "encryption_time_ms": 0.06354926154017448, "chunk_processing_time_ms": 0.06354926154017448, "key_utilization": 0.065, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007476806640625, "efficiency_ratio": 0.0095703125}, {"input_size": 32, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705336.2904687, "execution_time_ms": 0.07968926802277565, "setup_time_ms": 0.03792904317378998, "cleanup_time_ms": 28.150522150099277, "total_time_ms": 28.268140461295843, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "plaintext_size_bytes": 32, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 8.0, "throughput_mbps": 0.47, "bytes_per_second": 490450, "chunks_per_second": 15326, "bits_per_second": 3923604, "encryption_rate_per_second": 15326.58, "encryption_time_ms": 0.06524613127112389, "chunk_processing_time_ms": 0.06524613127112389, "key_utilization": 0.131, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 64, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705336.4888866, "execution_time_ms": 0.08810097351670265, "setup_time_ms": 0.022869091480970383, "cleanup_time_ms": 26.325374841690063, "total_time_ms": 26.436344906687737, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "plaintext_size_bytes": 64, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 4.0, "throughput_mbps": 0.86, "bytes_per_second": 899487, "chunks_per_second": 14054, "bits_per_second": 7195898, "encryption_rate_per_second": 14054.49, "encryption_time_ms": 0.07115164771676064, "chunk_processing_time_ms": 0.07115164771676064, "key_utilization": 0.261, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.00079345703125, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705336.6840844, "execution_time_ms": 0.08499911054968834, "setup_time_ms": 0.021447893232107162, "cleanup_time_ms": 27.861766051501036, "total_time_ms": 27.96821305528283, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "plaintext_size_bytes": 128, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 2.0, "throughput_mbps": 1.68, "bytes_per_second": 1762455, "chunks_per_second": 13769, "bits_per_second": 14099647, "encryption_rate_per_second": 13769.19, "encryption_time_ms": 0.07262593135237694, "chunk_processing_time_ms": 0.07262593135237694, "key_utilization": 0.522, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008544921875, "efficiency_ratio": 0.0}, {"input_size": 61, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705336.8742275, "execution_time_ms": 0.0873141922056675, "setup_time_ms": 0.021901912987232208, "cleanup_time_ms": 26.087725069373846, "total_time_ms": 26.196941174566746, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 61, "plaintext_size_bytes": 61, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 4.2, "throughput_mbps": 0.81, "bytes_per_second": 848791, "chunks_per_second": 13914, "bits_per_second": 6790330, "encryption_rate_per_second": 13914.61, "encryption_time_ms": 0.07186690345406532, "chunk_processing_time_ms": 0.07186690345406532, "key_utilization": 0.249, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0007905960083007812, "efficiency_ratio": 0.0}, {"input_size": 122, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705337.0625932, "execution_time_ms": 0.08315807208418846, "setup_time_ms": 0.021677929908037186, "cleanup_time_ms": 26.967633981257677, "total_time_ms": 27.072469983249903, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 122, "plaintext_size_bytes": 122, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 2.1, "throughput_mbps": 1.87, "bytes_per_second": 1956602, "chunks_per_second": 16037, "bits_per_second": 15652821, "encryption_rate_per_second": 16037.73, "encryption_time_ms": 0.0623529776930809, "chunk_processing_time_ms": 0.0623529776930809, "key_utilization": 0.498, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0008487701416015625, "efficiency_ratio": 0.0}, {"input_size": 235, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705337.2501564, "execution_time_ms": 0.0849124975502491, "setup_time_ms": 0.02274615690112114, "cleanup_time_ms": 29.282215982675552, "total_time_ms": 29.389874637126923, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 235, "plaintext_size_bytes": 235, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 1.09, "throughput_mbps": 3.13, "bytes_per_second": 3284169, "chunks_per_second": 13975, "bits_per_second": 26273357, "encryption_rate_per_second": 13975.19, "encryption_time_ms": 0.07155537605285645, "chunk_processing_time_ms": 0.07155537605285645, "key_utilization": 0.959, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0009565353393554688, "efficiency_ratio": 0.0}, {"input_size": 245, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705337.4506314, "execution_time_ms": 0.0865924172103405, "setup_time_ms": 0.025840941816568375, "cleanup_time_ms": 26.334277354180813, "total_time_ms": 26.44671071320772, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 245, "plaintext_size_bytes": 245, "ciphertext_size_bytes": 256, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 1, "actual_chunks": 1, "expansion_ratio": 1.04, "throughput_mbps": 3.24, "bytes_per_second": 3398663, "chunks_per_second": 13872, "bits_per_second": 27189307, "encryption_rate_per_second": 13872.1, "encryption_time_ms": 0.07208716124296188, "chunk_processing_time_ms": 0.07208716124296188, "key_utilization": 1.0, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=1, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0009660720825195312, "efficiency_ratio": 0.0}, {"input_size": 255, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705337.6449285, "execution_time_ms": 0.1048695296049118, "setup_time_ms": 0.024518929421901703, "cleanup_time_ms": 26.572804898023605, "total_time_ms": 26.70219335705042, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.59765625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 255, "plaintext_size_bytes": 255, "ciphertext_size_bytes": 512, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 2, "actual_chunks": 2, "expansion_ratio": 2.01, "throughput_mbps": 2.55, "bytes_per_second": 2678917, "chunks_per_second": 21011, "bits_per_second": 21431343, "encryption_rate_per_second": 10505.56, "encryption_time_ms": 0.09518768638372421, "chunk_processing_time_ms": 0.047593843191862106, "key_utilization": 0.52, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0012197494506835938, "efficiency_ratio": 0.0}, {"input_size": 490, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705337.830327, "execution_time_ms": 0.11200057342648506, "setup_time_ms": 0.026954803615808487, "cleanup_time_ms": 26.73734398558736, "total_time_ms": 26.876299362629652, "baseline_memory_mb": 420.59765625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 490, "plaintext_size_bytes": 490, "ciphertext_size_bytes": 512, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 2, "actual_chunks": 2, "expansion_ratio": 1.04, "throughput_mbps": 5.07, "bytes_per_second": 5316896, "chunks_per_second": 21701, "bits_per_second": 42535172, "encryption_rate_per_second": 10850.81, "encryption_time_ms": 0.09215902537107468, "chunk_processing_time_ms": 0.04607951268553734, "key_utilization": 1.0, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=2, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0014438629150390625, "efficiency_ratio": 0.36962890625}, {"input_size": 735, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705338.0327964, "execution_time_ms": 0.13267965987324715, "setup_time_ms": 0.04133721813559532, "cleanup_time_ms": 26.68876713141799, "total_time_ms": 26.862784009426832, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 735, "plaintext_size_bytes": 735, "ciphertext_size_bytes": 768, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 3, "actual_chunks": 3, "expansion_ratio": 1.04, "throughput_mbps": 6.37, "bytes_per_second": 6678855, "chunks_per_second": 27260, "bits_per_second": 53430841, "encryption_rate_per_second": 9086.88, "encryption_time_ms": 0.1100488007068634, "chunk_processing_time_ms": 0.03668293356895447, "key_utilization": 1.0, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=3, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.0019216537475585938, "efficiency_ratio": 0.0}, {"input_size": 980, "algorithm_name": "RSA-2048-PKCS1v15-Encrypt", "timestamp": 1753705338.2319508, "execution_time_ms": 0.15207193791866302, "setup_time_ms": 0.026461202651262283, "cleanup_time_ms": 26.749471668154, "total_time_ms": 26.928004808723927, "baseline_memory_mb": 420.6015625, "peak_memory_mb": 420.6015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 980, "plaintext_size_bytes": 980, "ciphertext_size_bytes": 1024, "key_size_bits": 2048, "key_size_bytes": 256, "padding_scheme": "PKCS1v15", "max_chunk_size_bytes": 245, "theoretical_chunks": 4, "actual_chunks": 4, "expansion_ratio": 1.04, "throughput_mbps": 7.33, "bytes_per_second": 7689284, "chunks_per_second": 31384, "bits_per_second": 61514272, "encryption_rate_per_second": 7846.21, "encryption_time_ms": 0.1274500973522663, "chunk_processing_time_ms": 0.03186252433806658, "key_utilization": 1.0, "has_error": false, "algorithm_family": "asymmetric_encryption", "algorithm_type": "RSA", "operation": "encryption"}, "theoretical_time_complexity": "O(n*k^3) [chunks=4, k=2048]", "theoretical_space_complexity": "O(n)", "theoretical_memory_mb": 0.002399444580078125, "efficiency_ratio": 0.0}]