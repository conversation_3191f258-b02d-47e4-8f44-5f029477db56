[{"input_size": 50, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656358.9233692, "execution_time_ms": 0.1638021320104599, "setup_time_ms": 0.9472537785768509, "cleanup_time_ms": 24.748214054852724, "total_time_ms": 25.859269965440035, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 9.06629631186598e-09, "relative_solution_error": 1.30500224878493e-09, "final_residual_norm": 3.5162481725728866e-05, "computed_residual_norm": 4.6433846453443467e-07, "relative_residual": 1.3280194800306498e-09, "converged": false, "iterations_performed": 5, "max_iterations": 50, "iteration_efficiency": 0.1, "convergence_rate": 4.050394790743565, "operations_count": 2500, "algorithm_type": "conjugate_gradient", "matrix_size": "50×50", "implementation": "custom_cg", "nnz": 100, "actual_sparsity_ratio": 0.04, "target_sparsity_ratio": 0.01, "b_norm": 349.64732936275755, "solution_norm": 6.947341523976289, "true_solution_norm": 6.94734152397629, "theoretical_flops": 2500, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 349.64732936275755, "final_residual": 4.643384593893639e-07}, "theoretical_time_complexity": "O(k*nnz) [N=50, nnz≈25, k≤50]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈275 elements]", "theoretical_memory_mb": 0.00238800048828125, "efficiency_ratio": 0.0}, {"input_size": 150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656359.1096718, "execution_time_ms": 0.17506349831819534, "setup_time_ms": 1.1927266605198383, "cleanup_time_ms": 25.132947601377964, "total_time_ms": 26.500737760215998, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 8.509314956702307e-10, "relative_solution_error": 7.121362227034164e-11, "final_residual_norm": 1.4285854344001963e-05, "computed_residual_norm": 1.2971156045113524e-07, "relative_residual": 7.17486311268398e-11, "converged": false, "iterations_performed": 5, "max_iterations": 150, "iteration_efficiency": 0.03333333333333333, "convergence_rate": 4.654175494513208, "operations_count": 10440, "algorithm_type": "conjugate_gradient", "matrix_size": "150×150", "implementation": "custom_cg", "nnz": 594, "actual_sparsity_ratio": 0.0264, "target_sparsity_ratio": 0.01, "b_norm": 1807.861117542807, "solution_norm": 11.948998921019896, "true_solution_norm": 11.948998921019896, "theoretical_flops": 10440, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 1807.861117542807, "final_residual": 1.2971157318743434e-07}, "theoretical_time_complexity": "O(k*nnz) [N=150, nnz≈225, k≤150]", "theoretical_space_complexity": "O(nnz+N) [N=150, memory≈975 elements]", "theoretical_memory_mb": 0.00887298583984375, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656359.292168, "execution_time_ms": 0.1950676552951336, "setup_time_ms": 1.6544079408049583, "cleanup_time_ms": 25.117075070738792, "total_time_ms": 26.966550666838884, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.1015625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 5.810304263185452e-10, "relative_solution_error": 3.705668228193835e-11, "final_residual_norm": 1.687050734243557e-05, "computed_residual_norm": 1.4761212464039308e-07, "relative_residual": 3.7268050745500834e-11, "converged": false, "iterations_performed": 5, "max_iterations": 250, "iteration_efficiency": 0.02, "convergence_rate": 4.8053165919368945, "operations_count": 22320, "algorithm_type": "conjugate_gradient", "matrix_size": "250×250", "implementation": "custom_cg", "nnz": 1482, "actual_sparsity_ratio": 0.023712, "target_sparsity_ratio": 0.01, "b_norm": 3960.822250898472, "solution_norm": 15.679504762404022, "true_solution_norm": 15.679504762404024, "theoretical_flops": 22320, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 3960.822250898472, "final_residual": 1.4761210437959725e-07}, "theoretical_time_complexity": "O(k*nnz) [N=250, nnz≈625, k≤250]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈1,875 elements]", "theoretical_memory_mb": 0.01764678955078125, "efficiency_ratio": 0.0}, {"input_size": 350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656359.4749777, "execution_time_ms": 0.1997332088649273, "setup_time_ms": 2.796008251607418, "cleanup_time_ms": 24.814138188958168, "total_time_ms": 27.809879649430513, "baseline_memory_mb": 433.1015625, "peak_memory_mb": 433.10546875, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.7242697037295063e-10, "relative_solution_error": 9.717207866114262e-12, "final_residual_norm": 9.952422918802784e-06, "computed_residual_norm": 6.119634042374447e-08, "relative_residual": 9.74984619513389e-12, "converged": false, "iterations_performed": 5, "max_iterations": 350, "iteration_efficiency": 0.014285714285714285, "convergence_rate": 5.071300261262805, "operations_count": 38340, "algorithm_type": "conjugate_gradient", "matrix_size": "350×350", "implementation": "custom_cg", "nnz": 2784, "actual_sparsity_ratio": 0.0227265306122449, "target_sparsity_ratio": 0.01, "b_norm": 6276.646748980238, "solution_norm": 17.74449746765591, "true_solution_norm": 17.74449746765591, "theoretical_flops": 38340, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 6276.646748980238, "final_residual": 6.119627516920947e-08}, "theoretical_time_complexity": "O(k*nnz) [N=350, nnz≈1,225, k≤350]", "theoretical_space_complexity": "O(nnz+N) [N=350, memory≈2,975 elements]", "theoretical_memory_mb": 0.02870941162109375, "efficiency_ratio": 7.349609375}, {"input_size": 450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656359.6596186, "execution_time_ms": 0.20583327859640121, "setup_time_ms": 4.252812825143337, "cleanup_time_ms": 25.340580381453037, "total_time_ms": 29.799226485192776, "baseline_memory_mb": 433.10546875, "peak_memory_mb": 433.7421875, "memory_increment_mb": 0.63671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.2510189933688801e-10, "relative_solution_error": 1.0458835028735679e-11, "final_residual_norm": 1.684297788029149e-05, "computed_residual_norm": 1.0264614323218201e-07, "relative_residual": 1.0486762924944413e-11, "converged": false, "iterations_performed": 5, "max_iterations": 450, "iteration_efficiency": 0.011111111111111112, "convergence_rate": 5.044147258729399, "operations_count": 58120, "algorithm_type": "conjugate_gradient", "matrix_size": "450×450", "implementation": "custom_cg", "nnz": 4462, "actual_sparsity_ratio": 0.022034567901234568, "target_sparsity_ratio": 0.01, "b_norm": 9788.162845564291, "solution_norm": 21.52265512539589, "true_solution_norm": 21.52265512539589, "theoretical_flops": 58120, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 9788.162845564291, "final_residual": 1.0264612423328764e-07}, "theoretical_time_complexity": "O(k*nnz) [N=450, nnz≈2,025, k≤450]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈4,275 elements]", "theoretical_memory_mb": 0.04206085205078125, "efficiency_ratio": 0.06605876150306748}, {"input_size": 550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656359.842702, "execution_time_ms": 0.22340202704071999, "setup_time_ms": 7.174102123826742, "cleanup_time_ms": 24.976918939501047, "total_time_ms": 32.37442309036851, "baseline_memory_mb": 433.8203125, "peak_memory_mb": 434.6015625, "memory_increment_mb": 0.78125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.4107667572315445e-10, "relative_solution_error": 5.996015486496999e-12, "final_residual_norm": 1.2546104239670377e-05, "computed_residual_norm": 7.868455517662141e-08, "relative_residual": 6.021463673577375e-12, "converged": false, "iterations_performed": 5, "max_iterations": 550, "iteration_efficiency": 0.00909090909090909, "convergence_rate": 5.160532544666053, "operations_count": 82060, "algorithm_type": "conjugate_gradient", "matrix_size": "550×550", "implementation": "custom_cg", "nnz": 6556, "actual_sparsity_ratio": 0.021672727272727274, "target_sparsity_ratio": 0.01, "b_norm": 13067.34698440431, "solution_norm": 23.52840416120647, "true_solution_norm": 23.528404161206474, "theoretical_flops": 82060, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 13067.34698440431, "final_residual": 7.868482566335272e-08}, "theoretical_time_complexity": "O(k*nnz) [N=550, nnz≈3,025, k≤550]", "theoretical_space_complexity": "O(nnz+N) [N=550, memory≈5,775 elements]", "theoretical_memory_mb": 0.05770111083984375, "efficiency_ratio": 0.073857421875}, {"input_size": 650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656360.0476925, "execution_time_ms": 0.24269651621580124, "setup_time_ms": 9.076925925910473, "cleanup_time_ms": 31.56370809301734, "total_time_ms": 40.883330535143614, "baseline_memory_mb": 434.6015625, "peak_memory_mb": 435.421875, "memory_increment_mb": 0.8203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.8939109417757914e-10, "relative_solution_error": 7.167497201653661e-12, "final_residual_norm": 2.0487822967036988e-05, "computed_residual_norm": 1.2485281578386547e-07, "relative_residual": 7.19938512151022e-12, "converged": false, "iterations_performed": 5, "max_iterations": 650, "iteration_efficiency": 0.007692307692307693, "convergence_rate": 5.110471834078889, "operations_count": 109940, "algorithm_type": "conjugate_gradient", "matrix_size": "650×650", "implementation": "custom_cg", "nnz": 9044, "actual_sparsity_ratio": 0.021405917159763314, "target_sparsity_ratio": 0.01, "b_norm": 17342.14987483195, "solution_norm": 26.423602109517876, "true_solution_norm": 26.423602109517876, "theoretical_flops": 109940, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 17342.14987483195, "final_residual": 1.2485288727528727e-07}, "theoretical_time_complexity": "O(k*nnz) [N=650, nnz≈4,225, k≤650]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈7,475 elements]", "theoretical_memory_mb": 0.07563018798828125, "efficiency_ratio": 0.09219680059523809}, {"input_size": 750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656360.2549222, "execution_time_ms": 0.2684805542230606, "setup_time_ms": 12.251483276486397, "cleanup_time_ms": 25.6684604100883, "total_time_ms": 38.18842424079776, "baseline_memory_mb": 435.421875, "peak_memory_mb": 436.4375, "memory_increment_mb": 1.015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 8.429865019709778e-11, "relative_solution_error": 2.966972426880812e-12, "final_residual_norm": 1.024962815010029e-05, "computed_residual_norm": 6.408810036697372e-08, "relative_residual": 2.977944575764286e-12, "converged": false, "iterations_performed": 5, "max_iterations": 750, "iteration_efficiency": 0.006666666666666667, "convergence_rate": 5.310006699222781, "operations_count": 141800, "algorithm_type": "conjugate_gradient", "matrix_size": "750×750", "implementation": "custom_cg", "nnz": 11930, "actual_sparsity_ratio": 0.021208888888888888, "target_sparsity_ratio": 0.01, "b_norm": 21520.917779514275, "solution_norm": 28.412347021950996, "true_solution_norm": 28.412347021951003, "theoretical_flops": 141800, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 21520.917779514275, "final_residual": 6.408833489116772e-08}, "theoretical_time_complexity": "O(k*nnz) [N=750, nnz≈5,625, k≤750]", "theoretical_space_complexity": "O(nnz+N) [N=750, memory≈9,375 elements]", "theoretical_memory_mb": 0.09584808349609375, "efficiency_ratio": 0.09437349759615385}, {"input_size": 850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656360.4555411, "execution_time_ms": 0.27289120480418205, "setup_time_ms": 16.253876965492964, "cleanup_time_ms": 24.954888969659805, "total_time_ms": 41.48165713995695, "baseline_memory_mb": 436.4375, "peak_memory_mb": 437.71875, "memory_increment_mb": 1.28125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.440855218351926e-11, "relative_solution_error": 2.122363997901878e-12, "final_residual_norm": 9.607053426532607e-06, "computed_residual_norm": 5.554336940593406e-08, "relative_residual": 2.131668337930439e-12, "converged": false, "iterations_performed": 5, "max_iterations": 850, "iteration_efficiency": 0.0058823529411764705, "convergence_rate": 5.381907154179541, "operations_count": 177700, "algorithm_type": "conjugate_gradient", "matrix_size": "850×850", "implementation": "custom_cg", "nnz": 15220, "actual_sparsity_ratio": 0.02106574394463668, "target_sparsity_ratio": 0.01, "b_norm": 26056.290473338428, "solution_norm": 30.3475521857665, "true_solution_norm": 30.3475521857665, "theoretical_flops": 177700, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 26056.290473338428, "final_residual": 5.55433554751066e-08}, "theoretical_time_complexity": "O(k*nnz) [N=850, nnz≈7,225, k≤850]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈11,475 elements]", "theoretical_memory_mb": 0.11835479736328125, "efficiency_ratio": 0.09237447599085366}, {"input_size": 950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656360.6556017, "execution_time_ms": 0.2922152169048786, "setup_time_ms": 19.835494924336672, "cleanup_time_ms": 25.10359836742282, "total_time_ms": 45.23130850866437, "baseline_memory_mb": 437.71875, "peak_memory_mb": 439.25, "memory_increment_mb": 1.53125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.808991434915876e-11, "relative_solution_error": 2.1315695631247586e-12, "final_residual_norm": 1.4062869520498457e-05, "computed_residual_norm": 6.548707221138665e-08, "relative_residual": 2.1365463576875494e-12, "converged": false, "iterations_performed": 5, "max_iterations": 950, "iteration_efficiency": 0.005263157894736842, "convergence_rate": 5.358966217285861, "operations_count": 217320, "algorithm_type": "conjugate_gradient", "matrix_size": "950×950", "implementation": "custom_cg", "nnz": 18882, "actual_sparsity_ratio": 0.020921883656509696, "target_sparsity_ratio": 0.01, "b_norm": 30650.90161781715, "solution_norm": 31.943557239268728, "true_solution_norm": 31.943557239268728, "theoretical_flops": 217320, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 30650.90161781715, "final_residual": 6.548684382165995e-08}, "theoretical_time_complexity": "O(k*nnz) [N=950, nnz≈9,025, k≤950]", "theoretical_space_complexity": "O(nnz+N) [N=950, memory≈13,775 elements]", "theoretical_memory_mb": 0.14315032958984375, "efficiency_ratio": 0.09348592952806123}, {"input_size": 1050, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656360.8581686, "execution_time_ms": 0.313340499997139, "setup_time_ms": 23.99967983365059, "cleanup_time_ms": 24.807611014693975, "total_time_ms": 49.1206313483417, "baseline_memory_mb": 439.25, "peak_memory_mb": 440.7890625, "memory_increment_mb": 1.5390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 6.583740420947233e-11, "relative_solution_error": 2.013609292532439e-12, "final_residual_norm": 1.6347595609107002e-05, "computed_residual_norm": 6.995074332269042e-08, "relative_residual": 2.017173283825998e-12, "converged": false, "iterations_performed": 5, "max_iterations": 1050, "iteration_efficiency": 0.004761904761904762, "convergence_rate": 5.355182212398757, "operations_count": 261040, "algorithm_type": "conjugate_gradient", "matrix_size": "1050×1050", "implementation": "custom_cg", "nnz": 22954, "actual_sparsity_ratio": 0.020819954648526078, "target_sparsity_ratio": 0.01, "b_norm": 34677.60746365526, "solution_norm": 32.696215921148806, "true_solution_norm": 32.696215921148806, "theoretical_flops": 261040, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 34677.60746365526, "final_residual": 6.995067105640261e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1050, nnz≈11,025, k≤1050]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈16,275 elements]", "theoretical_memory_mb": 0.17023468017578125, "efficiency_ratio": 0.1106093353426396}, {"input_size": 1150, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656361.0647874, "execution_time_ms": 0.34337928518652916, "setup_time_ms": 29.663936235010624, "cleanup_time_ms": 24.741745088249445, "total_time_ms": 54.7490606084466, "baseline_memory_mb": 440.7890625, "peak_memory_mb": 442.59375, "memory_increment_mb": 1.8046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.936140769353107e-11, "relative_solution_error": 1.1511265101744695e-12, "final_residual_norm": 9.202713989894135e-06, "computed_residual_norm": 4.590381208346894e-08, "relative_residual": 1.1557399036924228e-12, "converged": false, "iterations_performed": 5, "max_iterations": 1150, "iteration_efficiency": 0.004347826086956522, "convergence_rate": 5.498617115537248, "operations_count": 308880, "algorithm_type": "conjugate_gradient", "matrix_size": "1150×1150", "implementation": "custom_cg", "nnz": 27438, "actual_sparsity_ratio": 0.020747069943289224, "target_sparsity_ratio": 0.01, "b_norm": 39718.11645233747, "solution_norm": 34.19381566285472, "true_solution_norm": 34.19381566285472, "theoretical_flops": 308880, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 39718.11645233747, "final_residual": 4.5903189162029496e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1150, nnz≈13,225, k≤1150]", "theoretical_space_complexity": "O(nnz+N) [N=1150, memory≈18,975 elements]", "theoretical_memory_mb": 0.19960784912109375, "efficiency_ratio": 0.11060521509740259}, {"input_size": 1250, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656361.2783659, "execution_time_ms": 0.3692934289574623, "setup_time_ms": 35.53686710074544, "cleanup_time_ms": 24.83807085081935, "total_time_ms": 60.74423138052225, "baseline_memory_mb": 442.59375, "peak_memory_mb": 444.3984375, "memory_increment_mb": 1.8046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.6446827004575565e-11, "relative_solution_error": 1.3201083643019639e-12, "final_residual_norm": 1.2660635186273825e-05, "computed_residual_norm": 5.881847934576306e-08, "relative_residual": 1.3241594809837617e-12, "converged": false, "iterations_performed": 5, "max_iterations": 1250, "iteration_efficiency": 0.004, "convergence_rate": 5.452853655238197, "operations_count": 360820, "algorithm_type": "conjugate_gradient", "matrix_size": "1250×1250", "implementation": "custom_cg", "nnz": 32332, "actual_sparsity_ratio": 0.02069248, "target_sparsity_ratio": 0.01, "b_norm": 44419.48284210062, "solution_norm": 35.18410174541644, "true_solution_norm": 35.184101745416434, "theoretical_flops": 360820, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 44419.48284210062, "final_residual": 5.8818910171814204e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1250, nnz≈15,625, k≤1250]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈21,875 elements]", "theoretical_memory_mb": 0.23126983642578125, "efficiency_ratio": 0.12814951975108224}, {"input_size": 1350, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656361.5004778, "execution_time_ms": 0.4116198979318142, "setup_time_ms": 40.80317309126258, "cleanup_time_ms": 35.4099003598094, "total_time_ms": 76.62469334900379, "baseline_memory_mb": 444.3984375, "peak_memory_mb": 446.453125, "memory_increment_mb": 2.0546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 2.6836069757654693e-11, "relative_solution_error": 7.141370051743743e-13, "final_residual_norm": 8.433627559785708e-06, "computed_residual_norm": 3.672945557871316e-08, "relative_residual": 7.168703282527699e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1350, "iteration_efficiency": 0.003703703703703704, "convergence_rate": 5.589066541813874, "operations_count": 416160, "algorithm_type": "conjugate_gradient", "matrix_size": "1350×1350", "implementation": "custom_cg", "nnz": 37566, "actual_sparsity_ratio": 0.020612345679012347, "target_sparsity_ratio": 0.01, "b_norm": 51235.84298464964, "solution_norm": 37.578321194967344, "true_solution_norm": 37.578321194967344, "theoretical_flops": 416160, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 51235.84298464964, "final_residual": 3.672971042933521e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1350, nnz≈18,225, k≤1350]", "theoretical_space_complexity": "O(nnz+N) [N=1350, memory≈24,975 elements]", "theoretical_memory_mb": 0.26522064208984375, "efficiency_ratio": 0.12908076877376426}, {"input_size": 1450, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656361.739441, "execution_time_ms": 0.506804883480072, "setup_time_ms": 50.02290615811944, "cleanup_time_ms": 28.90077419579029, "total_time_ms": 79.4304852373898, "baseline_memory_mb": 446.56640625, "peak_memory_mb": 448.76953125, "memory_increment_mb": 2.203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 4.5187790089414727e-11, "relative_solution_error": 1.2128482472610307e-12, "final_residual_norm": 1.7487329320393547e-05, "computed_residual_norm": 6.62545292437224e-08, "relative_residual": 1.2141957695887694e-12, "converged": false, "iterations_performed": 5, "max_iterations": 1450, "iteration_efficiency": 0.0034482758620689655, "convergence_rate": 5.451413945454983, "operations_count": 476240, "algorithm_type": "conjugate_gradient", "matrix_size": "1450×1450", "implementation": "custom_cg", "nnz": 43274, "actual_sparsity_ratio": 0.02058216409036861, "target_sparsity_ratio": 0.01, "b_norm": 54566.595357321865, "solution_norm": 37.25757957886495, "true_solution_norm": 37.25757957886495, "theoretical_flops": 476240, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 54566.595357321865, "final_residual": 6.62545067117141e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1450, nnz≈21,025, k≤1450]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈28,275 elements]", "theoretical_memory_mb": 0.30146026611328125, "efficiency_ratio": 0.13683302859042554}, {"input_size": 1550, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656362.0016656, "execution_time_ms": 0.4903750494122505, "setup_time_ms": 56.55170790851116, "cleanup_time_ms": 24.764953646808863, "total_time_ms": 81.80703660473228, "baseline_memory_mb": 448.76953125, "peak_memory_mb": 451.11328125, "memory_increment_mb": 2.34375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.5191382961355815e-11, "relative_solution_error": 9.057363903519329e-13, "final_residual_norm": 1.2077826952874697e-05, "computed_residual_norm": 5.5223853351835624e-08, "relative_residual": 9.079320544087477e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1550, "iteration_efficiency": 0.0032258064516129032, "convergence_rate": 5.532672959660245, "operations_count": 539880, "algorithm_type": "conjugate_gradient", "matrix_size": "1550×1550", "implementation": "custom_cg", "nnz": 49338, "actual_sparsity_ratio": 0.020536108220603536, "target_sparsity_ratio": 0.01, "b_norm": 60823.773192805515, "solution_norm": 38.85389097337897, "true_solution_norm": 38.85389097337897, "theoretical_flops": 539880, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 60823.773192805515, "final_residual": 5.522373361757766e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1550, nnz≈24,025, k≤1550]", "theoretical_space_complexity": "O(nnz+N) [N=1550, memory≈31,775 elements]", "theoretical_memory_mb": 0.33998870849609375, "efficiency_ratio": 0.14506184895833332}, {"input_size": 1650, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656362.2517724, "execution_time_ms": 0.5590330809354782, "setup_time_ms": 63.95419919863343, "cleanup_time_ms": 25.5248686298728, "total_time_ms": 90.03810090944171, "baseline_memory_mb": 451.11328125, "peak_memory_mb": 453.42578125, "memory_increment_mb": 2.3125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.570653614972765e-11, "relative_solution_error": 8.660656865068529e-13, "final_residual_norm": 1.1767409171446561e-05, "computed_residual_norm": 5.965691646244975e-08, "relative_residual": 8.682808859973551e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1650, "iteration_efficiency": 0.0030303030303030303, "convergence_rate": 5.5477784432863615, "operations_count": 607360, "algorithm_type": "conjugate_gradient", "matrix_size": "1650×1650", "implementation": "custom_cg", "nnz": 55786, "actual_sparsity_ratio": 0.020490725436179982, "target_sparsity_ratio": 0.01, "b_norm": 68706.93277317114, "solution_norm": 41.22843879630498, "true_solution_norm": 41.228438796304985, "theoretical_flops": 607360, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 68706.93277317114, "final_residual": 5.96582680050042e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1650, nnz≈27,225, k≤1650]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈35,475 elements]", "theoretical_memory_mb": 0.38080596923828125, "efficiency_ratio": 0.1646728515625}, {"input_size": 1750, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656362.5149233, "execution_time_ms": 0.5780376493930817, "setup_time_ms": 72.06602115184069, "cleanup_time_ms": 25.237043853849173, "total_time_ms": 97.88110265508294, "baseline_memory_mb": 453.42578125, "peak_memory_mb": 456.25390625, "memory_increment_mb": 2.828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 3.003414686840355e-11, "relative_solution_error": 7.110274254560463e-13, "final_residual_norm": 1.4730294785465541e-05, "computed_residual_norm": 5.3126393125348606e-08, "relative_residual": 7.115711971354332e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1750, "iteration_efficiency": 0.002857142857142857, "convergence_rate": 5.558430671720635, "operations_count": 679400, "algorithm_type": "conjugate_gradient", "matrix_size": "1750×1750", "implementation": "custom_cg", "nnz": 62690, "actual_sparsity_ratio": 0.020470204081632654, "target_sparsity_ratio": 0.01, "b_norm": 74660.68516997193, "solution_norm": 42.240490019270254, "true_solution_norm": 42.240490019270254, "theoretical_flops": 679400, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 74660.68516997193, "final_residual": 5.312680678968906e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1750, nnz≈30,625, k≤1750]", "theoretical_space_complexity": "O(nnz+N) [N=1750, memory≈39,375 elements]", "theoretical_memory_mb": 0.42391204833984375, "efficiency_ratio": 0.14989155300414364}, {"input_size": 1850, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656362.7799914, "execution_time_ms": 0.6376158446073532, "setup_time_ms": 83.04066304117441, "cleanup_time_ms": 25.603273883461952, "total_time_ms": 109.28155276924372, "baseline_memory_mb": 456.25390625, "peak_memory_mb": 459.015625, "memory_increment_mb": 2.76171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.589954975817146e-11, "relative_solution_error": 3.6451856181073537e-13, "final_residual_norm": 6.4617294711300985e-06, "computed_residual_norm": 2.982063126503348e-08, "relative_residual": 3.6592893959608413e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1850, "iteration_efficiency": 0.002702702702702703, "convergence_rate": 5.739660801738552, "operations_count": 755080, "algorithm_type": "conjugate_gradient", "matrix_size": "1850×1850", "implementation": "custom_cg", "nnz": 69958, "actual_sparsity_ratio": 0.020440613586559533, "target_sparsity_ratio": 0.01, "b_norm": 81492.95679633804, "solution_norm": 43.61794274396044, "true_solution_norm": 43.61794274396044, "theoretical_flops": 755080, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 81492.95679633804, "final_residual": 2.982060678537482e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1850, nnz≈34,225, k≤1850]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈43,475 elements]", "theoretical_memory_mb": 0.46930694580078125, "efficiency_ratio": 0.16993292521216408}, {"input_size": 1950, "algorithm_name": "ConjugateGradient-scipy", "timestamp": 1753656363.06118, "execution_time_ms": 0.6703794933855534, "setup_time_ms": 92.08392957225442, "cleanup_time_ms": 26.487786322832108, "total_time_ms": 119.24209538847208, "baseline_memory_mb": 459.015625, "peak_memory_mb": 462.1015625, "memory_increment_mb": 3.0859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": false, "solution_error": 1.805395271478663e-11, "relative_solution_error": 4.0649955040773504e-13, "final_residual_norm": 7.547672627115616e-06, "computed_residual_norm": 3.565405217288169e-08, "relative_residual": 4.0762437133255136e-13, "converged": false, "iterations_performed": 5, "max_iterations": 1950, "iteration_efficiency": 0.002564102564102564, "convergence_rate": 5.715137737762896, "operations_count": 834120, "algorithm_type": "conjugate_gradient", "matrix_size": "1950×1950", "implementation": "custom_cg", "nnz": 77562, "actual_sparsity_ratio": 0.020397633136094676, "target_sparsity_ratio": 0.01, "b_norm": 87467.91085215588, "solution_norm": 44.413216931427854, "true_solution_norm": 44.41321693142786, "theoretical_flops": 834120, "storage_format": "CSR", "tolerance": 1e-06, "residual_history_length": 6, "initial_residual": 87467.91085215588, "final_residual": 3.565345607104048e-08}, "theoretical_time_complexity": "O(k*nnz) [N=1950, nnz≈38,025, k≤1950]", "theoretical_space_complexity": "O(nnz+N) [N=1950, memory≈47,775 elements]", "theoretical_memory_mb": 0.5169906616210938, "efficiency_ratio": 0.16753115110759495}]