input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_correctness_verified,custom_max_error,custom_operations_count,custom_algorithm_type,custom_matrix_size,custom_implementation,custom_nnz,custom_actual_sparsity_ratio,custom_target_sparsity_ratio,custom_vector_norm,custom_result_norm,custom_matrix_frobenius_norm,custom_theoretical_flops,custom_storage_format,custom_memory_efficiency
50,SpMV-scipy,1753651177.3265207,0.0543,0.4484,27.5646,28.0673,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=50, nnz≈250]","O(nnz+N) [N=50, memory≈350 elements]",0.00,0.0000,True,4.440892098500626e-16,500,spmv_scipy,50×50,scipy_sparse,250,0.1,0.1,6.218223073559123,7.932099411139785,9.028376627321862,500,CSR,0.1
250,SpMV-scipy,1753651177.5236742,0.0634,1.3945,27.6427,29.1006,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=250, nnz≈6,250]","O(nnz+N) [N=250, memory≈6,750 elements]",0.08,0.0000,True,0.0,12500,spmv_scipy,250×250,scipy_sparse,6250,0.1,0.1,16.23142233810483,46.16725070347335,45.28821169030181,12500,CSR,0.1
450,SpMV-scipy,1753651177.7192197,0.0845,4.6971,38.4747,43.2563,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=450, nnz≈20,250]","O(nnz+N) [N=450, memory≈21,150 elements]",0.24,0.0000,True,0.0,40500,spmv_scipy,450×450,scipy_sparse,20250,0.1,0.1,21.994760796152256,83.87813681772333,82.29871054743309,40500,CSR,0.1
650,SpMV-scipy,1753651177.9302251,0.1070,18.6566,38.5471,57.3108,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=650, nnz≈42,250]","O(nnz+N) [N=650, memory≈43,550 elements]",0.50,0.0000,True,0.0,84500,spmv_scipy,650×650,scipy_sparse,42250,0.1,0.1,26.586874473375342,124.87567352386547,118.52729950352465,84500,CSR,0.1
850,SpMV-scipy,1753651178.1768105,0.1359,33.1612,38.4862,71.7833,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=850, nnz≈72,250]","O(nnz+N) [N=850, memory≈73,950 elements]",0.84,0.0000,True,0.0,144500,spmv_scipy,850×850,scipy_sparse,72250,0.1,0.1,29.353462953592,162.49619953263795,155.06601079656363,144500,CSR,0.1
1050,SpMV-scipy,1753651178.4343376,0.1733,50.1450,38.3606,88.6789,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=1050, nnz≈110,250]","O(nnz+N) [N=1050, memory≈112,350 elements]",1.28,0.0000,True,0.0,220500,spmv_scipy,1050×1050,scipy_sparse,110250,0.1,0.1,31.450283149163084,180.54112392578423,192.2915259809192,220500,CSR,0.1
1250,SpMV-scipy,1753651178.705632,0.2265,63.5871,38.5822,102.3958,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=1250, nnz≈156,250]","O(nnz+N) [N=1250, memory≈158,750 elements]",1.81,0.0000,True,0.0,312500,spmv_scipy,1250×1250,scipy_sparse,156250,0.1,0.1,33.93925679643717,214.59041036945507,228.55574108111412,312500,CSR,0.1
1450,SpMV-scipy,1753651178.99295,0.2891,75.6008,38.4603,114.3502,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=1450, nnz≈210,250]","O(nnz+N) [N=1450, memory≈213,150 elements]",2.43,0.0000,True,0.0,420500,spmv_scipy,1450×1450,scipy_sparse,210250,0.1,0.1,37.4335888459925,275.4811291970337,264.3805487563863,420500,CSR,0.1
1650,SpMV-scipy,1753651179.294513,0.3586,93.7108,38.4689,132.5382,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=1650, nnz≈272,250]","O(nnz+N) [N=1650, memory≈275,550 elements]",3.15,0.0000,True,0.0,544500,spmv_scipy,1650×1650,scipy_sparse,272250,0.1,0.1,41.86485534603369,316.99432495881683,301.0980540693241,544500,CSR,0.1
1850,SpMV-scipy,1753651179.616653,0.4352,113.5706,38.9515,152.9572,471.54,471.54,0.00,0.00,,,,"O(nnz) [N=1850, nnz≈342,250]","O(nnz+N) [N=1850, memory≈345,950 elements]",3.95,0.0000,True,0.0,684500,spmv_scipy,1850×1850,scipy_sparse,342250,0.1,0.1,43.26560435529747,341.84689667573053,337.9048712837097,684500,CSR,0.1
2050,SpMV-scipy,1753651179.9600275,0.5179,136.6690,38.5527,175.7395,471.54,472.43,0.89,0.00,,,,"O(nnz) [N=2050, nnz≈420,250]","O(nnz+N) [N=2050, memory≈424,350 elements]",4.85,5.4439,True,0.0,840500,spmv_scipy,2050×2050,scipy_sparse,420250,0.1,0.1,44.79566838262384,369.38941617910785,374.5833018901906,840500,CSR,0.1
2250,SpMV-scipy,1753651180.3283987,0.6091,175.0715,38.5105,214.1911,472.43,480.87,8.44,0.00,,,,"O(nnz) [N=2250, nnz≈506,250]","O(nnz+N) [N=2250, memory≈510,750 elements]",5.84,0.6914,True,0.0,1012500,spmv_scipy,2250×2250,scipy_sparse,506250,0.1,0.1,48.206487730309554,473.13852279586365,410.7806651985443,1012500,CSR,0.1
2450,SpMV-scipy,1753651180.736067,0.7238,212.0628,40.5602,253.3467,480.87,490.21,9.34,0.00,,,,"O(nnz) [N=2450, nnz≈600,250]","O(nnz+N) [N=2450, memory≈605,150 elements]",6.92,0.7405,True,0.0,1200500,spmv_scipy,2450×2450,scipy_sparse,600250,0.1,0.1,48.49073513353407,423.09723076795143,447.04768279328766,1200500,CSR,0.1
2650,SpMV-scipy,1753651181.1829426,0.8179,264.3390,38.4873,303.6442,430.95,460.16,29.21,0.00,,,,"O(nnz) [N=2650, nnz≈702,250]","O(nnz+N) [N=2650, memory≈707,550 elements]",8.09,0.2768,True,0.0,1404500,spmv_scipy,2650×2650,scipy_sparse,702250,0.1,0.1,50.513886005088125,452.150217124165,484.5369829362244,1404500,CSR,0.1
2850,SpMV-scipy,1753651181.681397,0.9376,312.2040,38.4716,351.6132,460.16,464.86,4.70,0.00,,,,"O(nnz) [N=2850, nnz≈812,250]","O(nnz+N) [N=2850, memory≈817,950 elements]",9.35,1.9913,True,0.0,1624500,spmv_scipy,2850×2850,scipy_sparse,812250,0.1,0.1,53.65916203377644,510.6152081205109,519.8914890350728,1624500,CSR,0.1
3050,SpMV-scipy,1753651182.2280982,1.1030,356.0365,38.5550,395.6945,464.86,469.73,4.88,0.00,,,,"O(nnz) [N=3050, nnz≈930,250]","O(nnz+N) [N=3050, memory≈936,350 elements]",10.70,2.1957,True,0.0,1860500,spmv_scipy,3050×3050,scipy_sparse,930250,0.1,0.1,55.67590540231945,558.735014074936,556.6297286229354,1860500,CSR,0.1
3250,SpMV-scipy,1753651182.820441,1.2095,420.4741,38.5585,460.2421,469.73,475.14,5.40,0.00,,,,"O(nnz) [N=3250, nnz≈1,056,250]","O(nnz+N) [N=3250, memory≈1,062,750 elements]",12.15,2.2490,True,0.0,2112500,spmv_scipy,3250×3250,scipy_sparse,1056250,0.1,0.1,56.385378224401244,608.4597170942111,593.7040052743052,2112500,CSR,0.1
3450,SpMV-scipy,1753651183.4784825,1.3489,480.2914,38.4641,520.1044,475.14,480.68,5.54,0.00,,,,"O(nnz) [N=3450, nnz≈1,190,250]","O(nnz+N) [N=3450, memory≈1,197,150 elements]",13.69,2.4693,True,0.0,2380500,spmv_scipy,3450×3450,scipy_sparse,1190250,0.1,0.1,58.756696725148686,629.0539365387525,629.8673632308679,2380500,CSR,0.1
3650,SpMV-scipy,1753651184.197203,1.5339,552.8446,40.7131,595.0916,480.68,486.56,5.88,0.00,,,,"O(nnz) [N=3650, nnz≈1,332,250]","O(nnz+N) [N=3650, memory≈1,339,550 elements]",15.32,2.6052,True,0.0,2664500,spmv_scipy,3650×3650,scipy_sparse,1332250,0.1,0.1,60.02604616122741,644.039675005461,666.3780129592307,2664500,CSR,0.1
3850,SpMV-scipy,1753651184.9922435,1.6838,621.9631,39.9149,663.5618,430.95,492.96,62.01,0.00,,,,"O(nnz) [N=3850, nnz≈1,482,250]","O(nnz+N) [N=3850, memory≈1,489,950 elements]",17.04,0.2747,True,0.0,2964500,spmv_scipy,3850×3850,scipy_sparse,1482250,0.1,0.1,62.58863040358227,672.1499662772828,702.6208055588158,2964500,CSR,0.1
4050,SpMV-scipy,1753651185.8565745,1.8329,698.8220,39.9242,740.5792,430.95,499.60,68.65,0.00,,,,"O(nnz) [N=4050, nnz≈1,640,250]","O(nnz+N) [N=4050, memory≈1,648,350 elements]",18.85,0.2746,True,0.0,3280500,spmv_scipy,4050×4050,scipy_sparse,1640250,0.1,0.1,62.603804977711825,707.2922072731257,739.3018910984398,3280500,CSR,0.1
4250,SpMV-scipy,1753651186.7992277,2.0513,791.1131,38.5393,831.7036,430.95,479.05,48.11,0.00,,,,"O(nnz) [N=4250, nnz≈1,806,250]","O(nnz+N) [N=4250, memory≈1,814,750 elements]",20.75,0.4314,True,0.0,3612500,spmv_scipy,4250×4250,scipy_sparse,1806250,0.1,0.1,65.35218175112911,749.0313596005246,776.1804501507238,3612500,CSR,0.1
4450,SpMV-scipy,1753651187.834297,2.2773,869.7806,40.5459,912.6037,479.05,513.77,34.71,0.00,,,,"O(nnz) [N=4450, nnz≈1,980,250]","O(nnz+N) [N=4450, memory≈1,989,150 elements]",22.75,0.6553,True,0.0,3960500,spmv_scipy,4450×4450,scipy_sparse,1980250,0.1,0.1,67.52179003285211,796.0298078730307,812.3776311262926,3960500,CSR,0.1
4650,SpMV-scipy,1753651188.952023,2.4155,981.7962,39.7038,1023.9155,430.95,488.56,57.61,0.00,,,,"O(nnz) [N=4650, nnz≈2,162,250]","O(nnz+N) [N=4650, memory≈2,171,550 elements]",24.83,0.4310,True,0.0,4324500,spmv_scipy,4650×4650,scipy_sparse,2162250,0.1,0.1,68.53755885279584,815.7766033000802,848.9535448332157,4324500,CSR,0.1
4850,SpMV-scipy,1753651190.1825216,2.7284,1093.6804,39.8121,1136.2209,430.95,493.63,62.68,0.00,,,,"O(nnz) [N=4850, nnz≈2,352,250]","O(nnz+N) [N=4850, memory≈2,361,950 elements]",27.01,0.4309,True,0.0,4704500,spmv_scipy,4850×4850,scipy_sparse,2352250,0.1,0.1,70.05155400501597,862.7717116700629,885.2552922689824,4704500,CSR,0.1
5050,SpMV-scipy,1753651191.5261862,2.8767,1175.1539,39.7792,1217.8099,430.95,498.92,67.97,0.00,,,,"O(nnz) [N=5050, nnz≈2,550,250]","O(nnz+N) [N=5050, memory≈2,560,350 elements]",29.28,0.4308,True,0.0,5100500,spmv_scipy,5050×5050,scipy_sparse,2550250,0.1,0.1,69.28668652924382,1037.22756198876,922.1232369424766,5100500,CSR,0.1
5250,SpMV-scipy,1753651192.9529107,3.1722,1278.1826,47.9202,1329.2750,430.95,504.42,73.47,0.00,,,,"O(nnz) [N=5250, nnz≈2,756,250]","O(nnz+N) [N=5250, memory≈2,766,750 elements]",31.64,0.4307,True,0.0,5512500,spmv_scipy,5250×5250,scipy_sparse,2756250,0.1,0.1,73.09625531050156,1045.2455308215406,958.4705431948332,5512500,CSR,0.1
5450,SpMV-scipy,1753651194.493577,3.4246,1384.2411,39.8051,1427.4708,430.95,510.14,79.19,0.00,,,,"O(nnz) [N=5450, nnz≈2,970,250]","O(nnz+N) [N=5450, memory≈2,981,150 elements]",34.10,0.4306,True,0.0,5940500,spmv_scipy,5450×5450,scipy_sparse,2970250,0.1,0.1,73.1120273263701,1015.7474060752361,995.0821984664537,5940500,CSR,0.1
5650,SpMV-scipy,1753651196.134422,3.6794,1505.1709,39.7660,1548.6163,430.95,516.07,85.12,0.00,,,,"O(nnz) [N=5650, nnz≈3,192,250]","O(nnz+N) [N=5650, memory≈3,203,550 elements]",36.64,0.4305,True,0.0,6384500,spmv_scipy,5650×5650,scipy_sparse,3192250,0.1,0.1,77.59356270743513,1023.1821138993121,1031.4036784258085,6384500,CSR,0.1
5850,SpMV-scipy,1753651197.8983207,3.9075,1612.7081,39.7014,1656.3171,430.95,522.21,91.26,0.00,,,,"O(nnz) [N=5850, nnz≈3,422,250]","O(nnz+N) [N=5850, memory≈3,433,950 elements]",39.28,0.4304,True,0.0,6844500,spmv_scipy,5850×5850,scipy_sparse,3422250,0.1,0.1,77.20734885063318,1047.1486702389589,1067.9919364776958,6844500,CSR,0.1
6050,SpMV-scipy,1753651199.7803679,4.1865,1745.8663,38.8974,1788.9502,430.95,500.64,69.69,0.00,,,,"O(nnz) [N=6050, nnz≈3,660,250]","O(nnz+N) [N=6050, memory≈3,672,350 elements]",42.00,0.6027,True,0.0,7320500,spmv_scipy,6050×6050,scipy_sparse,3660250,0.1,0.1,77.72695008655155,1076.4300384708486,1104.0341766261856,7320500,CSR,0.1
6250,SpMV-scipy,1753651201.7875254,4.3491,1875.1383,40.4074,1919.8948,472.71,535.13,62.42,0.00,,,,"O(nnz) [N=6250, nnz≈3,906,250]","O(nnz+N) [N=6250, memory≈3,918,750 elements]",44.82,0.7181,True,0.0,7812500,spmv_scipy,6250×6250,scipy_sparse,3906250,0.1,0.1,79.28669859475255,1146.0655035737607,1140.152855706166,7812500,CSR,0.1
6450,SpMV-scipy,1753651203.9272513,4.7659,2029.2044,38.8263,2072.7966,430.95,510.18,79.23,0.00,,,,"O(nnz) [N=6450, nnz≈4,160,250]","O(nnz+N) [N=6450, memory≈4,173,150 elements]",47.73,0.6025,True,0.0,8320500,spmv_scipy,6450×6450,scipy_sparse,4160250,0.1,0.1,79.92964364566154,1208.6237627872765,1177.2701052321609,8320500,CSR,0.1
6650,SpMV-scipy,1753651206.2224414,5.0841,2157.2025,40.3425,2202.6291,478.43,548.91,70.48,0.00,,,,"O(nnz) [N=6650, nnz≈4,422,250]","O(nnz+N) [N=6650, memory≈4,435,550 elements]",50.74,0.7199,True,0.0,8844500,spmv_scipy,6650×6650,scipy_sparse,4422250,0.1,0.1,80.7637214255453,1194.6854124932477,1213.9088425985346,8844500,CSR,0.1
6850,SpMV-scipy,1753651208.6497014,5.3736,2313.6325,38.9726,2357.9787,430.95,520.32,89.37,0.00,,,,"O(nnz) [N=6850, nnz≈4,692,250]","O(nnz+N) [N=6850, memory≈4,705,950 elements]",53.83,0.6023,True,0.0,9384500,spmv_scipy,6850×6850,scipy_sparse,4692250,0.1,0.1,83.03095599858601,1201.6556357903632,1250.7383877528885,9384500,CSR,0.1
7050,SpMV-scipy,1753651211.234572,5.7495,2448.2876,40.5715,2494.6086,484.52,563.54,79.02,0.00,,,,"O(nnz) [N=7050, nnz≈4,970,250]","O(nnz+N) [N=7050, memory≈4,984,350 elements]",57.01,0.7215,True,0.0,9940500,spmv_scipy,7050×7050,scipy_sparse,4970250,0.1,0.1,83.71853762074879,1250.0585911835708,1286.9389299197715,9940500,CSR,0.1
7250,SpMV-scipy,1753651213.9586334,6.0520,2605.4304,38.7072,2650.1896,430.95,531.08,100.13,0.00,,,,"O(nnz) [N=7250, nnz≈5,256,250]","O(nnz+N) [N=7250, memory≈5,270,750 elements]",60.29,0.6021,True,0.0,10512500,spmv_scipy,7250×7250,scipy_sparse,5256250,0.1,0.1,84.48722352437153,1283.08508058914,1323.4702991516342,10512500,CSR,0.1
7450,SpMV-scipy,1753651216.8406062,6.3819,2742.7675,40.1746,2789.3241,490.98,579.03,88.05,0.00,,,,"O(nnz) [N=7450, nnz≈5,550,250]","O(nnz+N) [N=7450, memory≈5,565,150 elements]",63.66,0.7230,True,0.0,11100500,spmv_scipy,7450×7450,scipy_sparse,5550250,0.1,0.1,87.11066718002652,1340.5027465726562,1359.9408623840739,11100500,CSR,0.1
7650,SpMV-scipy,1753651219.8639312,6.6681,3021.6954,39.6839,3068.0474,431.00,542.45,111.45,0.00,,,,"O(nnz) [N=7650, nnz≈5,852,250]","O(nnz+N) [N=7650, memory≈5,867,550 elements]",67.12,0.6023,True,0.0,11704500,spmv_scipy,7650×7650,scipy_sparse,5852250,0.1,0.1,88.02304619512084,1396.9133916149274,1396.7262106346961,11704500,CSR,0.1
7850,SpMV-scipy,1753651223.1795084,7.1273,3068.6290,39.6844,3115.4407,431.01,548.36,117.35,0.00,,,,"O(nnz) [N=7850, nnz≈6,162,250]","O(nnz+N) [N=7850, memory≈6,177,950 elements]",70.67,0.6022,True,0.0,12324500,spmv_scipy,7850×7850,scipy_sparse,6162250,0.1,0.1,89.04264606488213,1412.9902181737975,1433.1881802094288,12324500,CSR,0.1
8050,SpMV-scipy,1753651226.5490358,7.1830,3228.0407,39.7928,3275.0165,431.01,554.43,123.42,0.00,,,,"O(nnz) [N=8050, nnz≈6,480,250]","O(nnz+N) [N=8050, memory≈6,496,350 elements]",74.31,0.6021,True,0.0,12960500,spmv_scipy,8050×8050,scipy_sparse,6480250,0.1,0.1,89.12353667321823,1470.7048946586294,1469.36047945575,12960500,CSR,0.1
8250,SpMV-scipy,1753651230.0645478,7.7516,3383.3006,39.9338,3430.9861,431.01,560.64,129.64,0.00,,,,"O(nnz) [N=8250, nnz≈6,806,250]","O(nnz+N) [N=8250, memory≈6,822,750 elements]",78.05,0.6021,True,0.0,13612500,spmv_scipy,8250×8250,scipy_sparse,6806250,0.1,0.1,90.50461818514009,1535.9109142438808,1505.6284232385194,13612500,CSR,0.1
8450,SpMV-scipy,1753651233.7395465,7.9593,3579.9525,39.7964,3627.7082,431.01,567.01,136.00,0.00,,,,"O(nnz) [N=8450, nnz≈7,140,250]","O(nnz+N) [N=8450, memory≈7,157,150 elements]",81.87,0.6020,True,0.0,14280500,spmv_scipy,8450×8450,scipy_sparse,7140250,0.1,0.1,91.55047381104333,1456.882397736049,1542.3193684769258,14280500,CSR,0.1
8650,SpMV-scipy,1753651237.612737,8.2904,3779.8336,39.7738,3827.8978,431.01,573.54,142.52,0.00,,,,"O(nnz) [N=8650, nnz≈7,482,250]","O(nnz+N) [N=8650, memory≈7,499,550 elements]",85.79,0.6020,True,0.0,14964500,spmv_scipy,8650×8650,scipy_sparse,7482250,0.1,0.1,93.77819460429141,1524.2445989694613,1579.4997238429914,14964500,CSR,0.1
8850,SpMV-scipy,1753651241.6891422,8.8379,4192.4382,39.9873,4241.2634,431.01,580.21,149.20,0.00,,,,"O(nnz) [N=8850, nnz≈7,832,250]","O(nnz+N) [N=8850, memory≈7,849,950 elements]",89.80,0.6019,True,0.0,15664500,spmv_scipy,8850×8850,scipy_sparse,7832250,0.1,0.1,93.50077741295098,1594.18905994289,1615.9142387553347,15664500,CSR,0.1
9050,SpMV-scipy,1753651246.1824346,9.5133,4211.0441,39.7392,4260.2966,431.02,587.04,156.02,0.00,,,,"O(nnz) [N=9050, nnz≈8,190,250]","O(nnz+N) [N=9050, memory≈8,208,350 elements]",93.90,0.6018,True,0.0,16380500,spmv_scipy,9050×9050,scipy_sparse,8190250,0.1,0.1,94.78054759305171,1621.7971439918329,1652.205429578378,16380500,CSR,0.1
9250,SpMV-scipy,1753651250.71273,9.8095,4420.8042,39.3704,4469.9841,431.02,528.94,97.92,0.00,,,,"O(nnz) [N=9250, nnz≈8,556,250]","O(nnz+N) [N=9250, memory≈8,574,750 elements]",98.09,1.0018,True,0.0,17112500,spmv_scipy,9250×9250,scipy_sparse,8556250,0.1,0.1,97.67332731894659,1671.8473785926226,1688.4320590816792,17112500,CSR,0.1
9450,SpMV-scipy,1753651255.4416733,10.2561,4617.9266,39.9239,4668.1066,431.04,533.23,102.20,0.00,,,,"O(nnz) [N=9450, nnz≈8,930,250]","O(nnz+N) [N=9450, memory≈8,949,150 elements]",102.38,1.0018,True,0.0,17860500,spmv_scipy,9450×9450,scipy_sparse,8930250,0.1,0.1,98.27033198268313,1662.8407052701568,1724.979464960153,17860500,CSR,0.1
9650,SpMV-scipy,1753651260.3715959,10.5973,4839.5716,39.4741,4889.6429,431.04,537.61,106.57,0.00,,,,"O(nnz) [N=9650, nnz≈9,312,250]","O(nnz+N) [N=9650, memory≈9,331,550 elements]",106.75,1.0017,True,0.0,18624500,spmv_scipy,9650×9650,scipy_sparse,9312250,0.1,0.1,98.65014061841552,1693.7217386667862,1761.6309211993962,18624500,CSR,0.1
9850,SpMV-scipy,1753651265.525698,11.1461,5046.3287,39.3898,5096.8646,431.04,542.08,111.04,0.00,,,,"O(nnz) [N=9850, nnz≈9,702,250]","O(nnz+N) [N=9850, memory≈9,721,950 elements]",111.22,1.0017,True,0.0,19404500,spmv_scipy,9850×9850,scipy_sparse,9702250,0.1,0.1,97.92975604747116,1802.752997687001,1798.36803659023,19404500,CSR,0.1
