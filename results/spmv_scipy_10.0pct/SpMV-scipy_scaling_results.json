[{"input_size": 50, "algorithm_name": "SpMV-scipy", "timestamp": 1753651177.3265207, "execution_time_ms": 0.05425913259387016, "setup_time_ms": 0.44839829206466675, "cleanup_time_ms": 27.56463596597314, "total_time_ms": 28.067293390631676, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 4.440892098500626e-16, "operations_count": 500, "algorithm_type": "spmv_scipy", "matrix_size": "50×50", "implementation": "scipy_sparse", "nnz": 250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 6.218223073559123, "result_norm": 7.932099411139785, "matrix_frobenius_norm": 9.028376627321862, "theoretical_flops": 500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=50, nnz≈250]", "theoretical_space_complexity": "O(nnz+N) [N=50, memory≈350 elements]", "theoretical_memory_mb": 0.003818511962890625, "efficiency_ratio": 0.0}, {"input_size": 250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651177.5236742, "execution_time_ms": 0.06337650120258331, "setup_time_ms": 1.3945451937615871, "cleanup_time_ms": 27.64270594343543, "total_time_ms": 29.1006276383996, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 12500, "algorithm_type": "spmv_scipy", "matrix_size": "250×250", "implementation": "scipy_sparse", "nnz": 6250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 16.23142233810483, "result_norm": 46.16725070347335, "matrix_frobenius_norm": 45.28821169030181, "theoretical_flops": 12500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=250, nnz≈6,250]", "theoretical_space_complexity": "O(nnz+N) [N=250, memory≈6,750 elements]", "theoretical_memory_mb": 0.07629776000976562, "efficiency_ratio": 0.0}, {"input_size": 450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651177.7192197, "execution_time_ms": 0.08452702313661575, "setup_time_ms": 4.697107709944248, "cleanup_time_ms": 38.47469808533788, "total_time_ms": 43.25633281841874, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 40500, "algorithm_type": "spmv_scipy", "matrix_size": "450×450", "implementation": "scipy_sparse", "nnz": 20250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 21.994760796152256, "result_norm": 83.87813681772333, "matrix_frobenius_norm": 82.29871054743309, "theoretical_flops": 40500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=450, nnz≈20,250]", "theoretical_space_complexity": "O(nnz+N) [N=450, memory≈21,150 elements]", "theoretical_memory_mb": 0.24032974243164062, "efficiency_ratio": 0.0}, {"input_size": 650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651177.9302251, "execution_time_ms": 0.10703392326831818, "setup_time_ms": 18.65661283954978, "cleanup_time_ms": 38.54712191969156, "total_time_ms": 57.31076868250966, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 84500, "algorithm_type": "spmv_scipy", "matrix_size": "650×650", "implementation": "scipy_sparse", "nnz": 42250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 26.586874473375342, "result_norm": 124.87567352386547, "matrix_frobenius_norm": 118.52729950352465, "theoretical_flops": 84500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=650, nnz≈42,250]", "theoretical_space_complexity": "O(nnz+N) [N=650, memory≈43,550 elements]", "theoretical_memory_mb": 0.4959144592285156, "efficiency_ratio": 0.0}, {"input_size": 850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651178.1768105, "execution_time_ms": 0.13586236163973808, "setup_time_ms": 33.16123317927122, "cleanup_time_ms": 38.48617011681199, "total_time_ms": 71.78326565772295, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 144500, "algorithm_type": "spmv_scipy", "matrix_size": "850×850", "implementation": "scipy_sparse", "nnz": 72250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 29.353462953592, "result_norm": 162.49619953263795, "matrix_frobenius_norm": 155.06601079656363, "theoretical_flops": 144500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=850, nnz≈72,250]", "theoretical_space_complexity": "O(nnz+N) [N=850, memory≈73,950 elements]", "theoretical_memory_mb": 0.8430519104003906, "efficiency_ratio": 0.0}, {"input_size": 1050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651178.4343376, "execution_time_ms": 0.17332592979073524, "setup_time_ms": 50.1449559815228, "cleanup_time_ms": 38.36058499291539, "total_time_ms": 88.67886690422893, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 220500, "algorithm_type": "spmv_scipy", "matrix_size": "1050×1050", "implementation": "scipy_sparse", "nnz": 110250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 31.450283149163084, "result_norm": 180.54112392578423, "matrix_frobenius_norm": 192.2915259809192, "theoretical_flops": 220500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=1050, nnz≈110,250]", "theoretical_space_complexity": "O(nnz+N) [N=1050, memory≈112,350 elements]", "theoretical_memory_mb": 1.2817420959472656, "efficiency_ratio": 0.0}, {"input_size": 1250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651178.705632, "execution_time_ms": 0.2265394665300846, "setup_time_ms": 63.58705973252654, "cleanup_time_ms": 38.58220484107733, "total_time_ms": 102.39580404013395, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 312500, "algorithm_type": "spmv_scipy", "matrix_size": "1250×1250", "implementation": "scipy_sparse", "nnz": 156250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 33.93925679643717, "result_norm": 214.59041036945507, "matrix_frobenius_norm": 228.55574108111412, "theoretical_flops": 312500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=1250, nnz≈156,250]", "theoretical_space_complexity": "O(nnz+N) [N=1250, memory≈158,750 elements]", "theoretical_memory_mb": 1.8119850158691406, "efficiency_ratio": 0.0}, {"input_size": 1450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651178.99295, "execution_time_ms": 0.2891041338443756, "setup_time_ms": 75.60081407427788, "cleanup_time_ms": 38.46026211977005, "total_time_ms": 114.3501803278923, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 420500, "algorithm_type": "spmv_scipy", "matrix_size": "1450×1450", "implementation": "scipy_sparse", "nnz": 210250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 37.4335888459925, "result_norm": 275.4811291970337, "matrix_frobenius_norm": 264.3805487563863, "theoretical_flops": 420500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=1450, nnz≈210,250]", "theoretical_space_complexity": "O(nnz+N) [N=1450, memory≈213,150 elements]", "theoretical_memory_mb": 2.4337806701660156, "efficiency_ratio": 0.0}, {"input_size": 1650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651179.294513, "execution_time_ms": 0.3585618920624256, "setup_time_ms": 93.71076291427016, "cleanup_time_ms": 38.468885235488415, "total_time_ms": 132.538210041821, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 544500, "algorithm_type": "spmv_scipy", "matrix_size": "1650×1650", "implementation": "scipy_sparse", "nnz": 272250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 41.86485534603369, "result_norm": 316.99432495881683, "matrix_frobenius_norm": 301.0980540693241, "theoretical_flops": 544500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=1650, nnz≈272,250]", "theoretical_space_complexity": "O(nnz+N) [N=1650, memory≈275,550 elements]", "theoretical_memory_mb": 3.1471290588378906, "efficiency_ratio": 0.0}, {"input_size": 1850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651179.616653, "execution_time_ms": 0.4351661540567875, "setup_time_ms": 113.57056256383657, "cleanup_time_ms": 38.951483089476824, "total_time_ms": 152.9572118073702, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 471.53515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 684500, "algorithm_type": "spmv_scipy", "matrix_size": "1850×1850", "implementation": "scipy_sparse", "nnz": 342250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 43.26560435529747, "result_norm": 341.84689667573053, "matrix_frobenius_norm": 337.9048712837097, "theoretical_flops": 684500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=1850, nnz≈342,250]", "theoretical_space_complexity": "O(nnz+N) [N=1850, memory≈345,950 elements]", "theoretical_memory_mb": 3.9520301818847656, "efficiency_ratio": 0.0}, {"input_size": 2050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651179.9600275, "execution_time_ms": 0.5178652703762054, "setup_time_ms": 136.66896987706423, "cleanup_time_ms": 38.55268005281687, "total_time_ms": 175.7395152002573, "baseline_memory_mb": 471.53515625, "peak_memory_mb": 472.42578125, "memory_increment_mb": 0.890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 840500, "algorithm_type": "spmv_scipy", "matrix_size": "2050×2050", "implementation": "scipy_sparse", "nnz": 420250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 44.79566838262384, "result_norm": 369.38941617910785, "matrix_frobenius_norm": 374.5833018901906, "theoretical_flops": 840500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=2050, nnz≈420,250]", "theoretical_space_complexity": "O(nnz+N) [N=2050, memory≈424,350 elements]", "theoretical_memory_mb": 4.848484039306641, "efficiency_ratio": 5.443911903782895}, {"input_size": 2250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651180.3283987, "execution_time_ms": 0.6090724840760231, "setup_time_ms": 175.0715160742402, "cleanup_time_ms": 38.51054375991225, "total_time_ms": 214.19113231822848, "baseline_memory_mb": 472.42578125, "peak_memory_mb": 480.8671875, "memory_increment_mb": 8.44140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1012500, "algorithm_type": "spmv_scipy", "matrix_size": "2250×2250", "implementation": "scipy_sparse", "nnz": 506250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 48.206487730309554, "result_norm": 473.13852279586365, "matrix_frobenius_norm": 410.7806651985443, "theoretical_flops": 1012500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=2250, nnz≈506,250]", "theoretical_space_complexity": "O(nnz+N) [N=2250, memory≈510,750 elements]", "theoretical_memory_mb": 5.836490631103516, "efficiency_ratio": 0.6914121247397038}, {"input_size": 2450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651180.736067, "execution_time_ms": 0.7237827405333519, "setup_time_ms": 212.06276584416628, "cleanup_time_ms": 40.56019987910986, "total_time_ms": 253.3467484638095, "baseline_memory_mb": 480.8671875, "peak_memory_mb": 490.20703125, "memory_increment_mb": 9.33984375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1200500, "algorithm_type": "spmv_scipy", "matrix_size": "2450×2450", "implementation": "scipy_sparse", "nnz": 600250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 48.49073513353407, "result_norm": 423.09723076795143, "matrix_frobenius_norm": 447.04768279328766, "theoretical_flops": 1200500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=2450, nnz≈600,250]", "theoretical_space_complexity": "O(nnz+N) [N=2450, memory≈605,150 elements]", "theoretical_memory_mb": 6.916049957275391, "efficiency_ratio": 0.7404888285497699}, {"input_size": 2650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651181.1829426, "execution_time_ms": 0.8178846910595894, "setup_time_ms": 264.3390060402453, "cleanup_time_ms": 38.48734591156244, "total_time_ms": 303.6442366428673, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 460.1640625, "memory_increment_mb": 29.21484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1404500, "algorithm_type": "spmv_scipy", "matrix_size": "2650×2650", "implementation": "scipy_sparse", "nnz": 702250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 50.513886005088125, "result_norm": 452.150217124165, "matrix_frobenius_norm": 484.5369829362244, "theoretical_flops": 1404500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=2650, nnz≈702,250]", "theoretical_space_complexity": "O(nnz+N) [N=2650, memory≈707,550 elements]", "theoretical_memory_mb": 8.087162017822266, "efficiency_ratio": 0.2768168841506217}, {"input_size": 2850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651181.681397, "execution_time_ms": 0.9376253932714462, "setup_time_ms": 312.2039958834648, "cleanup_time_ms": 38.471564184874296, "total_time_ms": 351.61318546161056, "baseline_memory_mb": 460.1640625, "peak_memory_mb": 464.859375, "memory_increment_mb": 4.6953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1624500, "algorithm_type": "spmv_scipy", "matrix_size": "2850×2850", "implementation": "scipy_sparse", "nnz": 812250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 53.65916203377644, "result_norm": 510.6152081205109, "matrix_frobenius_norm": 519.8914890350728, "theoretical_flops": 1624500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=2850, nnz≈812,250]", "theoretical_space_complexity": "O(nnz+N) [N=2850, memory≈817,950 elements]", "theoretical_memory_mb": 9.34982681274414, "efficiency_ratio": 1.9913108686044094}, {"input_size": 3050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651182.2280982, "execution_time_ms": 1.1030028574168682, "setup_time_ms": 356.0364870354533, "cleanup_time_ms": 38.555033039301634, "total_time_ms": 395.6945229321718, "baseline_memory_mb": 464.859375, "peak_memory_mb": 469.734375, "memory_increment_mb": 4.875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 1860500, "algorithm_type": "spmv_scipy", "matrix_size": "3050×3050", "implementation": "scipy_sparse", "nnz": 930250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 55.67590540231945, "result_norm": 558.735014074936, "matrix_frobenius_norm": 556.6297286229354, "theoretical_flops": 1860500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=3050, nnz≈930,250]", "theoretical_space_complexity": "O(nnz+N) [N=3050, memory≈936,350 elements]", "theoretical_memory_mb": 10.704044342041016, "efficiency_ratio": 2.195701403495593}, {"input_size": 3250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651182.820441, "execution_time_ms": 1.2094628065824509, "setup_time_ms": 420.47411436215043, "cleanup_time_ms": 38.5584831237793, "total_time_ms": 460.2420602925122, "baseline_memory_mb": 469.734375, "peak_memory_mb": 475.13671875, "memory_increment_mb": 5.40234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 2112500, "algorithm_type": "spmv_scipy", "matrix_size": "3250×3250", "implementation": "scipy_sparse", "nnz": 1056250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 56.385378224401244, "result_norm": 608.4597170942111, "matrix_frobenius_norm": 593.7040052743052, "theoretical_flops": 2112500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=3250, nnz≈1,056,250]", "theoretical_space_complexity": "O(nnz+N) [N=3250, memory≈1,062,750 elements]", "theoretical_memory_mb": 12.14981460571289, "efficiency_ratio": 2.2489895437906724}, {"input_size": 3450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651183.4784825, "execution_time_ms": 1.3489055447280407, "setup_time_ms": 480.29143875464797, "cleanup_time_ms": 38.46406610682607, "total_time_ms": 520.1044104062021, "baseline_memory_mb": 475.13671875, "peak_memory_mb": 480.6796875, "memory_increment_mb": 5.54296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 2380500, "algorithm_type": "spmv_scipy", "matrix_size": "3450×3450", "implementation": "scipy_sparse", "nnz": 1190250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 58.756696725148686, "result_norm": 629.0539365387525, "matrix_frobenius_norm": 629.8673632308679, "theoretical_flops": 2380500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=3450, nnz≈1,190,250]", "theoretical_space_complexity": "O(nnz+N) [N=3450, memory≈1,197,150 elements]", "theoretical_memory_mb": 13.687137603759766, "efficiency_ratio": 2.469279229430937}, {"input_size": 3650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651184.197203, "execution_time_ms": 1.5339434146881104, "setup_time_ms": 552.8445690870285, "cleanup_time_ms": 40.71312304586172, "total_time_ms": 595.0916355475783, "baseline_memory_mb": 480.6796875, "peak_memory_mb": 486.55859375, "memory_increment_mb": 5.87890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 2664500, "algorithm_type": "spmv_scipy", "matrix_size": "3650×3650", "implementation": "scipy_sparse", "nnz": 1332250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 60.02604616122741, "result_norm": 644.039675005461, "matrix_frobenius_norm": 666.3780129592307, "theoretical_flops": 2664500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=3650, nnz≈1,332,250]", "theoretical_space_complexity": "O(nnz+N) [N=3650, memory≈1,339,550 elements]", "theoretical_memory_mb": 15.31601333618164, "efficiency_ratio": 2.6052487801079733}, {"input_size": 3850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651184.9922435, "execution_time_ms": 1.683803927153349, "setup_time_ms": 621.963098179549, "cleanup_time_ms": 39.9148790165782, "total_time_ms": 663.5617811232805, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 492.95703125, "memory_increment_mb": 62.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 2964500, "algorithm_type": "spmv_scipy", "matrix_size": "3850×3850", "implementation": "scipy_sparse", "nnz": 1482250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 62.58863040358227, "result_norm": 672.1499662772828, "matrix_frobenius_norm": 702.6208055588158, "theoretical_flops": 2964500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=3850, nnz≈1,482,250]", "theoretical_space_complexity": "O(nnz+N) [N=3850, memory≈1,489,950 elements]", "theoretical_memory_mb": 17.036441802978516, "efficiency_ratio": 0.27474669910309313}, {"input_size": 4050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651185.8565745, "execution_time_ms": 1.8329054117202759, "setup_time_ms": 698.822018224746, "cleanup_time_ms": 39.92424113675952, "total_time_ms": 740.5791647732258, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 499.59765625, "memory_increment_mb": 68.6484375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 3280500, "algorithm_type": "spmv_scipy", "matrix_size": "4050×4050", "implementation": "scipy_sparse", "nnz": 1640250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 62.603804977711825, "result_norm": 707.2922072731257, "matrix_frobenius_norm": 739.3018910984398, "theoretical_flops": 3280500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=4050, nnz≈1,640,250]", "theoretical_space_complexity": "O(nnz+N) [N=4050, memory≈1,648,350 elements]", "theoretical_memory_mb": 18.84842300415039, "efficiency_ratio": 0.27456448668843175}, {"input_size": 4250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651186.7992277, "execution_time_ms": 2.051295433193445, "setup_time_ms": 791.1130702123046, "cleanup_time_ms": 38.53928064927459, "total_time_ms": 831.7036462947726, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 479.0546875, "memory_increment_mb": 48.10546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 3612500, "algorithm_type": "spmv_scipy", "matrix_size": "4250×4250", "implementation": "scipy_sparse", "nnz": 1806250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 65.35218175112911, "result_norm": 749.0313596005246, "matrix_frobenius_norm": 776.1804501507238, "theoretical_flops": 3612500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=4250, nnz≈1,806,250]", "theoretical_space_complexity": "O(nnz+N) [N=4250, memory≈1,814,750 elements]", "theoretical_memory_mb": 20.751956939697266, "efficiency_ratio": 0.4313845697574097}, {"input_size": 4450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651187.834297, "execution_time_ms": 2.277262881398201, "setup_time_ms": 869.7805991396308, "cleanup_time_ms": 40.54587706923485, "total_time_ms": 912.6037390902638, "baseline_memory_mb": 479.0546875, "peak_memory_mb": 513.765625, "memory_increment_mb": 34.7109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 3960500, "algorithm_type": "spmv_scipy", "matrix_size": "4450×4450", "implementation": "scipy_sparse", "nnz": 1980250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 67.52179003285211, "result_norm": 796.0298078730307, "matrix_frobenius_norm": 812.3776311262926, "theoretical_flops": 3960500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=4450, nnz≈1,980,250]", "theoretical_space_complexity": "O(nnz+N) [N=4450, memory≈1,989,150 elements]", "theoretical_memory_mb": 22.74704360961914, "efficiency_ratio": 0.6553278375042201}, {"input_size": 4650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651188.952023, "execution_time_ms": 2.415476832538843, "setup_time_ms": 981.7961878143251, "cleanup_time_ms": 39.7037947550416, "total_time_ms": 1023.9154594019055, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 488.5625, "memory_increment_mb": 57.61328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 4324500, "algorithm_type": "spmv_scipy", "matrix_size": "4650×4650", "implementation": "scipy_sparse", "nnz": 2162250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 68.53755885279584, "result_norm": 815.7766033000802, "matrix_frobenius_norm": 848.9535448332157, "theoretical_flops": 4324500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=4650, nnz≈2,162,250]", "theoretical_space_complexity": "O(nnz+N) [N=4650, memory≈2,171,550 elements]", "theoretical_memory_mb": 24.833683013916016, "efficiency_ratio": 0.4310409418647027}, {"input_size": 4850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651190.1825216, "execution_time_ms": 2.728425618261099, "setup_time_ms": 1093.6803710646927, "cleanup_time_ms": 39.81214202940464, "total_time_ms": 1136.2209387123585, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 493.6328125, "memory_increment_mb": 62.68359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 4704500, "algorithm_type": "spmv_scipy", "matrix_size": "4850×4850", "implementation": "scipy_sparse", "nnz": 2352250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 70.05155400501597, "result_norm": 862.7717116700629, "matrix_frobenius_norm": 885.2552922689824, "theoretical_flops": 4704500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=4850, nnz≈2,352,250]", "theoretical_space_complexity": "O(nnz+N) [N=4850, memory≈2,361,950 elements]", "theoretical_memory_mb": 27.01187515258789, "efficiency_ratio": 0.43092416271343553}, {"input_size": 5050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651191.5261862, "execution_time_ms": 2.8767147101461887, "setup_time_ms": 1175.1539227552712, "cleanup_time_ms": 39.77923933416605, "total_time_ms": 1217.8098767995834, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 498.921875, "memory_increment_mb": 67.97265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 5100500, "algorithm_type": "spmv_scipy", "matrix_size": "5050×5050", "implementation": "scipy_sparse", "nnz": 2550250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 69.28668652924382, "result_norm": 1037.22756198876, "matrix_frobenius_norm": 922.1232369424766, "theoretical_flops": 5100500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=5050, nnz≈2,550,250]", "theoretical_space_complexity": "O(nnz+N) [N=5050, memory≈2,560,350 elements]", "theoretical_memory_mb": 29.281620025634766, "efficiency_ratio": 0.43078528398152405}, {"input_size": 5250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651192.9529107, "execution_time_ms": 3.1722135841846466, "setup_time_ms": 1278.1825959682465, "cleanup_time_ms": 47.9201995767653, "total_time_ms": 1329.2750091291964, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 504.421875, "memory_increment_mb": 73.47265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 5512500, "algorithm_type": "spmv_scipy", "matrix_size": "5250×5250", "implementation": "scipy_sparse", "nnz": 2756250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 73.09625531050156, "result_norm": 1045.2455308215406, "matrix_frobenius_norm": 958.4705431948332, "theoretical_flops": 5512500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=5250, nnz≈2,756,250]", "theoretical_space_complexity": "O(nnz+N) [N=5250, memory≈2,766,750 elements]", "theoretical_memory_mb": 31.64291763305664, "efficiency_ratio": 0.4306761079303791}, {"input_size": 5450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651194.493577, "execution_time_ms": 3.4245770424604416, "setup_time_ms": 1384.241139050573, "cleanup_time_ms": 39.805067237466574, "total_time_ms": 1427.4707833305001, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 510.13671875, "memory_increment_mb": 79.1875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 5940500, "algorithm_type": "spmv_scipy", "matrix_size": "5450×5450", "implementation": "scipy_sparse", "nnz": 2970250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 73.1120273263701, "result_norm": 1015.7474060752361, "matrix_frobenius_norm": 995.0821984664537, "theoretical_flops": 5940500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=5450, nnz≈2,970,250]", "theoretical_space_complexity": "O(nnz+N) [N=5450, memory≈2,981,150 elements]", "theoretical_memory_mb": 34.095767974853516, "efficiency_ratio": 0.4305700770305101}, {"input_size": 5650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651196.134422, "execution_time_ms": 3.6793953739106655, "setup_time_ms": 1505.1708947867155, "cleanup_time_ms": 39.76597683504224, "total_time_ms": 1548.6162669956684, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 516.06640625, "memory_increment_mb": 85.1171875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 6384500, "algorithm_type": "spmv_scipy", "matrix_size": "5650×5650", "implementation": "scipy_sparse", "nnz": 3192250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 77.59356270743513, "result_norm": 1023.1821138993121, "matrix_frobenius_norm": 1031.4036784258085, "theoretical_flops": 6384500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=5650, nnz≈3,192,250]", "theoretical_space_complexity": "O(nnz+N) [N=5650, memory≈3,203,550 elements]", "theoretical_memory_mb": 36.64017105102539, "efficiency_ratio": 0.4304673606729004}, {"input_size": 5850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651197.8983207, "execution_time_ms": 3.9075324311852455, "setup_time_ms": 1612.7081150189042, "cleanup_time_ms": 39.701417088508606, "total_time_ms": 1656.317064538598, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 522.20703125, "memory_increment_mb": 91.2578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 6844500, "algorithm_type": "spmv_scipy", "matrix_size": "5850×5850", "implementation": "scipy_sparse", "nnz": 3422250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 77.20734885063318, "result_norm": 1047.1486702389589, "matrix_frobenius_norm": 1067.9919364776958, "theoretical_flops": 6844500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=5850, nnz≈3,422,250]", "theoretical_space_complexity": "O(nnz+N) [N=5850, memory≈3,433,950 elements]", "theoretical_memory_mb": 39.276126861572266, "efficiency_ratio": 0.4303864599162101}, {"input_size": 6050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651199.7803679, "execution_time_ms": 4.18649660423398, "setup_time_ms": 1745.8662861026824, "cleanup_time_ms": 38.897437043488026, "total_time_ms": 1788.9502197504044, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 500.63671875, "memory_increment_mb": 69.6875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 7320500, "algorithm_type": "spmv_scipy", "matrix_size": "6050×6050", "implementation": "scipy_sparse", "nnz": 3660250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 77.72695008655155, "result_norm": 1076.4300384708486, "matrix_frobenius_norm": 1104.0341766261856, "theoretical_flops": 7320500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=6050, nnz≈3,660,250]", "theoretical_space_complexity": "O(nnz+N) [N=6050, memory≈3,672,350 elements]", "theoretical_memory_mb": 42.00363540649414, "efficiency_ratio": 0.6027427502277186}, {"input_size": 6250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651201.7875254, "execution_time_ms": 4.349086154252291, "setup_time_ms": 1875.1383177004755, "cleanup_time_ms": 40.40735075250268, "total_time_ms": 1919.8947546072304, "baseline_memory_mb": 472.7109375, "peak_memory_mb": 535.1328125, "memory_increment_mb": 62.421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 7812500, "algorithm_type": "spmv_scipy", "matrix_size": "6250×6250", "implementation": "scipy_sparse", "nnz": 3906250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 79.28669859475255, "result_norm": 1146.0655035737607, "matrix_frobenius_norm": 1140.152855706166, "theoretical_flops": 7812500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=6250, nnz≈3,906,250]", "theoretical_space_complexity": "O(nnz+N) [N=6250, memory≈3,918,750 elements]", "theoretical_memory_mb": 44.822696685791016, "efficiency_ratio": 0.7180607228762516}, {"input_size": 6450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651203.9272513, "execution_time_ms": 4.765941854566336, "setup_time_ms": 2029.2043569497764, "cleanup_time_ms": 38.82626909762621, "total_time_ms": 2072.796567901969, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 510.17578125, "memory_increment_mb": 79.2265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 8320500, "algorithm_type": "spmv_scipy", "matrix_size": "6450×6450", "implementation": "scipy_sparse", "nnz": 4160250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 79.92964364566154, "result_norm": 1208.6237627872765, "matrix_frobenius_norm": 1177.2701052321609, "theoretical_flops": 8320500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=6450, nnz≈4,160,250]", "theoretical_space_complexity": "O(nnz+N) [N=6450, memory≈4,173,150 elements]", "theoretical_memory_mb": 47.73331069946289, "efficiency_ratio": 0.6024912503235628}, {"input_size": 6650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651206.2224414, "execution_time_ms": 5.084077455103397, "setup_time_ms": 2157.202497124672, "cleanup_time_ms": 40.342513006180525, "total_time_ms": 2202.629087585956, "baseline_memory_mb": 478.43359375, "peak_memory_mb": 548.9140625, "memory_increment_mb": 70.48046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 8844500, "algorithm_type": "spmv_scipy", "matrix_size": "6650×6650", "implementation": "scipy_sparse", "nnz": 4422250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 80.7637214255453, "result_norm": 1194.6854124932477, "matrix_frobenius_norm": 1213.9088425985346, "theoretical_flops": 8844500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=6650, nnz≈4,422,250]", "theoretical_space_complexity": "O(nnz+N) [N=6650, memory≈4,435,550 elements]", "theoretical_memory_mb": 50.735477447509766, "efficiency_ratio": 0.7198515893455911}, {"input_size": 6850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651208.6497014, "execution_time_ms": 5.373569950461388, "setup_time_ms": 2313.6325269006193, "cleanup_time_ms": 38.97256217896938, "total_time_ms": 2357.97865903005, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 520.3203125, "memory_increment_mb": 89.37109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 9384500, "algorithm_type": "spmv_scipy", "matrix_size": "6850×6850", "implementation": "scipy_sparse", "nnz": 4692250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 83.03095599858601, "result_norm": 1201.6556357903632, "matrix_frobenius_norm": 1250.7383877528885, "theoretical_flops": 9384500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=6850, nnz≈4,692,250]", "theoretical_space_complexity": "O(nnz+N) [N=6850, memory≈4,705,950 elements]", "theoretical_memory_mb": 53.82919692993164, "efficiency_ratio": 0.602311045677805}, {"input_size": 7050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651211.234572, "execution_time_ms": 5.749541521072388, "setup_time_ms": 2448.287629056722, "cleanup_time_ms": 40.57146608829498, "total_time_ms": 2494.6086366660893, "baseline_memory_mb": 484.51953125, "peak_memory_mb": 563.54296875, "memory_increment_mb": 79.0234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 9940500, "algorithm_type": "spmv_scipy", "matrix_size": "7050×7050", "implementation": "scipy_sparse", "nnz": 4970250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 83.71853762074879, "result_norm": 1250.0585911835708, "matrix_frobenius_norm": 1286.9389299197715, "theoretical_flops": 9940500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=7050, nnz≈4,970,250]", "theoretical_space_complexity": "O(nnz+N) [N=7050, memory≈4,984,350 elements]", "theoretical_memory_mb": 57.014469146728516, "efficiency_ratio": 0.7214880920198962}, {"input_size": 7250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651213.9586334, "execution_time_ms": 6.051977910101414, "setup_time_ms": 2605.43040279299, "cleanup_time_ms": 38.70720416307449, "total_time_ms": 2650.189584866166, "baseline_memory_mb": 430.94921875, "peak_memory_mb": 531.08203125, "memory_increment_mb": 100.1328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 10512500, "algorithm_type": "spmv_scipy", "matrix_size": "7250×7250", "implementation": "scipy_sparse", "nnz": 5256250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 84.48722352437153, "result_norm": 1283.08508058914, "matrix_frobenius_norm": 1323.4702991516342, "theoretical_flops": 10512500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=7250, nnz≈5,256,250]", "theoretical_space_complexity": "O(nnz+N) [N=7250, memory≈5,270,750 elements]", "theoretical_memory_mb": 60.29129409790039, "efficiency_ratio": 0.6021132593064875}, {"input_size": 7450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651216.8406062, "execution_time_ms": 6.38193441554904, "setup_time_ms": 2742.76752024889, "cleanup_time_ms": 40.17463605850935, "total_time_ms": 2789.3240907229483, "baseline_memory_mb": 490.9765625, "peak_memory_mb": 579.03125, "memory_increment_mb": 88.0546875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 11100500, "algorithm_type": "spmv_scipy", "matrix_size": "7450×7450", "implementation": "scipy_sparse", "nnz": 5550250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 87.11066718002652, "result_norm": 1340.5027465726562, "matrix_frobenius_norm": 1359.9408623840739, "theoretical_flops": 11100500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=7450, nnz≈5,550,250]", "theoretical_space_complexity": "O(nnz+N) [N=7450, memory≈5,565,150 elements]", "theoretical_memory_mb": 63.659671783447266, "efficiency_ratio": 0.7229560809405776}, {"input_size": 7650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651219.8639312, "execution_time_ms": 6.668103951960802, "setup_time_ms": 3021.6953582130373, "cleanup_time_ms": 39.6839021705091, "total_time_ms": 3068.047364335507, "baseline_memory_mb": 431.00390625, "peak_memory_mb": 542.44921875, "memory_increment_mb": 111.4453125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 11704500, "algorithm_type": "spmv_scipy", "matrix_size": "7650×7650", "implementation": "scipy_sparse", "nnz": 5852250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 88.02304619512084, "result_norm": 1396.9133916149274, "matrix_frobenius_norm": 1396.7262106346961, "theoretical_flops": 11704500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=7650, nnz≈5,852,250]", "theoretical_space_complexity": "O(nnz+N) [N=7650, memory≈5,867,550 elements]", "theoretical_memory_mb": 67.11960220336914, "efficiency_ratio": 0.6022649198760077}, {"input_size": 7850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651223.1795084, "execution_time_ms": 7.1273366920650005, "setup_time_ms": 3068.6289998702705, "cleanup_time_ms": 39.68437807634473, "total_time_ms": 3115.44071463868, "baseline_memory_mb": 431.0078125, "peak_memory_mb": 548.359375, "memory_increment_mb": 117.3515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 12324500, "algorithm_type": "spmv_scipy", "matrix_size": "7850×7850", "implementation": "scipy_sparse", "nnz": 6162250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 89.04264606488213, "result_norm": 1412.9902181737975, "matrix_frobenius_norm": 1433.1881802094288, "theoretical_flops": 12324500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=7850, nnz≈6,162,250]", "theoretical_space_complexity": "O(nnz+N) [N=7850, memory≈6,177,950 elements]", "theoretical_memory_mb": 70.67108535766602, "efficiency_ratio": 0.6022168248306538}, {"input_size": 8050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651226.5490358, "execution_time_ms": 7.1829551830887794, "setup_time_ms": 3228.0406979843974, "cleanup_time_ms": 39.79279799386859, "total_time_ms": 3275.016451161355, "baseline_memory_mb": 431.0078125, "peak_memory_mb": 554.42578125, "memory_increment_mb": 123.41796875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 12960500, "algorithm_type": "spmv_scipy", "matrix_size": "8050×8050", "implementation": "scipy_sparse", "nnz": 6480250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 89.12353667321823, "result_norm": 1470.7048946586294, "matrix_frobenius_norm": 1469.36047945575, "theoretical_flops": 12960500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=8050, nnz≈6,480,250]", "theoretical_space_complexity": "O(nnz+N) [N=8050, memory≈6,496,350 elements]", "theoretical_memory_mb": 74.31412124633789, "efficiency_ratio": 0.6021337249268081}, {"input_size": 8250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651230.0645478, "execution_time_ms": 7.751597464084625, "setup_time_ms": 3383.3006219938397, "cleanup_time_ms": 39.93383888155222, "total_time_ms": 3430.9860583394766, "baseline_memory_mb": 431.0078125, "peak_memory_mb": 560.64453125, "memory_increment_mb": 129.63671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 13612500, "algorithm_type": "spmv_scipy", "matrix_size": "8250×8250", "implementation": "scipy_sparse", "nnz": 6806250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 90.50461818514009, "result_norm": 1535.9109142438808, "matrix_frobenius_norm": 1505.6284232385194, "theoretical_flops": 13612500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=8250, nnz≈6,806,250]", "theoretical_space_complexity": "O(nnz+N) [N=8250, memory≈6,822,750 elements]", "theoretical_memory_mb": 78.04870986938477, "efficiency_ratio": 0.602057122564935}, {"input_size": 8450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651233.7395465, "execution_time_ms": 7.959266006946564, "setup_time_ms": 3579.952517990023, "cleanup_time_ms": 39.796424098312855, "total_time_ms": 3627.7082080952823, "baseline_memory_mb": 431.01171875, "peak_memory_mb": 567.01171875, "memory_increment_mb": 136.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 14280500, "algorithm_type": "spmv_scipy", "matrix_size": "8450×8450", "implementation": "scipy_sparse", "nnz": 7140250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 91.55047381104333, "result_norm": 1456.882397736049, "matrix_frobenius_norm": 1542.3193684769258, "theoretical_flops": 14280500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=8450, nnz≈7,140,250]", "theoretical_space_complexity": "O(nnz+N) [N=8450, memory≈7,157,150 elements]", "theoretical_memory_mb": 81.87485122680664, "efficiency_ratio": 0.60202096490299}, {"input_size": 8650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651237.612737, "execution_time_ms": 8.29038042575121, "setup_time_ms": 3779.8335971310735, "cleanup_time_ms": 39.77384977042675, "total_time_ms": 3827.8978273272514, "baseline_memory_mb": 431.01171875, "peak_memory_mb": 573.53515625, "memory_increment_mb": 142.5234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 14964500, "algorithm_type": "spmv_scipy", "matrix_size": "8650×8650", "implementation": "scipy_sparse", "nnz": 7482250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 93.77819460429141, "result_norm": 1524.2445989694613, "matrix_frobenius_norm": 1579.4997238429914, "theoretical_flops": 14964500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=8650, nnz≈7,482,250]", "theoretical_space_complexity": "O(nnz+N) [N=8650, memory≈7,499,550 elements]", "theoretical_memory_mb": 85.79254531860352, "efficiency_ratio": 0.601953944021337}, {"input_size": 8850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651241.6891422, "execution_time_ms": 8.837877865880728, "setup_time_ms": 4192.438160069287, "cleanup_time_ms": 39.98733498156071, "total_time_ms": 4241.263372916728, "baseline_memory_mb": 431.01171875, "peak_memory_mb": 580.21484375, "memory_increment_mb": 149.203125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 15664500, "algorithm_type": "spmv_scipy", "matrix_size": "8850×8850", "implementation": "scipy_sparse", "nnz": 7832250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 93.50077741295098, "result_norm": 1594.18905994289, "matrix_frobenius_norm": 1615.9142387553347, "theoretical_flops": 15664500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=8850, nnz≈7,832,250]", "theoretical_space_complexity": "O(nnz+N) [N=8850, memory≈7,849,950 elements]", "theoretical_memory_mb": 89.80179214477539, "efficiency_ratio": 0.6018760809787019}, {"input_size": 9050, "algorithm_name": "SpMV-scipy", "timestamp": 1753651246.1824346, "execution_time_ms": 9.513268247246742, "setup_time_ms": 4211.044086143374, "cleanup_time_ms": 39.73922599107027, "total_time_ms": 4260.2965803816915, "baseline_memory_mb": 431.015625, "peak_memory_mb": 587.0390625, "memory_increment_mb": 156.0234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 16380500, "algorithm_type": "spmv_scipy", "matrix_size": "9050×9050", "implementation": "scipy_sparse", "nnz": 8190250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 94.78054759305171, "result_norm": 1621.7971439918329, "matrix_frobenius_norm": 1652.205429578378, "theoretical_flops": 16380500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=9050, nnz≈8,190,250]", "theoretical_space_complexity": "O(nnz+N) [N=9050, memory≈8,208,350 elements]", "theoretical_memory_mb": 93.90259170532227, "efficiency_ratio": 0.6018492683531746}, {"input_size": 9250, "algorithm_name": "SpMV-scipy", "timestamp": 1753651250.71273, "execution_time_ms": 9.809534717351198, "setup_time_ms": 4420.804201159626, "cleanup_time_ms": 39.370386861264706, "total_time_ms": 4469.984122738242, "baseline_memory_mb": 431.015625, "peak_memory_mb": 528.9375, "memory_increment_mb": 97.921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 17112500, "algorithm_type": "spmv_scipy", "matrix_size": "9250×9250", "implementation": "scipy_sparse", "nnz": 8556250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 97.67332731894659, "result_norm": 1671.8473785926226, "matrix_frobenius_norm": 1688.4320590816792, "theoretical_flops": 17112500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=9250, nnz≈8,556,250]", "theoretical_space_complexity": "O(nnz+N) [N=9250, memory≈8,574,750 elements]", "theoretical_memory_mb": 98.09494400024414, "efficiency_ratio": 1.001767419182324}, {"input_size": 9450, "algorithm_name": "SpMV-scipy", "timestamp": 1753651255.4416733, "execution_time_ms": 10.256132390350103, "setup_time_ms": 4617.926578968763, "cleanup_time_ms": 39.92389282211661, "total_time_ms": 4668.10660418123, "baseline_memory_mb": 431.03515625, "peak_memory_mb": 533.234375, "memory_increment_mb": 102.19921875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 17860500, "algorithm_type": "spmv_scipy", "matrix_size": "9450×9450", "implementation": "scipy_sparse", "nnz": 8930250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 98.27033198268313, "result_norm": 1662.8407052701568, "matrix_frobenius_norm": 1724.979464960153, "theoretical_flops": 17860500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=9450, nnz≈8,930,250]", "theoretical_space_complexity": "O(nnz+N) [N=9450, memory≈8,949,150 elements]", "theoretical_memory_mb": 102.37884902954102, "efficiency_ratio": 1.0017576482652029}, {"input_size": 9650, "algorithm_name": "SpMV-scipy", "timestamp": 1753651260.3715959, "execution_time_ms": 10.59727305546403, "setup_time_ms": 4839.57156771794, "cleanup_time_ms": 39.47408311069012, "total_time_ms": 4889.642923884094, "baseline_memory_mb": 431.0390625, "peak_memory_mb": 537.609375, "memory_increment_mb": 106.5703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 18624500, "algorithm_type": "spmv_scipy", "matrix_size": "9650×9650", "implementation": "scipy_sparse", "nnz": 9312250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 98.65014061841552, "result_norm": 1693.7217386667862, "matrix_frobenius_norm": 1761.6309211993962, "theoretical_flops": 18624500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=9650, nnz≈9,312,250]", "theoretical_space_complexity": "O(nnz+N) [N=9650, memory≈9,331,550 elements]", "theoretical_memory_mb": 106.75430679321289, "efficiency_ratio": 1.0017265060868887}, {"input_size": 9850, "algorithm_name": "SpMV-scipy", "timestamp": 1753651265.525698, "execution_time_ms": 11.146125849336386, "setup_time_ms": 5046.328729018569, "cleanup_time_ms": 39.38975324854255, "total_time_ms": 5096.864608116448, "baseline_memory_mb": 431.04296875, "peak_memory_mb": 542.078125, "memory_increment_mb": 111.03515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"correctness_verified": true, "max_error": 0.0, "operations_count": 19404500, "algorithm_type": "spmv_scipy", "matrix_size": "9850×9850", "implementation": "scipy_sparse", "nnz": 9702250, "actual_sparsity_ratio": 0.1, "target_sparsity_ratio": 0.1, "vector_norm": 97.92975604747116, "result_norm": 1802.752997687001, "matrix_frobenius_norm": 1798.36803659023, "theoretical_flops": 19404500, "storage_format": "CSR", "memory_efficiency": 0.1}, "theoretical_time_complexity": "O(nnz) [N=9850, nnz≈9,702,250]", "theoretical_space_complexity": "O(nnz+N) [N=9850, memory≈9,721,950 elements]", "theoretical_memory_mb": 111.22131729125977, "efficiency_ratio": 1.0016765954815303}]