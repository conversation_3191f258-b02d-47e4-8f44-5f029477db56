input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_sequence_type,custom_input_size,custom_sequence_length,custom_dtw_distance,custom_normalized_distance,custom_distance_calculations,custom_theoretical_operations,custom_operation_efficiency,custom_matrix_accesses,custom_matrix_size,custom_theoretical_matrix_size,custom_optimal_path_length,custom_seq1_std,custom_seq2_std,custom_cross_correlation,custom_algorithm_type
50,DTW-random,1753706112.2320583,2.4229,0.0308,12.0290,14.4827,56.85,56.85,0.00,0.00,,,,O(n²) [n=50],O(n²) [n=50],0.02,0.0000,random,50,50,31.661223714009644,0.6332244742801929,2500,2500,1.0,7500,2601,2601,61,0.924,0.866,0.11,dynamic_programming
100,DTW-random,1753706112.5346317,16.3929,0.0533,4.7219,21.1681,58.98,59.69,0.71,0.00,,,,O(n²) [n=100],O(n²) [n=100],0.08,0.1095,random,100,100,57.128787387489766,0.5712878738748977,10000,10000,1.0,30000,10201,10201,126,0.904,0.949,-0.136,dynamic_programming
150,DTW-random,1753706112.700225,19.9131,0.0523,3.4627,23.4281,59.69,59.69,0.00,0.00,,,,O(n²) [n=150],O(n²) [n=150],0.17,0.0000,random,150,150,92.127710426118,0.61418473617412,22500,22500,1.0,67500,22801,22801,191,0.939,1.018,-0.024,dynamic_programming
200,DTW-random,1753706112.8927667,34.9975,0.0405,4.2082,39.2462,59.69,60.18,0.49,0.00,,,,O(n²) [n=200],O(n²) [n=200],0.31,0.6313,random,200,200,117.95359901272079,0.5897679950636039,40000,40000,1.0,120000,40401,40401,256,0.929,0.985,0.095,dynamic_programming
250,DTW-random,1753706113.2023103,55.1381,0.0418,5.0869,60.2668,59.72,60.49,0.77,0.00,,,,O(n²) [n=250],O(n²) [n=250],0.48,0.6246,random,250,250,143.65474779870763,0.5746189911948305,62500,62500,1.0,187500,63001,63001,314,0.964,0.996,-0.017,dynamic_programming
300,DTW-random,1753706113.6783888,79.8772,0.0461,16.5655,96.4888,59.75,61.04,1.29,0.00,,,,O(n²) [n=300],O(n²) [n=300],0.69,0.5362,random,300,300,175.5986146862435,0.5853287156208117,90000,90000,1.0,270000,90601,90601,366,0.983,0.96,-0.041,dynamic_programming
350,DTW-random,1753706114.370919,109.4511,0.0639,2.9132,112.4282,61.04,61.54,0.50,0.00,,,,O(n²) [n=350],O(n²) [n=350],0.94,1.8947,random,350,350,207.885414146315,0.5939583261323286,122500,122500,1.0,367500,123201,123201,455,0.955,1.014,-0.086,dynamic_programming
400,DTW-random,1753706115.358552,143.4997,0.0415,2.9594,146.5006,61.54,62.14,0.61,0.00,,,,O(n²) [n=400],O(n²) [n=400],1.23,2.0132,random,400,400,237.93033058867476,0.5948258264716869,160000,160000,1.0,480000,160801,160801,505,0.959,1.005,-0.114,dynamic_programming
450,DTW-random,1753706116.5407047,184.8007,0.0533,3.2971,188.1510,62.14,62.71,0.57,0.00,,,,O(n²) [n=450],O(n²) [n=450],1.55,2.7210,random,450,450,264.472737512241,0.5877171944716466,202500,202500,1.0,607500,203401,203401,575,0.971,0.99,0.051,dynamic_programming
500,DTW-random,1753706118.0522358,226.6205,0.0432,3.1113,229.7750,62.71,63.44,0.73,0.00,,,,O(n²) [n=500],O(n²) [n=500],1.91,2.6357,random,500,500,287.62348307953164,0.5752469661590632,250000,250000,1.0,750000,251001,251001,640,0.98,0.977,-0.076,dynamic_programming
