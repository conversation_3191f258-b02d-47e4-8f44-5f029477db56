[{"input_size": 50, "algorithm_name": "DTW-random", "timestamp": 1753706112.2320583, "execution_time_ms": 2.422920521348715, "setup_time_ms": 0.03080395981669426, "cleanup_time_ms": 12.028980068862438, "total_time_ms": 14.482704550027847, "baseline_memory_mb": 56.8515625, "peak_memory_mb": 56.8515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 50, "sequence_length": 50, "dtw_distance": 31.661223714009644, "normalized_distance": 0.6332244742801929, "distance_calculations": 2500, "theoretical_operations": 2500, "operation_efficiency": 1.0, "matrix_accesses": 7500, "matrix_size": 2601, "theoretical_matrix_size": 2601, "optimal_path_length": 61, "seq1_std": 0.924, "seq2_std": 0.866, "cross_correlation": 0.11, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=50]", "theoretical_space_complexity": "O(n²) [n=50]", "theoretical_memory_mb": 0.01984405517578125, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "DTW-random", "timestamp": 1753706112.5346317, "execution_time_ms": 16.39290014281869, "setup_time_ms": 0.053266994655132294, "cleanup_time_ms": 4.721929784864187, "total_time_ms": 21.16809692233801, "baseline_memory_mb": 58.98046875, "peak_memory_mb": 59.69140625, "memory_increment_mb": 0.7109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 100, "sequence_length": 100, "dtw_distance": 57.128787387489766, "normalized_distance": 0.5712878738748977, "distance_calculations": 10000, "theoretical_operations": 10000, "operation_efficiency": 1.0, "matrix_accesses": 30000, "matrix_size": 10201, "theoretical_matrix_size": 10201, "optimal_path_length": 126, "seq1_std": 0.904, "seq2_std": 0.949, "cross_correlation": -0.136, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=100]", "theoretical_space_complexity": "O(n²) [n=100]", "theoretical_memory_mb": 0.07782745361328125, "efficiency_ratio": 0.1094715831043956}, {"input_size": 150, "algorithm_name": "DTW-random", "timestamp": 1753706112.700225, "execution_time_ms": 19.913103617727757, "setup_time_ms": 0.05226302891969681, "cleanup_time_ms": 3.4627309069037437, "total_time_ms": 23.428097553551197, "baseline_memory_mb": 59.69140625, "peak_memory_mb": 59.69140625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 150, "sequence_length": 150, "dtw_distance": 92.127710426118, "normalized_distance": 0.61418473617412, "distance_calculations": 22500, "theoretical_operations": 22500, "operation_efficiency": 1.0, "matrix_accesses": 67500, "matrix_size": 22801, "theoretical_matrix_size": 22801, "optimal_path_length": 191, "seq1_std": 0.939, "seq2_std": 1.018, "cross_correlation": -0.024, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=150]", "theoretical_space_complexity": "O(n²) [n=150]", "theoretical_memory_mb": 0.17395782470703125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "DTW-random", "timestamp": 1753706112.8927667, "execution_time_ms": 34.9974618293345, "setup_time_ms": 0.04052324220538139, "cleanup_time_ms": 4.208218771964312, "total_time_ms": 39.24620384350419, "baseline_memory_mb": 59.69140625, "peak_memory_mb": 60.1796875, "memory_increment_mb": 0.48828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 200, "sequence_length": 200, "dtw_distance": 117.95359901272079, "normalized_distance": 0.5897679950636039, "distance_calculations": 40000, "theoretical_operations": 40000, "operation_efficiency": 1.0, "matrix_accesses": 120000, "matrix_size": 40401, "theoretical_matrix_size": 40401, "optimal_path_length": 256, "seq1_std": 0.929, "seq2_std": 0.985, "cross_correlation": 0.095, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=200]", "theoretical_space_complexity": "O(n²) [n=200]", "theoretical_memory_mb": 0.30823516845703125, "efficiency_ratio": 0.631265625}, {"input_size": 250, "algorithm_name": "DTW-random", "timestamp": 1753706113.2023103, "execution_time_ms": 55.138138961046934, "setup_time_ms": 0.04177074879407883, "cleanup_time_ms": 5.086917895823717, "total_time_ms": 60.26682760566473, "baseline_memory_mb": 59.72265625, "peak_memory_mb": 60.4921875, "memory_increment_mb": 0.76953125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 250, "sequence_length": 250, "dtw_distance": 143.65474779870763, "normalized_distance": 0.5746189911948305, "distance_calculations": 62500, "theoretical_operations": 62500, "operation_efficiency": 1.0, "matrix_accesses": 187500, "matrix_size": 63001, "theoretical_matrix_size": 63001, "optimal_path_length": 314, "seq1_std": 0.964, "seq2_std": 0.996, "cross_correlation": -0.017, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=250]", "theoretical_space_complexity": "O(n²) [n=250]", "theoretical_memory_mb": 0.48065948486328125, "efficiency_ratio": 0.6246133407360406}, {"input_size": 300, "algorithm_name": "DTW-random", "timestamp": 1753706113.6783888, "execution_time_ms": 79.87721879035234, "setup_time_ms": 0.0460953451693058, "cleanup_time_ms": 16.565486323088408, "total_time_ms": 96.48880045861006, "baseline_memory_mb": 59.74609375, "peak_memory_mb": 61.03515625, "memory_increment_mb": 1.2890625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 300, "sequence_length": 300, "dtw_distance": 175.5986146862435, "normalized_distance": 0.5853287156208117, "distance_calculations": 90000, "theoretical_operations": 90000, "operation_efficiency": 1.0, "matrix_accesses": 270000, "matrix_size": 90601, "theoretical_matrix_size": 90601, "optimal_path_length": 366, "seq1_std": 0.983, "seq2_std": 0.96, "cross_correlation": -0.041, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=300]", "theoretical_space_complexity": "O(n²) [n=300]", "theoretical_memory_mb": 0.6912307739257812, "efficiency_ratio": 0.536227509469697}, {"input_size": 350, "algorithm_name": "DTW-random", "timestamp": 1753706114.370919, "execution_time_ms": 109.4511310569942, "setup_time_ms": 0.06387103348970413, "cleanup_time_ms": 2.913182135671377, "total_time_ms": 112.42818422615528, "baseline_memory_mb": 61.04296875, "peak_memory_mb": 61.5390625, "memory_increment_mb": 0.49609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 350, "sequence_length": 350, "dtw_distance": 207.885414146315, "normalized_distance": 0.5939583261323286, "distance_calculations": 122500, "theoretical_operations": 122500, "operation_efficiency": 1.0, "matrix_accesses": 367500, "matrix_size": 123201, "theoretical_matrix_size": 123201, "optimal_path_length": 455, "seq1_std": 0.955, "seq2_std": 1.014, "cross_correlation": -0.086, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=350]", "theoretical_space_complexity": "O(n²) [n=350]", "theoretical_memory_mb": 0.9399490356445312, "efficiency_ratio": 1.8947004183070866}, {"input_size": 400, "algorithm_name": "DTW-random", "timestamp": 1753706115.358552, "execution_time_ms": 143.49972903728485, "setup_time_ms": 0.04151416942477226, "cleanup_time_ms": 2.95938178896904, "total_time_ms": 146.50062499567866, "baseline_memory_mb": 61.53515625, "peak_memory_mb": 62.14453125, "memory_increment_mb": 0.609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 400, "sequence_length": 400, "dtw_distance": 237.93033058867476, "normalized_distance": 0.5948258264716869, "distance_calculations": 160000, "theoretical_operations": 160000, "operation_efficiency": 1.0, "matrix_accesses": 480000, "matrix_size": 160801, "theoretical_matrix_size": 160801, "optimal_path_length": 505, "seq1_std": 0.959, "seq2_std": 1.005, "cross_correlation": -0.114, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=400]", "theoretical_space_complexity": "O(n²) [n=400]", "theoretical_memory_mb": 1.2268142700195312, "efficiency_ratio": 2.0132336738782053}, {"input_size": 450, "algorithm_name": "DTW-random", "timestamp": 1753706116.5407047, "execution_time_ms": 184.8006872460246, "setup_time_ms": 0.053250230848789215, "cleanup_time_ms": 3.2970672473311424, "total_time_ms": 188.15100472420454, "baseline_memory_mb": 62.14453125, "peak_memory_mb": 62.71484375, "memory_increment_mb": 0.5703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 450, "sequence_length": 450, "dtw_distance": 264.472737512241, "normalized_distance": 0.5877171944716466, "distance_calculations": 202500, "theoretical_operations": 202500, "operation_efficiency": 1.0, "matrix_accesses": 607500, "matrix_size": 203401, "theoretical_matrix_size": 203401, "optimal_path_length": 575, "seq1_std": 0.971, "seq2_std": 0.99, "cross_correlation": 0.051, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=450]", "theoretical_space_complexity": "O(n²) [n=450]", "theoretical_memory_mb": 1.5518264770507812, "efficiency_ratio": 2.7210108090753424}, {"input_size": 500, "algorithm_name": "DTW-random", "timestamp": 1753706118.0522358, "execution_time_ms": 226.62054039537907, "setup_time_ms": 0.04316400736570358, "cleanup_time_ms": 3.111300989985466, "total_time_ms": 229.77500539273024, "baseline_memory_mb": 62.71484375, "peak_memory_mb": 63.44140625, "memory_increment_mb": 0.7265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"sequence_type": "random", "input_size": 500, "sequence_length": 500, "dtw_distance": 287.62348307953164, "normalized_distance": 0.5752469661590632, "distance_calculations": 250000, "theoretical_operations": 250000, "operation_efficiency": 1.0, "matrix_accesses": 750000, "matrix_size": 251001, "theoretical_matrix_size": 251001, "optimal_path_length": 640, "seq1_std": 0.98, "seq2_std": 0.977, "cross_correlation": -0.076, "algorithm_type": "dynamic_programming"}, "theoretical_time_complexity": "O(n²) [n=500]", "theoretical_space_complexity": "O(n²) [n=500]", "theoretical_memory_mb": 1.9149856567382812, "efficiency_ratio": 2.635679183467742}]