#!/usr/bin/env python3
"""
RSA Encryption/Decryption Demo

This script demonstrates the RSA encryption and decryption scaling analyzers,
showing performance characteristics across different key sizes and input sizes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import RSAEncryptionScalingAnalyzer, RSADecryptionScalingAnalyzer


def demo_basic_rsa_operations():
    """Demonstrate basic RSA encryption and decryption"""
    print("=== Basic RSA Operations Demo ===")
    
    # Create analyzers
    encrypt_analyzer = RSAEncryptionScalingAnalyzer(
        key_size=2048,
        padding_scheme='OAEP',
        output_dir="demo_results/rsa_basic/encrypt",
        enable_gpu_tracking=False
    )
    
    decrypt_analyzer = RSADecryptionScalingAnalyzer(
        key_size=2048,
        padding_scheme='OAEP',
        output_dir="demo_results/rsa_basic/decrypt",
        enable_gpu_tracking=False
    )
    
    test_sizes = [32, 64, 128, 190]  # 190 is close to max for RSA-2048-OAEP
    
    print(f"RSA-2048-OAEP Configuration:")
    print(f"Max plaintext size: {encrypt_analyzer.max_plaintext_size} bytes")
    print(f"Key size: {encrypt_analyzer.key_size} bits ({encrypt_analyzer.key_size_bytes} bytes)")
    print()
    
    print(f"{'Size':<6} {'Encrypt Time':<12} {'Decrypt Time':<12} {'Enc Tput':<10} {'Dec Tput':<10} {'Expansion':<10}")
    print("-" * 70)
    
    for size in test_sizes:
        # Test encryption
        enc_result = encrypt_analyzer.measure_single_run(size)
        enc_custom = enc_result.custom_metrics
        
        # Test decryption
        dec_result = decrypt_analyzer.measure_single_run(size)
        dec_custom = dec_result.custom_metrics
        
        print(f"{size:<6} {enc_result.execution_time_ms:<12.2f} "
              f"{dec_result.execution_time_ms:<12.2f} "
              f"{enc_custom.get('throughput_mbps', 0):<10.2f} "
              f"{dec_custom.get('throughput_mbps', 0):<10.2f} "
              f"{enc_custom.get('expansion_ratio', 0):<10.1f}x")


def demo_key_size_comparison():
    """Compare RSA performance across different key sizes"""
    print("\n=== RSA Key Size Comparison ===")
    
    key_sizes = [1024, 2048, 3072]  # Skip 4096 for demo speed
    test_size = 64  # Small size that works with all key sizes
    
    print(f"Testing with {test_size} byte input:")
    print(f"{'Key Size':<10} {'Max Plain':<10} {'Enc Time':<10} {'Dec Time':<10} {'Enc/Dec Ratio':<12}")
    print("-" * 60)
    
    for key_size in key_sizes:
        try:
            # Create analyzers
            encrypt_analyzer = RSAEncryptionScalingAnalyzer(
                key_size=key_size,
                padding_scheme='OAEP',
                output_dir=f"demo_results/rsa_keysize/rsa_{key_size}_encrypt",
                enable_gpu_tracking=False
            )
            
            decrypt_analyzer = RSADecryptionScalingAnalyzer(
                key_size=key_size,
                padding_scheme='OAEP',
                output_dir=f"demo_results/rsa_keysize/rsa_{key_size}_decrypt",
                enable_gpu_tracking=False
            )
            
            # Test encryption and decryption
            enc_result = encrypt_analyzer.measure_single_run(test_size)
            dec_result = decrypt_analyzer.measure_single_run(test_size)
            
            enc_time = enc_result.execution_time_ms
            dec_time = dec_result.execution_time_ms
            ratio = dec_time / enc_time if enc_time > 0 else 0
            
            print(f"{key_size:<10} {encrypt_analyzer.max_plaintext_size:<10} "
                  f"{enc_time:<10.2f} {dec_time:<10.2f} {ratio:<12.1f}")
            
        except Exception as e:
            print(f"{key_size:<10} ERROR: {str(e)[:30]}")


def demo_padding_scheme_comparison():
    """Compare different RSA padding schemes"""
    print("\n=== RSA Padding Scheme Comparison ===")
    
    padding_schemes = ['OAEP', 'PKCS1v15']
    test_size = 64
    
    print(f"Testing RSA-2048 with {test_size} byte input:")
    print(f"{'Padding':<10} {'Max Plain':<10} {'Enc Time':<10} {'Dec Time':<10} {'Security':<10}")
    print("-" * 55)
    
    for padding in padding_schemes:
        try:
            # Create analyzers
            encrypt_analyzer = RSAEncryptionScalingAnalyzer(
                key_size=2048,
                padding_scheme=padding,
                output_dir=f"demo_results/rsa_padding/rsa_2048_{padding.lower()}_encrypt",
                enable_gpu_tracking=False
            )
            
            decrypt_analyzer = RSADecryptionScalingAnalyzer(
                key_size=2048,
                padding_scheme=padding,
                output_dir=f"demo_results/rsa_padding/rsa_2048_{padding.lower()}_decrypt",
                enable_gpu_tracking=False
            )
            
            # Test encryption and decryption
            enc_result = encrypt_analyzer.measure_single_run(test_size)
            dec_result = decrypt_analyzer.measure_single_run(test_size)
            
            security_note = "Higher" if padding == 'OAEP' else "Standard"
            
            print(f"{padding:<10} {encrypt_analyzer.max_plaintext_size:<10} "
                  f"{enc_result.execution_time_ms:<10.2f} "
                  f"{dec_result.execution_time_ms:<10.2f} {security_note:<10}")
            
        except Exception as e:
            print(f"{padding:<10} ERROR: {str(e)[:30]}")


def demo_chunk_boundary_analysis():
    """Analyze performance at chunk boundaries"""
    print("\n=== RSA Chunk Boundary Analysis ===")
    
    analyzer = RSAEncryptionScalingAnalyzer(
        key_size=2048,
        padding_scheme='OAEP',
        output_dir="demo_results/rsa_chunks/encrypt",
        enable_gpu_tracking=False
    )
    
    max_size = analyzer.max_plaintext_size  # 190 bytes for RSA-2048-OAEP
    
    # Test sizes around chunk boundaries
    test_sizes = [
        max_size - 10,  # Just under one chunk
        max_size,       # Exactly one chunk
        max_size + 1,   # Just over one chunk (triggers 2 chunks)
        max_size + 10,  # Slightly over one chunk
        max_size * 2,   # Exactly two chunks
        max_size * 2 + 10,  # Just over two chunks
        max_size * 3    # Three chunks
    ]
    
    print(f"Max plaintext per chunk: {max_size} bytes")
    print(f"{'Size':<6} {'Chunks':<7} {'Time(ms)':<10} {'Throughput':<12} {'Efficiency':<12}")
    print("-" * 60)
    
    for size in test_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        chunks = custom_metrics.get('actual_chunks', 0)
        throughput = custom_metrics.get('throughput_mbps', 0)
        
        # Calculate efficiency as throughput per chunk
        efficiency = throughput / chunks if chunks > 0 else 0
        
        print(f"{size:<6} {chunks:<7} {result.execution_time_ms:<10.2f} "
              f"{throughput:<12.2f} {efficiency:<12.2f}")


def demo_encryption_decryption_scaling():
    """Compare scaling behavior of encryption vs decryption"""
    print("\n=== Encryption vs Decryption Scaling ===")
    
    # Create analyzers
    encrypt_analyzer = RSAEncryptionScalingAnalyzer(
        key_size=2048,
        padding_scheme='OAEP',
        output_dir="demo_results/rsa_scaling/encrypt",
        enable_gpu_tracking=False
    )
    
    decrypt_analyzer = RSADecryptionScalingAnalyzer(
        key_size=2048,
        padding_scheme='OAEP',
        output_dir="demo_results/rsa_scaling/decrypt",
        enable_gpu_tracking=False
    )
    
    max_size = encrypt_analyzer.max_plaintext_size
    
    # Test sizes for scaling analysis
    scaling_sizes = [
        32, 64, 128, max_size,
        max_size + 10, max_size * 2, max_size * 3
    ]
    
    print(f"Scaling analysis with sizes: {scaling_sizes}")
    
    # Run scaling analysis
    enc_results = encrypt_analyzer.run_scaling_analysis(scaling_sizes)
    dec_results = decrypt_analyzer.run_scaling_analysis(scaling_sizes)
    
    # Analyze scaling behavior
    enc_scaling = encrypt_analyzer.analyze_scaling_behavior()
    dec_scaling = decrypt_analyzer.analyze_scaling_behavior()
    
    print(f"\n=== Scaling Results ===")
    print(f"{'Size':<6} {'Enc Time':<10} {'Dec Time':<10} {'Enc Chunks':<10} {'Dec Chunks':<10} {'Time Ratio':<10}")
    print("-" * 65)
    
    for i, size in enumerate(scaling_sizes):
        enc_time = enc_results[i].execution_time_ms
        dec_time = dec_results[i].execution_time_ms
        enc_chunks = enc_results[i].custom_metrics.get('actual_chunks', 0)
        dec_chunks = dec_results[i].custom_metrics.get('actual_chunks', 0)
        
        time_ratio = dec_time / enc_time if enc_time > 0 else 0
        
        print(f"{size:<6} {enc_time:<10.2f} {dec_time:<10.2f} "
              f"{enc_chunks:<10} {dec_chunks:<10} {time_ratio:<10.1f}")
    
    # Print scaling factors
    if enc_scaling and dec_scaling:
        print(f"\nEncryption scaling factor: {enc_scaling['mean_scaling_factor']:.2f} ± {enc_scaling['std_scaling_factor']:.2f}")
        print(f"Decryption scaling factor: {dec_scaling['mean_scaling_factor']:.2f} ± {dec_scaling['std_scaling_factor']:.2f}")
        print("Note: RSA scaling is chunk-based, not strictly linear")


def demo_security_performance_tradeoff():
    """Demonstrate security vs performance trade-offs"""
    print("\n=== Security vs Performance Trade-off ===")
    
    configurations = [
        {'key_size': 1024, 'security_bits': 80, 'note': 'Legacy (not recommended)'},
        {'key_size': 2048, 'security_bits': 112, 'note': 'Current standard'},
        {'key_size': 3072, 'security_bits': 128, 'note': 'Future-proof'},
    ]
    
    test_size = 64
    
    print(f"Testing with {test_size} byte input:")
    print(f"{'Key Size':<10} {'Security':<10} {'Enc Time':<10} {'Dec Time':<10} {'Note':<20}")
    print("-" * 70)
    
    for config in configurations:
        try:
            key_size = config['key_size']
            
            # Create analyzers
            encrypt_analyzer = RSAEncryptionScalingAnalyzer(
                key_size=key_size,
                padding_scheme='OAEP',
                output_dir=f"demo_results/rsa_security/rsa_{key_size}_encrypt",
                enable_gpu_tracking=False
            )
            
            decrypt_analyzer = RSADecryptionScalingAnalyzer(
                key_size=key_size,
                padding_scheme='OAEP',
                output_dir=f"demo_results/rsa_security/rsa_{key_size}_decrypt",
                enable_gpu_tracking=False
            )
            
            # Test performance
            enc_result = encrypt_analyzer.measure_single_run(test_size)
            dec_result = decrypt_analyzer.measure_single_run(test_size)
            
            print(f"{key_size:<10} {config['security_bits']:<10} "
                  f"{enc_result.execution_time_ms:<10.2f} "
                  f"{dec_result.execution_time_ms:<10.2f} {config['note']:<20}")
            
        except Exception as e:
            print(f"{key_size:<10} ERROR: {str(e)[:40]}")


def main():
    """Run comprehensive RSA demonstration"""
    print("RSA Encryption/Decryption Scaling Analysis Demo")
    print("=" * 60)
    
    try:
        demo_basic_rsa_operations()
        demo_key_size_comparison()
        demo_padding_scheme_comparison()
        demo_chunk_boundary_analysis()
        demo_encryption_decryption_scaling()
        demo_security_performance_tradeoff()
        
        print("\n" + "=" * 60)
        print("RSA demonstration completed successfully!")
        print("\nKey findings:")
        print("- RSA decryption is significantly slower than encryption")
        print("- Larger key sizes provide better security but lower performance")
        print("- OAEP padding provides better security than PKCS1v15")
        print("- Performance scales with number of chunks, not input size directly")
        print("- Chunk boundaries significantly affect processing efficiency")
        print("- Results saved in demo_results/rsa_*/ directories")
        
    except Exception as e:
        print(f"\nRSA demonstration failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
