#!/usr/bin/env python3
"""
SHA-256 Hashing Scaling Analysis Demo

This script demonstrates SHA-256 hashing scaling analysis with various
input sizes and provides detailed performance metrics.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import SHA256HashingScalingAnalyzer


def demo_basic_sha256_analysis():
    """Demonstrate basic SHA-256 hashing analysis"""
    print("=== Basic SHA-256 Hashing Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_basic",
        enable_gpu_tracking=False
    )
    
    # Test with various input sizes
    input_sizes = [1, 8, 32, 55, 64, 128, 256, 512, 1024, 2048, 4096]
    
    print(f"SHA-256 block size: {analyzer.block_size_bytes} bytes")
    print(f"Testing with input sizes: {input_sizes}")
    
    print(f"\n{'Size':<8} {'Time(ms)':<10} {'Throughput':<12} {'Blocks':<8} {'Hash Preview':<20}")
    print("-" * 70)
    
    for size in input_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        # Get hash preview
        input_data = analyzer.prepare_input(size)
        hash_result = analyzer.run_algorithm(input_data)
        hash_preview = hash_result.get('hash_hex', '')[:16] + "..."
        
        print(f"{size:<8} {result.execution_time_ms:<10.2f} "
              f"{custom_metrics.get('throughput_mbps', 0):<12.2f} "
              f"{custom_metrics.get('actual_blocks', 0):<8} "
              f"{hash_preview:<20}")


def demo_block_boundary_analysis():
    """Demonstrate behavior around SHA-256 block boundaries"""
    print("\n=== SHA-256 Block Boundary Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_boundary",
        enable_gpu_tracking=False
    )
    
    block_size = analyzer.block_size_bytes  # 64 bytes
    
    # Test sizes around block boundaries
    boundary_sizes = [
        block_size - 1,      # 63 bytes
        block_size,          # 64 bytes (1 block)
        block_size + 1,      # 65 bytes
        block_size * 2 - 1,  # 127 bytes
        block_size * 2,      # 128 bytes (2 blocks)
        block_size * 2 + 1,  # 129 bytes
        block_size * 4,      # 256 bytes (4 blocks)
        block_size * 8,      # 512 bytes (8 blocks)
    ]
    
    print(f"SHA-256 block size: {block_size} bytes")
    print(f"\n{'Size':<8} {'Blocks':<8} {'Time(ms)':<10} {'Block Time(μs)':<15} {'Efficiency':<12}")
    print("-" * 70)
    
    for size in boundary_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        blocks = custom_metrics.get('actual_blocks', 0)
        block_time = custom_metrics.get('block_processing_time_us', 0)
        
        # Calculate efficiency (throughput relative to single block)
        if blocks > 0:
            efficiency = (size / blocks) / block_size
        else:
            efficiency = 0
        
        print(f"{size:<8} {blocks:<8} {result.execution_time_ms:<10.2f} "
              f"{block_time:<15.2f} {efficiency:<12.3f}")


def demo_scaling_behavior():
    """Demonstrate SHA-256 scaling behavior"""
    print("\n=== SHA-256 Scaling Behavior Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_scaling",
        enable_gpu_tracking=False
    )
    
    # Progressive input sizes for scaling analysis
    scaling_sizes = [64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]
    
    print(f"Running scaling analysis with sizes: {scaling_sizes}")
    
    results = analyzer.run_scaling_analysis(scaling_sizes)
    scaling = analyzer.analyze_scaling_behavior()
    
    print(f"\n=== Scaling Results ===")
    print(f"{'Size':<8} {'Time(ms)':<10} {'Throughput':<12} {'Blocks':<8} {'Blocks/s':<10}")
    print("-" * 60)
    
    for result in results:
        custom_metrics = result.custom_metrics
        print(f"{result.input_size:<8} {result.execution_time_ms:<10.2f} "
              f"{custom_metrics.get('throughput_mbps', 0):<12.2f} "
              f"{custom_metrics.get('actual_blocks', 0):<8} "
              f"{custom_metrics.get('blocks_per_second', 0):<10.0f}")
    
    if scaling:
        print(f"\n=== Scaling Analysis ===")
        print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
        print(f"Expected: ~1.0 for linear scaling (O(n))")
        
        if scaling['mean_scaling_factor'] < 1.2:
            print("✓ Excellent linear scaling behavior")
        elif scaling['mean_scaling_factor'] < 1.5:
            print("✓ Good scaling behavior")
        else:
            print("⚠ Scaling behavior may be suboptimal")


def demo_throughput_analysis():
    """Demonstrate throughput analysis for different input sizes"""
    print("\n=== SHA-256 Throughput Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_throughput",
        enable_gpu_tracking=False
    )
    
    # Test with larger sizes for stable throughput measurements
    throughput_sizes = [1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072]
    
    print(f"Testing throughput with larger sizes: {throughput_sizes}")
    
    throughputs = []
    hash_rates = []
    
    print(f"\n{'Size':<8} {'Time(ms)':<10} {'Throughput':<12} {'Hash Rate':<12} {'Blocks/s':<10}")
    print("-" * 65)
    
    for size in throughput_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        throughput = custom_metrics.get('throughput_mbps', 0)
        hash_rate = custom_metrics.get('hash_rate_per_second', 0)
        blocks_per_sec = custom_metrics.get('blocks_per_second', 0)
        
        throughputs.append(throughput)
        hash_rates.append(hash_rate)
        
        print(f"{size:<8} {result.execution_time_ms:<10.2f} "
              f"{throughput:<12.2f} {hash_rate:<12.2f} {blocks_per_sec:<10.0f}")
    
    # Calculate averages for larger sizes (more stable)
    stable_throughputs = throughputs[-4:]  # Last 4 measurements
    stable_hash_rates = hash_rates[-4:]
    
    if stable_throughputs:
        avg_throughput = sum(stable_throughputs) / len(stable_throughputs)
        avg_hash_rate = sum(stable_hash_rates) / len(stable_hash_rates)
        
        print(f"\n=== Average Performance (large sizes) ===")
        print(f"Average throughput: {avg_throughput:.2f} MB/s")
        print(f"Average hash rate: {avg_hash_rate:.2f} hashes/s")
        print(f"Estimated peak performance achieved")


def demo_compression_analysis():
    """Demonstrate compression ratio analysis"""
    print("\n=== SHA-256 Compression Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_compression",
        enable_gpu_tracking=False
    )
    
    # Test various input sizes to show compression ratios
    compression_sizes = [32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384]
    
    print(f"SHA-256 always produces 32-byte (256-bit) output")
    print(f"\n{'Input Size':<12} {'Output Size':<12} {'Compression':<12} {'Space Saved':<12}")
    print("-" * 55)
    
    for size in compression_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        output_size = custom_metrics.get('output_size_bytes', 32)
        compression_ratio = custom_metrics.get('compression_ratio', 0)
        space_saved = ((size - output_size) / size * 100) if size > 0 else 0
        
        print(f"{size:<12} {output_size:<12} {compression_ratio:<12.1f}x "
              f"{space_saved:<12.1f}%")
    
    print(f"\nNote: SHA-256 is a cryptographic hash, not a compression algorithm.")
    print(f"High compression ratios indicate the fixed 32-byte output size.")


def demo_hash_examples():
    """Show hash examples for different inputs"""
    print("\n=== SHA-256 Hash Examples ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/sha256_examples",
        enable_gpu_tracking=False
    )
    
    # Test with specific patterns
    test_cases = [
        (1, "Single byte"),
        (8, "8 bytes"),
        (32, "32 bytes (half block)"),
        (55, "55 bytes (< 1 block)"),
        (64, "64 bytes (1 block)"),
        (128, "128 bytes (2 blocks)"),
        (256, "256 bytes (4 blocks)")
    ]
    
    print(f"{'Size':<8} {'Description':<20} {'Hash (first 16 chars)':<20} {'Blocks':<8}")
    print("-" * 65)
    
    for size, description in test_cases:
        input_data = analyzer.prepare_input(size)
        hash_result = analyzer.run_algorithm(input_data)
        
        hash_preview = hash_result.get('hash_hex', '')[:16]
        blocks = hash_result.get('input_blocks', 0)
        
        print(f"{size:<8} {description:<20} {hash_preview:<20} {blocks:<8}")


def main():
    """Run comprehensive SHA-256 demonstration"""
    print("SHA-256 Hashing Scaling Analysis Demonstration")
    print("=" * 60)
    
    try:
        demo_basic_sha256_analysis()
        demo_block_boundary_analysis()
        demo_scaling_behavior()
        demo_throughput_analysis()
        demo_compression_analysis()
        demo_hash_examples()
        
        print("\n" + "=" * 60)
        print("SHA-256 demonstration completed successfully!")
        print("\nKey findings:")
        print("- SHA-256 shows linear O(n) scaling with input size")
        print("- Performance is measured in blocks processed per second")
        print("- Block boundaries (64 bytes) affect processing efficiency")
        print("- Consistent 32-byte output regardless of input size")
        print("- Results saved in demo_results/sha256_*/ directories")
        
    except Exception as e:
        print(f"\nSHA-256 demonstration failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
