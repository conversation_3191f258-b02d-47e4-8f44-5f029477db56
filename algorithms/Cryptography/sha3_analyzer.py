"""
SHA-3 Hashing Scaling Analyzer

This module provides scaling analysis for SHA-3 (Secure Hash Algorithm 3) hashing,
measuring both time and space complexity across different input sizes.
SHA-3 is based on the Keccak algorithm and uses a sponge construction.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
import hashlib
import secrets
from typing import Any, Dict, Tuple
from core.scaling_framework import ScalingAnalyzer


class SHA3HashingScalingAnalyzer(ScalingAnalyzer):
    """SHA-3 Hashing scaling analysis using unified framework"""
    
    def __init__(self, hash_size: int = 256, **kwargs):
        """
        Initialize SHA-3 Hashing analyzer
        
        Args:
            hash_size: SHA-3 hash size in bits (224, 256, 384, or 512)
        """
        if hash_size not in [224, 256, 384, 512]:
            raise ValueError(f"Invalid hash size: {hash_size}. Must be 224, 256, 384, or 512")
        
        super().__init__(algorithm_name=f"SHA3-{hash_size}-Hash", **kwargs)
        
        # SHA-3 specific constants
        self.hash_size_bits = hash_size
        self.hash_size_bytes = hash_size // 8
        
        # SHA-3 uses different rate values based on hash size
        # Rate = 1600 - 2 * hash_size (in bits)
        self.capacity_bits = 2 * hash_size
        self.rate_bits = 1600 - self.capacity_bits
        self.rate_bytes = self.rate_bits // 8
        
        # Keccak state size is always 1600 bits (200 bytes)
        self.state_size_bits = 1600
        self.state_size_bytes = 200
        
        # Select appropriate hash function
        if hash_size == 224:
            self.hash_func = hashlib.sha3_224
        elif hash_size == 256:
            self.hash_func = hashlib.sha3_256
        elif hash_size == 384:
            self.hash_func = hashlib.sha3_384
        elif hash_size == 512:
            self.hash_func = hashlib.sha3_512
    
    def prepare_input(self, input_size: int) -> bytes:
        """Generate input data for SHA-3 hashing"""
        np.random.seed(42)  # For reproducibility
        
        # Generate random input data
        input_data = secrets.token_bytes(input_size)
        
        return input_data
    
    def run_algorithm(self, input_data: bytes) -> Dict[str, Any]:
        """Execute SHA-3 hashing algorithm"""
        try:
            # Create SHA-3 hash object
            sha3_hash = self.hash_func()
            
            # Measure hashing time
            start_time = time.perf_counter()
            sha3_hash.update(input_data)
            hash_digest = sha3_hash.digest()
            hashing_time = time.perf_counter() - start_time
            
            # Calculate metrics
            input_size = len(input_data)
            
            # Calculate number of absorption rounds
            # Each round absorbs 'rate_bytes' of data
            absorption_rounds = (input_size + self.rate_bytes - 1) // self.rate_bytes
            
            throughput_mbps = (input_size / (1024 * 1024)) / hashing_time if hashing_time > 0 else 0
            
            # Calculate processing rates
            if hashing_time > 0:
                bytes_per_second = input_size / hashing_time
                rounds_per_second = absorption_rounds / hashing_time
            else:
                bytes_per_second = 0
                rounds_per_second = 0
            
            return {
                'hash_digest': hash_digest,
                'hash_hex': hash_digest.hex(),
                'input_size_bytes': input_size,
                'output_size_bytes': len(hash_digest),
                'hashing_time_seconds': hashing_time,
                'absorption_rounds': absorption_rounds,
                'throughput_mbps': throughput_mbps,
                'bytes_per_second': bytes_per_second,
                'rounds_per_second': rounds_per_second,
                'hash_algorithm': f'SHA3-{self.hash_size_bits}',
                'rate_bytes': self.rate_bytes,
                'capacity_bits': self.capacity_bits,
                'hash_size_bytes': self.hash_size_bytes,
                'state_size_bytes': self.state_size_bytes
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'hash_digest': b'',
                'hash_hex': '',
                'input_size_bytes': len(input_data) if input_data else 0,
                'output_size_bytes': 0,
                'hashing_time_seconds': 0,
                'absorption_rounds': 0,
                'throughput_mbps': 0,
                'bytes_per_second': 0,
                'rounds_per_second': 0,
                'hash_algorithm': f'SHA3-{self.hash_size_bits}',
                'rate_bytes': self.rate_bytes,
                'capacity_bits': self.capacity_bits,
                'hash_size_bytes': self.hash_size_bytes,
                'state_size_bytes': self.state_size_bytes
            }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for SHA-3 hashing"""
        # SHA-3 is O(n) where n is the number of absorption rounds
        rounds = (input_size + self.rate_bytes - 1) // self.rate_bytes
        
        time_complexity = f"O(n) [rounds={rounds}]"
        space_complexity = "O(1)"  # Constant space overhead
        
        # Memory estimation: input data + Keccak state + output hash
        theoretical_memory_mb = (
            input_size +  # Input data
            self.state_size_bytes +  # Keccak state (1600 bits)
            self.hash_size_bytes  # Output hash
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate SHA-3 hashing-specific metrics"""
        
        # Extract results
        hash_result = result
        
        # Calculate theoretical values
        theoretical_rounds = (input_size + self.rate_bytes - 1) // self.rate_bytes
        
        # Calculate efficiency metrics
        actual_rounds = hash_result.get('absorption_rounds', 0)
        throughput = hash_result.get('throughput_mbps', 0)
        
        # Calculate compression ratio (input size to output size)
        output_size = hash_result.get('output_size_bytes', 0)
        compression_ratio = input_size / output_size if output_size > 0 else 0
        
        # Calculate hash rate metrics
        hashing_time = hash_result.get('hashing_time_seconds', 0)
        if hashing_time > 0:
            hash_rate_per_second = 1 / hashing_time
            bits_per_second = (input_size * 8) / hashing_time
        else:
            hash_rate_per_second = 0
            bits_per_second = 0
        
        # Calculate round processing efficiency
        round_processing_time = hashing_time / actual_rounds if actual_rounds > 0 else 0
        
        # Calculate rate efficiency (how much of the rate is utilized)
        if actual_rounds > 0:
            avg_bytes_per_round = input_size / actual_rounds
            rate_utilization = avg_bytes_per_round / self.rate_bytes
        else:
            rate_utilization = 0
        
        return {
            'input_size_bytes': input_size,
            'output_size_bytes': output_size,
            'hash_algorithm': f'SHA3-{self.hash_size_bits}',
            'hash_size_bits': self.hash_size_bits,
            'rate_bits': self.rate_bits,
            'rate_bytes': self.rate_bytes,
            'capacity_bits': self.capacity_bits,
            'state_size_bits': self.state_size_bits,
            'theoretical_rounds': theoretical_rounds,
            'actual_rounds': actual_rounds,
            'compression_ratio': round(compression_ratio, 2),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(hash_result.get('bytes_per_second', 0)),
            'rounds_per_second': int(hash_result.get('rounds_per_second', 0)),
            'bits_per_second': int(bits_per_second),
            'hash_rate_per_second': round(hash_rate_per_second, 2),
            'hashing_time_ms': hashing_time * 1000,
            'round_processing_time_us': round_processing_time * 1000000,
            'rate_utilization': round(rate_utilization, 3),
            'hash_hex_length': len(hash_result.get('hash_hex', '')),
            'has_error': 'error' in hash_result,
            'algorithm_family': 'cryptographic_hash',
            'hash_family': 'SHA-3',
            'construction': 'sponge'
        }


def main():
    """Run SHA-3 Hashing scaling analysis"""
    print("=== SHA-3 Hashing Scaling Analysis ===")

    # Test different SHA-3 variants
    sha3_variants = [224, 256, 384, 512]

    for hash_size in sha3_variants:
        print(f"\n--- Testing SHA3-{hash_size} ---")

        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"results/sha3_{hash_size}_hash",
                enable_gpu_tracking=False
            )

            # Input sizes for testing (in bytes)
            # Test various sizes to analyze scaling behavior
            input_sizes = [
                # Small sizes
                1, 8, 16, 32, 64,
                
                # Rate-based sizes (different for each SHA-3 variant)
                analyzer.rate_bytes,  # One absorption round
                analyzer.rate_bytes * 2,  # Two rounds
                analyzer.rate_bytes * 4,  # Four rounds
                
                # Standard sizes
                256, 512, 1024,  # KB range
                2048, 4096, 8192,  # Multi-KB range
                16384, 32768,     # Larger KB range
                65536, 131072,    # Hundreds of KB
                262144, 524288,   # MB range
                1048576           # 1MB
            ]

            print(f"Rate: {analyzer.rate_bytes} bytes per absorption round")
            print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== SHA3-{hash_size} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for SHA-3 hashing (O(n))")

            print(f"\n=== Sample Results ===")
            # Show results for different size categories
            sample_indices = [0, 3, 7, 12, len(results)-1]  # Various samples
            
            for i in sample_indices:
                if i < len(results):
                    result = results[i]
                    custom_metrics = result.custom_metrics
                    print(f"Size {result.input_size:8d}B: "
                          f"{result.execution_time_ms:8.2f}ms, "
                          f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                          f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                          f"Rounds: {custom_metrics.get('actual_rounds', 0):6d}")

            # Show hash examples for small inputs
            print(f"\n=== Hash Examples ===")
            for size in [1, 8, 32, analyzer.rate_bytes]:
                if size <= max(input_sizes):
                    test_input = analyzer.prepare_input(size)
                    hash_result = analyzer.run_algorithm(test_input)
                    print(f"Size {size:3d}B: {hash_result.get('hash_hex', '')[:16]}... "
                          f"(rounds: {hash_result.get('absorption_rounds', 0)})")

        except Exception as e:
            print(f"Error in SHA3-{hash_size} hashing analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nSHA-3 Hashing scaling analysis completed!")
    print("Results saved in:")
    for hash_size in sha3_variants:
        print(f"- results/sha3_{hash_size}_hash/")


if __name__ == "__main__":
    main()
