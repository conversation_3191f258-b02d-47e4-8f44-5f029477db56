# AES Decryption Scaling Analysis

This module provides comprehensive scaling analysis for AES (Advanced Encryption Standard) decryption operations, measuring both time and space complexity across different input sizes, key lengths, and encryption modes.

## Overview

The AES Decryption Scaling Analyzer measures the performance characteristics of AES decryption operations by:
- Creating encrypted ciphertext from random plaintext data
- Measuring decryption time and throughput across various input sizes
- Verifying correctness by comparing decrypted output with original plaintext
- Analyzing scaling behavior and complexity patterns

## Features

### Supported Configurations
- **Key Sizes**: AES-128, AES-192, AES-256
- **Encryption Modes**: CBC, ECB, CFB, OFB, CTR, GCM
- **Padding Algorithms**: PKCS7, ANSIX923 (for block modes)

### Performance Metrics
- **Time Complexity**: O(n) linear scaling with input size
- **Space Complexity**: O(1) constant space overhead
- **Throughput**: MB/s and blocks/s processing rates
- **Correctness Verification**: Ensures decrypted data matches original plaintext
- **Compression Ratio**: Ratio of ciphertext to plaintext size

### Custom Metrics
- Original input size and ciphertext size
- Plaintext output size after decryption
- Theoretical vs actual block counts
- Padding removal overhead
- Decryption time in milliseconds
- Bytes and blocks processed per second
- Algorithm family classification

## Usage

### Basic Usage

```python
from algorithms.Cryptography.aes_decryption_analyzer import AESDecryptionScalingAnalyzer

# Create analyzer for AES-256-CBC decryption
analyzer = AESDecryptionScalingAnalyzer(
    key_size=256,
    mode='CBC',
    padding_algorithm='PKCS7',
    output_dir="results/aes_decrypt_256_cbc"
)

# Run scaling analysis
input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### Single Measurement

```python
# Measure single decryption operation
metrics = analyzer.measure_single_run(1024)  # 1KB input
print(f"Decryption time: {metrics.execution_time_ms:.2f}ms")
print(f"Throughput: {metrics.custom_metrics['throughput_mbps']:.2f}MB/s")
print(f"Correctness: {metrics.custom_metrics['correctness_verified']}")
```

### Mode Comparison

```python
modes = ['CBC', 'CTR', 'GCM']
for mode in modes:
    analyzer = AESDecryptionScalingAnalyzer(key_size=256, mode=mode)
    metrics = analyzer.measure_single_run(1024)
    print(f"{mode}: {metrics.custom_metrics['throughput_mbps']:.2f}MB/s")
```

## Configuration Options

### Constructor Parameters

- `key_size` (int): AES key size in bits (128, 192, or 256)
- `mode` (str): Encryption mode ('CBC', 'ECB', 'CFB', 'OFB', 'CTR', 'GCM')
- `padding_algorithm` (str): Padding algorithm ('PKCS7', 'ANSIX923')
- `output_dir` (str): Directory for saving results
- `enable_gpu_tracking` (bool): Enable GPU memory tracking

### Mode-Specific Behavior

#### Block Modes (CBC, ECB)
- Require padding for non-block-aligned data
- Use PKCS7 or ANSIX923 padding algorithms
- Process data in 16-byte blocks

#### Stream Modes (CTR, CFB, OFB)
- No padding required
- Can handle arbitrary input sizes
- Generate keystream for XOR operations

#### Authenticated Modes (GCM)
- No padding required
- Provide built-in authentication
- Use 96-bit nonces (12 bytes)

## Expected Performance Characteristics

### Time Complexity
- **Theoretical**: O(n) where n is the number of 16-byte blocks
- **Practical**: Linear scaling with input size
- **Scaling Factor**: ~1.0 for well-behaved implementations

### Space Complexity
- **Theoretical**: O(1) constant space overhead
- **Memory Usage**: Input + output + key + IV/nonce + intermediate buffers
- **Typical Overhead**: ~32-64 bytes plus 2x input size

### Throughput Expectations
- **Modern Hardware**: 100-500 MB/s for software implementations
- **Mode Impact**: Stream modes (CTR) often faster than block modes (CBC)
- **Key Size Impact**: Minimal difference between AES-128/192/256

## Running Tests and Demos

### Test Suite
```bash
python algorithms/Cryptography/test_aes_decryption.py
```

Tests include:
- Basic functionality verification
- Different mode testing
- Key size comparison
- Correctness verification
- Small-scale scaling analysis

### Demo Script
```bash
python algorithms/Cryptography/demo_aes_decryption.py
```

Demonstrates:
- Basic scaling analysis
- Mode performance comparison
- Key size impact analysis
- Encryption vs decryption comparison

### Full Analysis
```bash
python algorithms/Cryptography/aes_decryption_analyzer.py
```

Runs comprehensive analysis across:
- Multiple key sizes (128, 192, 256)
- Multiple modes (CBC, ECB, CTR, GCM)
- Wide range of input sizes (16B to 2MB)

## Output Files

Results are saved in the specified output directory:

### CSV File
- `AES-{keysize}-{mode}-Decrypt_scaling_results.csv`
- Contains detailed metrics for each input size
- Suitable for spreadsheet analysis and plotting

### JSON File
- `AES-{keysize}-{mode}-Decrypt_scaling_results.json`
- Machine-readable format with complete metadata
- Includes configuration parameters and custom metrics

## Implementation Details

### Correctness Verification
The analyzer ensures correctness by:
1. Generating random plaintext data
2. Encrypting it to create test ciphertext
3. Decrypting the ciphertext back to plaintext
4. Comparing decrypted result with original plaintext
5. Reporting verification status in metrics

### Padding Handling
- **Block modes**: Automatically apply and remove padding
- **Stream modes**: No padding required
- **Padding algorithms**: PKCS7 (default) and ANSIX923 supported
- **Overhead tracking**: Measures bytes added/removed by padding

### Key and IV Management
- **Fixed keys**: Uses deterministic key generation for reproducible results
- **IV handling**: Generates appropriate IVs/nonces for each mode
- **Security note**: Uses fixed seeds for testing; not suitable for production

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `cryptography` library is installed
2. **Mode Errors**: Verify mode name spelling and case
3. **Key Size Errors**: Use only 128, 192, or 256-bit keys
4. **Padding Errors**: Some modes don't support certain padding algorithms

### Performance Considerations

1. **Small Inputs**: May show high variance due to measurement overhead
2. **Large Inputs**: May be limited by available memory
3. **Mode Selection**: Choose appropriate mode for your use case
4. **Hardware Impact**: Results vary significantly across different systems

## Integration with Main Framework

The AES Decryption analyzer integrates with the main algorithm scaling framework:

```python
from algorithms import get_analyzer
analyzer = get_analyzer('aes_decrypt')  # When registered
```

Results follow the standard `ScalingResult` format and can be compared with other algorithms in the framework.
