#!/usr/bin/env python3
"""
Comprehensive AES Encryption and Decryption Scaling Analysis Demo

This script demonstrates both AES encryption and decryption scaling analysis,
comparing their performance characteristics across different configurations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import AESEncryptionScalingAnalyzer, AESDecryptionScalingAnalyzer


def demo_encryption_vs_decryption_scaling():
    """Compare encryption vs decryption scaling behavior"""
    print("=== AES Encryption vs Decryption Scaling Comparison ===")
    
    # Test configuration
    key_size = 256
    mode = 'CBC'
    input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]
    
    print(f"Configuration: AES-{key_size}-{mode}")
    print(f"Input sizes: {input_sizes}")
    
    # Test encryption
    print(f"\n--- Running Encryption Analysis ---")
    enc_analyzer = AESEncryptionScalingAnalyzer(
        key_size=key_size,
        mode=mode,
        output_dir="demo_results/comprehensive/encryption",
        enable_gpu_tracking=False
    )
    
    enc_results = enc_analyzer.run_scaling_analysis(input_sizes)
    enc_scaling = enc_analyzer.analyze_scaling_behavior()
    
    # Test decryption
    print(f"\n--- Running Decryption Analysis ---")
    dec_analyzer = AESDecryptionScalingAnalyzer(
        key_size=key_size,
        mode=mode,
        output_dir="demo_results/comprehensive/decryption",
        enable_gpu_tracking=False
    )
    
    dec_results = dec_analyzer.run_scaling_analysis(input_sizes)
    dec_scaling = dec_analyzer.analyze_scaling_behavior()
    
    # Compare results
    print(f"\n=== Detailed Performance Comparison ===")
    print(f"{'Size':<8} {'Enc Time':<10} {'Dec Time':<10} {'Enc Tput':<12} {'Dec Tput':<12} {'Ratio':<8}")
    print("-" * 70)
    
    for i, size in enumerate(input_sizes):
        enc_time = enc_results[i].execution_time_ms
        dec_time = dec_results[i].execution_time_ms
        enc_tput = enc_results[i].custom_metrics.get('throughput_mbps', 0)
        dec_tput = dec_results[i].custom_metrics.get('throughput_mbps', 0)
        
        ratio = dec_time / enc_time if enc_time > 0 else 1.0
        
        print(f"{size:<8}B {enc_time:<10.2f} {dec_time:<10.2f} "
              f"{enc_tput:<12.2f} {dec_tput:<12.2f} {ratio:<8.2f}")
    
    # Scaling analysis
    print(f"\n=== Scaling Behavior Analysis ===")
    if enc_scaling:
        print(f"Encryption scaling factor: {enc_scaling['mean_scaling_factor']:.2f} ± {enc_scaling['std_scaling_factor']:.2f}")
    if dec_scaling:
        print(f"Decryption scaling factor: {dec_scaling['mean_scaling_factor']:.2f} ± {dec_scaling['std_scaling_factor']:.2f}")
    print(f"Expected: ~1.0 for linear scaling (O(n))")


def demo_mode_performance_matrix():
    """Compare performance across different modes for both operations"""
    print(f"\n=== AES Mode Performance Matrix ===")
    
    modes = ['CBC', 'ECB', 'CTR', 'GCM']
    key_sizes = [128, 256]
    test_size = 1024  # 1KB test
    
    print(f"Test size: {test_size} bytes")
    print(f"\n{'Mode':<6} {'Key':<6} {'Enc Time':<10} {'Dec Time':<10} {'Enc Tput':<12} {'Dec Tput':<12} {'Correct':<8}")
    print("-" * 80)
    
    for key_size in key_sizes:
        for mode in modes:
            try:
                # Test encryption
                enc_analyzer = AESEncryptionScalingAnalyzer(
                    key_size=key_size,
                    mode=mode,
                    output_dir=f"demo_results/matrix/enc_{key_size}_{mode.lower()}",
                    enable_gpu_tracking=False
                )
                enc_metrics = enc_analyzer.measure_single_run(test_size)
                enc_custom = enc_metrics.custom_metrics
                
                # Test decryption
                dec_analyzer = AESDecryptionScalingAnalyzer(
                    key_size=key_size,
                    mode=mode,
                    output_dir=f"demo_results/matrix/dec_{key_size}_{mode.lower()}",
                    enable_gpu_tracking=False
                )
                dec_metrics = dec_analyzer.measure_single_run(test_size)
                dec_custom = dec_metrics.custom_metrics
                
                print(f"{mode:<6} {key_size:<6} {enc_metrics.execution_time_ms:<10.2f} "
                      f"{dec_metrics.execution_time_ms:<10.2f} "
                      f"{enc_custom.get('throughput_mbps', 0):<12.2f} "
                      f"{dec_custom.get('throughput_mbps', 0):<12.2f} "
                      f"{dec_custom.get('correctness_verified', False):<8}")
                
            except Exception as e:
                print(f"{mode:<6} {key_size:<6} ERROR: {str(e)[:40]}")


def demo_throughput_analysis():
    """Analyze throughput characteristics"""
    print(f"\n=== Throughput Analysis ===")
    
    # Test with larger sizes to get stable throughput measurements
    large_sizes = [1024, 2048, 4096, 8192, 16384, 32768]
    
    print(f"Testing with larger sizes for stable throughput: {large_sizes}")
    
    # AES-256-CBC for both operations
    enc_analyzer = AESEncryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/throughput/encryption",
        enable_gpu_tracking=False
    )
    
    dec_analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/throughput/decryption",
        enable_gpu_tracking=False
    )
    
    print(f"\n{'Size':<8} {'Enc Tput':<12} {'Dec Tput':<12} {'Enc Blocks/s':<12} {'Dec Blocks/s':<12}")
    print("-" * 65)
    
    for size in large_sizes:
        # Encryption
        enc_metrics = enc_analyzer.measure_single_run(size)
        enc_custom = enc_metrics.custom_metrics
        enc_tput = enc_custom.get('throughput_mbps', 0)
        enc_blocks_per_sec = enc_custom.get('blocks_per_second', 0)
        
        # Decryption
        dec_metrics = dec_analyzer.measure_single_run(size)
        dec_custom = dec_metrics.custom_metrics
        dec_tput = dec_custom.get('throughput_mbps', 0)
        dec_blocks_per_sec = dec_custom.get('blocks_per_second', 0)
        
        print(f"{size:<8} {enc_tput:<12.2f} {dec_tput:<12.2f} "
              f"{enc_blocks_per_sec:<12} {dec_blocks_per_sec:<12}")
    
    # Calculate average throughput for larger sizes (more stable)
    stable_sizes = large_sizes[-3:]  # Last 3 sizes
    
    enc_avg_tput = sum(enc_analyzer.measure_single_run(size).custom_metrics.get('throughput_mbps', 0) 
                      for size in stable_sizes) / len(stable_sizes)
    dec_avg_tput = sum(dec_analyzer.measure_single_run(size).custom_metrics.get('throughput_mbps', 0) 
                      for size in stable_sizes) / len(stable_sizes)
    
    print(f"\n=== Average Throughput (large sizes) ===")
    print(f"Encryption: {enc_avg_tput:.2f} MB/s")
    print(f"Decryption: {dec_avg_tput:.2f} MB/s")
    print(f"Ratio (Dec/Enc): {dec_avg_tput/enc_avg_tput:.2f}")


def demo_correctness_verification():
    """Demonstrate correctness verification across different configurations"""
    print(f"\n=== Correctness Verification ===")
    
    test_configs = [
        {'key_size': 128, 'mode': 'CBC'},
        {'key_size': 192, 'mode': 'CBC'},
        {'key_size': 256, 'mode': 'CBC'},
        {'key_size': 256, 'mode': 'ECB'},
        {'key_size': 256, 'mode': 'CTR'},
        {'key_size': 256, 'mode': 'GCM'}
    ]
    
    test_sizes = [16, 64, 256, 1024]
    
    print(f"Testing correctness across configurations and sizes")
    print(f"{'Config':<12} {'Size':<6} {'Correct':<8} {'Enc Size':<10} {'Dec Size':<10}")
    print("-" * 55)
    
    for config in test_configs:
        config_name = f"AES-{config['key_size']}-{config['mode']}"
        
        for size in test_sizes:
            try:
                dec_analyzer = AESDecryptionScalingAnalyzer(
                    **config,
                    output_dir=f"demo_results/correctness/{config_name.lower()}",
                    enable_gpu_tracking=False
                )
                
                # Prepare input (creates ciphertext from plaintext)
                input_data = dec_analyzer.prepare_input(size)
                
                # Decrypt
                result = dec_analyzer.run_algorithm(input_data)
                
                is_correct = result.get('is_correct', False)
                cipher_size = len(input_data['ciphertext'])
                plain_size = len(result.get('decrypted_plaintext', b''))
                
                print(f"{config_name:<12} {size:<6} {is_correct:<8} {cipher_size:<10} {plain_size:<10}")
                
            except Exception as e:
                print(f"{config_name:<12} {size:<6} ERROR: {str(e)[:20]}")


def main():
    """Run comprehensive AES analysis"""
    print("Comprehensive AES Encryption and Decryption Analysis")
    print("=" * 60)
    
    try:
        demo_encryption_vs_decryption_scaling()
        demo_mode_performance_matrix()
        demo_throughput_analysis()
        demo_correctness_verification()
        
        print("\n" + "=" * 60)
        print("Comprehensive analysis completed successfully!")
        print("\nKey findings:")
        print("- Both encryption and decryption show linear O(n) scaling")
        print("- Performance varies by mode (stream vs block modes)")
        print("- Correctness verification ensures data integrity")
        print("- Results saved in demo_results/comprehensive/ directory")
        
    except Exception as e:
        print(f"\nComprehensive analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
