#!/usr/bin/env python3
"""
Test suite for RSA Decryption Scaling Analyzer

This test suite validates the RSA decryption analyzer functionality,
including basic decryption, correctness verification, scaling analysis, and performance metrics.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import unittest
import tempfile
import shutil
from algorithms.Cryptography.rsa_decryption_analyzer import RSADecryptionScalingAnalyzer


class TestRSADecryptionAnalyzer(unittest.TestCase):
    """Test cases for RSA Decryption Scaling Analyzer"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create analyzer with small key for faster testing
        self.analyzer = RSADecryptionScalingAnalyzer(
            key_size=2048,
            padding_scheme='OAEP',
            output_dir=self.temp_dir,
            enable_gpu_tracking=False
        )
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_analyzer_initialization(self):
        """Test analyzer initialization with different configurations"""
        # Test valid configurations
        valid_configs = [
            {'key_size': 1024, 'padding_scheme': 'OAEP'},
            {'key_size': 2048, 'padding_scheme': 'PKCS1v15'},
            {'key_size': 3072, 'padding_scheme': 'OAEP'},
            {'key_size': 4096, 'padding_scheme': 'PKCS1v15'}
        ]
        
        for config in valid_configs:
            with self.subTest(config=config):
                analyzer = RSADecryptionScalingAnalyzer(**config, output_dir=self.temp_dir)
                self.assertEqual(analyzer.key_size, config['key_size'])
                self.assertEqual(analyzer.padding_scheme, config['padding_scheme'])
                self.assertIsNotNone(analyzer.private_key)
                self.assertIsNotNone(analyzer.public_key)
        
        # Test invalid configurations
        invalid_configs = [
            {'key_size': 512, 'padding_scheme': 'OAEP'},  # Too small
            {'key_size': 2048, 'padding_scheme': 'INVALID'},  # Invalid padding
            {'key_size': 8192, 'padding_scheme': 'OAEP'},  # Unsupported size
        ]
        
        for config in invalid_configs:
            with self.subTest(config=config):
                with self.assertRaises(ValueError):
                    RSADecryptionScalingAnalyzer(**config, output_dir=self.temp_dir)
    
    def test_input_preparation(self):
        """Test input data preparation (encryption for decryption testing)"""
        test_sizes = [16, 64, 128, self.analyzer.max_plaintext_size]
        
        for size in test_sizes:
            with self.subTest(size=size):
                input_data = self.analyzer.prepare_input(size)
                
                # Check required fields
                self.assertIn('ciphertext', input_data)
                self.assertIn('original_plaintext', input_data)
                self.assertIn('ciphertext_size', input_data)
                self.assertIn('original_plaintext_size', input_data)
                self.assertIn('max_chunk_size', input_data)
                self.assertIn('chunks_needed', input_data)
                
                # Check data validity
                self.assertEqual(len(input_data['original_plaintext']), size)
                self.assertEqual(input_data['original_plaintext_size'], size)
                self.assertGreater(len(input_data['ciphertext']), 0)
                self.assertEqual(input_data['max_chunk_size'], self.analyzer.max_plaintext_size)
                
                # Check chunk calculation
                expected_chunks = (size + self.analyzer.max_plaintext_size - 1) // self.analyzer.max_plaintext_size
                self.assertEqual(input_data['chunks_needed'], expected_chunks)
                
                # Check ciphertext size (each chunk becomes key_size bytes)
                expected_ciphertext_size = expected_chunks * self.analyzer.key_size_bytes
                self.assertEqual(input_data['ciphertext_size'], expected_ciphertext_size)
    
    def test_basic_decryption(self):
        """Test basic RSA decryption functionality"""
        test_sizes = [16, 32, 64, 128]
        
        for size in test_sizes:
            with self.subTest(size=size):
                input_data = self.analyzer.prepare_input(size)
                result = self.analyzer.run_algorithm(input_data)
                
                # Check basic result structure
                self.assertIn('decrypted_plaintext', result)
                self.assertIn('original_plaintext', result)
                self.assertIn('ciphertext_size', result)
                self.assertIn('plaintext_size', result)
                self.assertIn('decryption_time_seconds', result)
                self.assertIn('chunks_decrypted', result)
                self.assertIn('is_correct', result)
                
                # Check result validity
                self.assertGreater(len(result['decrypted_plaintext']), 0)
                self.assertEqual(result['plaintext_size'], size)
                self.assertGreater(result['decryption_time_seconds'], 0)
                self.assertGreater(result['chunks_decrypted'], 0)
                
                # Check correctness
                self.assertTrue(result['is_correct'])
                self.assertEqual(result['decrypted_plaintext'], result['original_plaintext'])
                
                # Check no errors
                self.assertNotIn('error', result)
    
    def test_decryption_correctness(self):
        """Test decryption correctness verification"""
        test_sizes = [32, 64, 128, self.analyzer.max_plaintext_size // 2]
        
        for size in test_sizes:
            with self.subTest(size=size):
                input_data = self.analyzer.prepare_input(size)
                result = self.analyzer.run_algorithm(input_data)
                
                # Verify decryption correctness
                original = input_data['original_plaintext']
                decrypted = result['decrypted_plaintext']
                
                self.assertEqual(len(original), len(decrypted))
                self.assertEqual(original, decrypted)
                self.assertTrue(result['is_correct'])
    
    def test_chunk_processing(self):
        """Test decryption with multiple chunks"""
        max_size = self.analyzer.max_plaintext_size
        
        # Test sizes that require multiple chunks
        test_sizes = [
            max_size + 1,  # Just over one chunk
            max_size * 2,  # Exactly two chunks
            max_size * 2 + 10,  # Two chunks plus extra
            max_size * 3  # Three chunks
        ]
        
        for size in test_sizes:
            with self.subTest(size=size):
                input_data = self.analyzer.prepare_input(size)
                result = self.analyzer.run_algorithm(input_data)
                
                expected_chunks = (size + max_size - 1) // max_size
                
                # Check chunk processing
                self.assertEqual(result['chunks_decrypted'], expected_chunks)
                
                # Check correctness for multi-chunk decryption
                self.assertTrue(result['is_correct'])
                self.assertEqual(result['decrypted_plaintext'], result['original_plaintext'])
                
                # Check no errors
                self.assertNotIn('error', result)
    
    def test_decryption_metrics(self):
        """Test decryption-specific metrics calculation"""
        test_size = 128
        input_data = self.analyzer.prepare_input(test_size)
        result = self.analyzer.run_algorithm(input_data)
        
        # Check throughput calculation
        expected_throughput = (test_size / (1024 * 1024)) / result['decryption_time_seconds']
        self.assertAlmostEqual(result['throughput_mbps'], expected_throughput, places=5)
        
        # Check compression ratio (ciphertext to plaintext)
        expected_compression = result['ciphertext_size'] / result['plaintext_size']
        self.assertAlmostEqual(result['compression_ratio'], expected_compression, places=5)
        
        # Check chunks per second
        expected_chunks_per_sec = result['chunks_decrypted'] / result['decryption_time_seconds']
        self.assertAlmostEqual(result['chunks_per_second'], expected_chunks_per_sec, places=5)
    
    def test_custom_metrics_calculation(self):
        """Test custom metrics calculation"""
        test_size = 64
        input_data = self.analyzer.prepare_input(test_size)
        algorithm_result = self.analyzer.run_algorithm(input_data)
        
        custom_metrics = self.analyzer.calculate_custom_metrics(test_size, input_data, algorithm_result)
        
        # Check required metrics
        required_metrics = [
            'input_size_bytes', 'ciphertext_size_bytes', 'plaintext_size_bytes',
            'key_size_bits', 'key_size_bytes', 'padding_scheme',
            'max_chunk_size_bytes', 'theoretical_chunks', 'actual_chunks',
            'compression_ratio', 'throughput_mbps', 'bytes_per_second',
            'chunks_per_second', 'bits_per_second', 'decryption_rate_per_second',
            'decryption_time_ms', 'chunk_processing_time_ms', 'key_utilization',
            'correctness_verified', 'has_error', 'algorithm_family', 'algorithm_type', 'operation'
        ]
        
        for metric in required_metrics:
            with self.subTest(metric=metric):
                self.assertIn(metric, custom_metrics)
        
        # Check specific values
        self.assertEqual(custom_metrics['input_size_bytes'], test_size)
        self.assertEqual(custom_metrics['key_size_bits'], self.analyzer.key_size)
        self.assertEqual(custom_metrics['padding_scheme'], self.analyzer.padding_scheme)
        self.assertEqual(custom_metrics['algorithm_type'], 'RSA')
        self.assertEqual(custom_metrics['operation'], 'decryption')
        self.assertTrue(custom_metrics['correctness_verified'])
        self.assertFalse(custom_metrics['has_error'])
    
    def test_theoretical_complexity(self):
        """Test theoretical complexity calculation"""
        test_sizes = [64, 128, 256, self.analyzer.max_plaintext_size * 2]
        
        for size in test_sizes:
            with self.subTest(size=size):
                time_complexity, space_complexity, memory_mb = self.analyzer.get_theoretical_complexity(size)
                
                # Check complexity strings
                self.assertIn('O(n*k^3)', time_complexity)
                self.assertIn(str(self.analyzer.key_size), time_complexity)
                self.assertEqual(space_complexity, 'O(n)')
                
                # Check memory estimation is reasonable
                self.assertGreater(memory_mb, 0)
                self.assertLess(memory_mb, 1000)  # Should be reasonable for test sizes
    
    def test_scaling_analysis(self):
        """Test scaling analysis functionality"""
        # Use smaller sizes for faster testing
        max_size = self.analyzer.max_plaintext_size
        input_sizes = [16, 32, 64, max_size // 2, max_size, max_size + 10]
        
        print(f"\n=== Testing RSA Decryption Scaling Analysis ===")
        print(f"Key size: {self.analyzer.key_size} bits")
        print(f"Padding: {self.analyzer.padding_scheme}")
        print(f"Max plaintext size: {max_size} bytes")
        
        results = self.analyzer.run_scaling_analysis(input_sizes)
        
        # Check results structure
        self.assertEqual(len(results), len(input_sizes))
        
        for i, result in enumerate(results):
            with self.subTest(size=input_sizes[i]):
                self.assertEqual(result.input_size, input_sizes[i])
                self.assertGreater(result.execution_time_ms, 0)
                self.assertIsInstance(result.custom_metrics, dict)
                
                # Verify correctness for all test sizes
                self.assertTrue(result.custom_metrics.get('correctness_verified', False))
                
                # Print sample results
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:4d}B: "
                      f"{result.execution_time_ms:6.2f}ms, "
                      f"Throughput: {custom_metrics.get('throughput_mbps', 0):6.2f}MB/s, "
                      f"Chunks: {custom_metrics.get('actual_chunks', 0):2d}, "
                      f"Correct: {custom_metrics.get('correctness_verified', False)}")
        
        # Test scaling behavior analysis
        scaling_analysis = self.analyzer.analyze_scaling_behavior()
        
        if scaling_analysis:
            print(f"\nScaling factor: {scaling_analysis['mean_scaling_factor']:.2f} ± {scaling_analysis['std_scaling_factor']:.2f}")
            print("Expected: Variable due to chunk-based processing")
            
            # Check scaling analysis structure
            self.assertIn('mean_scaling_factor', scaling_analysis)
            self.assertIn('std_scaling_factor', scaling_analysis)
            self.assertIsInstance(scaling_analysis['mean_scaling_factor'], float)
            self.assertIsInstance(scaling_analysis['std_scaling_factor'], float)
    
    def test_different_key_sizes(self):
        """Test decryption with different key sizes"""
        key_sizes = [1024, 2048, 3072]  # Skip 4096 for speed
        test_size = 64
        
        print(f"\n=== Testing Different RSA Key Sizes ===")
        
        for key_size in key_sizes:
            with self.subTest(key_size=key_size):
                analyzer = RSADecryptionScalingAnalyzer(
                    key_size=key_size,
                    padding_scheme='OAEP',
                    output_dir=self.temp_dir
                )
                
                input_data = analyzer.prepare_input(test_size)
                result = analyzer.run_algorithm(input_data)
                
                # Check basic functionality
                self.assertNotIn('error', result)
                self.assertEqual(result['plaintext_size'], test_size)
                self.assertEqual(result['key_size'], key_size)
                self.assertTrue(result['is_correct'])
                
                print(f"RSA-{key_size}: {result['decryption_time_seconds']*1000:.2f}ms, "
                      f"Throughput: {result['throughput_mbps']:.2f}MB/s, "
                      f"Correct: {result['is_correct']}")
    
    def test_different_padding_schemes(self):
        """Test decryption with different padding schemes"""
        padding_schemes = ['OAEP', 'PKCS1v15']
        test_size = 64
        
        print(f"\n=== Testing Different Padding Schemes ===")
        
        for padding in padding_schemes:
            with self.subTest(padding=padding):
                analyzer = RSADecryptionScalingAnalyzer(
                    key_size=2048,
                    padding_scheme=padding,
                    output_dir=self.temp_dir
                )
                
                input_data = analyzer.prepare_input(test_size)
                result = analyzer.run_algorithm(input_data)
                
                # Check basic functionality
                self.assertNotIn('error', result)
                self.assertEqual(result['plaintext_size'], test_size)
                self.assertEqual(result['padding_scheme'], padding)
                self.assertTrue(result['is_correct'])
                
                print(f"RSA-2048-{padding}: {result['decryption_time_seconds']*1000:.2f}ms, "
                      f"Max plaintext: {analyzer.max_plaintext_size}B, "
                      f"Throughput: {result['throughput_mbps']:.2f}MB/s, "
                      f"Correct: {result['is_correct']}")
    
    def test_encryption_decryption_consistency(self):
        """Test that decryption correctly reverses encryption"""
        test_sizes = [32, 64, 128, self.analyzer.max_plaintext_size]
        
        print(f"\n=== Testing Encryption-Decryption Consistency ===")
        
        for size in test_sizes:
            with self.subTest(size=size):
                # Prepare input creates encrypted data from random plaintext
                input_data = self.analyzer.prepare_input(size)
                
                # Decrypt the prepared ciphertext
                result = self.analyzer.run_algorithm(input_data)
                
                # Verify perfect reconstruction
                original = input_data['original_plaintext']
                decrypted = result['decrypted_plaintext']
                
                self.assertEqual(len(original), size)
                self.assertEqual(len(decrypted), size)
                self.assertEqual(original, decrypted)
                self.assertTrue(result['is_correct'])
                
                print(f"Size {size:4d}B: Decryption correct, "
                      f"Time: {result['decryption_time_seconds']*1000:.2f}ms")


def main():
    """Run RSA decryption tests"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main()
