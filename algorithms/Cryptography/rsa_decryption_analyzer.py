"""
RSA Decryption Scaling Analyzer

This module provides scaling analysis for RSA (Rivest-Shamir-Ad<PERSON>an) decryption,
measuring both time and space complexity across different input sizes and key sizes.
RSA decryption is typically slower than encryption due to private key operations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
import secrets
from typing import Any, Dict, Tuple
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from core.scaling_framework import ScalingAnalyzer


class RSADecryptionScalingAnalyzer(ScalingAnalyzer):
    """RSA Decryption scaling analysis using unified framework"""
    
    def __init__(self, key_size: int = 2048, padding_scheme: str = 'OAEP', **kwargs):
        """
        Initialize RSA Decryption analyzer
        
        Args:
            key_size: RSA key size in bits (1024, 2048, 3072, 4096)
            padding_scheme: Padding scheme ('OAEP', 'PKCS1v15')
        """
        if key_size not in [1024, 2048, 3072, 4096]:
            raise ValueError(f"Invalid key size: {key_size}. Must be 1024, 2048, 3072, or 4096")
        
        if padding_scheme not in ['OAEP', 'PKCS1v15']:
            raise ValueError(f"Invalid padding scheme: {padding_scheme}. Must be 'OAEP' or 'PKCS1v15'")
        
        super().__init__(algorithm_name=f"RSA-{key_size}-{padding_scheme}-Decrypt", **kwargs)
        
        # RSA specific parameters
        self.key_size = key_size
        self.key_size_bytes = key_size // 8
        self.padding_scheme = padding_scheme
        
        # Calculate maximum plaintext size based on padding
        if padding_scheme == 'OAEP':
            # OAEP padding overhead: 2 * hash_length + 2 (SHA-256 = 32 bytes)
            self.max_plaintext_size = self.key_size_bytes - 2 * 32 - 2
        else:  # PKCS1v15
            # PKCS#1 v1.5 padding overhead: 11 bytes minimum
            self.max_plaintext_size = self.key_size_bytes - 11
        
        # Generate RSA key pair
        self._generate_key_pair()
        
        # Set up padding object
        self._setup_padding()
    
    def _generate_key_pair(self):
        """Generate RSA key pair"""
        try:
            self.private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=self.key_size
            )
            self.public_key = self.private_key.public_key()
        except Exception as e:
            raise RuntimeError(f"Failed to generate RSA key pair: {e}")
    
    def _setup_padding(self):
        """Setup padding scheme"""
        if self.padding_scheme == 'OAEP':
            self.padding_obj = padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        else:  # PKCS1v15
            self.padding_obj = padding.PKCS1v15()
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Generate encrypted data for RSA decryption"""
        np.random.seed(42)  # For reproducibility
        
        # For decryption, we need to create ciphertext by first encrypting plaintext
        # RSA can only encrypt data smaller than key size minus padding
        if input_size > self.max_plaintext_size:
            # For larger inputs, we'll encrypt in chunks
            actual_size = min(input_size, self.max_plaintext_size * 10)  # Limit for demo
            chunks_needed = (actual_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        else:
            actual_size = input_size
            chunks_needed = 1
        
        # Generate random plaintext
        original_plaintext = secrets.token_bytes(actual_size)
        
        # Encrypt the plaintext to create ciphertext for decryption testing
        ciphertext_chunks = []
        for i in range(0, actual_size, self.max_plaintext_size):
            chunk = original_plaintext[i:i + self.max_plaintext_size]
            encrypted_chunk = self.public_key.encrypt(chunk, self.padding_obj)
            ciphertext_chunks.append(encrypted_chunk)
        
        ciphertext = b''.join(ciphertext_chunks)
        
        return {
            'ciphertext': ciphertext,
            'original_plaintext': original_plaintext,
            'ciphertext_size': len(ciphertext),
            'original_plaintext_size': len(original_plaintext),
            'max_chunk_size': self.max_plaintext_size,
            'chunks_needed': chunks_needed,
            'key_size': self.key_size,
            'padding_scheme': self.padding_scheme
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute RSA decryption algorithm"""
        try:
            ciphertext = input_data['ciphertext']
            original_plaintext = input_data['original_plaintext']
            ciphertext_size = len(ciphertext)
            chunk_size = self.key_size_bytes  # Each encrypted chunk is key_size bytes
            
            # Measure decryption time
            start_time = time.perf_counter()
            
            plaintext_chunks = []
            chunks_decrypted = 0
            
            # Decrypt in chunks
            for i in range(0, ciphertext_size, chunk_size):
                encrypted_chunk = ciphertext[i:i + chunk_size]
                
                # Decrypt chunk
                decrypted_chunk = self.private_key.decrypt(encrypted_chunk, self.padding_obj)
                plaintext_chunks.append(decrypted_chunk)
                chunks_decrypted += 1
            
            decryption_time = time.perf_counter() - start_time
            
            # Combine all decrypted chunks
            decrypted_plaintext = b''.join(plaintext_chunks)
            
            # Verify correctness
            is_correct = decrypted_plaintext == original_plaintext
            
            # Calculate metrics
            plaintext_size = len(decrypted_plaintext)
            throughput_mbps = (plaintext_size / (1024 * 1024)) / decryption_time if decryption_time > 0 else 0
            
            # Calculate chunks per second
            chunks_per_second = chunks_decrypted / decryption_time if decryption_time > 0 else 0
            bytes_per_second = plaintext_size / decryption_time if decryption_time > 0 else 0
            
            # Calculate compression ratio (ciphertext to plaintext)
            compression_ratio = ciphertext_size / plaintext_size if plaintext_size > 0 else 0
            
            return {
                'decrypted_plaintext': decrypted_plaintext,
                'original_plaintext': original_plaintext,
                'ciphertext_size': ciphertext_size,
                'plaintext_size': plaintext_size,
                'decryption_time_seconds': decryption_time,
                'chunks_decrypted': chunks_decrypted,
                'throughput_mbps': throughput_mbps,
                'bytes_per_second': bytes_per_second,
                'chunks_per_second': chunks_per_second,
                'compression_ratio': compression_ratio,
                'is_correct': is_correct,
                'key_size': self.key_size,
                'padding_scheme': self.padding_scheme,
                'max_chunk_size': self.max_plaintext_size
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'decrypted_plaintext': b'',
                'original_plaintext': input_data.get('original_plaintext', b''),
                'ciphertext_size': len(input_data.get('ciphertext', b'')),
                'plaintext_size': 0,
                'decryption_time_seconds': 0,
                'chunks_decrypted': 0,
                'throughput_mbps': 0,
                'bytes_per_second': 0,
                'chunks_per_second': 0,
                'compression_ratio': 0,
                'is_correct': False,
                'key_size': self.key_size,
                'padding_scheme': self.padding_scheme,
                'max_chunk_size': self.max_plaintext_size
            }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for RSA decryption"""
        # RSA decryption is O(k^3) where k is key size, but for fixed key size it's O(n) for chunks
        chunks = (input_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        
        time_complexity = f"O(n*k^3) [chunks={chunks}, k={self.key_size}]"
        space_complexity = "O(n)"  # Output size grows with input
        
        # Memory estimation: input ciphertext + output plaintext + key storage
        theoretical_memory_mb = (
            chunks * self.key_size_bytes +  # Input ciphertext
            input_size +  # Output plaintext
            self.key_size_bytes * 2  # Key storage (public + private)
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate RSA decryption-specific metrics"""
        
        # Extract results
        decryption_result = result
        
        # Calculate theoretical values
        theoretical_chunks = (input_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        
        # Calculate efficiency metrics
        actual_chunks = decryption_result.get('chunks_decrypted', 0)
        throughput = decryption_result.get('throughput_mbps', 0)
        
        # Calculate compression metrics
        ciphertext_size = decryption_result.get('ciphertext_size', 0)
        plaintext_size = decryption_result.get('plaintext_size', 0)
        compression_ratio = decryption_result.get('compression_ratio', 0)
        
        # Calculate decryption rate metrics
        decryption_time = decryption_result.get('decryption_time_seconds', 0)
        if decryption_time > 0:
            decryption_rate_per_second = 1 / decryption_time
            bits_per_second = (plaintext_size * 8) / decryption_time
        else:
            decryption_rate_per_second = 0
            bits_per_second = 0
        
        # Calculate chunk processing efficiency
        chunk_processing_time = decryption_time / actual_chunks if actual_chunks > 0 else 0
        
        # Calculate key utilization
        key_utilization = plaintext_size / (actual_chunks * self.max_plaintext_size) if actual_chunks > 0 else 0
        
        # Correctness verification
        is_correct = decryption_result.get('is_correct', False)
        
        return {
            'input_size_bytes': input_size,
            'ciphertext_size_bytes': ciphertext_size,
            'plaintext_size_bytes': plaintext_size,
            'key_size_bits': self.key_size,
            'key_size_bytes': self.key_size_bytes,
            'padding_scheme': self.padding_scheme,
            'max_chunk_size_bytes': self.max_plaintext_size,
            'theoretical_chunks': theoretical_chunks,
            'actual_chunks': actual_chunks,
            'compression_ratio': round(compression_ratio, 2),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(decryption_result.get('bytes_per_second', 0)),
            'chunks_per_second': int(decryption_result.get('chunks_per_second', 0)),
            'bits_per_second': int(bits_per_second),
            'decryption_rate_per_second': round(decryption_rate_per_second, 2),
            'decryption_time_ms': decryption_time * 1000,
            'chunk_processing_time_ms': chunk_processing_time * 1000,
            'key_utilization': round(key_utilization, 3),
            'correctness_verified': is_correct,
            'has_error': 'error' in decryption_result,
            'algorithm_family': 'asymmetric_encryption',
            'algorithm_type': 'RSA',
            'operation': 'decryption'
        }


def main():
    """Run RSA Decryption scaling analysis"""
    print("=== RSA Decryption Scaling Analysis ===")

    # Test different RSA configurations
    configurations = [
        {'key_size': 2048, 'padding_scheme': 'OAEP'},
        {'key_size': 2048, 'padding_scheme': 'PKCS1v15'},
        {'key_size': 3072, 'padding_scheme': 'OAEP'},
        {'key_size': 4096, 'padding_scheme': 'OAEP'}
    ]

    for config in configurations:
        print(f"\n--- Testing RSA-{config['key_size']}-{config['padding_scheme']} Decryption ---")

        try:
            analyzer = RSADecryptionScalingAnalyzer(
                **config,
                output_dir=f"results/rsa_{config['key_size']}_{config['padding_scheme'].lower()}_decrypt",
                enable_gpu_tracking=False
            )

            print(f"Key size: {analyzer.key_size} bits")
            print(f"Max plaintext size: {analyzer.max_plaintext_size} bytes")
            print(f"Padding scheme: {analyzer.padding_scheme}")

            # Input sizes for testing (limited by RSA chunk size)
            max_size = analyzer.max_plaintext_size
            input_sizes = [
                # Small sizes
                16, 32, 64, 128,
                
                # Approach max chunk size
                max_size // 4,
                max_size // 2,
                max_size - 10,
                max_size,
                
                # Multiple chunks
                max_size + 10,
                max_size * 2,
                max_size * 3,
                max_size * 4
            ]

            # Filter out invalid sizes
            input_sizes = [size for size in input_sizes if size > 0 and size <= max_size * 10]

            print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== RSA-{config['key_size']}-{config['padding_scheme']} Decryption Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Note: RSA decryption scaling depends on chunk processing")

            print(f"\n=== Sample Results ===")
            # Show results for different size categories
            sample_indices = [0, len(input_sizes)//4, len(input_sizes)//2, len(input_sizes)-1]
            
            for i in sample_indices:
                if i < len(results):
                    result = results[i]
                    custom_metrics = result.custom_metrics
                    correctness = custom_metrics.get('correctness_verified', False)
                    print(f"Size {result.input_size:6d}B: "
                          f"{result.execution_time_ms:8.2f}ms, "
                          f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                          f"Chunks: {custom_metrics.get('actual_chunks', 0):3d}, "
                          f"Correct: {correctness}")

        except Exception as e:
            print(f"Error in RSA-{config['key_size']}-{config['padding_scheme']} decryption analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nRSA Decryption scaling analysis completed!")
    print("Results saved in: results/rsa_*_decrypt/")


if __name__ == "__main__":
    main()
