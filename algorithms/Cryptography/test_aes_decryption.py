#!/usr/bin/env python3
"""
Test script for AES Decryption Scaling Analyzer

This script provides a simple test to verify that the AES decryption analyzer works correctly
with different configurations and input sizes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography.aes_decryption_analyzer import AESDecryptionScalingAnalyzer


def test_basic_functionality():
    """Test basic AES decryption functionality"""
    print("=== Testing Basic AES Decryption Functionality ===")
    
    # Test AES-256-CBC
    analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="test_results/aes_decrypt_basic_test",
        enable_gpu_tracking=False
    )
    
    # Test with small input sizes
    test_sizes = [16, 64, 256, 1024]
    
    print(f"Testing with input sizes: {test_sizes}")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        # Prepare input (this creates encrypted data for decryption)
        input_data = analyzer.prepare_input(size)
        print(f"  Ciphertext prepared: {len(input_data['ciphertext'])} bytes")
        print(f"  Original plaintext: {len(input_data['original_plaintext'])} bytes")
        
        # Run algorithm (decrypt the ciphertext)
        result = analyzer.run_algorithm(input_data)
        print(f"  Decryption result: {len(result.get('decrypted_plaintext', b''))} bytes output")
        print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
        print(f"  Blocks processed: {result.get('input_blocks', 0)}")
        print(f"  Correctness verified: {result.get('is_correct', False)}")
        
        # Get theoretical complexity
        time_comp, space_comp, mem_mb = analyzer.get_theoretical_complexity(size)
        print(f"  Theoretical: {time_comp}, {space_comp}, {mem_mb:.4f} MB")
        
        # Calculate custom metrics
        custom_metrics = analyzer.calculate_custom_metrics(size, input_data, result)
        print(f"  Custom metrics: {len(custom_metrics)} metrics calculated")
        print(f"  Compression ratio: {custom_metrics.get('compression_ratio', 0):.3f}")
        print(f"  Padding removed: {custom_metrics.get('padding_removed_bytes', 0)} bytes")


def test_different_modes():
    """Test different AES decryption modes"""
    print("\n=== Testing Different AES Decryption Modes ===")
    
    modes = ['CBC', 'ECB', 'CTR', 'GCM']
    test_size = 1024
    
    for mode in modes:
        print(f"\nTesting AES-256-{mode} Decryption:")
        
        try:
            analyzer = AESDecryptionScalingAnalyzer(
                key_size=256,
                mode=mode,
                output_dir=f"test_results/aes_decrypt_{mode.lower()}_test",
                enable_gpu_tracking=False
            )
            
            input_data = analyzer.prepare_input(test_size)
            result = analyzer.run_algorithm(input_data)
            
            print(f"  Success: {len(result.get('decrypted_plaintext', b''))} bytes output")
            print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
            print(f"  Mode: {result.get('mode', 'Unknown')}")
            print(f"  Correctness: {result.get('is_correct', False)}")
            
            if 'error' in result:
                print(f"  Error: {result['error']}")
                
        except Exception as e:
            print(f"  Failed: {e}")


def test_different_key_sizes():
    """Test different AES key sizes for decryption"""
    print("\n=== Testing Different Key Sizes for Decryption ===")
    
    key_sizes = [128, 192, 256]
    test_size = 1024
    
    for key_size in key_sizes:
        print(f"\nTesting AES-{key_size}-CBC Decryption:")
        
        try:
            analyzer = AESDecryptionScalingAnalyzer(
                key_size=key_size,
                mode='CBC',
                output_dir=f"test_results/aes_decrypt_{key_size}_test",
                enable_gpu_tracking=False
            )
            
            input_data = analyzer.prepare_input(test_size)
            result = analyzer.run_algorithm(input_data)
            
            print(f"  Success: {len(result.get('decrypted_plaintext', b''))} bytes output")
            print(f"  Key size: {result.get('key_size_bits', 0)} bits")
            print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
            print(f"  Correctness: {result.get('is_correct', False)}")
            
        except Exception as e:
            print(f"  Failed: {e}")


def test_correctness_verification():
    """Test that decryption correctly recovers original plaintext"""
    print("\n=== Testing Correctness Verification ===")
    
    analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="test_results/aes_decrypt_correctness_test",
        enable_gpu_tracking=False
    )
    
    test_sizes = [16, 64, 256, 1024]
    
    for size in test_sizes:
        print(f"\nTesting correctness for size {size} bytes:")
        
        # Prepare input data
        input_data = analyzer.prepare_input(size)
        original_plaintext = input_data['original_plaintext']
        
        # Decrypt the ciphertext
        result = analyzer.run_algorithm(input_data)
        decrypted_plaintext = result.get('decrypted_plaintext', b'')
        
        # Verify correctness
        is_correct = original_plaintext == decrypted_plaintext
        print(f"  Original size: {len(original_plaintext)} bytes")
        print(f"  Decrypted size: {len(decrypted_plaintext)} bytes")
        print(f"  Correctness: {is_correct}")
        print(f"  Algorithm reports: {result.get('is_correct', False)}")
        
        if not is_correct:
            print(f"  ERROR: Decryption failed to recover original plaintext!")
        else:
            print(f"  SUCCESS: Decryption correctly recovered original plaintext")


def test_scaling_analysis():
    """Test a small scaling analysis for decryption"""
    print("\n=== Testing Decryption Scaling Analysis ===")
    
    analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="test_results/aes_decrypt_scaling_test",
        enable_gpu_tracking=False
    )
    
    # Small input sizes for quick test
    input_sizes = [64, 128, 256, 512, 1024]
    
    print(f"Running decryption scaling analysis with sizes: {input_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(input_sizes)
        
        print(f"\nDecryption scaling analysis completed!")
        print(f"Results: {len(results)} data points")
        
        # Show some results
        for i, result in enumerate(results):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:4d}B: "
                  f"{result.execution_time_ms:6.2f}ms, "
                  f"Throughput: {custom_metrics.get('throughput_mbps', 0):6.2f}MB/s, "
                  f"Correct: {custom_metrics.get('correctness_verified', False)}")
        
        # Analyze scaling behavior
        scaling = analyzer.analyze_scaling_behavior()
        if scaling:
            print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for linear scaling (O(n))")
        
    except Exception as e:
        print(f"Decryption scaling analysis failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests"""
    print("AES Decryption Scaling Analyzer Test Suite")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_different_modes()
        test_different_key_sizes()
        test_correctness_verification()
        test_scaling_analysis()
        
        print("\n" + "=" * 50)
        print("All decryption tests completed!")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
