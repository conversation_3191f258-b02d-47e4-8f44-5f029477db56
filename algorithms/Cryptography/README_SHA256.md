# SHA-256 Hashing Scaling Analyzer

## Overview

The SHA-256 Hashing Scaling Analyzer provides comprehensive performance analysis for the SHA-256 (Secure Hash Algorithm 256-bit) cryptographic hash function. SHA-256 is part of the SHA-2 family and is widely used in cryptographic applications, blockchain technology, and digital signatures.

## Algorithm Details

### SHA-256 Characteristics
- **Hash Size**: 256 bits (32 bytes)
- **Block Size**: 512 bits (64 bytes)
- **Algorithm Family**: SHA-2 (Merkle-Damgård construction)
- **Security Level**: 128-bit security
- **Time Complexity**: O(n) where n is the number of 512-bit blocks
- **Space Complexity**: O(1) - constant memory overhead

### Processing Model
SHA-256 processes input data in 512-bit (64-byte) blocks:
1. **Padding**: Input is padded to a multiple of 512 bits
2. **Block Processing**: Each 512-bit block is processed through 64 rounds
3. **State Update**: Internal 256-bit state is updated after each block
4. **Final Hash**: 256-bit hash is produced regardless of input size

## Usage

### Basic Usage

```python
from algorithms.Cryptography import SHA256HashingScalingAnalyzer

# Create analyzer
analyzer = SHA256HashingScalingAnalyzer(
    output_dir="results/sha256_analysis",
    enable_gpu_tracking=False
)

# Single measurement
result = analyzer.measure_single_run(1024)  # 1KB input
print(f"Throughput: {result.custom_metrics['throughput_mbps']:.2f} MB/s")

# Scaling analysis
input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]
results = analyzer.run_scaling_analysis(input_sizes)
```

### Framework Integration

```python
from algorithms import get_analyzer_class

# Get analyzer class
SHA256Analyzer = get_analyzer_class('sha256')
analyzer = SHA256Analyzer()

# Run analysis
results = analyzer.run_scaling_analysis([1024, 2048, 4096])
```

## Configuration Options

The analyzer inherits all configuration options from the base `ScalingAnalyzer` class:

- `output_dir`: Directory for saving results
- `enable_gpu_tracking`: Enable GPU memory tracking (default: False)
- `warmup_runs`: Number of warmup runs (default: 3)
- `measurement_runs`: Number of measurement runs (default: 5)

## Performance Metrics

### Standard Metrics
- **Execution Time**: Time to compute hash (milliseconds)
- **Memory Usage**: Memory increment during hashing
- **Throughput**: Data processing rate (MB/s)

### SHA-256 Specific Metrics
- **Blocks Processed**: Number of 512-bit blocks
- **Blocks per Second**: Block processing rate
- **Block Processing Time**: Average time per block (microseconds)
- **Compression Ratio**: Input size / output size ratio
- **Hash Rate**: Hashes computed per second
- **Bits per Second**: Bit processing rate

## Expected Performance Characteristics

### Scaling Behavior
- **Time Complexity**: Linear O(n) with number of blocks
- **Scaling Factor**: ~1.0 for ideal linear scaling
- **Block Efficiency**: Consistent processing time per 64-byte block

### Typical Performance (varies by hardware)
- **Throughput**: 100-500 MB/s on modern CPUs
- **Block Rate**: 1-10 million blocks/second
- **Hash Rate**: 10,000-100,000 hashes/second for small inputs

### Block Boundary Effects
- Inputs are padded to 512-bit boundaries
- Processing efficiency is highest for block-aligned inputs
- Small inputs (< 64 bytes) still require one full block processing

## Testing and Validation

### Running Tests
```bash
# Comprehensive test suite
python algorithms/Cryptography/test_sha256.py

# Basic demonstration
python algorithms/Cryptography/demo_sha256.py

# Direct analyzer execution
python algorithms/Cryptography/sha256_analyzer.py
```

### Test Coverage
- **Basic Functionality**: Hash computation and metrics
- **Known Vectors**: Validation against standard test vectors
- **Block Boundaries**: Behavior around 64-byte boundaries
- **Scaling Analysis**: Performance across different input sizes
- **Consistency**: Reproducible results for identical inputs

## Example Results

### Sample Scaling Analysis
```
Size     Time(ms)  Throughput(MB/s)  Blocks  Hash Preview
64       0.05      200.0             1       ba7816bf8f01cfea...
128      0.06      333.3             2       cdc76e5c9914fb92...
256      0.08      500.0             4       248d6a61d20638b8...
512      0.12      666.7             8       cf83e1357eefb8bd...
1024     0.20      800.0             16      b94d27b9934d3e08...
```

### Block Boundary Analysis
```
Size  Blocks  Time(ms)  Block Time(μs)  Efficiency
63    1       0.05      50.0            0.984
64    1       0.05      50.0            1.000
65    2       0.10      50.0            0.508
127   2       0.10      50.0            0.992
128   2       0.10      50.0            1.000
```

## Implementation Notes

### Correctness Verification
- Uses Python's `hashlib.sha256()` for reliable implementation
- Validates against known test vectors
- Ensures consistent results across runs

### Performance Considerations
- Block-based processing provides predictable scaling
- Memory usage is constant regardless of input size
- CPU-bound algorithm with minimal I/O overhead

### Security Properties
- Cryptographically secure hash function
- Collision resistance: 2^128 operations
- Preimage resistance: 2^256 operations
- Second preimage resistance: 2^256 operations

## Comparison with Other Hash Functions

### SHA-256 vs SHA-3
- SHA-256: Merkle-Damgård construction, 64-byte blocks
- SHA-3: Sponge construction, variable absorption rates
- SHA-256: Generally faster on current hardware
- SHA-3: More flexible, different security assumptions

### Performance Trade-offs
- **Speed**: SHA-256 typically faster than SHA-3
- **Security**: Both provide 128-bit security level
- **Standardization**: SHA-256 more widely deployed
- **Future-proofing**: SHA-3 designed as SHA-2 alternative

## Troubleshooting

### Common Issues
1. **Low Throughput**: Check for system load or thermal throttling
2. **Inconsistent Results**: Ensure stable system conditions
3. **Memory Errors**: Verify sufficient available memory
4. **Import Errors**: Check Python path and dependencies

### Performance Optimization
- Use larger input sizes for stable throughput measurements
- Run multiple iterations for statistical significance
- Consider system warmup time for accurate measurements
- Monitor CPU frequency scaling during tests

## References

- [FIPS PUB 180-4: Secure Hash Standard (SHS)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf)
- [RFC 6234: US Secure Hash Algorithms](https://tools.ietf.org/html/rfc6234)
- [SHA-256 Test Vectors](https://www.di-mgt.com.au/sha_testvectors.html)
