#!/usr/bin/env python3
"""
Test script for SHA-256 Hashing Scaling Analyzer

This script provides comprehensive tests to verify that the SHA-256 hashing analyzer
works correctly with different input sizes and configurations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import hashlib
from algorithms.Cryptography.sha256_analyzer import SHA256HashingScalingAnalyzer


def test_basic_functionality():
    """Test basic SHA-256 hashing functionality"""
    print("=== Testing Basic SHA-256 Hashing Functionality ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_basic_test",
        enable_gpu_tracking=False
    )
    
    # Test with various input sizes
    test_sizes = [1, 8, 32, 55, 64, 128, 256, 1024]
    
    print(f"Testing with input sizes: {test_sizes}")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        # Prepare input
        input_data = analyzer.prepare_input(size)
        print(f"  Input prepared: {len(input_data)} bytes")
        
        # Run algorithm
        result = analyzer.run_algorithm(input_data)
        print(f"  Hash result: {len(result.get('hash_digest', b''))} bytes output")
        print(f"  Hash hex: {result.get('hash_hex', '')[:16]}...")
        print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
        print(f"  Blocks processed: {result.get('input_blocks', 0)}")
        
        # Verify hash length
        expected_hash_length = 32  # SHA-256 produces 32-byte hash
        actual_hash_length = len(result.get('hash_digest', b''))
        print(f"  Hash length correct: {actual_hash_length == expected_hash_length}")
        
        # Get theoretical complexity
        time_comp, space_comp, mem_mb = analyzer.get_theoretical_complexity(size)
        print(f"  Theoretical: {time_comp}, {space_comp}, {mem_mb:.4f} MB")
        
        # Calculate custom metrics
        custom_metrics = analyzer.calculate_custom_metrics(size, input_data, result)
        print(f"  Custom metrics: {len(custom_metrics)} metrics calculated")
        print(f"  Compression ratio: {custom_metrics.get('compression_ratio', 0):.1f}")
        print(f"  Block processing time: {custom_metrics.get('block_processing_time_us', 0):.2f} μs")


def test_hash_consistency():
    """Test that identical inputs produce identical hashes"""
    print("\n=== Testing Hash Consistency ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_consistency_test",
        enable_gpu_tracking=False
    )
    
    test_sizes = [1, 32, 64, 256]
    
    for size in test_sizes:
        print(f"\nTesting consistency for size {size} bytes:")
        
        # Generate same input twice (using same seed)
        input_data1 = analyzer.prepare_input(size)
        input_data2 = analyzer.prepare_input(size)
        
        # Hash both inputs
        result1 = analyzer.run_algorithm(input_data1)
        result2 = analyzer.run_algorithm(input_data2)
        
        hash1 = result1.get('hash_hex', '')
        hash2 = result2.get('hash_hex', '')
        
        print(f"  Input identical: {input_data1 == input_data2}")
        print(f"  Hash identical: {hash1 == hash2}")
        print(f"  Hash 1: {hash1[:16]}...")
        print(f"  Hash 2: {hash2[:16]}...")
        
        if hash1 == hash2:
            print(f"  SUCCESS: Consistent hashing")
        else:
            print(f"  ERROR: Inconsistent hashing!")


def test_known_vectors():
    """Test with known SHA-256 test vectors"""
    print("\n=== Testing Known SHA-256 Vectors ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_vectors_test",
        enable_gpu_tracking=False
    )
    
    # Known test vectors for SHA-256
    test_vectors = [
        {
            'input': b'',
            'expected': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'
        },
        {
            'input': b'abc',
            'expected': 'ba7816bf8f01cfea414140de5dae2223b00361a396177a9cb410ff61f20015ad'
        },
        {
            'input': b'message digest',
            'expected': 'f7846f55cf23e14eebeab5b4e1550cad5b509e3348fbc4efa3a1413d393cb650'
        },
        {
            'input': b'a' * 55,  # Just under one block
            'expected': None  # We'll compute this
        },
        {
            'input': b'a' * 64,  # Exactly one block
            'expected': None  # We'll compute this
        }
    ]
    
    for i, vector in enumerate(test_vectors):
        print(f"\nTesting vector {i+1}: {len(vector['input'])} bytes")
        
        # Hash using our analyzer
        result = analyzer.run_algorithm(vector['input'])
        our_hash = result.get('hash_hex', '')
        
        # Hash using standard library for comparison
        std_hash = hashlib.sha256(vector['input']).hexdigest()
        
        print(f"  Our hash:      {our_hash}")
        print(f"  Standard hash: {std_hash}")
        
        if vector['expected']:
            print(f"  Expected hash: {vector['expected']}")
            expected_match = our_hash == vector['expected']
            print(f"  Expected match: {expected_match}")
        
        std_match = our_hash == std_hash
        print(f"  Standard match: {std_match}")
        
        if std_match:
            print(f"  SUCCESS: Hash matches standard library")
        else:
            print(f"  ERROR: Hash does not match standard library!")


def test_block_boundary_behavior():
    """Test behavior around SHA-256 block boundaries"""
    print("\n=== Testing Block Boundary Behavior ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_boundary_test",
        enable_gpu_tracking=False
    )
    
    # SHA-256 block size is 64 bytes (512 bits)
    block_size = 64
    
    # Test sizes around block boundaries
    test_sizes = [
        block_size - 1,  # 63 bytes (just under one block)
        block_size,      # 64 bytes (exactly one block)
        block_size + 1,  # 65 bytes (just over one block)
        block_size * 2 - 1,  # 127 bytes
        block_size * 2,      # 128 bytes (exactly two blocks)
        block_size * 2 + 1,  # 129 bytes
    ]
    
    print(f"SHA-256 block size: {block_size} bytes")
    print(f"Testing sizes around block boundaries: {test_sizes}")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        input_data = analyzer.prepare_input(size)
        result = analyzer.run_algorithm(input_data)
        
        blocks = result.get('input_blocks', 0)
        expected_blocks = (size + block_size - 1) // block_size
        
        print(f"  Input size: {size} bytes")
        print(f"  Calculated blocks: {blocks}")
        print(f"  Expected blocks: {expected_blocks}")
        print(f"  Blocks correct: {blocks == expected_blocks}")
        print(f"  Hash: {result.get('hash_hex', '')[:16]}...")
        print(f"  Processing time: {result.get('hashing_time_seconds', 0)*1000:.3f} ms")


def test_scaling_analysis():
    """Test a small scaling analysis"""
    print("\n=== Testing SHA-256 Scaling Analysis ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_scaling_test",
        enable_gpu_tracking=False
    )
    
    # Small input sizes for quick test
    input_sizes = [32, 64, 128, 256, 512, 1024]
    
    print(f"Running SHA-256 scaling analysis with sizes: {input_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(input_sizes)
        
        print(f"\nSHA-256 scaling analysis completed!")
        print(f"Results: {len(results)} data points")
        
        # Show some results
        for i, result in enumerate(results):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:4d}B: "
                  f"{result.execution_time_ms:6.2f}ms, "
                  f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                  f"Blocks: {custom_metrics.get('actual_blocks', 0):3d}")
        
        # Analyze scaling behavior
        scaling = analyzer.analyze_scaling_behavior()
        if scaling:
            print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for linear scaling (O(n))")
        
    except Exception as e:
        print(f"SHA-256 scaling analysis failed: {e}")
        import traceback
        traceback.print_exc()


def test_performance_characteristics():
    """Test performance characteristics"""
    print("\n=== Testing Performance Characteristics ===")
    
    analyzer = SHA256HashingScalingAnalyzer(
        output_dir="test_results/sha256_performance_test",
        enable_gpu_tracking=False
    )
    
    # Test with larger sizes for stable performance measurements
    large_sizes = [1024, 2048, 4096, 8192, 16384]
    
    print(f"Testing performance with larger sizes: {large_sizes}")
    
    throughputs = []
    
    for size in large_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        throughput = custom_metrics.get('throughput_mbps', 0)
        throughputs.append(throughput)
        
        print(f"Size {size:5d}B: "
              f"{result.execution_time_ms:6.2f}ms, "
              f"Throughput: {throughput:8.2f}MB/s, "
              f"Hash rate: {custom_metrics.get('hash_rate_per_second', 0):6.2f}/s")
    
    # Calculate average throughput for larger sizes
    if throughputs:
        avg_throughput = sum(throughputs) / len(throughputs)
        print(f"\nAverage throughput (large sizes): {avg_throughput:.2f} MB/s")


def main():
    """Run all tests"""
    print("SHA-256 Hashing Scaling Analyzer Test Suite")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_hash_consistency()
        test_known_vectors()
        test_block_boundary_behavior()
        test_scaling_analysis()
        test_performance_characteristics()
        
        print("\n" + "=" * 50)
        print("All SHA-256 tests completed!")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
