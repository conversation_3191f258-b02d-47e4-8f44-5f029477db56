#!/usr/bin/env python3
"""
SHA-3 Hashing Scaling Analysis Demo

This script demonstrates SHA-3 hashing scaling analysis with different
hash sizes and provides detailed performance metrics.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import SHA3Hashing<PERSON><PERSON>ing<PERSON>naly<PERSON>


def demo_sha3_variants():
    """Demonstrate different SHA-3 variants"""
    print("=== SHA-3 Variants Comparison ===")
    
    variants = [224, 256, 384, 512]
    test_size = 1024  # 1KB test
    
    print(f"Testing all SHA-3 variants with {test_size} byte input:")
    print(f"{'Variant':<12} {'Hash Size':<12} {'Rate':<8} {'Rounds':<8} {'Time(ms)':<10} {'Throughput':<12}")
    print("-" * 75)
    
    for hash_size in variants:
        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"demo_results/sha3_variants/sha3_{hash_size}",
                enable_gpu_tracking=False
            )
            
            result = analyzer.measure_single_run(test_size)
            custom_metrics = result.custom_metrics
            
            print(f"SHA3-{hash_size:<6} {hash_size//8:<12} {analyzer.rate_bytes:<8} "
                  f"{custom_metrics.get('actual_rounds', 0):<8} "
                  f"{result.execution_time_ms:<10.2f} "
                  f"{custom_metrics.get('throughput_mbps', 0):<12.2f}")
            
        except Exception as e:
            print(f"SHA3-{hash_size:<6} ERROR: {str(e)[:40]}")
    
    print(f"\nNote: Higher hash sizes have lower rates (fewer bytes per round)")
    print(f"Rate = (1600 - 2 × hash_size) / 8 bytes")


def demo_rate_boundary_analysis():
    """Demonstrate behavior around SHA-3 rate boundaries"""
    print("\n=== SHA-3 Rate Boundary Analysis ===")
    
    # Use SHA3-256 as example (rate = 136 bytes)
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="demo_results/sha3_boundary",
        enable_gpu_tracking=False
    )
    
    rate_size = analyzer.rate_bytes  # 136 bytes for SHA3-256
    
    # Test sizes around rate boundaries
    boundary_sizes = [
        rate_size - 1,      # 135 bytes
        rate_size,          # 136 bytes (1 round)
        rate_size + 1,      # 137 bytes
        rate_size * 2 - 1,  # 271 bytes
        rate_size * 2,      # 272 bytes (2 rounds)
        rate_size * 2 + 1,  # 273 bytes
        rate_size * 4,      # 544 bytes (4 rounds)
        rate_size * 8,      # 1088 bytes (8 rounds)
    ]
    
    print(f"SHA3-256 rate: {rate_size} bytes per absorption round")
    print(f"\n{'Size':<8} {'Rounds':<8} {'Time(ms)':<10} {'Round Time(μs)':<15} {'Utilization':<12}")
    print("-" * 70)
    
    for size in boundary_sizes:
        result = analyzer.measure_single_run(size)
        custom_metrics = result.custom_metrics
        
        rounds = custom_metrics.get('actual_rounds', 0)
        round_time = custom_metrics.get('round_processing_time_us', 0)
        utilization = custom_metrics.get('rate_utilization', 0)
        
        print(f"{size:<8} {rounds:<8} {result.execution_time_ms:<10.2f} "
              f"{round_time:<15.2f} {utilization:<12.3f}")


def demo_scaling_behavior():
    """Demonstrate SHA-3 scaling behavior"""
    print("\n=== SHA-3 Scaling Behavior Analysis ===")
    
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="demo_results/sha3_scaling",
        enable_gpu_tracking=False
    )
    
    rate = analyzer.rate_bytes
    
    # Progressive input sizes for scaling analysis
    scaling_sizes = [
        rate,           # 1 round
        rate * 2,       # 2 rounds
        rate * 4,       # 4 rounds
        rate * 8,       # 8 rounds
        rate * 16,      # 16 rounds
        rate * 32,      # 32 rounds
        rate * 64,      # 64 rounds
        rate * 128      # 128 rounds
    ]
    
    print(f"SHA3-256 rate: {rate} bytes per round")
    print(f"Running scaling analysis with sizes: {scaling_sizes}")
    
    results = analyzer.run_scaling_analysis(scaling_sizes)
    scaling = analyzer.analyze_scaling_behavior()
    
    print(f"\n=== Scaling Results ===")
    print(f"{'Size':<8} {'Rounds':<8} {'Time(ms)':<10} {'Throughput':<12} {'Rounds/s':<10}")
    print("-" * 65)
    
    for result in results:
        custom_metrics = result.custom_metrics
        print(f"{result.input_size:<8} {custom_metrics.get('actual_rounds', 0):<8} "
              f"{result.execution_time_ms:<10.2f} "
              f"{custom_metrics.get('throughput_mbps', 0):<12.2f} "
              f"{custom_metrics.get('rounds_per_second', 0):<10.0f}")
    
    if scaling:
        print(f"\n=== Scaling Analysis ===")
        print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
        print(f"Expected: ~1.0 for linear scaling (O(n))")
        
        if scaling['mean_scaling_factor'] < 1.2:
            print("✓ Excellent linear scaling behavior")
        elif scaling['mean_scaling_factor'] < 1.5:
            print("✓ Good scaling behavior")
        else:
            print("⚠ Scaling behavior may be suboptimal")


def demo_throughput_comparison():
    """Compare throughput across SHA-3 variants"""
    print("\n=== SHA-3 Throughput Comparison ===")
    
    variants = [224, 256, 384, 512]
    test_sizes = [2048, 4096, 8192, 16384]  # Larger sizes for stable measurements
    
    print(f"Comparing throughput across SHA-3 variants:")
    print(f"Test sizes: {test_sizes}")
    
    # Collect results for each variant
    variant_results = {}
    
    for hash_size in variants:
        print(f"\n--- SHA3-{hash_size} ---")
        
        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"demo_results/sha3_throughput/sha3_{hash_size}",
                enable_gpu_tracking=False
            )
            
            throughputs = []
            
            print(f"Rate: {analyzer.rate_bytes} bytes per round")
            print(f"{'Size':<8} {'Time(ms)':<10} {'Throughput':<12} {'Rounds':<8}")
            print("-" * 45)
            
            for size in test_sizes:
                result = analyzer.measure_single_run(size)
                custom_metrics = result.custom_metrics
                
                throughput = custom_metrics.get('throughput_mbps', 0)
                throughputs.append(throughput)
                
                print(f"{size:<8} {result.execution_time_ms:<10.2f} "
                      f"{throughput:<12.2f} {custom_metrics.get('actual_rounds', 0):<8}")
            
            # Calculate average throughput
            avg_throughput = sum(throughputs) / len(throughputs) if throughputs else 0
            variant_results[hash_size] = avg_throughput
            
            print(f"Average throughput: {avg_throughput:.2f} MB/s")
            
        except Exception as e:
            print(f"Error with SHA3-{hash_size}: {e}")
            variant_results[hash_size] = 0
    
    # Summary comparison
    print(f"\n=== Throughput Summary ===")
    print(f"{'Variant':<12} {'Avg Throughput':<15} {'Relative Performance':<20}")
    print("-" * 50)
    
    max_throughput = max(variant_results.values()) if variant_results.values() else 1
    
    for hash_size in variants:
        throughput = variant_results.get(hash_size, 0)
        relative = (throughput / max_throughput * 100) if max_throughput > 0 else 0
        
        print(f"SHA3-{hash_size:<6} {throughput:<15.2f} {relative:<20.1f}%")


def demo_compression_analysis():
    """Demonstrate compression ratio analysis for SHA-3"""
    print("\n=== SHA-3 Compression Analysis ===")
    
    # Test SHA3-256 and SHA3-512 for comparison
    hash_sizes = [256, 512]
    test_sizes = [64, 128, 256, 512, 1024, 2048, 4096, 8192]
    
    for hash_size in hash_sizes:
        print(f"\n--- SHA3-{hash_size} Compression Analysis ---")
        
        analyzer = SHA3HashingScalingAnalyzer(
            hash_size=hash_size,
            output_dir=f"demo_results/sha3_compression/sha3_{hash_size}",
            enable_gpu_tracking=False
        )
        
        output_size = hash_size // 8  # bytes
        
        print(f"SHA3-{hash_size} always produces {output_size}-byte output")
        print(f"{'Input Size':<12} {'Output Size':<12} {'Compression':<12} {'Space Saved':<12}")
        print("-" * 55)
        
        for size in test_sizes:
            result = analyzer.measure_single_run(size)
            custom_metrics = result.custom_metrics
            
            compression_ratio = custom_metrics.get('compression_ratio', 0)
            space_saved = ((size - output_size) / size * 100) if size > 0 else 0
            
            print(f"{size:<12} {output_size:<12} {compression_ratio:<12.1f}x "
                  f"{space_saved:<12.1f}%")


def demo_hash_examples():
    """Show hash examples for different SHA-3 variants"""
    print("\n=== SHA-3 Hash Examples ===")
    
    variants = [224, 256, 384, 512]
    test_size = 64  # Small test size
    
    print(f"Hash examples for {test_size}-byte input:")
    print(f"{'Variant':<12} {'Hash Length':<12} {'Hash Preview':<20} {'Rounds':<8}")
    print("-" * 60)
    
    for hash_size in variants:
        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"demo_results/sha3_examples/sha3_{hash_size}",
                enable_gpu_tracking=False
            )
            
            input_data = analyzer.prepare_input(test_size)
            hash_result = analyzer.run_algorithm(input_data)
            
            hash_hex = hash_result.get('hash_hex', '')
            hash_preview = hash_hex[:16] + "..." if len(hash_hex) > 16 else hash_hex
            rounds = hash_result.get('absorption_rounds', 0)
            
            print(f"SHA3-{hash_size:<6} {len(hash_hex):<12} {hash_preview:<20} {rounds:<8}")
            
        except Exception as e:
            print(f"SHA3-{hash_size:<6} ERROR: {str(e)[:30]}")


def main():
    """Run comprehensive SHA-3 demonstration"""
    print("SHA-3 Hashing Scaling Analysis Demonstration")
    print("=" * 60)
    
    try:
        demo_sha3_variants()
        demo_rate_boundary_analysis()
        demo_scaling_behavior()
        demo_throughput_comparison()
        demo_compression_analysis()
        demo_hash_examples()
        
        print("\n" + "=" * 60)
        print("SHA-3 demonstration completed successfully!")
        print("\nKey findings:")
        print("- SHA-3 shows linear O(n) scaling with absorption rounds")
        print("- Different variants have different rates and performance")
        print("- Rate boundaries affect processing efficiency")
        print("- Higher security (larger hash) comes with lower throughput")
        print("- Sponge construction provides flexible output sizes")
        print("- Results saved in demo_results/sha3_*/ directories")
        
    except Exception as e:
        print(f"\nSHA-3 demonstration failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
