# SHA-3 Hashing Scaling Analyzer

## Overview

The SHA-3 Hashing Scaling Analyzer provides comprehensive performance analysis for the SHA-3 (Secure Hash Algorithm 3) cryptographic hash function family. SHA-3 is based on the Keccak algorithm and uses a sponge construction, offering a different approach to cryptographic hashing compared to SHA-2.

## Algorithm Details

### SHA-3 Characteristics
- **Hash Sizes**: 224, 256, 384, or 512 bits
- **Construction**: Sponge function based on Keccak
- **State Size**: 1600 bits (200 bytes) - fixed for all variants
- **Security Level**: Half the hash size (e.g., SHA3-256 provides 128-bit security)
- **Time Complexity**: O(n) where n is the number of absorption rounds
- **Space Complexity**: O(1) - constant memory overhead

### Sponge Construction
SHA-3 uses a sponge construction with two phases:
1. **Absorption**: Input data is absorbed into the state at the rate
2. **Squeezing**: Output hash is squeezed from the state

### Rate and Capacity
Each SHA-3 variant has different rate and capacity values:
- **Rate**: Number of bits absorbed per round
- **Capacity**: Security parameter (2 × hash size)
- **Rate = 1600 - Capacity**

| Variant  | Hash Size | Capacity | Rate (bits) | Rate (bytes) |
|----------|-----------|----------|-------------|--------------|
| SHA3-224 | 224 bits  | 448 bits | 1152 bits   | 144 bytes    |
| SHA3-256 | 256 bits  | 512 bits | 1088 bits   | 136 bytes    |
| SHA3-384 | 384 bits  | 768 bits | 832 bits    | 104 bytes    |
| SHA3-512 | 512 bits  | 1024 bits| 576 bits    | 72 bytes     |

## Usage

### Basic Usage

```python
from algorithms.Cryptography import SHA3HashingScalingAnalyzer

# Create analyzer for SHA3-256
analyzer = SHA3HashingScalingAnalyzer(
    hash_size=256,  # 224, 256, 384, or 512
    output_dir="results/sha3_256_analysis",
    enable_gpu_tracking=False
)

# Single measurement
result = analyzer.measure_single_run(1024)  # 1KB input
print(f"Throughput: {result.custom_metrics['throughput_mbps']:.2f} MB/s")
print(f"Absorption rounds: {result.custom_metrics['actual_rounds']}")

# Scaling analysis
input_sizes = [136, 272, 544, 1088, 2176]  # Multiples of rate
results = analyzer.run_scaling_analysis(input_sizes)
```

### Different SHA-3 Variants

```python
# Test all SHA-3 variants
for hash_size in [224, 256, 384, 512]:
    analyzer = SHA3HashingScalingAnalyzer(hash_size=hash_size)
    result = analyzer.measure_single_run(1024)
    print(f"SHA3-{hash_size}: {result.custom_metrics['throughput_mbps']:.2f} MB/s")
```

### Framework Integration

```python
from algorithms import get_analyzer_class

# Get analyzer class (defaults to SHA3-256)
SHA3Analyzer = get_analyzer_class('sha3')
analyzer = SHA3Analyzer(hash_size=384)  # Override to SHA3-384

# Run analysis
results = analyzer.run_scaling_analysis([1024, 2048, 4096])
```

## Configuration Options

### SHA-3 Specific Options
- `hash_size`: SHA-3 variant (224, 256, 384, or 512 bits)

### Inherited Options
- `output_dir`: Directory for saving results
- `enable_gpu_tracking`: Enable GPU memory tracking (default: False)
- `warmup_runs`: Number of warmup runs (default: 3)
- `measurement_runs`: Number of measurement runs (default: 5)

## Performance Metrics

### Standard Metrics
- **Execution Time**: Time to compute hash (milliseconds)
- **Memory Usage**: Memory increment during hashing
- **Throughput**: Data processing rate (MB/s)

### SHA-3 Specific Metrics
- **Absorption Rounds**: Number of rate-sized chunks processed
- **Rounds per Second**: Round processing rate
- **Round Processing Time**: Average time per round (microseconds)
- **Rate Utilization**: How efficiently the rate is used (0.0-1.0)
- **Compression Ratio**: Input size / output size ratio
- **Hash Rate**: Hashes computed per second
- **Bits per Second**: Bit processing rate

## Expected Performance Characteristics

### Scaling Behavior
- **Time Complexity**: Linear O(n) with number of absorption rounds
- **Scaling Factor**: ~1.0 for ideal linear scaling
- **Round Efficiency**: Consistent processing time per absorption round

### Typical Performance (varies by hardware)
- **Throughput**: 50-300 MB/s on modern CPUs
- **Round Rate**: 100,000-1,000,000 rounds/second
- **Hash Rate**: 1,000-50,000 hashes/second for small inputs

### Rate Boundary Effects
- Processing efficiency is highest when input aligns with rate boundaries
- Partial rounds still require full round processing
- Higher hash sizes have lower rates and thus more rounds

### Variant Performance Comparison
Generally: SHA3-224 > SHA3-256 > SHA3-384 > SHA3-512 (throughput)
- SHA3-224: Highest rate (144 bytes), best throughput
- SHA3-512: Lowest rate (72 bytes), highest security

## Testing and Validation

### Running Tests
```bash
# Comprehensive test suite
python algorithms/Cryptography/test_sha3.py

# Basic demonstration
python algorithms/Cryptography/demo_sha3.py

# Direct analyzer execution
python algorithms/Cryptography/sha3_analyzer.py
```

### Test Coverage
- **Basic Functionality**: Hash computation and metrics for all variants
- **Known Vectors**: Validation against standard test vectors
- **Rate Boundaries**: Behavior around rate-sized boundaries
- **Scaling Analysis**: Performance across different input sizes
- **Consistency**: Reproducible results for identical inputs
- **Variant Comparison**: Performance differences between hash sizes

## Example Results

### SHA-3 Variants Comparison
```
Variant      Hash Size    Rate    Rounds  Time(ms)  Throughput
SHA3-224    28 bytes     144     8       0.04      150.0 MB/s
SHA3-256    32 bytes     136     8       0.04      145.0 MB/s
SHA3-384    48 bytes     104     10      0.05      120.0 MB/s
SHA3-512    64 bytes     72      15      0.06      100.0 MB/s
```

### Rate Boundary Analysis (SHA3-256)
```
Size  Rounds  Time(ms)  Round Time(μs)  Utilization
135   1       0.03      30.0            0.993
136   1       0.03      30.0            1.000
137   2       0.06      30.0            0.504
271   2       0.06      30.0            0.996
272   2       0.06      30.0            1.000
```

### Scaling Analysis (SHA3-256)
```
Size     Rounds  Time(ms)  Throughput(MB/s)  Rounds/s
136      1       0.03      150.0             33333
272      2       0.06      150.0             33333
544      4       0.12      150.0             33333
1088     8       0.24      150.0             33333
2176     16      0.48      150.0             33333
```

## Implementation Notes

### Correctness Verification
- Uses Python's `hashlib.sha3_*()` functions for reliable implementation
- Validates against known test vectors from NIST
- Ensures consistent results across runs and variants

### Performance Considerations
- Round-based processing provides predictable scaling
- Memory usage is constant regardless of input size
- CPU-bound algorithm with minimal I/O overhead
- Performance varies significantly between variants

### Security Properties
- Cryptographically secure hash function family
- Different security levels based on hash size
- Resistance to length extension attacks (unlike SHA-2)
- Based on different mathematical foundation than SHA-2

## Comparison with SHA-256

### Architectural Differences
- **SHA-256**: Merkle-Damgård construction, 64-byte blocks
- **SHA-3**: Sponge construction, variable absorption rates
- **SHA-256**: Fixed block processing
- **SHA-3**: Flexible rate-based absorption

### Performance Comparison
- **Speed**: SHA-256 typically 20-50% faster
- **Memory**: Both use constant memory
- **Scalability**: Both show linear scaling
- **Flexibility**: SHA-3 offers more variants and extensibility

### Security Comparison
- **Both**: Provide equivalent security levels for same hash sizes
- **SHA-256**: Mature, extensively analyzed
- **SHA-3**: Different attack surface, designed as SHA-2 backup
- **Future-proofing**: SHA-3 provides algorithmic diversity

## Troubleshooting

### Common Issues
1. **Invalid Hash Size**: Ensure hash_size is 224, 256, 384, or 512
2. **Low Throughput**: Check system load and thermal conditions
3. **Inconsistent Results**: Verify stable system conditions
4. **Rate Confusion**: Remember rates are in bytes, not bits

### Performance Optimization
- Use input sizes that are multiples of the rate for best efficiency
- Consider variant selection based on security vs. performance needs
- Run multiple iterations for statistical significance
- Monitor system conditions during measurements

## References

- [FIPS PUB 202: SHA-3 Standard](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf)
- [Keccak Team: The Keccak Reference](https://keccak.team/keccak.html)
- [NIST SHA-3 Test Vectors](https://csrc.nist.gov/projects/cryptographic-algorithm-validation-program/secure-hashing)
- [SHA-3 vs SHA-2 Comparison](https://keccak.team/2013/sha3_vs_sha2.html)
