"""
AES Decryption Scaling Analyzer

This module provides scaling analysis for AES (Advanced Encryption Standard) decryption,
measuring both time and space complexity across different input sizes and key lengths.
Supports AES-128, AES-192, and AES-256 decryption modes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
from typing import Any, Dict, Tuple
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import secrets
from core.scaling_framework import ScalingAnalyzer


class AESDecryptionScalingAnalyzer(ScalingAnalyzer):
    """AES Decryption scaling analysis using unified framework"""
    
    def __init__(self, key_size: int = 256, mode: str = 'CBC', 
                 padding_algorithm: str = 'PKCS7', **kwargs):
        """
        Initialize AES Decryption analyzer
        
        Args:
            key_size: AES key size in bits (128, 192, or 256)
            mode: Decryption mode ('CBC', 'ECB', 'CFB', 'OFB', 'CTR', 'GCM')
            padding_algorithm: Padding algorithm ('PKCS7', 'ANSIX923')
        """
        super().__init__(algorithm_name=f"AES-{key_size}-{mode}-Decrypt", **kwargs)
        
        # Validate key size
        if key_size not in [128, 192, 256]:
            raise ValueError(f"Invalid key size: {key_size}. Must be 128, 192, or 256")
        
        self.key_size = key_size
        self.mode = mode.upper()
        self.padding_algorithm = padding_algorithm
        self.key_bytes = key_size // 8
        
        # Generate a fixed key for consistent measurements
        np.random.seed(42)
        self.key = secrets.token_bytes(self.key_bytes)
        
        # Initialize IV for modes that require it
        self.iv = None
        if self.mode in ['CBC', 'CFB', 'OFB']:
            self.iv = secrets.token_bytes(16)  # AES block size is always 16 bytes
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Generate encrypted data for AES decryption"""
        np.random.seed(42)  # For reproducibility
        
        # Generate random plaintext data
        plaintext = secrets.token_bytes(input_size)
        
        # Encrypt the data to create ciphertext for decryption
        cipher_obj = self._create_cipher()
        encryptor = cipher_obj.encryptor()
        
        # Apply padding if necessary
        padded_plaintext = self._apply_padding(plaintext)
        
        # Perform encryption to get ciphertext
        ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()
        
        return {
            'ciphertext': ciphertext,
            'original_plaintext': plaintext,
            'padded_size': len(padded_plaintext),
            'cipher_size': len(ciphertext)
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute AES decryption algorithm"""
        try:
            ciphertext = input_data['ciphertext']
            original_plaintext = input_data['original_plaintext']
            
            # Create cipher for decryption
            cipher_obj = self._create_cipher()
            decryptor = cipher_obj.decryptor()
            
            # Perform decryption
            start_time = time.perf_counter()
            padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            decryption_time = time.perf_counter() - start_time
            
            # Remove padding if necessary
            final_plaintext = self._remove_padding(padded_plaintext)
            
            # Verify correctness
            is_correct = final_plaintext == original_plaintext
            
            # Calculate metrics
            input_blocks = len(ciphertext) // 16  # AES block size is 16 bytes
            throughput_mbps = (len(ciphertext) / (1024 * 1024)) / decryption_time if decryption_time > 0 else 0
            
            return {
                'decrypted_plaintext': final_plaintext,
                'input_ciphertext_size': len(ciphertext),
                'output_plaintext_size': len(final_plaintext),
                'decryption_time_seconds': decryption_time,
                'input_blocks': input_blocks,
                'throughput_mbps': throughput_mbps,
                'key_size_bits': self.key_size,
                'mode': self.mode,
                'is_correct': is_correct,
                'padding_removed': len(padded_plaintext) - len(final_plaintext)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'decrypted_plaintext': b'',
                'input_ciphertext_size': len(input_data.get('ciphertext', b'')),
                'output_plaintext_size': 0,
                'decryption_time_seconds': 0,
                'input_blocks': 0,
                'throughput_mbps': 0,
                'key_size_bits': self.key_size,
                'mode': self.mode,
                'is_correct': False,
                'padding_removed': 0
            }
    
    def _create_cipher(self) -> Cipher:
        """Create cipher object based on mode"""
        algorithm = algorithms.AES(self.key)
        
        if self.mode == 'CBC':
            mode_obj = modes.CBC(self.iv)
        elif self.mode == 'ECB':
            mode_obj = modes.ECB()
        elif self.mode == 'CFB':
            mode_obj = modes.CFB(self.iv)
        elif self.mode == 'OFB':
            mode_obj = modes.OFB(self.iv)
        elif self.mode == 'CTR':
            # Use the same nonce for encryption and decryption
            nonce = secrets.token_bytes(16)
            mode_obj = modes.CTR(nonce)
        elif self.mode == 'GCM':
            # Use the same nonce for encryption and decryption
            nonce = secrets.token_bytes(12)  # GCM typically uses 96-bit nonce
            mode_obj = modes.GCM(nonce)
        else:
            raise ValueError(f"Unsupported mode: {self.mode}")
        
        return Cipher(algorithm, mode_obj, backend=default_backend())
    
    def _apply_padding(self, data: bytes) -> bytes:
        """Apply padding to data if required by the mode"""
        # Stream modes and GCM don't require padding
        if self.mode in ['CTR', 'CFB', 'OFB', 'GCM']:
            return data
        
        # Block modes require padding
        if self.padding_algorithm == 'PKCS7':
            padder = padding.PKCS7(128).padder()  # AES block size is 128 bits
            padded_data = padder.update(data)
            padded_data += padder.finalize()
            return padded_data
        elif self.padding_algorithm == 'ANSIX923':
            padder = padding.ANSIX923(128).padder()
            padded_data = padder.update(data)
            padded_data += padder.finalize()
            return padded_data
        else:
            raise ValueError(f"Unsupported padding: {self.padding_algorithm}")
    
    def _remove_padding(self, data: bytes) -> bytes:
        """Remove padding from decrypted data if required by the mode"""
        # Stream modes and GCM don't use padding
        if self.mode in ['CTR', 'CFB', 'OFB', 'GCM']:
            return data
        
        # Block modes require padding removal
        if self.padding_algorithm == 'PKCS7':
            unpadder = padding.PKCS7(128).unpadder()
            unpadded_data = unpadder.update(data)
            unpadded_data += unpadder.finalize()
            return unpadded_data
        elif self.padding_algorithm == 'ANSIX923':
            unpadder = padding.ANSIX923(128).unpadder()
            unpadded_data = unpadder.update(data)
            unpadded_data += unpadder.finalize()
            return unpadded_data
        else:
            raise ValueError(f"Unsupported padding: {self.padding_algorithm}")
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for AES decryption"""
        # AES decryption is O(n) where n is the number of blocks
        blocks = (input_size + 15) // 16  # Round up to nearest block
        
        time_complexity = f"O(n) [blocks={blocks}]"
        space_complexity = "O(1)"  # Constant space overhead
        
        # Memory estimation: ciphertext + plaintext + key + IV
        padding_overhead = 16 - (input_size % 16) if input_size % 16 != 0 else 0
        if self.mode in ['CTR', 'CFB', 'OFB', 'GCM']:
            padding_overhead = 0  # Stream modes don't need padding
        
        theoretical_memory_mb = (
            input_size + padding_overhead +  # Ciphertext (padded)
            input_size +  # Plaintext output
            input_size + padding_overhead +  # Intermediate padded plaintext
            self.key_bytes +  # Key
            16  # IV/nonce
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate AES decryption-specific metrics"""
        
        # Extract results
        decryption_result = result
        
        # Calculate theoretical values
        ciphertext_size = decryption_result.get('input_ciphertext_size', 0)
        theoretical_blocks = (ciphertext_size + 15) // 16
        block_size = 16  # AES block size in bytes
        
        # Calculate efficiency metrics
        actual_blocks = decryption_result.get('input_blocks', 0)
        throughput = decryption_result.get('throughput_mbps', 0)
        
        # Calculate decryption efficiency
        if decryption_result.get('decryption_time_seconds', 0) > 0:
            bytes_per_second = ciphertext_size / decryption_result['decryption_time_seconds']
            blocks_per_second = actual_blocks / decryption_result['decryption_time_seconds']
        else:
            bytes_per_second = 0
            blocks_per_second = 0
        
        # Calculate size ratios
        padding_removed = decryption_result.get('padding_removed', 0)
        output_size = decryption_result.get('output_plaintext_size', 0)
        compression_ratio = ciphertext_size / output_size if output_size > 0 else 1.0
        
        return {
            'original_input_size_bytes': input_size,
            'ciphertext_size_bytes': ciphertext_size,
            'plaintext_size_bytes': output_size,
            'key_size_bits': self.key_size,
            'decryption_mode': self.mode,
            'padding_algorithm': self.padding_algorithm,
            'theoretical_blocks': theoretical_blocks,
            'actual_blocks': actual_blocks,
            'block_size_bytes': block_size,
            'padding_removed_bytes': padding_removed,
            'compression_ratio': round(compression_ratio, 4),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(bytes_per_second),
            'blocks_per_second': int(blocks_per_second),
            'decryption_time_ms': decryption_result.get('decryption_time_seconds', 0) * 1000,
            'correctness_verified': decryption_result.get('is_correct', False),
            'has_error': 'error' in decryption_result,
            'algorithm_family': 'symmetric_decryption'
        }


def main():
    """Run AES Decryption scaling analysis"""
    print("=== AES Decryption Scaling Analysis ===")

    # Test different AES configurations
    test_configs = [
        {'key_size': 128, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 192, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'ECB', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'CTR', 'padding_algorithm': 'PKCS7'},  # CTR doesn't use padding
        {'key_size': 256, 'mode': 'GCM', 'padding_algorithm': 'PKCS7'}   # GCM doesn't use padding
    ]

    for config in test_configs:
        print(f"\n--- Testing AES-{config['key_size']} {config['mode']} Decryption ---")

        try:
            analyzer = AESDecryptionScalingAnalyzer(
                **config,
                output_dir=f"results/aes_decrypt_{config['key_size']}_{config['mode'].lower()}",
                enable_gpu_tracking=False
            )

            # Input sizes for testing (in bytes)
            # Start small and scale up to test different scenarios
            input_sizes = [
                16, 32, 64, 128, 256, 512,  # Small sizes
                1024, 2048, 4096, 8192,     # KB range
                16384, 32768, 65536,        # Larger KB range
                131072, 262144, 524288,     # MB range
                1048576, 2097152            # Multi-MB range
            ]

            print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== {config['mode']} Decryption Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for AES decryption (O(n))")

            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:8]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:8d}B: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Throughput: {custom_metrics.get('throughput_mbps', 0):6.2f}MB/s, "
                      f"Correct: {custom_metrics.get('correctness_verified', False)}")

        except Exception as e:
            print(f"Error in AES-{config['key_size']} {config['mode']} decryption analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nAES Decryption scaling analysis completed!")
    print("Results saved in:")
    for config in test_configs:
        print(f"- results/aes_decrypt_{config['key_size']}_{config['mode'].lower()}/")


if __name__ == "__main__":
    main()
