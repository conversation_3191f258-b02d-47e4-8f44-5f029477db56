"""
AES Encryption Scaling Analyzer

This module provides scaling analysis for AES (Advanced Encryption Standard) encryption,
measuring both time and space complexity across different input sizes and key lengths.
Supports AES-128, AES-192, and AES-256 encryption modes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
from typing import Any, Dict, Tuple
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import secrets
from core.scaling_framework import ScalingAnalyzer


class AESEncryptionScalingAnalyzer(ScalingAnalyzer):
    """AES Encryption scaling analysis using unified framework"""
    
    def __init__(self, key_size: int = 256, mode: str = 'CBC', 
                 padding_algorithm: str = 'PKCS7', **kwargs):
        """
        Initialize AES Encryption analyzer
        
        Args:
            key_size: AES key size in bits (128, 192, or 256)
            mode: Encryption mode ('CBC', 'ECB', 'CFB', 'OFB', 'CTR', 'GCM')
            padding_algorithm: Padding algorithm ('PKCS7', 'ANSIX923')
        """
        super().__init__(algorithm_name=f"AES-{key_size}-{mode}", **kwargs)
        
        # Validate key size
        if key_size not in [128, 192, 256]:
            raise ValueError(f"Invalid key size: {key_size}. Must be 128, 192, or 256")
        
        self.key_size = key_size
        self.mode = mode.upper()
        self.padding_algorithm = padding_algorithm
        self.key_bytes = key_size // 8
        
        # Generate a fixed key for consistent measurements
        np.random.seed(42)
        self.key = secrets.token_bytes(self.key_bytes)
        
        # Initialize IV for modes that require it
        self.iv = None
        if self.mode in ['CBC', 'CFB', 'OFB']:
            self.iv = secrets.token_bytes(16)  # AES block size is always 16 bytes
    
    def prepare_input(self, input_size: int) -> bytes:
        """Generate input data for AES encryption"""
        np.random.seed(42)  # For reproducibility
        
        # Generate random bytes for encryption
        data = secrets.token_bytes(input_size)
        return data
    
    def run_algorithm(self, input_data: bytes) -> Dict[str, Any]:
        """Execute AES encryption algorithm"""
        try:
            # Create cipher based on mode
            cipher_obj = self._create_cipher()
            encryptor = cipher_obj.encryptor()
            
            # Apply padding if necessary
            padded_data = self._apply_padding(input_data)
            
            # Perform encryption
            start_time = time.perf_counter()
            ciphertext = encryptor.update(padded_data) + encryptor.finalize()
            encryption_time = time.perf_counter() - start_time
            
            # Calculate metrics
            input_blocks = len(padded_data) // 16  # AES block size is 16 bytes
            throughput_mbps = (len(input_data) / (1024 * 1024)) / encryption_time if encryption_time > 0 else 0
            
            return {
                'ciphertext': ciphertext,
                'padded_input_size': len(padded_data),
                'output_size': len(ciphertext),
                'encryption_time_seconds': encryption_time,
                'input_blocks': input_blocks,
                'throughput_mbps': throughput_mbps,
                'key_size_bits': self.key_size,
                'mode': self.mode,
                'padding_overhead': len(padded_data) - len(input_data)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'ciphertext': b'',
                'padded_input_size': len(input_data),
                'output_size': 0,
                'encryption_time_seconds': 0,
                'input_blocks': 0,
                'throughput_mbps': 0,
                'key_size_bits': self.key_size,
                'mode': self.mode,
                'padding_overhead': 0
            }
    
    def _create_cipher(self) -> Cipher:
        """Create cipher object based on mode"""
        algorithm = algorithms.AES(self.key)
        
        if self.mode == 'CBC':
            mode_obj = modes.CBC(self.iv)
        elif self.mode == 'ECB':
            mode_obj = modes.ECB()
        elif self.mode == 'CFB':
            mode_obj = modes.CFB(self.iv)
        elif self.mode == 'OFB':
            mode_obj = modes.OFB(self.iv)
        elif self.mode == 'CTR':
            # Generate a random nonce for CTR mode
            nonce = secrets.token_bytes(16)
            mode_obj = modes.CTR(nonce)
        elif self.mode == 'GCM':
            # Generate a random nonce for GCM mode
            nonce = secrets.token_bytes(12)  # GCM typically uses 96-bit nonce
            mode_obj = modes.GCM(nonce)
        else:
            raise ValueError(f"Unsupported mode: {self.mode}")
        
        return Cipher(algorithm, mode_obj, backend=default_backend())
    
    def _apply_padding(self, data: bytes) -> bytes:
        """Apply padding to data if required by the mode"""
        # Stream modes and GCM don't require padding
        if self.mode in ['CTR', 'CFB', 'OFB', 'GCM']:
            return data
        
        # Block modes require padding
        if self.padding_algorithm == 'PKCS7':
            padder = padding.PKCS7(128).padder()  # AES block size is 128 bits
            padded_data = padder.update(data)
            padded_data += padder.finalize()
            return padded_data
        elif self.padding_algorithm == 'ANSIX923':
            padder = padding.ANSIX923(128).padder()
            padded_data = padder.update(data)
            padded_data += padder.finalize()
            return padded_data
        else:
            raise ValueError(f"Unsupported padding: {self.padding_algorithm}")
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for AES encryption"""
        # AES encryption is O(n) where n is the number of blocks
        blocks = (input_size + 15) // 16  # Round up to nearest block
        
        time_complexity = f"O(n) [blocks={blocks}]"
        space_complexity = "O(1)"  # Constant space overhead
        
        # Memory estimation: input + output + key + IV + padding overhead
        padding_overhead = 16 - (input_size % 16) if input_size % 16 != 0 else 0
        if self.mode in ['CTR', 'CFB', 'OFB', 'GCM']:
            padding_overhead = 0  # Stream modes don't need padding
        
        theoretical_memory_mb = (
            input_size +  # Original data
            input_size + padding_overhead +  # Padded data
            input_size + padding_overhead +  # Ciphertext
            self.key_bytes +  # Key
            16  # IV/nonce
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate AES encryption-specific metrics"""
        
        # Extract results
        encryption_result = result
        
        # Calculate theoretical values
        theoretical_blocks = (input_size + 15) // 16
        block_size = 16  # AES block size in bytes
        
        # Calculate efficiency metrics
        actual_blocks = encryption_result.get('input_blocks', 0)
        throughput = encryption_result.get('throughput_mbps', 0)
        
        # Calculate encryption efficiency
        if encryption_result.get('encryption_time_seconds', 0) > 0:
            bytes_per_second = input_size / encryption_result['encryption_time_seconds']
            blocks_per_second = actual_blocks / encryption_result['encryption_time_seconds']
        else:
            bytes_per_second = 0
            blocks_per_second = 0
        
        # Calculate overhead ratios
        padding_overhead = encryption_result.get('padding_overhead', 0)
        padding_ratio = padding_overhead / input_size if input_size > 0 else 0
        
        output_size = encryption_result.get('output_size', 0)
        expansion_ratio = output_size / input_size if input_size > 0 else 1.0
        
        return {
            'input_size_bytes': input_size,
            'key_size_bits': self.key_size,
            'encryption_mode': self.mode,
            'padding_algorithm': self.padding_algorithm,
            'theoretical_blocks': theoretical_blocks,
            'actual_blocks': actual_blocks,
            'block_size_bytes': block_size,
            'padding_overhead_bytes': padding_overhead,
            'padding_ratio': round(padding_ratio, 4),
            'output_size_bytes': output_size,
            'expansion_ratio': round(expansion_ratio, 4),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(bytes_per_second),
            'blocks_per_second': int(blocks_per_second),
            'encryption_time_ms': encryption_result.get('encryption_time_seconds', 0) * 1000,
            'has_error': 'error' in encryption_result,
            'algorithm_family': 'symmetric_encryption'
        }


def main():
    """Run AES Encryption scaling analysis"""
    print("=== AES Encryption Scaling Analysis ===")

    # Test different AES configurations
    test_configs = [
        {'key_size': 128, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 192, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'CBC', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'ECB', 'padding_algorithm': 'PKCS7'},
        {'key_size': 256, 'mode': 'CTR', 'padding_algorithm': 'PKCS7'},  # CTR doesn't use padding
        {'key_size': 256, 'mode': 'GCM', 'padding_algorithm': 'PKCS7'}   # GCM doesn't use padding
    ]

    for config in test_configs:
        print(f"\n--- Testing AES-{config['key_size']} {config['mode']} ---")

        try:
            analyzer = AESEncryptionScalingAnalyzer(
                **config,
                output_dir=f"results/aes_{config['key_size']}_{config['mode'].lower()}",
                enable_gpu_tracking=False
            )

            # Input sizes for testing (in bytes)
            # Start small and scale up to test different scenarios
            input_sizes = [
                16, 32, 64, 128, 256, 512,  # Small sizes
                1024, 2048, 4096, 8192,     # KB range
                16384, 32768, 65536,        # Larger KB range
                131072, 262144, 524288,     # MB range
                1048576, 2097152            # Multi-MB range
            ]

            print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== {config['mode']} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for AES encryption (O(n))")

            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:8]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:8d}B: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Throughput: {custom_metrics.get('throughput_mbps', 0):6.2f}MB/s, "
                      f"Blocks: {custom_metrics.get('actual_blocks', 0):6d}")

        except Exception as e:
            print(f"Error in AES-{config['key_size']} {config['mode']} analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nAES Encryption scaling analysis completed!")
    print("Results saved in:")
    for config in test_configs:
        print(f"- results/aes_{config['key_size']}_{config['mode'].lower()}/")


if __name__ == "__main__":
    main()
