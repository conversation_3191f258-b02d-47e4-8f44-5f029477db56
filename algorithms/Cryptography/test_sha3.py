#!/usr/bin/env python3
"""
Test script for SHA-3 Hashing Scaling Analyzer

This script provides comprehensive tests to verify that the SHA-3 hashing analyzer
works correctly with different hash sizes and input configurations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import hashlib
from algorithms.Cryptography.sha3_analyzer import SHA3HashingScalingAnalyzer


def test_basic_functionality():
    """Test basic SHA-3 hashing functionality"""
    print("=== Testing Basic SHA-3 Hashing Functionality ===")
    
    # Test SHA3-256 as default
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="test_results/sha3_basic_test",
        enable_gpu_tracking=False
    )
    
    # Test with various input sizes
    test_sizes = [1, 8, 32, 64, 136, 256, 512, 1024]  # 136 is rate for SHA3-256
    
    print(f"Testing SHA3-256 with input sizes: {test_sizes}")
    print(f"Rate: {analyzer.rate_bytes} bytes per absorption round")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        # Prepare input
        input_data = analyzer.prepare_input(size)
        print(f"  Input prepared: {len(input_data)} bytes")
        
        # Run algorithm
        result = analyzer.run_algorithm(input_data)
        print(f"  Hash result: {len(result.get('hash_digest', b''))} bytes output")
        print(f"  Hash hex: {result.get('hash_hex', '')[:16]}...")
        print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
        print(f"  Absorption rounds: {result.get('absorption_rounds', 0)}")
        
        # Verify hash length
        expected_hash_length = 32  # SHA3-256 produces 32-byte hash
        actual_hash_length = len(result.get('hash_digest', b''))
        print(f"  Hash length correct: {actual_hash_length == expected_hash_length}")
        
        # Get theoretical complexity
        time_comp, space_comp, mem_mb = analyzer.get_theoretical_complexity(size)
        print(f"  Theoretical: {time_comp}, {space_comp}, {mem_mb:.4f} MB")
        
        # Calculate custom metrics
        custom_metrics = analyzer.calculate_custom_metrics(size, input_data, result)
        print(f"  Custom metrics: {len(custom_metrics)} metrics calculated")
        print(f"  Compression ratio: {custom_metrics.get('compression_ratio', 0):.1f}")
        print(f"  Rate utilization: {custom_metrics.get('rate_utilization', 0):.3f}")


def test_different_hash_sizes():
    """Test different SHA-3 hash sizes"""
    print("\n=== Testing Different SHA-3 Hash Sizes ===")
    
    hash_sizes = [224, 256, 384, 512]
    test_size = 1024  # 1KB test
    
    for hash_size in hash_sizes:
        print(f"\nTesting SHA3-{hash_size}:")
        
        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"test_results/sha3_{hash_size}_test",
                enable_gpu_tracking=False
            )
            
            print(f"  Hash size: {hash_size} bits ({hash_size//8} bytes)")
            print(f"  Rate: {analyzer.rate_bytes} bytes")
            print(f"  Capacity: {analyzer.capacity_bits} bits")
            
            input_data = analyzer.prepare_input(test_size)
            result = analyzer.run_algorithm(input_data)
            
            print(f"  Hash length: {len(result.get('hash_digest', b''))} bytes")
            print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
            print(f"  Absorption rounds: {result.get('absorption_rounds', 0)}")
            print(f"  Hash: {result.get('hash_hex', '')[:16]}...")
            
            # Verify expected hash length
            expected_length = hash_size // 8
            actual_length = len(result.get('hash_digest', b''))
            print(f"  Length correct: {actual_length == expected_length}")
            
        except Exception as e:
            print(f"  Failed: {e}")


def test_hash_consistency():
    """Test that identical inputs produce identical hashes"""
    print("\n=== Testing Hash Consistency ===")
    
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="test_results/sha3_consistency_test",
        enable_gpu_tracking=False
    )
    
    test_sizes = [1, 32, 136, 256]  # Include rate size (136 for SHA3-256)
    
    for size in test_sizes:
        print(f"\nTesting consistency for size {size} bytes:")
        
        # Generate same input twice (using same seed)
        input_data1 = analyzer.prepare_input(size)
        input_data2 = analyzer.prepare_input(size)
        
        # Hash both inputs
        result1 = analyzer.run_algorithm(input_data1)
        result2 = analyzer.run_algorithm(input_data2)
        
        hash1 = result1.get('hash_hex', '')
        hash2 = result2.get('hash_hex', '')
        
        print(f"  Input identical: {input_data1 == input_data2}")
        print(f"  Hash identical: {hash1 == hash2}")
        print(f"  Hash 1: {hash1[:16]}...")
        print(f"  Hash 2: {hash2[:16]}...")
        
        if hash1 == hash2:
            print(f"  SUCCESS: Consistent hashing")
        else:
            print(f"  ERROR: Inconsistent hashing!")


def test_known_vectors():
    """Test with known SHA-3 test vectors"""
    print("\n=== Testing Known SHA-3 Vectors ===")
    
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="test_results/sha3_vectors_test",
        enable_gpu_tracking=False
    )
    
    # Known test vectors for SHA3-256
    test_vectors = [
        {
            'input': b'',
            'expected': 'a7ffc6f8bf1ed76651c14756a061d662f580ff4de43b49fa82d80a4b80f8434a'
        },
        {
            'input': b'abc',
            'expected': '3a985da74fe225b2045c172d6bd390bd855f086e3e9d525b46bfe24511431532'
        },
        {
            'input': b'message digest',
            'expected': 'edcdb2069366e75243860c18c3a11465eca34bce6143d30c8665cefcfd32bffd'
        }
    ]
    
    for i, vector in enumerate(test_vectors):
        print(f"\nTesting vector {i+1}: {len(vector['input'])} bytes")
        
        # Hash using our analyzer
        result = analyzer.run_algorithm(vector['input'])
        our_hash = result.get('hash_hex', '')
        
        # Hash using standard library for comparison
        std_hash = hashlib.sha3_256(vector['input']).hexdigest()
        
        print(f"  Our hash:      {our_hash}")
        print(f"  Standard hash: {std_hash}")
        print(f"  Expected hash: {vector['expected']}")
        
        expected_match = our_hash == vector['expected']
        std_match = our_hash == std_hash
        
        print(f"  Expected match: {expected_match}")
        print(f"  Standard match: {std_match}")
        
        if std_match and expected_match:
            print(f"  SUCCESS: Hash matches both expected and standard")
        else:
            print(f"  ERROR: Hash mismatch!")


def test_rate_boundary_behavior():
    """Test behavior around SHA-3 rate boundaries"""
    print("\n=== Testing Rate Boundary Behavior ===")
    
    # Test SHA3-256 (rate = 136 bytes)
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="test_results/sha3_boundary_test",
        enable_gpu_tracking=False
    )
    
    rate_size = analyzer.rate_bytes
    
    # Test sizes around rate boundaries
    test_sizes = [
        rate_size - 1,      # Just under one absorption round
        rate_size,          # Exactly one absorption round
        rate_size + 1,      # Just over one absorption round
        rate_size * 2 - 1,  # Just under two rounds
        rate_size * 2,      # Exactly two rounds
        rate_size * 2 + 1,  # Just over two rounds
    ]
    
    print(f"SHA3-256 rate: {rate_size} bytes per absorption round")
    print(f"Testing sizes around rate boundaries: {test_sizes}")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        input_data = analyzer.prepare_input(size)
        result = analyzer.run_algorithm(input_data)
        
        rounds = result.get('absorption_rounds', 0)
        expected_rounds = (size + rate_size - 1) // rate_size
        
        print(f"  Input size: {size} bytes")
        print(f"  Calculated rounds: {rounds}")
        print(f"  Expected rounds: {expected_rounds}")
        print(f"  Rounds correct: {rounds == expected_rounds}")
        print(f"  Hash: {result.get('hash_hex', '')[:16]}...")
        print(f"  Processing time: {result.get('hashing_time_seconds', 0)*1000:.3f} ms")


def test_scaling_analysis():
    """Test a small scaling analysis"""
    print("\n=== Testing SHA-3 Scaling Analysis ===")
    
    analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="test_results/sha3_scaling_test",
        enable_gpu_tracking=False
    )
    
    # Small input sizes for quick test
    input_sizes = [64, 136, 272, 544, 1024, 2048]  # Include multiples of rate (136)
    
    print(f"Running SHA3-256 scaling analysis with sizes: {input_sizes}")
    print(f"Rate: {analyzer.rate_bytes} bytes")
    
    try:
        results = analyzer.run_scaling_analysis(input_sizes)
        
        print(f"\nSHA3-256 scaling analysis completed!")
        print(f"Results: {len(results)} data points")
        
        # Show some results
        for i, result in enumerate(results):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:4d}B: "
                  f"{result.execution_time_ms:6.2f}ms, "
                  f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                  f"Rounds: {custom_metrics.get('actual_rounds', 0):3d}")
        
        # Analyze scaling behavior
        scaling = analyzer.analyze_scaling_behavior()
        if scaling:
            print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for linear scaling (O(n))")
        
    except Exception as e:
        print(f"SHA3-256 scaling analysis failed: {e}")
        import traceback
        traceback.print_exc()


def test_performance_comparison():
    """Compare performance across different SHA-3 variants"""
    print("\n=== Testing SHA-3 Performance Comparison ===")
    
    hash_sizes = [224, 256, 384, 512]
    test_size = 2048  # 2KB test
    
    print(f"Comparing SHA-3 variants with {test_size} byte input:")
    print(f"{'Variant':<10} {'Rate':<6} {'Rounds':<7} {'Time(ms)':<10} {'Throughput(MB/s)':<15}")
    print("-" * 60)
    
    for hash_size in hash_sizes:
        try:
            analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"test_results/sha3_perf_{hash_size}",
                enable_gpu_tracking=False
            )
            
            result = analyzer.measure_single_run(test_size)
            custom_metrics = result.custom_metrics
            
            print(f"SHA3-{hash_size:<4} {analyzer.rate_bytes:<6} "
                  f"{custom_metrics.get('actual_rounds', 0):<7} "
                  f"{result.execution_time_ms:<10.2f} "
                  f"{custom_metrics.get('throughput_mbps', 0):<15.2f}")
            
        except Exception as e:
            print(f"SHA3-{hash_size:<4} ERROR: {str(e)[:30]}")


def main():
    """Run all tests"""
    print("SHA-3 Hashing Scaling Analyzer Test Suite")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_different_hash_sizes()
        test_hash_consistency()
        test_known_vectors()
        test_rate_boundary_behavior()
        test_scaling_analysis()
        test_performance_comparison()
        
        print("\n" + "=" * 50)
        print("All SHA-3 tests completed!")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
