#!/usr/bin/env python3
"""
Comprehensive Hash Algorithm Comparison Demo

This script compares SHA-256 and SHA-3 hashing algorithms across
different metrics and input sizes to analyze their relative performance.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import SHA256HashingScalingAnalyzer, SHA3HashingScalingAnalyzer


def demo_basic_comparison():
    """Compare basic performance between SHA-256 and SHA-3 variants"""
    print("=== Basic Hash Algorithm Comparison ===")
    
    test_size = 1024  # 1KB test
    
    # SHA-256
    sha256_analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/hash_comparison/sha256",
        enable_gpu_tracking=False
    )
    
    # SHA-3 variants
    sha3_variants = [224, 256, 384, 512]
    
    print(f"Testing with {test_size} byte input:")
    print(f"{'Algorithm':<12} {'Hash Size':<10} {'Block/Rate':<10} {'Time(ms)':<10} {'Throughput':<12} {'Hash Preview':<20}")
    print("-" * 85)
    
    # Test SHA-256
    sha256_result = sha256_analyzer.measure_single_run(test_size)
    sha256_custom = sha256_result.custom_metrics
    sha256_input = sha256_analyzer.prepare_input(test_size)
    sha256_hash = sha256_analyzer.run_algorithm(sha256_input)
    sha256_preview = sha256_hash.get('hash_hex', '')[:16] + "..."
    
    print(f"SHA-256     {256:<10} {sha256_analyzer.block_size_bytes:<10} "
          f"{sha256_result.execution_time_ms:<10.2f} "
          f"{sha256_custom.get('throughput_mbps', 0):<12.2f} "
          f"{sha256_preview:<20}")
    
    # Test SHA-3 variants
    for hash_size in sha3_variants:
        try:
            sha3_analyzer = SHA3HashingScalingAnalyzer(
                hash_size=hash_size,
                output_dir=f"demo_results/hash_comparison/sha3_{hash_size}",
                enable_gpu_tracking=False
            )
            
            sha3_result = sha3_analyzer.measure_single_run(test_size)
            sha3_custom = sha3_result.custom_metrics
            sha3_input = sha3_analyzer.prepare_input(test_size)
            sha3_hash = sha3_analyzer.run_algorithm(sha3_input)
            sha3_preview = sha3_hash.get('hash_hex', '')[:16] + "..."
            
            print(f"SHA3-{hash_size:<6} {hash_size:<10} {sha3_analyzer.rate_bytes:<10} "
                  f"{sha3_result.execution_time_ms:<10.2f} "
                  f"{sha3_custom.get('throughput_mbps', 0):<12.2f} "
                  f"{sha3_preview:<20}")
            
        except Exception as e:
            print(f"SHA3-{hash_size:<6} ERROR: {str(e)[:40]}")


def demo_scaling_comparison():
    """Compare scaling behavior between SHA-256 and SHA-3"""
    print("\n=== Scaling Behavior Comparison ===")
    
    # Test sizes for scaling analysis
    scaling_sizes = [256, 512, 1024, 2048, 4096, 8192]
    
    # SHA-256 analysis
    print("\n--- SHA-256 Scaling ---")
    sha256_analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/hash_comparison/sha256_scaling",
        enable_gpu_tracking=False
    )
    
    sha256_results = sha256_analyzer.run_scaling_analysis(scaling_sizes)
    sha256_scaling = sha256_analyzer.analyze_scaling_behavior()
    
    print(f"SHA-256 scaling factor: {sha256_scaling['mean_scaling_factor']:.2f} ± {sha256_scaling['std_scaling_factor']:.2f}")
    
    # SHA3-256 analysis for comparison
    print("\n--- SHA3-256 Scaling ---")
    sha3_analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="demo_results/hash_comparison/sha3_256_scaling",
        enable_gpu_tracking=False
    )
    
    sha3_results = sha3_analyzer.run_scaling_analysis(scaling_sizes)
    sha3_scaling = sha3_analyzer.analyze_scaling_behavior()
    
    print(f"SHA3-256 scaling factor: {sha3_scaling['mean_scaling_factor']:.2f} ± {sha3_scaling['std_scaling_factor']:.2f}")
    
    # Side-by-side comparison
    print(f"\n=== Side-by-Side Scaling Results ===")
    print(f"{'Size':<8} {'SHA-256 Time':<12} {'SHA3-256 Time':<12} {'SHA-256 Tput':<12} {'SHA3-256 Tput':<12} {'Ratio':<8}")
    print("-" * 75)
    
    for i, size in enumerate(scaling_sizes):
        sha256_time = sha256_results[i].execution_time_ms
        sha3_time = sha3_results[i].execution_time_ms
        sha256_tput = sha256_results[i].custom_metrics.get('throughput_mbps', 0)
        sha3_tput = sha3_results[i].custom_metrics.get('throughput_mbps', 0)
        
        ratio = sha256_tput / sha3_tput if sha3_tput > 0 else 0
        
        print(f"{size:<8} {sha256_time:<12.2f} {sha3_time:<12.2f} "
              f"{sha256_tput:<12.2f} {sha3_tput:<12.2f} {ratio:<8.2f}")


def demo_throughput_analysis():
    """Analyze throughput characteristics across algorithms"""
    print("\n=== Throughput Analysis ===")
    
    # Test with larger sizes for stable measurements
    throughput_sizes = [2048, 4096, 8192, 16384, 32768]
    
    algorithms = [
        ("SHA-256", SHA256HashingScalingAnalyzer, {}),
        ("SHA3-224", SHA3HashingScalingAnalyzer, {"hash_size": 224}),
        ("SHA3-256", SHA3HashingScalingAnalyzer, {"hash_size": 256}),
        ("SHA3-384", SHA3HashingScalingAnalyzer, {"hash_size": 384}),
        ("SHA3-512", SHA3HashingScalingAnalyzer, {"hash_size": 512}),
    ]
    
    print(f"Testing throughput with sizes: {throughput_sizes}")
    
    # Collect results for each algorithm
    algorithm_results = {}
    
    for name, analyzer_class, kwargs in algorithms:
        print(f"\n--- {name} Throughput ---")
        
        try:
            analyzer = analyzer_class(
                output_dir=f"demo_results/hash_comparison/throughput_{name.lower().replace('-', '_')}",
                enable_gpu_tracking=False,
                **kwargs
            )
            
            throughputs = []
            
            print(f"{'Size':<8} {'Time(ms)':<10} {'Throughput':<12}")
            print("-" * 35)
            
            for size in throughput_sizes:
                result = analyzer.measure_single_run(size)
                custom_metrics = result.custom_metrics
                
                throughput = custom_metrics.get('throughput_mbps', 0)
                throughputs.append(throughput)
                
                print(f"{size:<8} {result.execution_time_ms:<10.2f} {throughput:<12.2f}")
            
            # Calculate average throughput for larger sizes
            avg_throughput = sum(throughputs[-3:]) / 3  # Last 3 measurements
            algorithm_results[name] = avg_throughput
            
            print(f"Average throughput: {avg_throughput:.2f} MB/s")
            
        except Exception as e:
            print(f"Error with {name}: {e}")
            algorithm_results[name] = 0
    
    # Summary comparison
    print(f"\n=== Throughput Summary ===")
    print(f"{'Algorithm':<12} {'Avg Throughput':<15} {'Relative Performance':<20}")
    print("-" * 50)
    
    max_throughput = max(algorithm_results.values()) if algorithm_results.values() else 1
    
    for name in ["SHA-256", "SHA3-224", "SHA3-256", "SHA3-384", "SHA3-512"]:
        throughput = algorithm_results.get(name, 0)
        relative = (throughput / max_throughput * 100) if max_throughput > 0 else 0
        
        print(f"{name:<12} {throughput:<15.2f} {relative:<20.1f}%")


def demo_block_vs_rate_analysis():
    """Compare block-based vs rate-based processing"""
    print("\n=== Block vs Rate Processing Analysis ===")
    
    # SHA-256: 64-byte blocks
    sha256_analyzer = SHA256HashingScalingAnalyzer(
        output_dir="demo_results/hash_comparison/block_analysis",
        enable_gpu_tracking=False
    )
    
    # SHA3-256: 136-byte rate
    sha3_analyzer = SHA3HashingScalingAnalyzer(
        hash_size=256,
        output_dir="demo_results/hash_comparison/rate_analysis",
        enable_gpu_tracking=False
    )
    
    block_size = sha256_analyzer.block_size_bytes  # 64
    rate_size = sha3_analyzer.rate_bytes  # 136
    
    print(f"SHA-256 block size: {block_size} bytes")
    print(f"SHA3-256 rate size: {rate_size} bytes")
    
    # Test sizes around boundaries
    test_sizes = [
        32, 64, 96, 128, 136, 192, 256, 272, 384, 512, 544, 768, 1024
    ]
    
    print(f"\n{'Size':<6} {'SHA-256':<15} {'SHA3-256':<15} {'SHA-256 Units':<15} {'SHA3-256 Units':<15}")
    print("-" * 75)
    
    for size in test_sizes:
        # SHA-256 analysis
        sha256_result = sha256_analyzer.measure_single_run(size)
        sha256_custom = sha256_result.custom_metrics
        sha256_blocks = sha256_custom.get('actual_blocks', 0)
        
        # SHA3-256 analysis
        sha3_result = sha3_analyzer.measure_single_run(size)
        sha3_custom = sha3_result.custom_metrics
        sha3_rounds = sha3_custom.get('actual_rounds', 0)
        
        print(f"{size:<6} {sha256_result.execution_time_ms:<15.3f} "
              f"{sha3_result.execution_time_ms:<15.3f} "
              f"{sha256_blocks:<15} {sha3_rounds:<15}")


def demo_security_vs_performance():
    """Analyze security vs performance trade-offs"""
    print("\n=== Security vs Performance Trade-offs ===")
    
    test_size = 4096  # 4KB test for stable measurements
    
    algorithms = [
        ("SHA-256", 128, SHA256HashingScalingAnalyzer, {}),
        ("SHA3-224", 112, SHA3HashingScalingAnalyzer, {"hash_size": 224}),
        ("SHA3-256", 128, SHA3HashingScalingAnalyzer, {"hash_size": 256}),
        ("SHA3-384", 192, SHA3HashingScalingAnalyzer, {"hash_size": 384}),
        ("SHA3-512", 256, SHA3HashingScalingAnalyzer, {"hash_size": 512}),
    ]
    
    print(f"Testing with {test_size} byte input:")
    print(f"{'Algorithm':<12} {'Security':<10} {'Hash Size':<10} {'Time(ms)':<10} {'Throughput':<12} {'Efficiency':<12}")
    print("-" * 80)
    
    for name, security_bits, analyzer_class, kwargs in algorithms:
        try:
            analyzer = analyzer_class(
                output_dir=f"demo_results/hash_comparison/security_{name.lower().replace('-', '_')}",
                enable_gpu_tracking=False,
                **kwargs
            )
            
            result = analyzer.measure_single_run(test_size)
            custom_metrics = result.custom_metrics
            
            hash_size = custom_metrics.get('hash_size_bits', security_bits * 2)
            throughput = custom_metrics.get('throughput_mbps', 0)
            
            # Efficiency: throughput per bit of security
            efficiency = throughput / security_bits if security_bits > 0 else 0
            
            print(f"{name:<12} {security_bits:<10} {hash_size:<10} "
                  f"{result.execution_time_ms:<10.2f} "
                  f"{throughput:<12.2f} {efficiency:<12.3f}")
            
        except Exception as e:
            print(f"{name:<12} ERROR: {str(e)[:40]}")


def main():
    """Run comprehensive hash algorithm comparison"""
    print("Comprehensive Hash Algorithm Comparison")
    print("=" * 60)
    
    try:
        demo_basic_comparison()
        demo_scaling_comparison()
        demo_throughput_analysis()
        demo_block_vs_rate_analysis()
        demo_security_vs_performance()
        
        print("\n" + "=" * 60)
        print("Hash algorithm comparison completed successfully!")
        print("\nKey findings:")
        print("- SHA-256 generally provides higher throughput than SHA-3")
        print("- SHA-3 variants show performance decreases with higher security")
        print("- Both algorithms exhibit linear O(n) scaling behavior")
        print("- Block vs rate processing affects boundary efficiency")
        print("- Security vs performance trade-offs vary by use case")
        print("- Results saved in demo_results/hash_comparison/ directories")
        
    except Exception as e:
        print(f"\nHash comparison failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
