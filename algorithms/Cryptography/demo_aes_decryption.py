#!/usr/bin/env python3
"""
AES Decryption Scaling Analysis Demo

This script demonstrates how to use the AES Decryption Scaling Analyzer
to measure the performance characteristics of different AES decryption configurations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography.aes_decryption_analyzer import AESDecryptionScalingAnalyzer


def demo_basic_usage():
    """Demonstrate basic AES decryption scaling analysis"""
    print("=== Basic AES-256-CBC Decryption Scaling Analysis ===")
    
    # Create analyzer
    analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/aes_decrypt_256_cbc",
        enable_gpu_tracking=False
    )
    
    # Define input sizes (in bytes)
    input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]
    
    print(f"Testing with input sizes: {input_sizes}")
    print("Running decryption scaling analysis...")
    
    # Run analysis
    results = analyzer.run_scaling_analysis(input_sizes)
    
    # Show results
    print(f"\n=== Results Summary ===")
    for result in results:
        custom_metrics = result.custom_metrics
        print(f"Size {result.input_size:4d}B: "
              f"{result.execution_time_ms:6.2f}ms, "
              f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
              f"Correct: {custom_metrics.get('correctness_verified', False)}")
    
    # Analyze scaling behavior
    scaling = analyzer.analyze_scaling_behavior()
    if scaling:
        print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
        print("Expected: ~1.0 for linear scaling (O(n))")


def demo_mode_comparison():
    """Compare different AES decryption modes"""
    print("\n=== AES Decryption Mode Comparison ===")
    
    modes = ['CBC', 'CTR', 'GCM']
    test_size = 1024  # 1KB test
    
    results = {}
    
    for mode in modes:
        print(f"\nTesting AES-256-{mode} Decryption...")
        
        analyzer = AESDecryptionScalingAnalyzer(
            key_size=256,
            mode=mode,
            output_dir=f"demo_results/aes_decrypt_256_{mode.lower()}",
            enable_gpu_tracking=False
        )
        
        # Single measurement
        metrics = analyzer.measure_single_run(test_size)
        custom_metrics = metrics.custom_metrics
        
        results[mode] = {
            'time_ms': metrics.execution_time_ms,
            'throughput_mbps': custom_metrics.get('throughput_mbps', 0),
            'compression_ratio': custom_metrics.get('compression_ratio', 1.0),
            'correctness': custom_metrics.get('correctness_verified', False)
        }
        
        print(f"  Time: {metrics.execution_time_ms:.2f}ms")
        print(f"  Throughput: {custom_metrics.get('throughput_mbps', 0):.2f}MB/s")
        print(f"  Compression ratio: {custom_metrics.get('compression_ratio', 1.0):.3f}")
        print(f"  Correctness: {custom_metrics.get('correctness_verified', False)}")
    
    # Compare results
    print(f"\n=== Decryption Mode Comparison Summary (1KB data) ===")
    print(f"{'Mode':<6} {'Time(ms)':<10} {'Throughput(MB/s)':<15} {'Compression':<12} {'Correct':<8}")
    print("-" * 65)
    for mode, data in results.items():
        print(f"{mode:<6} {data['time_ms']:<10.2f} {data['throughput_mbps']:<15.2f} "
              f"{data['compression_ratio']:<12.3f} {data['correctness']:<8}")


def demo_key_size_comparison():
    """Compare different AES key sizes for decryption"""
    print("\n=== AES Key Size Comparison for Decryption ===")
    
    key_sizes = [128, 192, 256]
    test_size = 1024  # 1KB test
    
    results = {}
    
    for key_size in key_sizes:
        print(f"\nTesting AES-{key_size}-CBC Decryption...")
        
        analyzer = AESDecryptionScalingAnalyzer(
            key_size=key_size,
            mode='CBC',
            output_dir=f"demo_results/aes_decrypt_{key_size}_cbc",
            enable_gpu_tracking=False
        )
        
        # Single measurement
        metrics = analyzer.measure_single_run(test_size)
        custom_metrics = metrics.custom_metrics
        
        results[key_size] = {
            'time_ms': metrics.execution_time_ms,
            'throughput_mbps': custom_metrics.get('throughput_mbps', 0),
            'correctness': custom_metrics.get('correctness_verified', False)
        }
        
        print(f"  Time: {metrics.execution_time_ms:.2f}ms")
        print(f"  Throughput: {custom_metrics.get('throughput_mbps', 0):.2f}MB/s")
        print(f"  Correctness: {custom_metrics.get('correctness_verified', False)}")
    
    # Compare results
    print(f"\n=== Key Size Comparison Summary for Decryption (1KB data) ===")
    print(f"{'Key Size':<10} {'Time(ms)':<10} {'Throughput(MB/s)':<15} {'Correct':<8}")
    print("-" * 50)
    for key_size, data in results.items():
        print(f"AES-{key_size:<4} {data['time_ms']:<10.2f} {data['throughput_mbps']:<15.2f} {data['correctness']:<8}")


def demo_encryption_vs_decryption():
    """Compare encryption vs decryption performance"""
    print("\n=== Encryption vs Decryption Performance Comparison ===")
    
    # Import encryption analyzer for comparison
    from algorithms.Cryptography.aes_analyzer import AESEncryptionScalingAnalyzer
    
    test_size = 1024
    
    # Test encryption
    print(f"\nTesting AES-256-CBC Encryption...")
    enc_analyzer = AESEncryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/aes_encrypt_comparison",
        enable_gpu_tracking=False
    )
    enc_metrics = enc_analyzer.measure_single_run(test_size)
    enc_custom = enc_metrics.custom_metrics
    
    print(f"  Encryption Time: {enc_metrics.execution_time_ms:.2f}ms")
    print(f"  Encryption Throughput: {enc_custom.get('throughput_mbps', 0):.2f}MB/s")
    
    # Test decryption
    print(f"\nTesting AES-256-CBC Decryption...")
    dec_analyzer = AESDecryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/aes_decrypt_comparison",
        enable_gpu_tracking=False
    )
    dec_metrics = dec_analyzer.measure_single_run(test_size)
    dec_custom = dec_metrics.custom_metrics
    
    print(f"  Decryption Time: {dec_metrics.execution_time_ms:.2f}ms")
    print(f"  Decryption Throughput: {dec_custom.get('throughput_mbps', 0):.2f}MB/s")
    print(f"  Correctness: {dec_custom.get('correctness_verified', False)}")
    
    # Compare results
    print(f"\n=== Encryption vs Decryption Summary (1KB data) ===")
    print(f"{'Operation':<12} {'Time(ms)':<10} {'Throughput(MB/s)':<15}")
    print("-" * 40)
    print(f"{'Encryption':<12} {enc_metrics.execution_time_ms:<10.2f} {enc_custom.get('throughput_mbps', 0):<15.2f}")
    print(f"{'Decryption':<12} {dec_metrics.execution_time_ms:<10.2f} {dec_custom.get('throughput_mbps', 0):<15.2f}")
    
    # Calculate relative performance
    if enc_metrics.execution_time_ms > 0:
        speed_ratio = dec_metrics.execution_time_ms / enc_metrics.execution_time_ms
        print(f"\nDecryption is {speed_ratio:.2f}x {'slower' if speed_ratio > 1 else 'faster'} than encryption")


def main():
    """Run all demonstrations"""
    print("AES Decryption Scaling Analysis Demo")
    print("=" * 50)
    
    try:
        demo_basic_usage()
        demo_mode_comparison()
        demo_key_size_comparison()
        demo_encryption_vs_decryption()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        print("\nResults saved in demo_results/ directory")
        print("Check the CSV and JSON files for detailed metrics.")
        
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
