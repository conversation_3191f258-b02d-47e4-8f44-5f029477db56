# AES Encryption Scaling Analysis

This module provides comprehensive scaling analysis for AES (Advanced Encryption Standard) encryption algorithms, measuring both time and space complexity across different input sizes, key lengths, and encryption modes.

## Features

### Supported AES Configurations
- **Key Sizes**: AES-128, AES-192, AES-256
- **Encryption Modes**: CBC, ECB, CFB, OFB, CTR, GCM
- **Padding Algorithms**: PKCS7, ANSIX923 (for block modes)

### Scaling Analysis
- Time complexity analysis (expected O(n) for AES encryption)
- Space complexity analysis (O(1) constant space overhead)
- Custom metrics specific to cryptographic operations
- Throughput analysis (MB/s)
- Block processing efficiency
- Padding overhead analysis

## Usage

### Basic Usage

```python
from algorithms.Cryptography.aes_analyzer import AESEncryptionScalingAnalyzer

# Create analyzer with default AES-256-CBC
analyzer = AESEncryptionScalingAnalyzer(
    key_size=256,
    mode='CBC',
    padding_algorithm='PKCS7'
)

# Run scaling analysis
input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]  # bytes
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### Advanced Configuration

```python
# Test different AES configurations
configs = [
    {'key_size': 128, 'mode': 'CBC'},
    {'key_size': 192, 'mode': 'CBC'},
    {'key_size': 256, 'mode': 'CBC'},
    {'key_size': 256, 'mode': 'CTR'},  # Stream mode
    {'key_size': 256, 'mode': 'GCM'},  # Authenticated encryption
]

for config in configs:
    analyzer = AESEncryptionScalingAnalyzer(
        **config,
        output_dir=f"results/aes_{config['key_size']}_{config['mode'].lower()}"
    )
    results = analyzer.run_scaling_analysis(input_sizes)
```

### Testing Different Input Sizes

```python
# Small data (bytes to KB)
small_sizes = [16, 32, 64, 128, 256, 512, 1024]

# Medium data (KB range)
medium_sizes = [1024, 2048, 4096, 8192, 16384, 32768, 65536]

# Large data (MB range)
large_sizes = [65536, 131072, 262144, 524288, 1048576, 2097152]

analyzer = AESEncryptionScalingAnalyzer(key_size=256, mode='CBC')
results = analyzer.run_scaling_analysis(small_sizes + medium_sizes + large_sizes)
```

## Key Metrics

### Time Complexity Metrics
- **Execution Time**: Mean encryption time in milliseconds
- **Throughput**: Data processing rate in MB/s
- **Blocks per Second**: AES blocks processed per second
- **Scaling Factor**: Empirical scaling behavior

### Space Complexity Metrics
- **Memory Increment**: Additional memory used during encryption
- **Padding Overhead**: Extra bytes added for block alignment
- **Expansion Ratio**: Output size / Input size ratio
- **Theoretical Memory**: Expected memory usage

### Cryptographic Metrics
- **Block Count**: Number of AES blocks processed
- **Key Size**: Encryption key length in bits
- **Mode**: Encryption mode (CBC, CTR, GCM, etc.)
- **Padding Algorithm**: Padding scheme used

## Expected Complexity

### Time Complexity
- **AES Encryption**: O(n) where n is the number of blocks
- **Block Processing**: Constant time per 16-byte block
- **Scaling Factor**: Should be close to 1.0 for linear scaling

### Space Complexity
- **Memory Usage**: O(1) constant overhead
- **Padding**: Up to 15 bytes for block modes
- **Stream Modes**: No padding required (CTR, CFB, OFB, GCM)

## Performance Characteristics

### Mode Comparison
- **CBC**: Block mode, requires padding, sequential processing
- **ECB**: Block mode, requires padding, parallel processing possible
- **CTR**: Stream mode, no padding, parallel processing possible
- **GCM**: Authenticated encryption, no padding, includes authentication tag

### Key Size Impact
- **AES-128**: Fastest, 10 rounds
- **AES-192**: Medium speed, 12 rounds
- **AES-256**: Slowest, 14 rounds

## Running the Analysis

### Full Analysis
```bash
python algorithms/Cryptography/aes_analyzer.py
```

### Quick Test
```bash
python algorithms/Cryptography/test_aes.py
```

## Output Files

Results are saved in CSV and JSON formats:
- `results/aes_{keysize}_{mode}/AES-{keysize}-{mode}_scaling_results.csv`
- `results/aes_{keysize}_{mode}/AES-{keysize}-{mode}_scaling_results.json`

### Sample Output
```
Size     1024B:     0.13ms, Memory: +0.00MB, Throughput: 112.85MB/s, Blocks:     65
Size     2048B:     0.15ms, Memory: +0.00MB, Throughput: 196.84MB/s, Blocks:    129
Size     4096B:     0.18ms, Memory: +0.00MB, Throughput: 343.50MB/s, Blocks:    257
```

## Dependencies

- `cryptography`: Python cryptographic library
- `numpy`: Numerical computations
- `core.scaling_framework`: Unified scaling analysis framework

## Security Note

This analyzer is designed for performance measurement only. The generated keys and data are for testing purposes and should not be used in production cryptographic applications.
