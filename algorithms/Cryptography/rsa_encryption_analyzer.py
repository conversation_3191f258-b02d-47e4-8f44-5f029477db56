"""
RSA Encryption Scaling Analyzer

This module provides scaling analysis for RSA (Rivest-Shamir-Adleman) encryption,
measuring both time and space complexity across different input sizes and key sizes.
RSA is an asymmetric cryptographic algorithm widely used for secure data transmission.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
import secrets
from typing import Any, Dict, Tuple
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes, serialization
from core.scaling_framework import ScalingAnalyzer


class RSAEncryptionScalingAnalyzer(ScalingAnalyzer):
    """RSA Encryption scaling analysis using unified framework"""
    
    def __init__(self, key_size: int = 2048, padding_scheme: str = 'OAEP', **kwargs):
        """
        Initialize RSA Encryption analyzer
        
        Args:
            key_size: RSA key size in bits (1024, 2048, 3072, 4096)
            padding_scheme: Padding scheme ('OAEP', 'PKCS1v15')
        """
        if key_size not in [1024, 2048, 3072, 4096]:
            raise ValueError(f"Invalid key size: {key_size}. Must be 1024, 2048, 3072, or 4096")
        
        if padding_scheme not in ['OAEP', 'PKCS1v15']:
            raise ValueError(f"Invalid padding scheme: {padding_scheme}. Must be 'OAEP' or 'PKCS1v15'")
        
        super().__init__(algorithm_name=f"RSA-{key_size}-{padding_scheme}-Encrypt", **kwargs)
        
        # RSA specific parameters
        self.key_size = key_size
        self.key_size_bytes = key_size // 8
        self.padding_scheme = padding_scheme
        
        # Calculate maximum plaintext size based on padding
        if padding_scheme == 'OAEP':
            # OAEP padding overhead: 2 * hash_length + 2 (SHA-256 = 32 bytes)
            self.max_plaintext_size = self.key_size_bytes - 2 * 32 - 2
        else:  # PKCS1v15
            # PKCS#1 v1.5 padding overhead: 11 bytes minimum
            self.max_plaintext_size = self.key_size_bytes - 11
        
        # Generate RSA key pair
        self._generate_key_pair()
        
        # Set up padding object
        self._setup_padding()
    
    def _generate_key_pair(self):
        """Generate RSA key pair"""
        try:
            self.private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=self.key_size
            )
            self.public_key = self.private_key.public_key()
        except Exception as e:
            raise RuntimeError(f"Failed to generate RSA key pair: {e}")
    
    def _setup_padding(self):
        """Setup padding scheme"""
        if self.padding_scheme == 'OAEP':
            self.padding_obj = padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        else:  # PKCS1v15
            self.padding_obj = padding.PKCS1v15()
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Generate input data for RSA encryption"""
        np.random.seed(42)  # For reproducibility
        
        # RSA can only encrypt data smaller than key size minus padding
        if input_size > self.max_plaintext_size:
            # For larger inputs, we'll encrypt in chunks
            actual_size = min(input_size, self.max_plaintext_size * 10)  # Limit for demo
            chunks_needed = (actual_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        else:
            actual_size = input_size
            chunks_needed = 1
        
        # Generate random plaintext
        plaintext = secrets.token_bytes(actual_size)
        
        return {
            'plaintext': plaintext,
            'plaintext_size': len(plaintext),
            'max_chunk_size': self.max_plaintext_size,
            'chunks_needed': chunks_needed,
            'key_size': self.key_size,
            'padding_scheme': self.padding_scheme
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute RSA encryption algorithm"""
        try:
            plaintext = input_data['plaintext']
            plaintext_size = len(plaintext)
            max_chunk_size = self.max_plaintext_size
            
            # Measure encryption time
            start_time = time.perf_counter()
            
            ciphertext_chunks = []
            chunks_encrypted = 0
            
            # Encrypt in chunks if necessary
            for i in range(0, plaintext_size, max_chunk_size):
                chunk = plaintext[i:i + max_chunk_size]
                
                # Encrypt chunk
                encrypted_chunk = self.public_key.encrypt(chunk, self.padding_obj)
                ciphertext_chunks.append(encrypted_chunk)
                chunks_encrypted += 1
            
            encryption_time = time.perf_counter() - start_time
            
            # Combine all encrypted chunks
            ciphertext = b''.join(ciphertext_chunks)
            
            # Calculate metrics
            ciphertext_size = len(ciphertext)
            throughput_mbps = (plaintext_size / (1024 * 1024)) / encryption_time if encryption_time > 0 else 0
            
            # Calculate chunks per second
            chunks_per_second = chunks_encrypted / encryption_time if encryption_time > 0 else 0
            bytes_per_second = plaintext_size / encryption_time if encryption_time > 0 else 0
            
            # Calculate expansion ratio
            expansion_ratio = ciphertext_size / plaintext_size if plaintext_size > 0 else 0
            
            return {
                'ciphertext': ciphertext,
                'plaintext_size': plaintext_size,
                'ciphertext_size': ciphertext_size,
                'encryption_time_seconds': encryption_time,
                'chunks_encrypted': chunks_encrypted,
                'throughput_mbps': throughput_mbps,
                'bytes_per_second': bytes_per_second,
                'chunks_per_second': chunks_per_second,
                'expansion_ratio': expansion_ratio,
                'key_size': self.key_size,
                'padding_scheme': self.padding_scheme,
                'max_chunk_size': max_chunk_size
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'ciphertext': b'',
                'plaintext_size': len(input_data.get('plaintext', b'')),
                'ciphertext_size': 0,
                'encryption_time_seconds': 0,
                'chunks_encrypted': 0,
                'throughput_mbps': 0,
                'bytes_per_second': 0,
                'chunks_per_second': 0,
                'expansion_ratio': 0,
                'key_size': self.key_size,
                'padding_scheme': self.padding_scheme,
                'max_chunk_size': self.max_plaintext_size
            }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for RSA encryption"""
        # RSA encryption is O(k^3) where k is key size, but for fixed key size it's O(n) for chunks
        chunks = (input_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        
        time_complexity = f"O(n*k^3) [chunks={chunks}, k={self.key_size}]"
        space_complexity = "O(n)"  # Output size grows with input
        
        # Memory estimation: input + output + key storage
        theoretical_memory_mb = (
            input_size +  # Input plaintext
            chunks * self.key_size_bytes +  # Output ciphertext
            self.key_size_bytes * 2  # Key storage (public + private)
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate RSA encryption-specific metrics"""
        
        # Extract results
        encryption_result = result
        
        # Calculate theoretical values
        theoretical_chunks = (input_size + self.max_plaintext_size - 1) // self.max_plaintext_size
        
        # Calculate efficiency metrics
        actual_chunks = encryption_result.get('chunks_encrypted', 0)
        throughput = encryption_result.get('throughput_mbps', 0)
        
        # Calculate expansion metrics
        plaintext_size = encryption_result.get('plaintext_size', 0)
        ciphertext_size = encryption_result.get('ciphertext_size', 0)
        expansion_ratio = encryption_result.get('expansion_ratio', 0)
        
        # Calculate encryption rate metrics
        encryption_time = encryption_result.get('encryption_time_seconds', 0)
        if encryption_time > 0:
            encryption_rate_per_second = 1 / encryption_time
            bits_per_second = (plaintext_size * 8) / encryption_time
        else:
            encryption_rate_per_second = 0
            bits_per_second = 0
        
        # Calculate chunk processing efficiency
        chunk_processing_time = encryption_time / actual_chunks if actual_chunks > 0 else 0
        
        # Calculate key utilization
        key_utilization = plaintext_size / (actual_chunks * self.max_plaintext_size) if actual_chunks > 0 else 0
        
        return {
            'input_size_bytes': input_size,
            'plaintext_size_bytes': plaintext_size,
            'ciphertext_size_bytes': ciphertext_size,
            'key_size_bits': self.key_size,
            'key_size_bytes': self.key_size_bytes,
            'padding_scheme': self.padding_scheme,
            'max_chunk_size_bytes': self.max_plaintext_size,
            'theoretical_chunks': theoretical_chunks,
            'actual_chunks': actual_chunks,
            'expansion_ratio': round(expansion_ratio, 2),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(encryption_result.get('bytes_per_second', 0)),
            'chunks_per_second': int(encryption_result.get('chunks_per_second', 0)),
            'bits_per_second': int(bits_per_second),
            'encryption_rate_per_second': round(encryption_rate_per_second, 2),
            'encryption_time_ms': encryption_time * 1000,
            'chunk_processing_time_ms': chunk_processing_time * 1000,
            'key_utilization': round(key_utilization, 3),
            'has_error': 'error' in encryption_result,
            'algorithm_family': 'asymmetric_encryption',
            'algorithm_type': 'RSA',
            'operation': 'encryption'
        }


def main():
    """Run RSA Encryption scaling analysis"""
    print("=== RSA Encryption Scaling Analysis ===")

    # Test different RSA configurations
    configurations = [
        {'key_size': 2048, 'padding_scheme': 'OAEP'},
        {'key_size': 2048, 'padding_scheme': 'PKCS1v15'},
        {'key_size': 3072, 'padding_scheme': 'OAEP'},
        {'key_size': 4096, 'padding_scheme': 'OAEP'}
    ]

    for config in configurations:
        print(f"\n--- Testing RSA-{config['key_size']}-{config['padding_scheme']} ---")

        try:
            analyzer = RSAEncryptionScalingAnalyzer(
                **config,
                output_dir=f"results/rsa_{config['key_size']}_{config['padding_scheme'].lower()}_encrypt",
                enable_gpu_tracking=False
            )

            print(f"Key size: {analyzer.key_size} bits")
            print(f"Max plaintext size: {analyzer.max_plaintext_size} bytes")
            print(f"Padding scheme: {analyzer.padding_scheme}")

            # Input sizes for testing (limited by RSA chunk size)
            max_size = analyzer.max_plaintext_size
            input_sizes = [
                # Small sizes
                16, 32, 64, 128,
                
                # Approach max chunk size
                max_size // 4,
                max_size // 2,
                max_size - 10,
                max_size,
                
                # Multiple chunks
                max_size + 10,
                max_size * 2,
                max_size * 3,
                max_size * 4
            ]

            # Filter out invalid sizes
            input_sizes = [size for size in input_sizes if size > 0 and size <= max_size * 10]

            print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== RSA-{config['key_size']}-{config['padding_scheme']} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Note: RSA scaling depends on chunk processing")

            print(f"\n=== Sample Results ===")
            # Show results for different size categories
            sample_indices = [0, len(input_sizes)//4, len(input_sizes)//2, len(input_sizes)-1]
            
            for i in sample_indices:
                if i < len(results):
                    result = results[i]
                    custom_metrics = result.custom_metrics
                    print(f"Size {result.input_size:6d}B: "
                          f"{result.execution_time_ms:8.2f}ms, "
                          f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                          f"Chunks: {custom_metrics.get('actual_chunks', 0):3d}, "
                          f"Expansion: {custom_metrics.get('expansion_ratio', 0):4.1f}x")

        except Exception as e:
            print(f"Error in RSA-{config['key_size']}-{config['padding_scheme']} encryption analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nRSA Encryption scaling analysis completed!")
    print("Results saved in: results/rsa_*_encrypt/")


if __name__ == "__main__":
    main()
