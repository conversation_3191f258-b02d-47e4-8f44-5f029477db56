# RSA Encryption/Decryption Scaling Analyzers

## Overview

The RSA Encryption and Decryption Scaling Analyzers provide comprehensive performance analysis for the RSA (Rivest-Shamir-Adleman) asymmetric cryptographic algorithm. RSA is widely used for secure data transmission, digital signatures, and key exchange in modern cryptographic systems.

## Algorithm Details

### RSA Characteristics
- **Algorithm Type**: Asymmetric (public-key) cryptography
- **Key Sizes**: 1024, 2048, 3072, 4096 bits (2048+ recommended)
- **Security Level**: Approximately half the key size in bits
- **Time Complexity**: O(k³) per operation, where k is key size
- **Space Complexity**: O(n) where n is input size (due to expansion)

### RSA Operations
- **Encryption**: Uses public key, relatively fast
- **Decryption**: Uses private key, significantly slower than encryption
- **Key Generation**: One-time setup, computationally expensive

### Padding Schemes
- **OAEP (Optimal Asymmetric Encryption Padding)**: More secure, recommended
- **PKCS#1 v1.5**: Legacy standard, less secure but faster

## Implementation Details

### Maximum Plaintext Sizes
RSA can only encrypt data smaller than the key size minus padding overhead:

| Key Size | OAEP Max Plaintext | PKCS1v15 Max Plaintext |
|----------|-------------------|------------------------|
| 1024 bits | 62 bytes         | 117 bytes             |
| 2048 bits | 190 bytes        | 245 bytes             |
| 3072 bits | 318 bytes        | 373 bytes             |
| 4096 bits | 446 bytes        | 501 bytes             |

### Chunk Processing
For inputs larger than the maximum plaintext size:
- Data is split into chunks of maximum size
- Each chunk is encrypted/decrypted independently
- Output size = number of chunks × key size in bytes

## Usage

### Basic Usage

```python
from algorithms.Cryptography import RSAEncryptionScalingAnalyzer, RSADecryptionScalingAnalyzer

# Create encryption analyzer
encrypt_analyzer = RSAEncryptionScalingAnalyzer(
    key_size=2048,
    padding_scheme='OAEP',
    output_dir="results/rsa_encrypt",
    enable_gpu_tracking=False
)

# Create decryption analyzer
decrypt_analyzer = RSADecryptionScalingAnalyzer(
    key_size=2048,
    padding_scheme='OAEP',
    output_dir="results/rsa_decrypt",
    enable_gpu_tracking=False
)

# Single measurement
enc_result = encrypt_analyzer.measure_single_run(128)
dec_result = decrypt_analyzer.measure_single_run(128)

print(f"Encryption: {enc_result.custom_metrics['throughput_mbps']:.2f} MB/s")
print(f"Decryption: {dec_result.custom_metrics['throughput_mbps']:.2f} MB/s")
```

### Framework Integration

```python
from algorithms import get_analyzer_class

# Get analyzer classes
RSAEncrypt = get_analyzer_class('rsa_encrypt')
RSADecrypt = get_analyzer_class('rsa_decrypt')

# Create analyzers with custom parameters
encrypt_analyzer = RSAEncrypt(key_size=3072, padding_scheme='OAEP')
decrypt_analyzer = RSADecrypt(key_size=3072, padding_scheme='OAEP')

# Run scaling analysis
enc_results = encrypt_analyzer.run_scaling_analysis([64, 128, 256])
dec_results = decrypt_analyzer.run_scaling_analysis([64, 128, 256])
```

## Configuration Options

### RSA-Specific Options
- `key_size`: RSA key size in bits (1024, 2048, 3072, 4096)
- `padding_scheme`: Padding scheme ('OAEP', 'PKCS1v15')

### Inherited Options
- `output_dir`: Directory for saving results
- `enable_gpu_tracking`: Enable GPU memory tracking (default: False)
- `warmup_runs`: Number of warmup runs (default: 3)
- `measurement_runs`: Number of measurement runs (default: 5)

## Performance Metrics

### Standard Metrics
- **Execution Time**: Time for encryption/decryption (milliseconds)
- **Memory Usage**: Memory increment during operation
- **Throughput**: Data processing rate (MB/s)

### RSA-Specific Metrics
- **Chunks Processed**: Number of key-size chunks processed
- **Chunks per Second**: Chunk processing rate
- **Expansion Ratio**: Ciphertext size / plaintext size (encryption)
- **Compression Ratio**: Ciphertext size / plaintext size (decryption)
- **Key Utilization**: How efficiently the maximum chunk size is used
- **Correctness Verified**: Whether decryption correctly reverses encryption

## Expected Performance Characteristics

### Encryption vs Decryption
- **Encryption**: Fast, uses public key exponent (typically 65537)
- **Decryption**: Slow, uses private key with large exponent
- **Typical Ratio**: Decryption is 5-20x slower than encryption

### Key Size Impact
- **Security**: Larger keys provide exponentially better security
- **Performance**: Larger keys have cubic performance degradation
- **Recommended**: 2048-bit minimum, 3072-bit for future-proofing

### Scaling Behavior
- **Time Complexity**: O(chunks × k³) where k is key size
- **Chunk-Based**: Performance scales with number of chunks, not input size directly
- **Boundary Effects**: Significant performance jumps at chunk boundaries

## Testing and Validation

### Running Tests
```bash
# RSA encryption tests
python algorithms/Cryptography/test_rsa_encryption.py

# RSA decryption tests
python algorithms/Cryptography/test_rsa_decryption.py

# Comprehensive demonstration
python algorithms/Cryptography/demo_rsa.py

# Direct analyzer execution
python algorithms/Cryptography/rsa_encryption_analyzer.py
python algorithms/Cryptography/rsa_decryption_analyzer.py
```

### Test Coverage
- **Basic Functionality**: Encryption and decryption operations
- **Correctness Verification**: Decryption correctly reverses encryption
- **Key Size Variations**: Performance across different key sizes
- **Padding Schemes**: Comparison of OAEP vs PKCS1v15
- **Chunk Processing**: Multi-chunk encryption/decryption
- **Scaling Analysis**: Performance across different input sizes
- **Boundary Analysis**: Behavior at chunk boundaries

## Example Results

### Basic Performance Comparison
```
RSA-2048-OAEP Configuration:
Size   Encrypt Time  Decrypt Time  Enc Tput   Dec Tput   Expansion
32     0.12ms        0.62ms        0.32MB/s   0.05MB/s   8.0x
64     0.11ms        0.63ms        0.65MB/s   0.10MB/s   4.0x
128    0.11ms        0.61ms        1.31MB/s   0.21MB/s   2.0x
190    0.11ms        0.64ms        1.99MB/s   0.28MB/s   1.4x
```

### Key Size Comparison
```
Key Size   Security   Enc Time   Dec Time   Enc/Dec Ratio
1024       80 bits    0.12ms     0.26ms     2.2
2048       112 bits   0.11ms     0.62ms     5.6
3072       128 bits   0.13ms     1.70ms     13.2
4096       150 bits   0.15ms     4.20ms     28.0
```

### Chunk Boundary Analysis
```
Size   Chunks  Time(ms)   Throughput   Efficiency
180    1       0.11       1.81MB/s     1.81
190    1       0.11       1.91MB/s     1.91
191    2       0.14       1.49MB/s     0.74  <- Boundary jump
200    2       0.14       1.57MB/s     0.79
380    2       0.14       2.86MB/s     1.43
```

## Security Considerations

### Key Size Recommendations
- **1024-bit**: Legacy, not recommended (80-bit security)
- **2048-bit**: Current standard (112-bit security)
- **3072-bit**: Future-proof (128-bit security)
- **4096-bit**: Maximum security (150-bit security)

### Padding Scheme Security
- **OAEP**: Provably secure, resistant to chosen ciphertext attacks
- **PKCS1v15**: Vulnerable to padding oracle attacks, use only for legacy compatibility

### Performance vs Security Trade-offs
- Larger keys provide exponentially better security
- Performance degrades cubically with key size
- Choose key size based on security requirements and performance constraints

## Implementation Notes

### Correctness Verification
- Uses Python's `cryptography` library for reliable implementation
- Decryption analyzer verifies correctness by comparing with original plaintext
- All operations use the same key pair for consistency

### Performance Considerations
- Encryption is CPU-bound with minimal memory overhead
- Decryption is significantly more CPU-intensive
- Memory usage scales linearly with input size due to ciphertext expansion
- Key generation is expensive but done once per analyzer instance

### Limitations
- Maximum input size limited by available memory (chunks × key_size)
- Very large inputs may require streaming approaches
- Performance highly dependent on CPU capabilities
- No hardware acceleration support in current implementation

## Comparison with Symmetric Algorithms

### RSA vs AES
- **RSA**: Asymmetric, no shared secret needed, much slower
- **AES**: Symmetric, requires shared secret, much faster
- **Typical Use**: RSA for key exchange, AES for bulk data encryption

### Performance Comparison
- **AES**: 100-1000 MB/s throughput
- **RSA**: 0.1-10 MB/s throughput
- **Ratio**: AES is 100-1000x faster than RSA

## Troubleshooting

### Common Issues
1. **Input Too Large**: Ensure input size ≤ max_plaintext_size for single chunk
2. **Key Generation Slow**: Normal for large key sizes, done once per analyzer
3. **Low Throughput**: Expected for RSA, use for small data or key exchange only
4. **Memory Errors**: Reduce input size or number of chunks

### Performance Optimization
- Use appropriate key size for security requirements
- Consider PKCS1v15 for better performance if security permits
- Process data in optimal chunk sizes
- Use RSA for key exchange, symmetric algorithms for bulk data

## References

- [RFC 8017: PKCS #1: RSA Cryptography Specifications](https://tools.ietf.org/html/rfc8017)
- [RFC 3447: Public-Key Cryptography Standards (PKCS) #1](https://tools.ietf.org/html/rfc3447)
- [NIST SP 800-56B: Recommendation for Pair-Wise Key Establishment](https://csrc.nist.gov/publications/detail/sp/800-56b/rev-2/final)
- [Cryptography Library Documentation](https://cryptography.io/en/latest/hazmat/primitives/asymmetric/rsa/)
