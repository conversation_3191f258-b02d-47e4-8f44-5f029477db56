#!/usr/bin/env python3
"""
AES Encryption Scaling Analysis Demo

This script demonstrates how to use the AES Encryption Scaling Analyzer
to measure the performance characteristics of different AES configurations.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography import AESEncryptionScalingAnaly<PERSON>


def demo_basic_usage():
    """Demonstrate basic AES scaling analysis"""
    print("=== Basic AES-256-CBC Scaling Analysis ===")
    
    # Create analyzer
    analyzer = AESEncryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="demo_results/aes_256_cbc",
        enable_gpu_tracking=False
    )
    
    # Define input sizes (in bytes)
    input_sizes = [64, 128, 256, 512, 1024, 2048, 4096]
    
    print(f"Testing with input sizes: {input_sizes}")
    print("Running scaling analysis...")
    
    # Run analysis
    results = analyzer.run_scaling_analysis(input_sizes)
    
    # Show results
    print(f"\n=== Results Summary ===")
    for result in results:
        custom_metrics = result.custom_metrics
        print(f"Size {result.input_size:4d}B: "
              f"{result.execution_time_ms:6.2f}ms, "
              f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
              f"Blocks: {custom_metrics.get('actual_blocks', 0):4d}")
    
    # Analyze scaling behavior
    scaling = analyzer.analyze_scaling_behavior()
    if scaling:
        print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
        print("Expected: ~1.0 for linear scaling (O(n))")


def demo_mode_comparison():
    """Compare different AES modes"""
    print("\n=== AES Mode Comparison ===")
    
    modes = ['CBC', 'CTR', 'GCM']
    test_size = 1024  # 1KB test
    
    results = {}
    
    for mode in modes:
        print(f"\nTesting AES-256-{mode}...")
        
        analyzer = AESEncryptionScalingAnalyzer(
            key_size=256,
            mode=mode,
            output_dir=f"demo_results/aes_256_{mode.lower()}",
            enable_gpu_tracking=False
        )
        
        # Single measurement
        metrics = analyzer.measure_single_run(test_size)
        custom_metrics = metrics.custom_metrics
        
        results[mode] = {
            'time_ms': metrics.execution_time_ms,
            'throughput_mbps': custom_metrics.get('throughput_mbps', 0),
            'expansion_ratio': custom_metrics.get('expansion_ratio', 1.0),
            'padding_overhead': custom_metrics.get('padding_overhead_bytes', 0)
        }
        
        print(f"  Time: {metrics.execution_time_ms:.2f}ms")
        print(f"  Throughput: {custom_metrics.get('throughput_mbps', 0):.2f}MB/s")
        print(f"  Expansion ratio: {custom_metrics.get('expansion_ratio', 1.0):.3f}")
        print(f"  Padding overhead: {custom_metrics.get('padding_overhead_bytes', 0)} bytes")
    
    # Compare results
    print(f"\n=== Mode Comparison Summary (1KB data) ===")
    print(f"{'Mode':<6} {'Time(ms)':<10} {'Throughput(MB/s)':<15} {'Expansion':<10} {'Padding':<8}")
    print("-" * 60)
    for mode, data in results.items():
        print(f"{mode:<6} {data['time_ms']:<10.2f} {data['throughput_mbps']:<15.2f} "
              f"{data['expansion_ratio']:<10.3f} {data['padding_overhead']:<8}")


def demo_key_size_comparison():
    """Compare different AES key sizes"""
    print("\n=== AES Key Size Comparison ===")
    
    key_sizes = [128, 192, 256]
    test_size = 1024  # 1KB test
    
    results = {}
    
    for key_size in key_sizes:
        print(f"\nTesting AES-{key_size}-CBC...")
        
        analyzer = AESEncryptionScalingAnalyzer(
            key_size=key_size,
            mode='CBC',
            output_dir=f"demo_results/aes_{key_size}_cbc",
            enable_gpu_tracking=False
        )
        
        # Single measurement
        metrics = analyzer.measure_single_run(test_size)
        custom_metrics = metrics.custom_metrics
        
        results[key_size] = {
            'time_ms': metrics.execution_time_ms,
            'throughput_mbps': custom_metrics.get('throughput_mbps', 0)
        }
        
        print(f"  Time: {metrics.execution_time_ms:.2f}ms")
        print(f"  Throughput: {custom_metrics.get('throughput_mbps', 0):.2f}MB/s")
    
    # Compare results
    print(f"\n=== Key Size Comparison Summary (1KB data) ===")
    print(f"{'Key Size':<10} {'Time(ms)':<10} {'Throughput(MB/s)':<15}")
    print("-" * 40)
    for key_size, data in results.items():
        print(f"AES-{key_size:<4} {data['time_ms']:<10.2f} {data['throughput_mbps']:<15.2f}")


def main():
    """Run all demonstrations"""
    print("AES Encryption Scaling Analysis Demo")
    print("=" * 50)
    
    try:
        demo_basic_usage()
        demo_mode_comparison()
        demo_key_size_comparison()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        print("\nResults saved in demo_results/ directory")
        print("Check the CSV and JSON files for detailed metrics.")
        
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
