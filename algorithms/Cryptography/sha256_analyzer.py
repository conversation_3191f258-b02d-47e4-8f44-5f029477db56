"""
SHA-256 Hashing Scaling Analyzer

This module provides scaling analysis for SHA-256 (Secure Hash Algorithm 256-bit) hashing,
measuring both time and space complexity across different input sizes.
SHA-256 is part of the SHA-2 family of cryptographic hash functions.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

import os
import numpy as np
import time
import hashlib
import secrets
from typing import Any, Dict, Tuple
from core.scaling_framework import ScalingAnalyzer


class SHA256HashingScalingAnalyzer(ScalingAnalyzer):
    """SHA-256 Hashing scaling analysis using unified framework"""
    
    def __init__(self, **kwargs):
        """
        Initialize SHA-256 Hashing analyzer
        """
        super().__init__(algorithm_name="SHA-256-Hash", **kwargs)
        
        # SHA-256 specific constants
        self.hash_size_bits = 256
        self.hash_size_bytes = 32
        self.block_size_bits = 512
        self.block_size_bytes = 64
    
    def prepare_input(self, input_size: int) -> bytes:
        """Generate input data for SHA-256 hashing"""
        np.random.seed(42)  # For reproducibility
        
        # Generate random input data
        input_data = secrets.token_bytes(input_size)
        
        return input_data
    
    def run_algorithm(self, input_data: bytes) -> Dict[str, Any]:
        """Execute SHA-256 hashing algorithm"""
        try:
            # Create SHA-256 hash object
            sha256_hash = hashlib.sha256()
            
            # Measure hashing time
            start_time = time.perf_counter()
            sha256_hash.update(input_data)
            hash_digest = sha256_hash.digest()
            hashing_time = time.perf_counter() - start_time
            
            # Calculate metrics
            input_size = len(input_data)
            input_blocks = (input_size + self.block_size_bytes - 1) // self.block_size_bytes
            throughput_mbps = (input_size / (1024 * 1024)) / hashing_time if hashing_time > 0 else 0
            
            # Calculate blocks per second
            blocks_per_second = input_blocks / hashing_time if hashing_time > 0 else 0
            bytes_per_second = input_size / hashing_time if hashing_time > 0 else 0
            
            return {
                'hash_digest': hash_digest,
                'hash_hex': hash_digest.hex(),
                'input_size_bytes': input_size,
                'output_size_bytes': len(hash_digest),
                'hashing_time_seconds': hashing_time,
                'input_blocks': input_blocks,
                'throughput_mbps': throughput_mbps,
                'bytes_per_second': bytes_per_second,
                'blocks_per_second': blocks_per_second,
                'hash_algorithm': 'SHA-256',
                'block_size_bytes': self.block_size_bytes,
                'hash_size_bytes': self.hash_size_bytes
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'hash_digest': b'',
                'hash_hex': '',
                'input_size_bytes': len(input_data) if input_data else 0,
                'output_size_bytes': 0,
                'hashing_time_seconds': 0,
                'input_blocks': 0,
                'throughput_mbps': 0,
                'bytes_per_second': 0,
                'blocks_per_second': 0,
                'hash_algorithm': 'SHA-256',
                'block_size_bytes': self.block_size_bytes,
                'hash_size_bytes': self.hash_size_bytes
            }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for SHA-256 hashing"""
        # SHA-256 is O(n) where n is the number of 512-bit blocks
        blocks = (input_size + self.block_size_bytes - 1) // self.block_size_bytes
        
        time_complexity = f"O(n) [blocks={blocks}]"
        space_complexity = "O(1)"  # Constant space overhead
        
        # Memory estimation: input data + hash state + output hash
        theoretical_memory_mb = (
            input_size +  # Input data
            256 +  # Internal state (8 × 32-bit words + working variables)
            self.hash_size_bytes  # Output hash
        ) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate SHA-256 hashing-specific metrics"""
        
        # Extract results
        hash_result = result
        
        # Calculate theoretical values
        theoretical_blocks = (input_size + self.block_size_bytes - 1) // self.block_size_bytes
        
        # Calculate efficiency metrics
        actual_blocks = hash_result.get('input_blocks', 0)
        throughput = hash_result.get('throughput_mbps', 0)
        
        # Calculate compression ratio (input size to output size)
        output_size = hash_result.get('output_size_bytes', 0)
        compression_ratio = input_size / output_size if output_size > 0 else 0
        
        # Calculate hash rate metrics
        hashing_time = hash_result.get('hashing_time_seconds', 0)
        if hashing_time > 0:
            hash_rate_per_second = 1 / hashing_time
            bits_per_second = (input_size * 8) / hashing_time
        else:
            hash_rate_per_second = 0
            bits_per_second = 0
        
        # Calculate block processing efficiency
        block_processing_time = hashing_time / actual_blocks if actual_blocks > 0 else 0
        
        return {
            'input_size_bytes': input_size,
            'output_size_bytes': output_size,
            'hash_algorithm': 'SHA-256',
            'hash_size_bits': self.hash_size_bits,
            'block_size_bits': self.block_size_bits,
            'block_size_bytes': self.block_size_bytes,
            'theoretical_blocks': theoretical_blocks,
            'actual_blocks': actual_blocks,
            'compression_ratio': round(compression_ratio, 2),
            'throughput_mbps': round(throughput, 2),
            'bytes_per_second': int(hash_result.get('bytes_per_second', 0)),
            'blocks_per_second': int(hash_result.get('blocks_per_second', 0)),
            'bits_per_second': int(bits_per_second),
            'hash_rate_per_second': round(hash_rate_per_second, 2),
            'hashing_time_ms': hashing_time * 1000,
            'block_processing_time_us': block_processing_time * 1000000,
            'hash_hex_length': len(hash_result.get('hash_hex', '')),
            'has_error': 'error' in hash_result,
            'algorithm_family': 'cryptographic_hash',
            'hash_family': 'SHA-2'
        }


def main():
    """Run SHA-256 Hashing scaling analysis"""
    print("=== SHA-256 Hashing Scaling Analysis ===")

    try:
        analyzer = SHA256HashingScalingAnalyzer(
            output_dir="results/sha256_hash",
            enable_gpu_tracking=False
        )

        # Input sizes for testing (in bytes)
        # Test various sizes to analyze scaling behavior
        input_sizes = [
            # Small sizes (less than one block)
            1, 8, 16, 32, 55,  # 55 bytes = 440 bits (< 512 bits)

            # Single block size
            64,  # Exactly one 512-bit block

            # Multiple blocks
            128, 256, 512, 1024,  # KB range
            2048, 4096, 8192,     # Multi-KB range
            16384, 32768, 65536,  # Larger KB range
            131072, 262144,       # Hundreds of KB
            524288, 1048576       # MB range
        ]

        print(f"Input sizes: {len(input_sizes)} points from {input_sizes[0]} to {input_sizes[-1]} bytes")

        results = analyzer.run_scaling_analysis(input_sizes)
        scaling = analyzer.analyze_scaling_behavior()

        if scaling:
            print(f"\n=== SHA-256 Hashing Scaling Analysis ===")
            print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for SHA-256 hashing (O(n))")

        print(f"\n=== Sample Results ===")
        # Show results for different size categories
        sample_indices = [0, 5, 10, 15, len(results)-1]  # First, some middle, and last

        for i in sample_indices:
            if i < len(results):
                result = results[i]
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:8d}B: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Throughput: {custom_metrics.get('throughput_mbps', 0):8.2f}MB/s, "
                      f"Blocks: {custom_metrics.get('actual_blocks', 0):6d}")

        # Show hash examples for small inputs
        print(f"\n=== Hash Examples ===")
        for size in [1, 8, 32, 64]:
            if size <= max(input_sizes):
                test_input = analyzer.prepare_input(size)
                hash_result = analyzer.run_algorithm(test_input)
                print(f"Size {size:2d}B: {hash_result.get('hash_hex', '')[:16]}... "
                      f"(blocks: {hash_result.get('input_blocks', 0)})")

    except Exception as e:
        print(f"Error in SHA-256 hashing analysis: {e}")
        import traceback
        traceback.print_exc()

    print(f"\nSHA-256 Hashing scaling analysis completed!")
    print("Results saved in: results/sha256_hash/")


if __name__ == "__main__":
    main()
