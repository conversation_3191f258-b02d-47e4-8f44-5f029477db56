#!/usr/bin/env python3
"""
Test script for AES Encryption Scaling Analyzer

This script provides a simple test to verify that the AES analyzer works correctly
with different configurations and input sizes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Cryptography.aes_analyzer import AESEncryptionScalingAnalyzer


def test_basic_functionality():
    """Test basic AES encryption functionality"""
    print("=== Testing Basic AES Functionality ===")
    
    # Test AES-256-CBC
    analyzer = AESEncryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="test_results/aes_basic_test",
        enable_gpu_tracking=False
    )
    
    # Test with small input sizes
    test_sizes = [16, 64, 256, 1024]
    
    print(f"Testing with input sizes: {test_sizes}")
    
    for size in test_sizes:
        print(f"\nTesting size {size} bytes:")
        
        # Prepare input
        input_data = analyzer.prepare_input(size)
        print(f"  Input prepared: {len(input_data)} bytes")
        
        # Run algorithm
        result = analyzer.run_algorithm(input_data)
        print(f"  Encryption result: {len(result.get('ciphertext', b''))} bytes output")
        print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
        print(f"  Blocks processed: {result.get('input_blocks', 0)}")
        
        # Get theoretical complexity
        time_comp, space_comp, mem_mb = analyzer.get_theoretical_complexity(size)
        print(f"  Theoretical: {time_comp}, {space_comp}, {mem_mb:.4f} MB")
        
        # Calculate custom metrics
        custom_metrics = analyzer.calculate_custom_metrics(size, input_data, result)
        print(f"  Custom metrics: {len(custom_metrics)} metrics calculated")
        print(f"  Expansion ratio: {custom_metrics.get('expansion_ratio', 0):.3f}")
        print(f"  Padding overhead: {custom_metrics.get('padding_overhead_bytes', 0)} bytes")


def test_different_modes():
    """Test different AES modes"""
    print("\n=== Testing Different AES Modes ===")
    
    modes = ['CBC', 'ECB', 'CTR', 'GCM']
    test_size = 1024
    
    for mode in modes:
        print(f"\nTesting AES-256-{mode}:")
        
        try:
            analyzer = AESEncryptionScalingAnalyzer(
                key_size=256,
                mode=mode,
                output_dir=f"test_results/aes_{mode.lower()}_test",
                enable_gpu_tracking=False
            )
            
            input_data = analyzer.prepare_input(test_size)
            result = analyzer.run_algorithm(input_data)
            
            print(f"  Success: {len(result.get('ciphertext', b''))} bytes output")
            print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
            print(f"  Mode: {result.get('mode', 'Unknown')}")
            
            if 'error' in result:
                print(f"  Error: {result['error']}")
                
        except Exception as e:
            print(f"  Failed: {e}")


def test_different_key_sizes():
    """Test different AES key sizes"""
    print("\n=== Testing Different Key Sizes ===")
    
    key_sizes = [128, 192, 256]
    test_size = 1024
    
    for key_size in key_sizes:
        print(f"\nTesting AES-{key_size}-CBC:")
        
        try:
            analyzer = AESEncryptionScalingAnalyzer(
                key_size=key_size,
                mode='CBC',
                output_dir=f"test_results/aes_{key_size}_test",
                enable_gpu_tracking=False
            )
            
            input_data = analyzer.prepare_input(test_size)
            result = analyzer.run_algorithm(input_data)
            
            print(f"  Success: {len(result.get('ciphertext', b''))} bytes output")
            print(f"  Key size: {result.get('key_size_bits', 0)} bits")
            print(f"  Throughput: {result.get('throughput_mbps', 0):.2f} MB/s")
            
        except Exception as e:
            print(f"  Failed: {e}")


def test_scaling_analysis():
    """Test a small scaling analysis"""
    print("\n=== Testing Scaling Analysis ===")
    
    analyzer = AESEncryptionScalingAnalyzer(
        key_size=256,
        mode='CBC',
        output_dir="test_results/aes_scaling_test",
        enable_gpu_tracking=False
    )
    
    # Small input sizes for quick test
    input_sizes = [64, 128, 256, 512, 1024]
    
    print(f"Running scaling analysis with sizes: {input_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(input_sizes)
        
        print(f"\nScaling analysis completed!")
        print(f"Results: {len(results)} data points")
        
        # Show some results
        for i, result in enumerate(results):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:4d}B: "
                  f"{result.execution_time_ms:6.2f}ms, "
                  f"Throughput: {custom_metrics.get('throughput_mbps', 0):6.2f}MB/s")
        
        # Analyze scaling behavior
        scaling = analyzer.analyze_scaling_behavior()
        if scaling:
            print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for linear scaling (O(n))")
        
    except Exception as e:
        print(f"Scaling analysis failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all tests"""
    print("AES Encryption Scaling Analyzer Test Suite")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_different_modes()
        test_different_key_sizes()
        test_scaling_analysis()
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
