"""
Gemma-7B Scaling Analysis using Unified Framework
Migrated from gemma/scaling_gemma_7b.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import <PERSON>alingAnaly<PERSON>, ScalingMetrics
from typing import Any, <PERSON><PERSON>, Dict, List
import random
import numpy as np

# Optional imports with fallbacks
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

class GemmaScalingAnalyzer(ScalingAnalyzer):
    """Gemma-7B scaling analysis - migrated from gemma/scaling_gemma_7b.py"""
    
    def __init__(self, 
                 model_name: str = "google/gemma-7b",
                 max_new_tokens: int = 20,
                 **kwargs):
        super().__init__(algorithm_name="Gemma_7B", **kwargs)
        
        if not HAS_TORCH or not HAS_TRANSFORMERS:
            raise ImportError("This analyzer requires torch and transformers. Install with: pip install torch transformers")
        
        self.model_name = model_name
        self.max_new_tokens = max_new_tokens
        
        print(f"Loading Gemma model: {model_name}")
        
        # Load tokenizer and model (exactly like original)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Move to GPU if available
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            self.model = self.model.cuda()
        
        self.model.eval()
        
        # Base words from original (exactly as in gemma/scaling_gemma_7b.py)
        self.base_words = [
            "machine", "learning", "artificial", "intelligence", "neural", "network", 
            "data", "science", "algorithm", "model", "training", "deep", "computer", 
            "vision", "natural", "language", "processing"
        ]
        
        print(f"Gemma model loaded successfully on {self.device}")
        print(f"Context length: 8192 tokens (Gemma-7b)")
    
    def generate_random_text(self, target_length: int) -> str:
        """Generate random text with approximately target_length tokens (from original)"""
        text = ""
        current_tokens = 0
        
        while current_tokens < target_length:
            word = random.choice(self.base_words)
            test_text = text + " " + word if text else word
            
            # Check token count
            tokens = self.tokenizer(test_text, return_tensors="pt")
            token_count = tokens['input_ids'].shape[1]
            
            if token_count <= target_length:
                text = test_text
                current_tokens = token_count
            else:
                break
        
        return text
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Prepare Gemma input data with specified sequence length"""
        
        # Generate random text with target length (from original)
        input_text = self.generate_random_text(input_size)
        
        # Tokenize
        input_ids = self.tokenizer(input_text, return_tensors="pt")
        
        # Get actual input length
        actual_input_length = input_ids['input_ids'].shape[1]
        
        # Move to GPU if available (implied in original)
        if torch.cuda.is_available():
            input_ids = {k: v.cuda() for k, v in input_ids.items()}
        
        return {
            'input_ids': input_ids,
            'input_text': input_text,
            'actual_length': actual_input_length
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Gemma inference (from original implementation)"""
        
        input_ids = input_data['input_ids']
        actual_length = input_data['actual_length']
        
        # Clear GPU cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # Generate text (exactly like original)
        with torch.no_grad():
            outputs = self.model.generate(
                **input_ids,
                max_new_tokens=self.max_new_tokens,
                do_sample=False  # From original: limit output for faster testing
            )
        
        # Decode generated text (only new tokens, from original)
        generated_text = self.tokenizer.decode(
            outputs[0][actual_length:], 
            skip_special_tokens=True
        )
        
        # Calculate tokens per second
        total_tokens = outputs.shape[1]
        new_tokens = total_tokens - actual_length
        
        return {
            'generated_text': generated_text,
            'input_tokens': actual_length,
            'output_tokens': new_tokens,
            'total_tokens': total_tokens
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Gemma theoretical complexity analysis"""
        
        # Gemma-7b specific parameters (estimated)
        hidden_size = 3072  # Gemma-7b hidden size
        num_layers = 28
        num_attention_heads = 16
        
        # Memory estimation
        # Input embeddings + attention (KV cache) + activations
        input_embedding_mb = (input_size * hidden_size * 4) / (1024 * 1024)
        head_dim = hidden_size // num_attention_heads
        kv_cache_mb = (2 * input_size * num_attention_heads * head_dim * num_layers * 4) / (1024 * 1024)
        activations_mb = (input_size * hidden_size * num_layers * 4) / (1024 * 1024)
        
        total_theoretical_mb = input_embedding_mb + kv_cache_mb + activations_mb
        
        return (
            f"O(n²×d) [n={input_size}, d={hidden_size}]",  # Attention dominates
            f"O(n×d×L) [n={input_size}, d={hidden_size}, L={num_layers}]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Gemma-specific metrics (from original)"""
        
        # Tokens per second calculation
        if hasattr(self.timer, 'last_measurement') and self.timer.last_measurement > 0:
            tokens_per_second = result['output_tokens'] / (self.timer.last_measurement / 1000)
        else:
            tokens_per_second = 0
        
        # GPU memory usage
        gpu_memory_mb = 0
        if torch.cuda.is_available():
            gpu_memory_mb = torch.cuda.memory_allocated() / (1024 * 1024)
        
        return {
            'input_tokens': result['input_tokens'],
            'output_tokens': result['output_tokens'],
            'total_tokens': result['total_tokens'],
            'tokens_per_second': tokens_per_second,
            'gpu_memory_mb': gpu_memory_mb,
            'model_name': self.model_name,
            'device': self.device,
            'max_new_tokens': self.max_new_tokens,
            'generated_text_preview': result['generated_text'][:100] + "..." if len(result['generated_text']) > 100 else result['generated_text']
        }

def main():
    """Run Gemma scaling analysis with original parameters"""
    
    print("=== Gemma-7B Scaling Analysis (Original Parameters) ===")
    print("Migrated from gemma/scaling_gemma_7b.py")
    
    try:
        # Initialize analyzer
        analyzer = GemmaScalingAnalyzer(
            output_dir="results/gemma"
        )
        
        # Original input ranges from gemma/scaling_gemma_7b.py:
        # Original: 1 to 8192 with step 50
        end_length = 8192  # max length is 8192 for Gemma
        step_length = 50
        
        # Use smaller range for demo to avoid long runtime
        max_demo_length = min(1000, end_length)
        input_sizes = list(range(1, max_demo_length, step_length))
        
        print(f"\n=== Running Gemma Analysis ===")
        print(f"Input sequence lengths: {len(input_sizes)} points from {min(input_sizes)} to {max(input_sizes)}")
        print(f"Context limit: 8192 tokens (Gemma-7b)")
        print(f"Original range: 1-8192 with step 50")
        
        # Run scaling analysis
        results = analyzer.run_scaling_analysis(input_sizes)
        
        # Analyze scaling behavior
        scaling_analysis = analyzer.analyze_scaling_behavior()
        
        if scaling_analysis:
            print(f"\n=== Scaling Behavior Analysis ===")
            print(f"Mean scaling factor: {scaling_analysis['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling_analysis['std_scaling_factor']:.2f}")
            print(f"Expected: ~2.0 for attention mechanism (O(n²×d))")
        
        # Print sample results (from original progress output)
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):  # First 5 results
            custom_metrics = result.custom_metrics
            
            print(f"Input {result.input_size:4d}: {result.execution_time_ms:7.2f}ms, "
                  f"TPS: {custom_metrics.get('tokens_per_second', 0):6.2f}, "
                  f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                  f"GPU: {custom_metrics.get('gpu_memory_mb', 0):6.2f}MB")
        
        print(f"\n✅ Gemma-7B analysis completed!")
        print(f"📁 Results saved to: results/gemma/")
        print(f"📊 CSV: Gemma_7B_scaling_results.csv")
        print(f"📈 JSON: Gemma_7B_scaling_results.json")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("This might be due to:")
        print("- Missing dependencies (torch, transformers)")
        print("- Model access requirements")
        print("- Insufficient GPU memory")
        print("\n💡 Try running with smaller input ranges or CPU-only mode")

if __name__ == "__main__":
    main() 