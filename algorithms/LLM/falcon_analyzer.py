"""
Falcon-7B Scaling Analysis using Unified Framework
Migrated from falcon/scaling_falcon_7b.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import <PERSON>alingAnalyzer, ScalingMetrics
from typing import Any, Tu<PERSON>, Dict, List
import random
import numpy as np

# Optional imports with fallbacks
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

class FalconScalingAnalyzer(ScalingAnalyzer):
    """Falcon-7B scaling analysis - migrated from falcon/scaling_falcon_7b.py"""
    
    def __init__(self, 
                 model_name: str = "tiiuae/falcon-7b",
                 max_new_tokens: int = 20,
                 analysis_type: str = "basic",
                 **kwargs):
        super().__init__(algorithm_name="Falcon_7B", **kwargs)
        
        if not HAS_TORCH or not HAS_TRANSFORMERS:
            raise ImportError("This analyzer requires torch and transformers. Install with: pip install torch transformers")
        
        self.model_name = model_name
        self.max_new_tokens = max_new_tokens
        self.analysis_type = analysis_type  # "basic", "extended", or "batch"
        
        print(f"Loading Falcon model: {model_name}")
        print(f"Analysis type: {analysis_type}")
        
        # Load tokenizer and model (exactly like original)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True)
        
        # Set pad token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Move to GPU if available
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            self.model = self.model.cuda()
        
        self.model.eval()
        
        # Base words from original (Falcon-specific vocabulary)
        self.base_words = [
            "machine", "learning", "artificial", "intelligence", "neural", "network", 
            "data", "science", "algorithm", "model", "training", "deep", "computer", 
            "vision", "natural", "language", "processing", "transformer", "attention", 
            "embedding", "token", "sequence", "generation", "inference", "optimization",
            "falcon", "performance", "efficiency", "scalability", "computation", "memory", "throughput"
        ]
        
        print(f"Falcon model loaded successfully on {self.device}")
        print(f"Context length: 2048 tokens (Falcon-7b)")
    
    def generate_random_text(self, target_length: int) -> str:
        """Generate random text with approximately target_length tokens (from original)"""
        text = ""
        current_tokens = 0
        
        while current_tokens < target_length:
            word = random.choice(self.base_words)
            test_text = text + " " + word if text else word
            
            # Check token count
            tokens = self.tokenizer(test_text, return_tensors="pt")
            token_count = tokens['input_ids'].shape[1]
            
            if token_count <= target_length:
                text = test_text
                current_tokens = token_count
            else:
                break
        
        return text
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Prepare Falcon input data with specified sequence length"""
        
        # Generate random text with target length (from original)
        input_text = self.generate_random_text(input_size)
        
        # Tokenize
        input_ids = self.tokenizer(input_text, return_tensors="pt")
        
        # Get actual input length
        actual_input_length = input_ids['input_ids'].shape[1]
        
        # Move to GPU if available (from original)
        if torch.cuda.is_available():
            input_ids = {k: v.cuda() for k, v in input_ids.items()}
        
        return {
            'input_ids': input_ids,
            'input_text': input_text,
            'actual_length': actual_input_length
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Falcon inference (from original implementation)"""
        
        input_ids = input_data['input_ids']
        actual_length = input_data['actual_length']
        
        # Clear GPU cache if available (from original)
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # Configure generation parameters based on analysis type
        if self.analysis_type == "extended":
            max_new_tokens = 50
            generation_kwargs = {
                'temperature': 1.0,
                'top_p': 1.0
            }
        else:
            max_new_tokens = self.max_new_tokens
            generation_kwargs = {}
        
        # Generate text (exactly like original)
        with torch.no_grad():
            outputs = self.model.generate(
                **input_ids,
                max_new_tokens=max_new_tokens,
                do_sample=False,
                pad_token_id=self.tokenizer.eos_token_id,
                **generation_kwargs
            )
        
        # Decode generated text (only new tokens, from original)
        generated_text = self.tokenizer.decode(
            outputs[0][actual_length:], 
            skip_special_tokens=True
        )
        
        # Calculate tokens per second
        total_tokens = outputs.shape[1]
        new_tokens = total_tokens - actual_length
        
        return {
            'generated_text': generated_text,
            'input_tokens': actual_length,
            'output_tokens': new_tokens,
            'total_tokens': total_tokens
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Falcon theoretical complexity analysis"""
        
        # Falcon-7b specific parameters
        hidden_size = 4544  # Falcon-7b hidden size (from original)
        num_layers = 32
        num_attention_heads = 32
        
        # Memory estimation (from original extended analysis)
        # Input embeddings + attention (KV cache) + activations
        input_embedding_mb = (input_size * hidden_size * 4) / (1024 * 1024)
        head_dim = hidden_size // num_attention_heads
        kv_cache_mb = (2 * input_size * num_attention_heads * head_dim * num_layers * 4) / (1024 * 1024)
        activations_mb = (input_size * hidden_size * num_layers * 4) / (1024 * 1024)
        
        total_theoretical_mb = input_embedding_mb + kv_cache_mb + activations_mb
        
        return (
            f"O(n²×d) [n={input_size}, d={hidden_size}]",  # Attention dominates
            f"O(n×d×L) [n={input_size}, d={hidden_size}, L={num_layers}]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Falcon-specific metrics (from original)"""
        
        # Tokens per second calculation
        if hasattr(self.timer, 'last_measurement') and self.timer.last_measurement > 0:
            tokens_per_second = result['output_tokens'] / (self.timer.last_measurement / 1000)
        else:
            tokens_per_second = 0
        
        # GPU memory usage (from original extended analysis)
        gpu_memory_mb = 0
        if torch.cuda.is_available():
            gpu_memory_mb = torch.cuda.memory_allocated() / (1024 * 1024)
        
        return {
            'input_tokens': result['input_tokens'],
            'output_tokens': result['output_tokens'],
            'total_tokens': result['total_tokens'],
            'tokens_per_second': tokens_per_second,
            'gpu_memory_mb': gpu_memory_mb,
            'model_name': self.model_name,
            'device': self.device,
            'max_new_tokens': self.max_new_tokens,
            'generated_text_preview': result['generated_text'][:100] + "..." if len(result['generated_text']) > 100 else result['generated_text'],
            'analysis_type': self.analysis_type
        }

def main():
    """Run Falcon scaling analysis with original parameters"""
    
    print("=== Falcon-7B Scaling Analysis (Original Parameters) ===")
    print("Migrated from falcon/scaling_falcon_7b.py")
    
    # Analysis types from original file
    analysis_types = {
        '1': {'name': 'Basic', 'type': 'basic', 'desc': 'latency vs input length'},
        '2': {'name': 'Extended', 'type': 'extended', 'desc': 'with memory and performance metrics'},
        '3': {'name': 'Batch', 'type': 'batch', 'desc': 'throughput with different batch sizes'}
    }
    
    print("\nChoose analysis type:")
    for key, info in analysis_types.items():
        print(f"{key}. {info['name']} analysis ({info['desc']})")
    
    choice = input("Enter choice (1, 2, or 3): ").strip()
    
    if choice not in analysis_types:
        choice = '1'  # Default to basic
    
    selected_analysis = analysis_types[choice]
    
    try:
        # Initialize analyzer
        analyzer = FalconScalingAnalyzer(
            analysis_type=selected_analysis['type'],
            output_dir=f"results/falcon_{selected_analysis['type']}"
        )
        
        # Original input ranges from falcon/scaling_falcon_7b.py:
        if selected_analysis['type'] == 'basic':
            # Original: 1 to 2048 with step 50
            end_length = 2048
            step_length = 50
            input_sizes = list(range(1, end_length, step_length))
            
        elif selected_analysis['type'] == 'extended':
            # Original: 1 to 2048 with step 100 (larger steps)
            end_length = 2048  
            step_length = 100
            input_sizes = list(range(1, end_length, step_length))
            
        elif selected_analysis['type'] == 'batch':
            # Batch analysis: fixed input length, varying batch sizes
            # For now, we'll test different input lengths as a proxy
            input_sizes = [256, 512, 1024, 1536, 2048]
        
        # Use smaller range for demo to avoid long runtime
        max_demo_length = min(1000, max(input_sizes))
        input_sizes = [x for x in input_sizes if x <= max_demo_length]
        
        print(f"\n=== Running {selected_analysis['name']} Analysis ===")
        print(f"Input sequence lengths: {len(input_sizes)} points from {min(input_sizes)} to {max(input_sizes)}")
        print(f"Context limit: 2048 tokens (Falcon-7b)")
        print(f"Analysis type: {selected_analysis['desc']}")
        
        # Run scaling analysis
        results = analyzer.run_scaling_analysis(input_sizes)
        
        # Analyze scaling behavior
        scaling_analysis = analyzer.analyze_scaling_behavior()
        
        if scaling_analysis:
            print(f"\n=== Scaling Behavior Analysis ===")
            print(f"Mean scaling factor: {scaling_analysis['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling_analysis['std_scaling_factor']:.2f}")
            print(f"Expected: ~2.0 for attention mechanism (O(n²×d))")
        
        # Print sample results (from original progress output)
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):  # First 5 results
            custom_metrics = result.custom_metrics
            
            print(f"Input {result.input_size:4d}: {result.execution_time_ms:7.2f}ms, "
                  f"TPS: {custom_metrics.get('tokens_per_second', 0):6.2f}, "
                  f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                  f"GPU: {custom_metrics.get('gpu_memory_mb', 0):6.2f}MB")
        
        print(f"\n✅ Falcon-7B {selected_analysis['name']} analysis completed!")
        print(f"📁 Results saved to: results/falcon_{selected_analysis['type']}/")
        print(f"📊 CSV: Falcon_7B_scaling_results.csv")
        print(f"📈 JSON: Falcon_7B_scaling_results.json")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("This might be due to:")
        print("- Missing dependencies (torch, transformers)")
        print("- Model access requirements")
        print("- Insufficient GPU memory")
        print("\n💡 Try running with smaller input ranges or CPU-only mode")

if __name__ == "__main__":
    main() 