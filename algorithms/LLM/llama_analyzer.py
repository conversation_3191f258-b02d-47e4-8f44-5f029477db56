"""
LLaMA-7B Scaling Analysis using Unified Framework
Migrated from llama/scaling_llama2_7b_hf.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import <PERSON>aling<PERSON>naly<PERSON>, ScalingMetrics
from typing import Any, Tu<PERSON>, Dict, List
import random
import numpy as np

# Optional imports with fallbacks
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from huggingface_hub import login
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

class LlamaScalingAnalyzer(ScalingAnalyzer):
    """LLaMA-7B scaling analysis - migrated from llama/scaling_llama2_7b_hf.py"""
    
    def __init__(self, 
                 model_name: str = "meta-llama/Llama-2-7b-hf",
                 max_new_tokens: int = 20,
                 analysis_type: str = "basic",
                 **kwargs):
        super().__init__(algorithm_name="LLaMA2_7B", **kwargs)
        
        if not HAS_TORCH or not HAS_TRANSFORMERS:
            raise ImportError("This analyzer requires torch and transformers. Install with: pip install torch transformers")
        
        self.model_name = model_name
        self.max_new_tokens = max_new_tokens
        self.analysis_type = analysis_type  # "basic" or "extended"
        
        print(f"Loading LLaMA model: {model_name}")
        print(f"Analysis type: {analysis_type}")
        
        # Authenticate if needed (from original)
        try:
            login()  # Uncomment if you need to authenticate
        except:
            print("Note: Hugging Face authentication skipped")
        
        # Load tokenizer and model (exactly like original)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Move to GPU if available
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            self.model = self.model.cuda()
        
        self.model.eval()
        
        # Base words from original (exactly as in llama/scaling_llama2_7b_hf.py)
        self.base_words = [
            "machine", "learning", "artificial", "intelligence", "neural", "network", 
            "data", "science", "algorithm", "model", "training", "deep", "computer", 
            "vision", "natural", "language", "processing", "transformer", "attention", 
            "embedding", "token", "sequence", "generation", "inference", "optimization"
        ]
        
        print(f"LLaMA model loaded successfully on {self.device}")
        print(f"Context length: 4096 tokens (LLaMA-2-7b)")
    
    def generate_random_text(self, target_length: int) -> str:
        """Generate random text with approximately target_length tokens (from original)"""
        text = ""
        current_tokens = 0
        
        while current_tokens < target_length:
            word = random.choice(self.base_words)
            test_text = text + " " + word if text else word
            
            # Check token count
            tokens = self.tokenizer(test_text, return_tensors="pt")
            token_count = tokens['input_ids'].shape[1]
            
            if token_count <= target_length:
                text = test_text
                current_tokens = token_count
            else:
                break
        
        return text
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Prepare LLaMA input data with specified sequence length"""
        
        # Generate random text with target length (from original)
        input_text = self.generate_random_text(input_size)
        
        # Tokenize
        input_ids = self.tokenizer(input_text, return_tensors="pt")
        
        # Get actual input length
        actual_input_length = input_ids['input_ids'].shape[1]
        
        # Move to GPU if available (implied in original)
        if torch.cuda.is_available():
            input_ids = {k: v.cuda() for k, v in input_ids.items()}
        
        return {
            'input_ids': input_ids,
            'input_text': input_text,
            'actual_length': actual_input_length
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute LLaMA inference (from original implementation)"""
        
        input_ids = input_data['input_ids']
        actual_length = input_data['actual_length']
        
        # Clear GPU cache if available (from extended analysis)
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # Configure generation parameters based on analysis type
        if self.analysis_type == "extended":
            max_new_tokens = 50
        else:
            max_new_tokens = self.max_new_tokens
        
        # Generate text (exactly like original)
        with torch.no_grad():
            outputs = self.model.generate(
                **input_ids,
                max_new_tokens=max_new_tokens,
                do_sample=False  # From original: limit output for faster testing
            )
        
        # Decode generated text (only new tokens, from original)
        generated_text = self.tokenizer.decode(
            outputs[0][actual_length:], 
            skip_special_tokens=True
        )
        
        # Calculate tokens per second
        total_tokens = outputs.shape[1]
        new_tokens = total_tokens - actual_length
        
        return {
            'generated_text': generated_text,
            'input_tokens': actual_length,
            'output_tokens': new_tokens,
            'total_tokens': total_tokens
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return LLaMA theoretical complexity analysis"""
        
        # LLaMA-2-7b specific parameters
        hidden_size = 4096  # LLaMA-2-7b hidden size
        num_layers = 32
        num_attention_heads = 32
        
        # Memory estimation (from extended analysis)
        # Input embeddings + attention (KV cache) + activations
        input_embedding_mb = (input_size * hidden_size * 4) / (1024 * 1024)
        head_dim = hidden_size // num_attention_heads
        kv_cache_mb = (2 * input_size * num_attention_heads * head_dim * num_layers * 4) / (1024 * 1024)
        activations_mb = (input_size * hidden_size * num_layers * 4) / (1024 * 1024)
        
        total_theoretical_mb = input_embedding_mb + kv_cache_mb + activations_mb
        
        return (
            f"O(n²×d) [n={input_size}, d={hidden_size}]",  # Attention dominates
            f"O(n×d×L) [n={input_size}, d={hidden_size}, L={num_layers}]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate LLaMA-specific metrics (from original)"""
        
        # Tokens per second calculation (from extended analysis)
        if hasattr(self.timer, 'last_measurement') and self.timer.last_measurement > 0:
            tokens_per_second = result['output_tokens'] / (self.timer.last_measurement / 1000)
        else:
            tokens_per_second = 0
        
        # Memory estimation (from extended analysis)
        memory_mb = input_size * 4 * 4096 / (1024 * 1024)  # Rough estimate from original
        
        # GPU memory usage
        gpu_memory_mb = 0
        if torch.cuda.is_available():
            gpu_memory_mb = torch.cuda.memory_allocated() / (1024 * 1024)
        
        return {
            'input_tokens': result['input_tokens'],
            'output_tokens': result['output_tokens'],
            'total_tokens': result['total_tokens'],
            'tokens_per_second': tokens_per_second,
            'memory_mb': memory_mb,
            'gpu_memory_mb': gpu_memory_mb,
            'model_name': self.model_name,
            'device': self.device,
            'max_new_tokens': self.max_new_tokens,
            'generated_text_preview': result['generated_text'][:100] + "..." if len(result['generated_text']) > 100 else result['generated_text'],
            'analysis_type': self.analysis_type
        }

def main():
    """Run LLaMA scaling analysis with original parameters"""
    
    print("=== LLaMA-2-7B Scaling Analysis (Original Parameters) ===")
    print("Migrated from llama/scaling_llama2_7b_hf.py")
    
    # Analysis types from original file
    analysis_types = {
        '1': {'name': 'Basic', 'type': 'basic', 'desc': 'similar to Gemma'},
        '2': {'name': 'Extended', 'type': 'extended', 'desc': 'with additional metrics'}
    }
    
    print("\nChoose analysis type:")
    for key, info in analysis_types.items():
        print(f"{key}. {info['name']} scaling analysis ({info['desc']})")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice not in analysis_types:
        choice = '1'  # Default to basic
    
    selected_analysis = analysis_types[choice]
    
    try:
        # Initialize analyzer
        analyzer = LlamaScalingAnalyzer(
            analysis_type=selected_analysis['type'],
            output_dir=f"results/llama_{selected_analysis['type']}"
        )
        
        # Original input ranges from llama/scaling_llama2_7b_hf.py:
        if selected_analysis['type'] == 'basic':
            # Original: 1 to 4096 with step 50
            end_length = 4096  # max length is 4096 for LLaMA-2
            step_length = 50
            
        elif selected_analysis['type'] == 'extended':
            # Original: 1 to 4096 with step 100 (larger steps)
            end_length = 4096
            step_length = 100
        
        # Use smaller range for demo to avoid long runtime
        max_demo_length = min(1000, end_length)
        input_sizes = list(range(1, max_demo_length, step_length))
        
        print(f"\n=== Running {selected_analysis['name']} Analysis ===")
        print(f"Input sequence lengths: {len(input_sizes)} points from {min(input_sizes)} to {max(input_sizes)}")
        print(f"Context limit: 4096 tokens (LLaMA-2-7b)")
        print(f"Analysis type: {selected_analysis['desc']}")
        
        # Run scaling analysis
        results = analyzer.run_scaling_analysis(input_sizes)
        
        # Analyze scaling behavior
        scaling_analysis = analyzer.analyze_scaling_behavior()
        
        if scaling_analysis:
            print(f"\n=== Scaling Behavior Analysis ===")
            print(f"Mean scaling factor: {scaling_analysis['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling_analysis['std_scaling_factor']:.2f}")
            print(f"Expected: ~2.0 for attention mechanism (O(n²×d))")
        
        # Print sample results (from original progress output)
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):  # First 5 results
            custom_metrics = result.custom_metrics
            
            print(f"Input {result.input_size:4d}: {result.execution_time_ms:7.2f}ms, "
                  f"TPS: {custom_metrics.get('tokens_per_second', 0):6.2f}, "
                  f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                  f"Est: {custom_metrics.get('memory_mb', 0):6.2f}MB")
        
        print(f"\n✅ LLaMA-2-7B {selected_analysis['name']} analysis completed!")
        print(f"📁 Results saved to: results/llama_{selected_analysis['type']}/")
        print(f"📊 CSV: LLaMA2_7B_scaling_results.csv")
        print(f"📈 JSON: LLaMA2_7B_scaling_results.json")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("This might be due to:")
        print("- Missing dependencies (torch, transformers)")
        print("- Model access requirements (HuggingFace authentication)")
        print("- Insufficient GPU memory")
        print("\n💡 Try running with smaller input ranges or CPU-only mode")

if __name__ == "__main__":
    main() 