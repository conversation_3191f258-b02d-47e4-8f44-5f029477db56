"""
GeMM (General Matrix Multiplication) Scaling Analysis using Unified Framework

This module implements scaling analysis for matrix multiplication operations.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Tuple, Dict

class GemmScalingAnalyzer(ScalingAnalyzer):
    """GeMM scaling analysis for matrix multiplication operations"""
    
    def __init__(self, **kwargs):
        """Initialize GeMM analyzer with numpy implementation"""
        super().__init__(algorithm_name="GeMM-numpy", **kwargs)
    
    def prepare_input(self, input_size: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate random square matrices for multiplication"""
        np.random.seed(42)  # For reproducibility
        matrix_a = np.random.randn(input_size, input_size).astype(np.float64)
        matrix_b = np.random.randn(input_size, input_size).astype(np.float64)
        return matrix_a, matrix_b
    
    def run_algorithm(self, input_data: Tuple[np.ndarray, np.ndarray]) -> np.ndarray:
        """Execute matrix multiplication using NumPy"""
        matrix_a, matrix_b = input_data
        return np.dot(matrix_a, matrix_b)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return GeMM theoretical complexity"""
        n = input_size
        # Memory: A(n²) + B(n²) + C(n²) + overhead
        matrix_mb = (3 * n * n * 8) / (1024 * 1024)  # 8 bytes per float64
        overhead_mb = matrix_mb * 0.1  # ~10% overhead
        total_theoretical_mb = matrix_mb + overhead_mb
        
        return (
            f"O(N³) [N={n}, ops≈{2*n**3:,}]",
            f"O(N²) [N={n}, memory≈{3*n*n:,} elements]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate GeMM-specific metrics"""
        matrix_a, matrix_b = input_data
        
        # Verify correctness for small matrices
        verification_size = min(10, input_size)
        if verification_size < input_size:
            # Verify a small submatrix
            a_sub = matrix_a[:verification_size, :verification_size]
            b_sub = matrix_b[:verification_size, :verification_size]
            expected_sub = np.dot(a_sub, b_sub)
            actual_sub = result[:verification_size, :verification_size]
            max_error = np.max(np.abs(expected_sub - actual_sub))
        else:
            # Verify first element manually
            expected_00 = np.sum(matrix_a[0, :] * matrix_b[:, 0])
            actual_00 = result[0, 0]
            max_error = abs(expected_00 - actual_00)
        
        is_correct = max_error < 1e-10
        n = input_size
        flops = 2 * n**3 - n**2  # n³ multiplications + (n³-n²) additions
        
        # Calculate matrix norms
        matrix_a_norm = np.linalg.norm(matrix_a, 'fro')
        matrix_b_norm = np.linalg.norm(matrix_b, 'fro')
        result_norm = np.linalg.norm(result, 'fro')
        
        return {
            'correctness_verified': is_correct,
            'max_computation_error': float(max_error),
            'operations_count': int(flops),
            'algorithm_type': 'gemm_numpy',
            'matrix_size': f'{n}×{n}',
            'implementation': 'numpy',
            'matrix_a_frobenius_norm': float(matrix_a_norm),
            'matrix_b_frobenius_norm': float(matrix_b_norm),
            'result_frobenius_norm': float(result_norm),
            'theoretical_flops': int(flops),
            'matrix_elements_count': int(3 * n * n)
        }

def main():
    """Run GeMM scaling analysis"""
    print("=== GeMM (General Matrix Multiplication) Scaling Analysis ===")
    print("Testing matrix multiplication performance scaling with NumPy")
    
    analyzer = GemmScalingAnalyzer(
        output_dir="results/gemm_numpy",
        enable_gpu_tracking=False
    )
    
    # Matrix sizes for testing
    matrix_sizes = [i for i in range(50, 10050, 200)]
    print(f"Matrix sizes: {matrix_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(matrix_sizes)
        scaling = analyzer.analyze_scaling_behavior()
        
        if scaling:
            print(f"\n=== Scaling Analysis ===")
            print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~3.0 for GeMM (O(N³))")
        
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                  f"{result.execution_time_ms:8.2f}ms, "
                  f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                  f"FLOPS: {custom_metrics.get('theoretical_flops', 0):12,}, "
                  f"Correct: {custom_metrics.get('correctness_verified', False)}")
        
        if len(results) > 5:
            print(f"... and {len(results) - 5} more results")
        
        print(f"\nAnalysis completed! Results saved to: results/gemm_numpy/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 GeMM Analysis Summary")
    print(f"{'='*60}")
    print(f"📈 Time Complexity: O(N³) - cubic scaling with matrix size")
    print(f"💾 Space Complexity: O(N²) - quadratic memory usage")
    print(f"🚀 NumPy uses optimized BLAS libraries")
    print(f"⚡ Matrix multiplication is fundamental to deep learning")

if __name__ == "__main__":
    main() 
