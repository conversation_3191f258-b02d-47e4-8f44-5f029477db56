"""
Cholesky Decomposition Scaling Analysis using Unified Framework

This module implements scaling analysis for Cholesky decomposition operations.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Tuple, Dict

class CholeskyScalingAnalyzer(ScalingAnalyzer):
    """Cholesky scaling analysis for matrix decomposition operations"""
    
    def __init__(self, **kwargs):
        """Initialize Cholesky analyzer with numpy implementation"""
        super().__init__(algorithm_name="Cholesky-numpy", **kwargs)
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate a random symmetric positive-definite matrix for Cholesky decomposition"""
        np.random.seed(42)  # For reproducibility
        A = np.random.randn(input_size, input_size)
        matrix = np.dot(A, A.T) + input_size * np.eye(input_size)
        return matrix.astype(np.float64)
    
    def run_algorithm(self, input_data: np.ndarray) -> np.ndarray:
        """Execute Cholesky decomposition using NumPy"""
        return np.linalg.cholesky(input_data)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Cholesky theoretical complexity"""
        n = input_size
        # Memory: input(n²) + L(n²) + overhead
        matrix_mb = (2 * n * n * 8) / (1024 * 1024)  # 8 bytes per float64
        overhead_mb = matrix_mb * 0.1  # ~10% overhead
        total_theoretical_mb = matrix_mb + overhead_mb
        
        return (
            f"O(N³) [N={n}, ops≈{int(n**3/3):,}]",
            f"O(N²) [N={n}, memory≈{2*n*n:,} elements]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Cholesky-specific metrics"""
        L = result
        
        # Verify correctness: reconstruct input from L and compare
        reconstructed = L @ L.T
        max_error = np.max(np.abs(input_data - reconstructed))
        is_correct = max_error < 1e-8
        
        n = input_size
        flops = int((1/3) * n**3)  # Cholesky FLOPs: (1/3)n^3 (approx)
        
        # Calculate norms
        input_norm = np.linalg.norm(input_data, 'fro')
        L_norm = np.linalg.norm(L, 'fro')
        
        return {
            'correctness_verified': is_correct,
            'max_reconstruction_error': float(max_error),
            'operations_count': flops,
            'algorithm_type': 'cholesky_numpy',
            'matrix_size': f'{n}×{n}',
            'implementation': 'numpy',
            'input_frobenius_norm': float(input_norm),
            'L_frobenius_norm': float(L_norm),
            'theoretical_flops': flops,
            'matrix_elements_count': int(2 * n * n)
        }

def main():
    """Run Cholesky scaling analysis"""
    print("=== Cholesky Decomposition Scaling Analysis ===")
    print("Testing Cholesky decomposition performance scaling with NumPy")
    
    analyzer = CholeskyScalingAnalyzer(
        output_dir="results/cholesky_numpy",
        enable_gpu_tracking=False
    )
    
    # Matrix sizes for testing
    matrix_sizes = [i for i in range(50, 10050, 200)]
    print(f"Matrix sizes: {matrix_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(matrix_sizes)
        scaling = analyzer.analyze_scaling_behavior()
        
        if scaling:
            print(f"\n=== Scaling Analysis ===")
            print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~3.0 for Cholesky (O(N³))")
        
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                  f"{result.execution_time_ms:8.2f}ms, "
                  f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                  f"FLOPS: {custom_metrics.get('theoretical_flops', 0):12,}, "
                  f"Correct: {custom_metrics.get('correctness_verified', False)}")
        
        if len(results) > 5:
            print(f"... and {len(results) - 5} more results")
        
        print(f"\nAnalysis completed! Results saved to: results/cholesky_numpy/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Cholesky Analysis Summary")
    print(f"{'='*60}")
    print(f"📈 Time Complexity: O(N³) - cubic scaling with matrix size")
    print(f"💾 Space Complexity: O(N²) - quadratic memory usage")
    print(f"🚀 NumPy uses optimized LAPACK libraries")
    print(f"⚡ Cholesky decomposition is efficient for positive-definite matrices")

if __name__ == "__main__":
    main() 
