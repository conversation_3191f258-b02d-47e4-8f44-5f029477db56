"""
LU Decomposition Scaling Analysis using Unified Framework

This module implements scaling analysis for LU decomposition operations.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Tuple, Dict
from scipy.linalg import lu

class LUScalingAnalyzer(ScalingAnalyzer):
    """LU scaling analysis for matrix decomposition operations"""
    
    def __init__(self, **kwargs):
        """Initialize LU analyzer with numpy implementation"""
        super().__init__(algorithm_name="LU-numpy", **kwargs)
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate a random square matrix for LU decomposition"""
        np.random.seed(42)  # For reproducibility
        matrix = np.random.randn(input_size, input_size).astype(np.float64)
        # Add diagonal dominance to ensure numerical stability
        matrix += np.eye(input_size) * input_size
        return matrix
    
    def run_algorithm(self, input_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Execute LU decomposition using SciPy (most reliable implementation)"""
        return lu(input_data)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return LU theoretical complexity"""
        n = input_size
        # Memory: input(n²) + P(n²) + L(n²) + U(n²) + overhead
        matrix_mb = (4 * n * n * 8) / (1024 * 1024)  # 8 bytes per float64
        overhead_mb = matrix_mb * 0.1  # ~10% overhead
        total_theoretical_mb = matrix_mb + overhead_mb
        
        return (
            f"O(N³) [N={n}, ops≈{int((2/3)*n**3):,}]",
            f"O(N²) [N={n}, memory≈{4*n*n:,} elements]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate LU-specific metrics"""
        P, L, U = result
        
        # Verify correctness: P @ A = L @ U
        reconstructed = L @ U
        original_permuted = P @ input_data
        max_error = np.max(np.abs(original_permuted - reconstructed))
        is_correct = max_error < 1e-10
        
        n = input_size
        flops = int((2/3) * n**3)  # LU FLOPs: (2/3)n^3 (approx)
        
        # Calculate norms
        input_norm = np.linalg.norm(input_data, 'fro')
        L_norm = np.linalg.norm(L, 'fro')
        U_norm = np.linalg.norm(U, 'fro')
        P_norm = np.linalg.norm(P, 'fro')
        
        # Calculate condition number
        try:
            cond_num = np.linalg.cond(input_data)
        except:
            cond_num = float('inf')
        
        return {
            'correctness_verified': is_correct,
            'max_reconstruction_error': float(max_error),
            'operations_count': flops,
            'algorithm_type': 'lu_numpy',
            'matrix_size': f'{n}×{n}',
            'implementation': 'numpy',
            'input_frobenius_norm': float(input_norm),
            'L_frobenius_norm': float(L_norm),
            'U_frobenius_norm': float(U_norm),
            'P_frobenius_norm': float(P_norm),
            'condition_number': float(cond_num),
            'theoretical_flops': flops,
            'matrix_elements_count': int(4 * n * n)
        }

def main():
    """Run LU scaling analysis"""
    print("=== LU Decomposition Scaling Analysis ===")
    print("Testing LU decomposition performance scaling with NumPy/SciPy")
    
    analyzer = LUScalingAnalyzer(
        output_dir="results/lu_numpy",
        enable_gpu_tracking=False
    )
    
    # Matrix sizes for testing
    matrix_sizes = [i for i in range(50, 10050, 200)]
    print(f"Matrix sizes: {matrix_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(matrix_sizes)
        scaling = analyzer.analyze_scaling_behavior()
        
        if scaling:
            print(f"\n=== Scaling Analysis ===")
            print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~3.0 for LU (O(N³))")
        
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                  f"{result.execution_time_ms:8.2f}ms, "
                  f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                  f"FLOPS: {custom_metrics.get('theoretical_flops', 0):12,}, "
                  f"Correct: {custom_metrics.get('correctness_verified', False)}, "
                  f"Cond: {custom_metrics.get('condition_number', 0):.2e}")
        
        if len(results) > 5:
            print(f"... and {len(results) - 5} more results")
        
        print(f"\nAnalysis completed! Results saved to: results/lu_numpy/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 LU Analysis Summary")
    print(f"{'='*60}")
    print(f"📈 Time Complexity: O(N³) - cubic scaling with matrix size")
    print(f"💾 Space Complexity: O(N²) - quadratic memory usage")
    print(f"🚀 SciPy uses optimized LAPACK libraries")
    print(f"⚡ LU decomposition is fundamental for solving linear systems")

if __name__ == "__main__":
    main()
