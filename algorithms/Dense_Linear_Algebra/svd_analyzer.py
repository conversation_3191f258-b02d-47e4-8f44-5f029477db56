"""
SVD (Singular Value Decomposition) Scaling Analysis using Unified Framework

This module implements scaling analysis for singular value decomposition operations.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, <PERSON><PERSON>, Dict

class SVDScalingAnalyzer(ScalingAnalyzer):
    """SVD scaling analysis for singular value decomposition operations"""
    
    def __init__(self, **kwargs):
        """Initialize SVD analyzer with numpy implementation"""
        super().__init__(algorithm_name="SVD-numpy", **kwargs)
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate a random square matrix for SVD"""
        np.random.seed(42)  # For reproducibility
        matrix = np.random.randn(input_size, input_size).astype(np.float64)
        return matrix
    
    def run_algorithm(self, input_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Execute SVD using NumPy"""
        return np.linalg.svd(input_data)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return SVD theoretical complexity"""
        n = input_size
        # Memory: input(n²) + U(n²) + S(n) + Vh(n²) + overhead
        matrix_mb = (3 * n * n * 8 + n * 8) / (1024 * 1024)  # 8 bytes per float64
        overhead_mb = matrix_mb * 0.1  # ~10% overhead
        total_theoretical_mb = matrix_mb + overhead_mb
        
        return (
            f"O(N³) [N={n}, ops≈{4*n**3:,}]",
            f"O(N²) [N={n}, memory≈{3*n*n + n:,} elements]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate SVD-specific metrics"""
        U, S, Vh = result
        
        # Verify correctness: A = U @ diag(S) @ Vh
        reconstructed = U @ np.diag(S) @ Vh
        max_error = np.max(np.abs(input_data - reconstructed))
        is_correct = max_error < 1e-10
        
        n = input_size
        flops = int(4 * n**3)  # SVD FLOPs: approximately 4n³
        
        # Calculate norms
        input_norm = np.linalg.norm(input_data, 'fro')
        U_norm = np.linalg.norm(U, 'fro')
        S_norm = np.linalg.norm(S)
        Vh_norm = np.linalg.norm(Vh, 'fro')
        
        # Calculate condition number
        try:
            cond_num = np.linalg.cond(input_data)
        except:
            cond_num = float('inf')
        
        # Singular value statistics
        singular_values_stats = {
            'max_singular_value': float(np.max(S)),
            'min_singular_value': float(np.min(S)),
            'singular_value_ratio': float(np.max(S) / np.min(S)) if np.min(S) > 1e-15 else float('inf'),
            'rank_estimate': int(np.sum(S > 1e-10))
        }
        
        return {
            'correctness_verified': is_correct,
            'max_reconstruction_error': float(max_error),
            'operations_count': flops,
            'algorithm_type': 'svd_numpy',
            'matrix_size': f'{n}×{n}',
            'implementation': 'numpy',
            'input_frobenius_norm': float(input_norm),
            'U_frobenius_norm': float(U_norm),
            'S_norm': float(S_norm),
            'Vh_frobenius_norm': float(Vh_norm),
            'condition_number': float(cond_num),
            'theoretical_flops': flops,
            'matrix_elements_count': int(3 * n * n + n),
            **singular_values_stats
        }

def main():
    """Run SVD scaling analysis"""
    print("=== SVD (Singular Value Decomposition) Scaling Analysis ===")
    print("Testing SVD performance scaling with NumPy")
    
    analyzer = SVDScalingAnalyzer(
        output_dir="results/svd_numpy",
        enable_gpu_tracking=False
    )
    
    # Matrix sizes for testing
    matrix_sizes = [i for i in range(50, 10050, 200)]
    print(f"Matrix sizes: {matrix_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(matrix_sizes)
        scaling = analyzer.analyze_scaling_behavior()
        
        if scaling:
            print(f"\n=== Scaling Analysis ===")
            print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~3.0 for SVD (O(N³))")
        
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:5]):
            custom_metrics = result.custom_metrics
            print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                  f"{result.execution_time_ms:8.2f}ms, "
                  f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                  f"FLOPS: {custom_metrics.get('theoretical_flops', 0):12,}, "
                  f"Correct: {custom_metrics.get('correctness_verified', False)}, "
                  f"Cond: {custom_metrics.get('condition_number', 0):.2e}, "
                  f"Rank: {custom_metrics.get('rank_estimate', 0)}")
        
        if len(results) > 5:
            print(f"... and {len(results) - 5} more results")
        
        print(f"\nAnalysis completed! Results saved to: results/svd_numpy/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 SVD Analysis Summary")
    print(f"{'='*60}")
    print(f"📈 Time Complexity: O(N³) - cubic scaling with matrix size")
    print(f"💾 Space Complexity: O(N²) - quadratic memory usage")
    print(f"🚀 NumPy uses optimized LAPACK routines")
    print(f"⚡ SVD is fundamental for dimensionality reduction and data analysis")

if __name__ == "__main__":
    main()