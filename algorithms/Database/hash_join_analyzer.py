"""
Hash Join Scaling Analysis

This module implements scaling analysis for the Hash Join algorithm,
measuring both time and space complexity across different input sizes and join selectivities.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Dict, Tuple, List, Optional
import random
import copy
import math
from collections import defaultdict


class HashJoinScalingAnalyzer(ScalingAnalyzer):
    """Hash Join scaling analysis for database join operations"""
    
    def __init__(self, 
                 join_selectivity: float = 0.5,
                 left_table_ratio: float = 1.0,
                 right_table_ratio: float = 1.0,
                 data_distribution: str = 'uniform',
                 **kwargs):
        """
        Initialize Hash Join analyzer
        
        Args:
            join_selectivity: Fraction of left table rows that have matches (0.0 to 1.0)
            left_table_ratio: Size ratio for left table relative to input_size
            right_table_ratio: Size ratio for right table relative to input_size
            data_distribution: Distribution of join keys ('uniform', 'skewed', 'zipf')
        """
        algorithm_name = f"HashJoin-sel{join_selectivity:.2f}-{data_distribution}"
        super().__init__(algorithm_name=algorithm_name, **kwargs)
        
        self.join_selectivity = max(0.0, min(1.0, join_selectivity))
        self.left_table_ratio = max(0.1, left_table_ratio)
        self.right_table_ratio = max(0.1, right_table_ratio)
        self.data_distribution = data_distribution
    
    def prepare_input(self, input_size: int) -> Tuple[List[Tuple], List[Tuple]]:
        """Generate input tables for Hash Join analysis"""
        left_size = int(input_size * self.left_table_ratio)
        right_size = int(input_size * self.right_table_ratio)
        
        # Generate join keys based on distribution
        if self.data_distribution == 'uniform':
            left_table, right_table = self._generate_uniform_tables(left_size, right_size)
        elif self.data_distribution == 'skewed':
            left_table, right_table = self._generate_skewed_tables(left_size, right_size)
        elif self.data_distribution == 'zipf':
            left_table, right_table = self._generate_zipf_tables(left_size, right_size)
        else:
            raise ValueError(f"Unknown data distribution: {self.data_distribution}")
        
        return left_table, right_table
    
    def _generate_uniform_tables(self, left_size: int, right_size: int) -> Tuple[List[Tuple], List[Tuple]]:
        """Generate tables with uniform key distribution"""
        random.seed(42)  # For reproducibility
        
        # Determine key range based on selectivity
        # Higher selectivity means more overlap
        max_key = int(max(left_size, right_size) / max(0.1, self.join_selectivity))
        
        # Generate left table: (join_key, left_data)
        left_table = []
        for i in range(left_size):
            join_key = random.randint(1, max_key)
            left_data = f"L{i}"
            left_table.append((join_key, left_data))
        
        # Generate right table with controlled selectivity
        right_table = []
        left_keys = [row[0] for row in left_table]
        unique_left_keys = list(set(left_keys))
        
        # Ensure some matches based on selectivity
        matching_keys = random.sample(unique_left_keys, 
                                    int(len(unique_left_keys) * self.join_selectivity))
        
        for i in range(right_size):
            if i < len(matching_keys) and random.random() < 0.7:  # 70% chance to use matching key
                join_key = random.choice(matching_keys)
            else:
                join_key = random.randint(1, max_key)
            
            right_data = f"R{i}"
            right_table.append((join_key, right_data))
        
        return left_table, right_table
    
    def _generate_skewed_tables(self, left_size: int, right_size: int) -> Tuple[List[Tuple], List[Tuple]]:
        """Generate tables with skewed key distribution (80/20 rule)"""
        random.seed(42)
        
        # 20% of keys appear in 80% of records
        total_keys = max(10, int(max(left_size, right_size) * 0.2))
        hot_keys = list(range(1, int(total_keys * 0.2) + 1))
        cold_keys = list(range(int(total_keys * 0.2) + 1, total_keys + 1))
        
        def generate_skewed_key():
            if random.random() < 0.8:  # 80% chance for hot keys
                return random.choice(hot_keys)
            else:
                return random.choice(cold_keys)
        
        # Generate left table
        left_table = [(generate_skewed_key(), f"L{i}") for i in range(left_size)]
        
        # Generate right table with selectivity control
        right_table = []
        left_keys = [row[0] for row in left_table]
        unique_left_keys = list(set(left_keys))
        matching_keys = random.sample(unique_left_keys,
                                    int(len(unique_left_keys) * self.join_selectivity))
        
        for i in range(right_size):
            if i < len(matching_keys) and random.random() < 0.6:
                join_key = random.choice(matching_keys)
            else:
                join_key = generate_skewed_key()
            
            right_table.append((join_key, f"R{i}"))
        
        return left_table, right_table
    
    def _generate_zipf_tables(self, left_size: int, right_size: int) -> Tuple[List[Tuple], List[Tuple]]:
        """Generate tables with Zipf distribution"""
        random.seed(42)
        
        # Generate Zipf-distributed keys
        def zipf_key(max_key: int, alpha: float = 1.5):
            # Simple Zipf approximation
            r = random.random()
            return int(max_key * (r ** (-1.0 / alpha))) % max_key + 1
        
        max_key = max(left_size, right_size)
        
        # Generate left table
        left_table = [(zipf_key(max_key), f"L{i}") for i in range(left_size)]
        
        # Generate right table with selectivity
        right_table = []
        left_keys = [row[0] for row in left_table]
        unique_left_keys = list(set(left_keys))
        matching_keys = random.sample(unique_left_keys,
                                    int(len(unique_left_keys) * self.join_selectivity))
        
        for i in range(right_size):
            if i < len(matching_keys) and random.random() < 0.5:
                join_key = random.choice(matching_keys)
            else:
                join_key = zipf_key(max_key)
            
            right_table.append((join_key, f"R{i}"))
        
        return left_table, right_table
    
    def run_algorithm(self, input_data: Tuple[List[Tuple], List[Tuple]]) -> Dict[str, Any]:
        """Execute Hash Join algorithm with detailed tracking"""
        left_table, right_table = input_data
        
        # Track algorithm metrics
        hash_operations = [0]
        hash_collisions = [0]
        comparisons = [0]
        memory_accesses = [0]
        build_time = [0]
        probe_time = [0]
        
        def hash_join(left_rel: List[Tuple], right_rel: List[Tuple]) -> List[Tuple]:
            """Hash Join implementation with performance tracking"""
            import time
            
            # Build phase: create hash table from smaller relation
            build_start = time.time()
            
            # Choose smaller relation for building hash table
            if len(left_rel) <= len(right_rel):
                build_rel, probe_rel = left_rel, right_rel
                build_is_left = True
            else:
                build_rel, probe_rel = right_rel, left_rel
                build_is_left = False
            
            # Build hash table
            hash_table = defaultdict(list)
            for row in build_rel:
                join_key = row[0]
                hash_operations[0] += 1
                
                # Simple hash collision detection
                if join_key in hash_table:
                    hash_collisions[0] += 1
                
                hash_table[join_key].append(row)
                memory_accesses[0] += 1
            
            build_time[0] = (time.time() - build_start) * 1000  # Convert to ms
            
            # Probe phase: probe hash table with other relation
            probe_start = time.time()
            result = []
            
            for probe_row in probe_rel:
                join_key = probe_row[0]
                hash_operations[0] += 1
                memory_accesses[0] += 1
                
                if join_key in hash_table:
                    # Found matching key(s)
                    for build_row in hash_table[join_key]:
                        comparisons[0] += 1
                        
                        # Create joined tuple
                        if build_is_left:
                            joined_row = (join_key, build_row[1], probe_row[1])
                        else:
                            joined_row = (join_key, probe_row[1], build_row[1])
                        
                        result.append(joined_row)
                        memory_accesses[0] += 1
            
            probe_time[0] = (time.time() - probe_start) * 1000  # Convert to ms
            
            return result
        
        # Execute hash join
        join_result = hash_join(left_table, right_table)
        
        # Calculate additional metrics
        left_size = len(left_table)
        right_size = len(right_table)
        result_size = len(join_result)
        
        # Calculate actual selectivity
        left_keys = set(row[0] for row in left_table)
        right_keys = set(row[0] for row in right_table)
        matching_keys = len(left_keys.intersection(right_keys))
        actual_selectivity = matching_keys / len(left_keys) if left_keys else 0
        
        return {
            'join_result': join_result,
            'left_table_size': left_size,
            'right_table_size': right_size,
            'result_size': result_size,
            'hash_operations': hash_operations[0],
            'hash_collisions': hash_collisions[0],
            'comparisons': comparisons[0],
            'memory_accesses': memory_accesses[0],
            'build_time_ms': build_time[0],
            'probe_time_ms': probe_time[0],
            'actual_selectivity': actual_selectivity,
            'expected_selectivity': self.join_selectivity,
            'unique_left_keys': len(left_keys),
            'unique_right_keys': len(right_keys),
            'matching_keys': matching_keys
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Hash Join theoretical complexity"""
        left_size = int(input_size * self.left_table_ratio)
        right_size = int(input_size * self.right_table_ratio)
        
        # Time complexity: O(|R| + |S|) for hash join where R and S are relations
        time_complexity = f"O(|R|+|S|) [|R|={left_size}, |S|={right_size}]"
        
        # Space complexity: O(min(|R|, |S|)) for hash table + O(result_size) for output
        min_table_size = min(left_size, right_size)
        expected_result_size = int(left_size * right_size * self.join_selectivity * 0.1)  # Rough estimate
        space_complexity = f"O(min(|R|,|S|)+result) [min={min_table_size}, est_result={expected_result_size}]"
        
        # Memory estimation: hash table + result
        theoretical_memory_mb = ((min_table_size + expected_result_size) * 64) / (1024 * 1024)  # 64 bytes per row estimate
        
        return time_complexity, space_complexity, theoretical_memory_mb

    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Hash Join specific metrics"""
        join_result = result
        left_table, right_table = input_data

        # Calculate join efficiency metrics
        left_size = join_result['left_table_size']
        right_size = join_result['right_table_size']
        result_size = join_result['result_size']

        # Hash table efficiency
        hash_efficiency = join_result['hash_operations'] / (left_size + right_size) if (left_size + right_size) > 0 else 0
        collision_rate = join_result['hash_collisions'] / join_result['hash_operations'] if join_result['hash_operations'] > 0 else 0

        # Join selectivity analysis
        actual_selectivity = join_result['actual_selectivity']
        selectivity_accuracy = 1.0 - abs(actual_selectivity - self.join_selectivity)

        # Performance breakdown
        total_join_time = join_result['build_time_ms'] + join_result['probe_time_ms']
        build_time_ratio = join_result['build_time_ms'] / total_join_time if total_join_time > 0 else 0
        probe_time_ratio = join_result['probe_time_ms'] / total_join_time if total_join_time > 0 else 0

        # Memory access efficiency
        theoretical_min_accesses = left_size + right_size  # Minimum: read each row once
        access_efficiency = theoretical_min_accesses / join_result['memory_accesses'] if join_result['memory_accesses'] > 0 else 0

        # Join result analysis
        join_ratio = result_size / (left_size * right_size) if (left_size * right_size) > 0 else 0

        return {
            'join_selectivity': self.join_selectivity,
            'data_distribution': self.data_distribution,
            'left_table_ratio': self.left_table_ratio,
            'right_table_ratio': self.right_table_ratio,
            'input_size': input_size,
            'left_table_size': left_size,
            'right_table_size': right_size,
            'result_size': result_size,
            'hash_operations': join_result['hash_operations'],
            'hash_collisions': join_result['hash_collisions'],
            'collision_rate': round(collision_rate, 4),
            'hash_efficiency': round(hash_efficiency, 3),
            'comparisons': join_result['comparisons'],
            'memory_accesses': join_result['memory_accesses'],
            'access_efficiency': round(access_efficiency, 3),
            'build_time_ms': round(join_result['build_time_ms'], 3),
            'probe_time_ms': round(join_result['probe_time_ms'], 3),
            'build_time_ratio': round(build_time_ratio, 3),
            'probe_time_ratio': round(probe_time_ratio, 3),
            'actual_selectivity': round(actual_selectivity, 4),
            'expected_selectivity': self.join_selectivity,
            'selectivity_accuracy': round(selectivity_accuracy, 4),
            'unique_left_keys': join_result['unique_left_keys'],
            'unique_right_keys': join_result['unique_right_keys'],
            'matching_keys': join_result['matching_keys'],
            'join_ratio': round(join_ratio, 6),
            'algorithm_type': 'hash_based_join'
        }


def main():
    """Run Hash Join scaling analysis"""
    print("=== Hash Join Scaling Analysis ===")

    # Test different configurations
    test_configs = [
        (0.1, 'uniform', 'Low Selectivity - Uniform'),
        (0.5, 'uniform', 'Medium Selectivity - Uniform'),
        (0.9, 'uniform', 'High Selectivity - Uniform'),
        (0.5, 'skewed', 'Medium Selectivity - Skewed'),
        (0.5, 'zipf', 'Medium Selectivity - Zipf'),
        (0.3, 'uniform', 'Low-Med Selectivity - Different Table Sizes')
    ]

    for i, (selectivity, distribution, description) in enumerate(test_configs):
        print(f"\n--- Testing {description.upper()} ---")

        # Use different table size ratios for the last test
        left_ratio = 1.5 if i == len(test_configs) - 1 else 1.0
        right_ratio = 0.8 if i == len(test_configs) - 1 else 1.0

        analyzer = HashJoinScalingAnalyzer(
            join_selectivity=selectivity,
            left_table_ratio=left_ratio,
            right_table_ratio=right_ratio,
            data_distribution=distribution,
            output_dir=f"results/hash_join_{distribution}_sel{selectivity:.1f}",
            enable_gpu_tracking=False
        )

        # Table sizes for testing (representing total input size)
        input_sizes = [i for i in range(100, 1001, 100)]
        print(f"Input sizes: {input_sizes}")

        try:
            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for Hash Join (O(|R|+|S|))")

            print(f"\n=== Sample Results ===")
            for j, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:4d}: "
                      f"Time: {result.execution_time_ms:6.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Left: {custom_metrics.get('left_table_size', 0):3d}, "
                      f"Right: {custom_metrics.get('right_table_size', 0):3d}, "
                      f"Result: {custom_metrics.get('result_size', 0):4d}, "
                      f"Collisions: {custom_metrics.get('hash_collisions', 0):3d}")

        except Exception as e:
            print(f"Error in {description} Hash Join analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nHash Join scaling analysis completed!")
    print("Results saved in:")
    for selectivity, distribution, _ in test_configs:
        print(f"- results/hash_join_{distribution}_sel{selectivity:.1f}/")


if __name__ == "__main__":
    main()
