"""
TPC-H Q6 Query Scaling Analysis

This module implements scaling analysis for the TPC-H Q6 query,
measuring both time and space complexity across different data sizes.

TPC-H Q6 Query:
SELECT SUM(l_extendedprice * l_discount) AS revenue
FROM lineitem
WHERE l_shipdate >= date '1995-01-01'
  AND l_shipdate < date '1995-01-01' + interval '1' year
  AND l_discount BETWEEN 0.05 - 0.01 AND 0.05 + 0.01
  AND l_quantity < 24;
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Dict, Tuple, List, Optional
import random
import copy
import math
from datetime import datetime, timedelta
from decimal import Decimal, getcontext

# Set decimal precision for financial calculations
getcontext().prec = 10


class TPCHQuery6ScalingAnalyzer(ScalingAnalyzer):
    """TPC-H Q6 scaling analysis for database query operations"""
    
    def __init__(self, 
                 selectivity_factor: float = 0.01,
                 data_distribution: str = 'uniform',
                 use_indexes: bool = False,
                 **kwargs):
        """
        Initialize TPC-H Q6 analyzer
        
        Args:
            selectivity_factor: Expected fraction of rows that match the filter (0.0 to 1.0)
            data_distribution: Distribution of data ('uniform', 'skewed', 'clustered')
            use_indexes: Whether to simulate index usage for filtering
        """
        algorithm_name = f"TPCH-Q6-{data_distribution}-sel{selectivity_factor:.3f}"
        if use_indexes:
            algorithm_name += "-indexed"
        super().__init__(algorithm_name=algorithm_name, **kwargs)
        
        self.selectivity_factor = max(0.001, min(1.0, selectivity_factor))
        self.data_distribution = data_distribution
        self.use_indexes = use_indexes
        
        # TPC-H Q6 filter constants
        self.start_date = datetime(1995, 1, 1)
        self.end_date = datetime(1996, 1, 1)
        self.discount_min = 0.04  # 0.05 - 0.01
        self.discount_max = 0.06  # 0.05 + 0.01
        self.quantity_max = 24
    
    def prepare_input(self, input_size: int) -> List[Dict]:
        """Generate lineitem table data for TPC-H Q6 analysis"""
        if self.data_distribution == 'uniform':
            return self._generate_uniform_lineitem(input_size)
        elif self.data_distribution == 'skewed':
            return self._generate_skewed_lineitem(input_size)
        elif self.data_distribution == 'clustered':
            return self._generate_clustered_lineitem(input_size)
        else:
            raise ValueError(f"Unknown data distribution: {self.data_distribution}")
    
    def _generate_uniform_lineitem(self, size: int) -> List[Dict]:
        """Generate lineitem table with uniform distribution"""
        random.seed(42)  # For reproducibility
        lineitem = []
        
        # Date range for random generation (wider than filter range)
        date_start = datetime(1992, 1, 1)
        date_end = datetime(1998, 12, 31)
        date_range_days = (date_end - date_start).days
        
        for i in range(size):
            # Generate random date
            random_days = random.randint(0, date_range_days)
            shipdate = date_start + timedelta(days=random_days)
            
            # Generate other fields
            extendedprice = Decimal(str(round(random.uniform(1000.0, 100000.0), 2)))
            discount = Decimal(str(round(random.uniform(0.0, 0.10), 2)))
            quantity = random.randint(1, 50)
            
            # Control selectivity by biasing some records to match filters
            if random.random() < self.selectivity_factor:
                # Make this record likely to match filters
                if random.random() < 0.8:  # 80% chance to be in date range
                    filter_days = (self.end_date - self.start_date).days
                    random_filter_days = random.randint(0, filter_days - 1)
                    shipdate = self.start_date + timedelta(days=random_filter_days)
                
                if random.random() < 0.7:  # 70% chance to be in discount range
                    discount = Decimal(str(round(random.uniform(self.discount_min, self.discount_max), 2)))
                
                if random.random() < 0.6:  # 60% chance to be under quantity limit
                    quantity = random.randint(1, self.quantity_max - 1)
            
            lineitem.append({
                'l_orderkey': i + 1,
                'l_linenumber': 1,
                'l_extendedprice': extendedprice,
                'l_discount': discount,
                'l_quantity': quantity,
                'l_shipdate': shipdate
            })
        
        return lineitem
    
    def _generate_skewed_lineitem(self, size: int) -> List[Dict]:
        """Generate lineitem table with skewed distribution (80/20 rule)"""
        random.seed(42)
        lineitem = []
        
        # 20% of dates appear in 80% of records (hot dates)
        hot_date_start = datetime(1994, 6, 1)
        hot_date_end = datetime(1996, 6, 1)
        cold_date_start = datetime(1992, 1, 1)
        cold_date_end = datetime(1998, 12, 31)
        
        hot_date_range = (hot_date_end - hot_date_start).days
        cold_date_range = (cold_date_end - cold_date_start).days
        
        for i in range(size):
            # 80% chance for hot dates, 20% for cold dates
            if random.random() < 0.8:
                random_days = random.randint(0, hot_date_range)
                shipdate = hot_date_start + timedelta(days=random_days)
            else:
                random_days = random.randint(0, cold_date_range)
                shipdate = cold_date_start + timedelta(days=random_days)
            
            # Skewed price distribution
            if random.random() < 0.8:  # 80% low prices
                extendedprice = Decimal(str(round(random.uniform(1000.0, 20000.0), 2)))
            else:  # 20% high prices
                extendedprice = Decimal(str(round(random.uniform(50000.0, 100000.0), 2)))
            
            discount = Decimal(str(round(random.uniform(0.0, 0.10), 2)))
            quantity = random.randint(1, 50)
            
            # Apply selectivity bias
            if random.random() < self.selectivity_factor:
                if random.random() < 0.9:
                    filter_days = (self.end_date - self.start_date).days
                    random_filter_days = random.randint(0, filter_days - 1)
                    shipdate = self.start_date + timedelta(days=random_filter_days)
                    discount = Decimal(str(round(random.uniform(self.discount_min, self.discount_max), 2)))
                    quantity = random.randint(1, self.quantity_max - 1)
            
            lineitem.append({
                'l_orderkey': i + 1,
                'l_linenumber': 1,
                'l_extendedprice': extendedprice,
                'l_discount': discount,
                'l_quantity': quantity,
                'l_shipdate': shipdate
            })
        
        return lineitem

    def _generate_clustered_lineitem(self, size: int) -> List[Dict]:
        """Generate lineitem table with clustered distribution (data locality)"""
        random.seed(42)
        lineitem = []

        # Generate data in clusters to simulate real-world data locality
        cluster_size = max(100, size // 20)  # 20 clusters
        num_clusters = (size + cluster_size - 1) // cluster_size

        for cluster_id in range(num_clusters):
            # Each cluster has its own characteristics
            cluster_start_date = datetime(1992, 1, 1) + timedelta(days=cluster_id * 30)
            cluster_end_date = cluster_start_date + timedelta(days=90)

            cluster_base_price = 10000 + (cluster_id * 5000)
            cluster_discount_bias = 0.02 + (cluster_id * 0.01)

            records_in_cluster = min(cluster_size, size - cluster_id * cluster_size)

            for i in range(records_in_cluster):
                # Clustered date generation
                cluster_days = (cluster_end_date - cluster_start_date).days
                random_days = random.randint(0, cluster_days)
                shipdate = cluster_start_date + timedelta(days=random_days)

                # Clustered price around base price
                price_variance = cluster_base_price * 0.3
                extendedprice = Decimal(str(round(
                    random.uniform(cluster_base_price - price_variance,
                                 cluster_base_price + price_variance), 2)))

                # Clustered discount around bias
                discount = Decimal(str(round(
                    max(0.0, min(0.10, random.gauss(cluster_discount_bias, 0.02))), 2)))

                quantity = random.randint(1, 50)

                # Apply selectivity for some clusters
                if cluster_id % 5 == 0 and random.random() < self.selectivity_factor * 5:
                    filter_days = (self.end_date - self.start_date).days
                    random_filter_days = random.randint(0, filter_days - 1)
                    shipdate = self.start_date + timedelta(days=random_filter_days)
                    discount = Decimal(str(round(random.uniform(self.discount_min, self.discount_max), 2)))
                    quantity = random.randint(1, self.quantity_max - 1)

                lineitem.append({
                    'l_orderkey': cluster_id * cluster_size + i + 1,
                    'l_linenumber': 1,
                    'l_extendedprice': extendedprice,
                    'l_discount': discount,
                    'l_quantity': quantity,
                    'l_shipdate': shipdate
                })

        return lineitem

    def run_algorithm(self, input_data: List[Dict]) -> Dict[str, Any]:
        """Execute TPC-H Q6 query with detailed tracking"""
        lineitem = input_data

        # Track algorithm metrics
        rows_scanned = [0]
        rows_filtered = [0]
        comparisons = [0]
        arithmetic_operations = [0]
        memory_accesses = [0]
        index_lookups = [0]

        def tpch_q6_query(data: List[Dict]) -> Dict[str, Any]:
            """TPC-H Q6 implementation with performance tracking"""
            import time

            scan_start = time.time()
            revenue = Decimal('0.0')
            qualifying_rows = []

            # Simulate index usage if enabled
            if self.use_indexes:
                # Simulate index lookup for date range
                index_lookups[0] += 1
                # In real scenario, index would reduce scan set
                scan_factor = min(0.3, self.selectivity_factor * 10)  # Index effectiveness
                effective_scan_size = max(100, int(len(data) * scan_factor))
                scan_data = random.sample(data, min(effective_scan_size, len(data)))
            else:
                scan_data = data

            # Full table scan with filtering
            for row in scan_data:
                rows_scanned[0] += 1
                memory_accesses[0] += 1

                # Apply WHERE clause filters
                # l_shipdate >= '1995-01-01' AND l_shipdate < '1996-01-01'
                comparisons[0] += 2
                date_match = (self.start_date <= row['l_shipdate'] < self.end_date)

                if date_match:
                    # l_discount BETWEEN 0.04 AND 0.06
                    comparisons[0] += 2
                    discount_match = (self.discount_min <= row['l_discount'] <= self.discount_max)

                    if discount_match:
                        # l_quantity < 24
                        comparisons[0] += 1
                        quantity_match = (row['l_quantity'] < self.quantity_max)

                        if quantity_match:
                            rows_filtered[0] += 1

                            # Calculate l_extendedprice * l_discount
                            arithmetic_operations[0] += 1
                            line_revenue = row['l_extendedprice'] * row['l_discount']

                            # Add to SUM
                            arithmetic_operations[0] += 1
                            revenue += line_revenue

                            qualifying_rows.append({
                                'orderkey': row['l_orderkey'],
                                'extendedprice': row['l_extendedprice'],
                                'discount': row['l_discount'],
                                'quantity': row['l_quantity'],
                                'shipdate': row['l_shipdate'],
                                'revenue': line_revenue
                            })

                            memory_accesses[0] += 1

            scan_time = (time.time() - scan_start) * 1000  # Convert to ms

            return {
                'revenue': revenue,
                'qualifying_rows': qualifying_rows,
                'scan_time_ms': scan_time,
                'total_rows': len(data),
                'effective_scan_size': len(scan_data)
            }

        # Execute TPC-H Q6 query
        query_result = tpch_q6_query(lineitem)

        # Calculate additional metrics
        total_rows = len(lineitem)
        qualifying_rows_count = len(query_result['qualifying_rows'])
        actual_selectivity = qualifying_rows_count / total_rows if total_rows > 0 else 0

        # Calculate scan efficiency
        scan_efficiency = rows_filtered[0] / rows_scanned[0] if rows_scanned[0] > 0 else 0

        return {
            'query_result': query_result,
            'revenue': float(query_result['revenue']),
            'total_rows': total_rows,
            'rows_scanned': rows_scanned[0],
            'rows_filtered': rows_filtered[0],
            'qualifying_rows_count': qualifying_rows_count,
            'comparisons': comparisons[0],
            'arithmetic_operations': arithmetic_operations[0],
            'memory_accesses': memory_accesses[0],
            'index_lookups': index_lookups[0],
            'scan_time_ms': query_result['scan_time_ms'],
            'actual_selectivity': actual_selectivity,
            'expected_selectivity': self.selectivity_factor,
            'scan_efficiency': scan_efficiency,
            'effective_scan_size': query_result['effective_scan_size']
        }

    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return TPC-H Q6 theoretical complexity"""
        n = input_size

        if self.use_indexes:
            # With indexes: O(log n + k) where k is result size
            expected_result_size = max(1, int(n * self.selectivity_factor))
            time_complexity = f"O(log n + k) [n={n}, k≈{expected_result_size}]"
            # Space: O(k) for result + O(log n) for index
            space_complexity = f"O(k + log n) [k≈{expected_result_size}]"
            theoretical_memory_mb = (expected_result_size * 64 + math.log2(n) * 8) / (1024 * 1024)
        else:
            # Without indexes: O(n) full table scan
            time_complexity = f"O(n) [n={n}]"
            # Space: O(k) for result where k is qualifying rows
            expected_result_size = max(1, int(n * self.selectivity_factor))
            space_complexity = f"O(k) [k≈{expected_result_size}]"
            theoretical_memory_mb = (expected_result_size * 64) / (1024 * 1024)  # 64 bytes per row estimate

        return time_complexity, space_complexity, theoretical_memory_mb

    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate TPC-H Q6 specific metrics"""
        query_result = result
        lineitem = input_data

        # Basic query metrics
        total_rows = query_result['total_rows']
        rows_scanned = query_result['rows_scanned']
        rows_filtered = query_result['rows_filtered']
        qualifying_rows = query_result['qualifying_rows_count']

        # Performance metrics
        comparisons = query_result['comparisons']
        arithmetic_ops = query_result['arithmetic_operations']
        memory_accesses = query_result['memory_accesses']
        index_lookups = query_result['index_lookups']

        # Efficiency calculations
        scan_ratio = rows_scanned / total_rows if total_rows > 0 else 0
        filter_efficiency = rows_filtered / rows_scanned if rows_scanned > 0 else 0
        selectivity_accuracy = 1.0 - abs(query_result['actual_selectivity'] - self.selectivity_factor)

        # Query processing efficiency
        comparisons_per_row = comparisons / rows_scanned if rows_scanned > 0 else 0
        arithmetic_per_qualifying = arithmetic_ops / qualifying_rows if qualifying_rows > 0 else 0

        # Memory access patterns
        memory_efficiency = rows_scanned / memory_accesses if memory_accesses > 0 else 0

        # Index effectiveness (if used)
        index_effectiveness = 0
        if self.use_indexes and index_lookups > 0:
            theoretical_full_scan = total_rows
            actual_scan = query_result['effective_scan_size']
            index_effectiveness = 1.0 - (actual_scan / theoretical_full_scan)

        # Revenue analysis
        revenue = query_result['revenue']
        avg_revenue_per_row = revenue / qualifying_rows if qualifying_rows > 0 else 0

        # Data distribution analysis
        if qualifying_rows > 0:
            qualifying_data = query_result['query_result']['qualifying_rows']
            prices = [float(row['extendedprice']) for row in qualifying_data]
            discounts = [float(row['discount']) for row in qualifying_data]
            quantities = [row['quantity'] for row in qualifying_data]

            price_variance = np.var(prices) if len(prices) > 1 else 0
            discount_variance = np.var(discounts) if len(discounts) > 1 else 0
            quantity_variance = np.var(quantities) if len(quantities) > 1 else 0
        else:
            price_variance = discount_variance = quantity_variance = 0

        return {
            'selectivity_factor': self.selectivity_factor,
            'data_distribution': self.data_distribution,
            'use_indexes': self.use_indexes,
            'input_size': input_size,
            'total_rows': total_rows,
            'rows_scanned': rows_scanned,
            'rows_filtered': rows_filtered,
            'qualifying_rows': qualifying_rows,
            'scan_ratio': round(scan_ratio, 4),
            'filter_efficiency': round(filter_efficiency, 4),
            'actual_selectivity': round(query_result['actual_selectivity'], 6),
            'expected_selectivity': self.selectivity_factor,
            'selectivity_accuracy': round(selectivity_accuracy, 4),
            'comparisons': comparisons,
            'arithmetic_operations': arithmetic_ops,
            'memory_accesses': memory_accesses,
            'index_lookups': index_lookups,
            'comparisons_per_row': round(comparisons_per_row, 2),
            'arithmetic_per_qualifying': round(arithmetic_per_qualifying, 2),
            'memory_efficiency': round(memory_efficiency, 3),
            'index_effectiveness': round(index_effectiveness, 4),
            'scan_time_ms': round(query_result['scan_time_ms'], 3),
            'revenue': round(revenue, 2),
            'avg_revenue_per_row': round(avg_revenue_per_row, 2),
            'price_variance': round(price_variance, 2),
            'discount_variance': round(discount_variance, 6),
            'quantity_variance': round(quantity_variance, 2),
            'effective_scan_size': query_result['effective_scan_size'],
            'algorithm_type': 'analytical_query'
        }


def main():
    """Run TPC-H Q6 scaling analysis"""
    print("=== TPC-H Q6 Query Scaling Analysis ===")

    # Test different configurations
    test_configs = [
        (0.001, 'uniform', False, 'Low Selectivity - Uniform - No Index'),
        (0.01, 'uniform', False, 'Medium Selectivity - Uniform - No Index'),
        (0.05, 'uniform', False, 'High Selectivity - Uniform - No Index'),
        (0.01, 'uniform', True, 'Medium Selectivity - Uniform - With Index'),
        (0.01, 'skewed', False, 'Medium Selectivity - Skewed - No Index'),
        (0.01, 'clustered', False, 'Medium Selectivity - Clustered - No Index'),
    ]

    for selectivity, distribution, use_indexes, description in test_configs:
        print(f"\n--- Testing {description.upper()} ---")

        analyzer = TPCHQuery6ScalingAnalyzer(
            selectivity_factor=selectivity,
            data_distribution=distribution,
            use_indexes=use_indexes,
            output_dir=f"results/tpch_q6_{distribution}_sel{selectivity:.3f}_idx{use_indexes}",
            enable_gpu_tracking=False
        )

        # Data sizes for testing (representing number of lineitem rows)
        input_sizes = [i for i in range(1000, 10001, 1000)]
        print(f"Input sizes: {input_sizes}")

        try:
            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                if use_indexes:
                    print(f"Expected: ~0.0-1.0 for Indexed Query (O(log n + k))")
                else:
                    print(f"Expected: ~1.0 for Full Scan Query (O(n))")

            print(f"\n=== Sample Results ===")
            for j, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:5d}: "
                      f"Time: {result.execution_time_ms:7.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Scanned: {custom_metrics.get('rows_scanned', 0):5d}, "
                      f"Qualified: {custom_metrics.get('qualifying_rows', 0):4d}, "
                      f"Revenue: ${custom_metrics.get('revenue', 0):10.2f}")

        except Exception as e:
            print(f"Error in {description} TPC-H Q6 analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nTPC-H Q6 scaling analysis completed!")
    print("Results saved in:")
    for selectivity, distribution, use_indexes, _ in test_configs:
        print(f"- results/tpch_q6_{distribution}_sel{selectivity:.3f}_idx{use_indexes}/")


if __name__ == "__main__":
    main()
