"""
Database Algorithms Package

This package contains scaling analysis implementations for various database algorithms
including join operations, indexing, and query processing algorithms.

Available Analyzers:
- HashJoinScalingAnalyzer: Hash Join algorithm scaling analysis
- TPCHQuery6ScalingAnalyzer: TPC-H Q6 query scaling analysis

Author: Algorithm Scaling Analysis Framework
"""

from .hash_join_analyzer import HashJoinScalingAnalyzer
from .tpch_q6_analyzer import TPCHQuery6ScalingAnalyzer

__all__ = [
    'HashJoinScalingAnalyzer',
    'TPCHQuery6ScalingAnalyzer'
]

# Package metadata
__version__ = "1.0.0"
__author__ = "Algorithm Scaling Analysis Framework"
__description__ = "Database algorithms scaling analysis package"
