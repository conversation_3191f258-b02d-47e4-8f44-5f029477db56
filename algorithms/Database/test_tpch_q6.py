#!/usr/bin/env python3
"""
Test script for TPC-H Q6 Scaling Analyzer

This script demonstrates how to use the TPC-H Q6 analyzer and validates
that it works correctly with the scaling framework.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from tpch_q6_analyzer import TPCHQuery6ScalingAnalyzer


def test_basic_functionality():
    """Test basic TPC-H Q6 analyzer functionality"""
    print("=== Testing TPC-H Q6 Basic Functionality ===")
    
    # Create analyzer with small test parameters
    analyzer = TPCHQuery6ScalingAnalyzer(
        selectivity_factor=0.01,
        data_distribution='uniform',
        use_indexes=False,
        output_dir="test_results/tpch_q6_basic_test",
        enable_gpu_tracking=False
    )
    
    print(f"Algorithm name: {analyzer.algorithm_name}")
    print(f"Selectivity factor: {analyzer.selectivity_factor}")
    print(f"Data distribution: {analyzer.data_distribution}")
    print(f"Use indexes: {analyzer.use_indexes}")
    
    # Test data generation
    print("\n--- Testing Data Generation ---")
    test_size = 1000
    lineitem_data = analyzer.prepare_input(test_size)
    
    print(f"Generated {len(lineitem_data)} lineitem records")
    print(f"Sample record: {lineitem_data[0]}")
    
    # Test query execution
    print("\n--- Testing Query Execution ---")
    result = analyzer.run_algorithm(lineitem_data)
    
    print(f"Query result keys: {list(result.keys())}")
    print(f"Total rows: {result['total_rows']}")
    print(f"Rows scanned: {result['rows_scanned']}")
    print(f"Qualifying rows: {result['qualifying_rows_count']}")
    print(f"Revenue: ${result['revenue']:.2f}")
    print(f"Actual selectivity: {result['actual_selectivity']:.4f}")
    
    # Test theoretical complexity
    print("\n--- Testing Theoretical Complexity ---")
    time_complexity, space_complexity, memory_mb = analyzer.get_theoretical_complexity(test_size)
    print(f"Time complexity: {time_complexity}")
    print(f"Space complexity: {space_complexity}")
    print(f"Theoretical memory: {memory_mb:.2f} MB")
    
    # Test custom metrics
    print("\n--- Testing Custom Metrics ---")
    custom_metrics = analyzer.calculate_custom_metrics(test_size, lineitem_data, result)
    
    important_metrics = [
        'selectivity_factor', 'actual_selectivity', 'scan_ratio', 
        'filter_efficiency', 'comparisons', 'arithmetic_operations',
        'revenue', 'avg_revenue_per_row'
    ]
    
    for metric in important_metrics:
        if metric in custom_metrics:
            print(f"{metric}: {custom_metrics[metric]}")
    
    print("\n✅ Basic functionality test passed!")
    return True


def test_different_configurations():
    """Test different analyzer configurations"""
    print("\n=== Testing Different Configurations ===")
    
    configs = [
        (0.001, 'uniform', False, 'Low selectivity, uniform, no index'),
        (0.05, 'skewed', False, 'High selectivity, skewed, no index'),
        (0.01, 'clustered', True, 'Medium selectivity, clustered, with index'),
    ]
    
    test_size = 500
    
    for selectivity, distribution, use_indexes, description in configs:
        print(f"\n--- Testing: {description} ---")
        
        analyzer = TPCHQuery6ScalingAnalyzer(
            selectivity_factor=selectivity,
            data_distribution=distribution,
            use_indexes=use_indexes,
            enable_gpu_tracking=False
        )
        
        # Generate data and run query
        data = analyzer.prepare_input(test_size)
        result = analyzer.run_algorithm(data)
        
        print(f"Algorithm: {analyzer.algorithm_name}")
        print(f"Qualifying rows: {result['qualifying_rows_count']}")
        print(f"Actual selectivity: {result['actual_selectivity']:.4f}")
        print(f"Expected selectivity: {selectivity:.4f}")
        print(f"Revenue: ${result['revenue']:.2f}")
        
        if use_indexes:
            print(f"Effective scan size: {result['effective_scan_size']}")
            print(f"Index lookups: {result['index_lookups']}")
    
    print("\n✅ Configuration test passed!")
    return True


def test_scaling_analysis():
    """Test small-scale scaling analysis"""
    print("\n=== Testing Scaling Analysis ===")
    
    analyzer = TPCHQuery6ScalingAnalyzer(
        selectivity_factor=0.02,
        data_distribution='uniform',
        use_indexes=False,
        output_dir="test_results/tpch_q6_scaling_test",
        enable_gpu_tracking=False
    )
    
    # Small input sizes for quick testing
    input_sizes = [100, 200, 300, 400, 500]
    print(f"Testing with input sizes: {input_sizes}")
    
    try:
        results = analyzer.run_scaling_analysis(input_sizes)
        print(f"Generated {len(results)} scaling results")
        
        # Display results
        for result in results:
            print(f"Size {result.input_size:3d}: "
                  f"Time: {result.execution_time_ms:6.2f}ms, "
                  f"Memory: +{result.memory_increment_mb:5.2f}MB, "
                  f"Qualified: {result.custom_metrics.get('qualifying_rows', 0):2d}")
        
        # Analyze scaling behavior
        scaling = analyzer.analyze_scaling_behavior()
        if scaling:
            print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
            print("Expected: ~1.0 for O(n) full scan")
        
        print("\n✅ Scaling analysis test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Scaling analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("TPC-H Q6 Scaling Analyzer Test Suite")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_different_configurations,
        test_scaling_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! TPC-H Q6 analyzer is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
