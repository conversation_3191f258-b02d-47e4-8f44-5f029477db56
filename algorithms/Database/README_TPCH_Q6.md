# TPC-H Q6 Query Scaling Analysis

## Overview

This module implements scaling analysis for the TPC-H Q6 query, which is a standard database benchmark query that performs aggregation with selective filtering. The implementation follows the algorithm scaling analysis framework and provides detailed performance metrics for different data distributions and configurations.

## TPC-H Q6 Query

The TPC-H Q6 query is defined as:

```sql
SELECT SUM(l_extendedprice * l_discount) AS revenue
FROM lineitem
WHERE l_shipdate >= date '1995-01-01'
  AND l_shipdate < date '1995-01-01' + interval '1' year
  AND l_discount BETWEEN 0.05 - 0.01 AND 0.05 + 0.01
  AND l_quantity < 24;
```

This query calculates the total revenue from line items that meet specific criteria:
- Shipped within the year 1995
- Discount between 4% and 6%
- Quantity less than 24

## Features

### Data Generation
- **Uniform Distribution**: Random data with controlled selectivity
- **Skewed Distribution**: 80/20 distribution simulating real-world data skew
- **Clustered Distribution**: Data locality patterns common in databases

### Query Execution
- Full table scan simulation
- Index-based filtering simulation
- Detailed performance tracking (comparisons, arithmetic operations, memory accesses)
- Revenue calculation with high precision using Decimal arithmetic

### Scaling Analysis
- Time complexity analysis (expected O(n) for full scan)
- Space complexity analysis (O(k) where k is result size)
- Custom metrics specific to database query performance

## Usage

### Basic Usage

```python
from tpch_q6_analyzer import TPCHQuery6ScalingAnalyzer

# Create analyzer with default settings
analyzer = TPCHQuery6ScalingAnalyzer(
    selectivity_factor=0.01,  # 1% of rows expected to qualify
    data_distribution='uniform',
    use_indexes=False
)

# Run scaling analysis
input_sizes = [1000, 2000, 5000, 10000]
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### Advanced Configuration

```python
# Test different configurations
configs = [
    # Low selectivity, uniform distribution, no indexes
    TPCHQuery6ScalingAnalyzer(
        selectivity_factor=0.001,
        data_distribution='uniform',
        use_indexes=False
    ),
    
    # High selectivity, skewed distribution, with indexes
    TPCHQuery6ScalingAnalyzer(
        selectivity_factor=0.05,
        data_distribution='skewed',
        use_indexes=True
    ),
    
    # Medium selectivity, clustered distribution
    TPCHQuery6ScalingAnalyzer(
        selectivity_factor=0.01,
        data_distribution='clustered',
        use_indexes=False
    )
]

for analyzer in configs:
    results = analyzer.run_scaling_analysis([1000, 5000, 10000])
    print(f"Algorithm: {analyzer.algorithm_name}")
    print(f"Results: {len(results)} data points")
```

## Parameters

### Constructor Parameters

- **selectivity_factor** (float): Expected fraction of rows that match the filter (0.0 to 1.0)
- **data_distribution** (str): Distribution of data ('uniform', 'skewed', 'clustered')
- **use_indexes** (bool): Whether to simulate index usage for filtering
- **output_dir** (str): Directory to save results
- **enable_gpu_tracking** (bool): Enable GPU memory tracking (default: False)

### Data Distributions

1. **Uniform**: Random data with uniform distribution across all fields
2. **Skewed**: 80/20 distribution where 20% of date ranges contain 80% of records
3. **Clustered**: Data organized in clusters with locality patterns

## Metrics

### Standard Metrics
- Execution time (milliseconds)
- Memory usage (MB)
- Theoretical complexity analysis

### Custom Database Metrics
- **Selectivity**: Actual vs expected selectivity
- **Scan Efficiency**: Ratio of qualifying rows to scanned rows
- **Query Operations**: Comparisons, arithmetic operations, memory accesses
- **Index Effectiveness**: Reduction in scan size when using indexes
- **Revenue Analysis**: Total revenue, average per qualifying row
- **Data Distribution**: Variance in prices, discounts, and quantities

## Expected Performance

### Time Complexity
- **Without Indexes**: O(n) - Full table scan
- **With Indexes**: O(log n + k) - Index lookup + result processing

### Space Complexity
- **Result Storage**: O(k) where k is the number of qualifying rows
- **Index Storage**: O(log n) additional space when using indexes

### Scaling Factors
- **Full Scan**: ~1.0 (linear scaling with input size)
- **Indexed Query**: ~0.0-1.0 (depends on selectivity and index effectiveness)

## Files

- `tpch_q6_analyzer.py`: Main implementation
- `test_tpch_q6.py`: Test suite
- `README_TPCH_Q6.md`: This documentation

## Testing

Run the test suite to verify functionality:

```bash
python test_tpch_q6.py
```

The test suite includes:
1. Basic functionality test
2. Different configuration test
3. Scaling analysis test

## Example Output

```
=== TPC-H Q6 Query Scaling Analysis ===

--- Testing MEDIUM SELECTIVITY - UNIFORM - NO INDEX ---
Input sizes: [1000, 2000, 3000, 4000, 5000]

=== MEDIUM SELECTIVITY - UNIFORM - NO INDEX Scaling Analysis ===
Mean scaling factor: 0.98
Standard deviation: 0.15
Expected: ~1.0 for Full Scan Query (O(n))

=== Sample Results ===
Size  1000: Time:    0.55ms, Memory: +  2.20MB, Scanned:  1000, Qualified:   18, Revenue: $  43279.12
Size  2000: Time:    0.93ms, Memory: +  0.48MB, Scanned:  2000, Qualified:   21, Revenue: $  53673.76
Size  3000: Time:    1.34ms, Memory: +  0.77MB, Scanned:  3000, Qualified:   28, Revenue: $  66993.11
```

## Integration

The TPC-H Q6 analyzer integrates with the main algorithm scaling framework and can be imported through the Database package:

```python
from algorithms.Database import TPCHQuery6ScalingAnalyzer
```

Results are automatically saved in CSV and JSON formats for further analysis and visualization.
