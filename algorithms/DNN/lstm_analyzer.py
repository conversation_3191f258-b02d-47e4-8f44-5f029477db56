"""
LSTM Scaling Analysis using Unified Framework

Migrated from rnn/scaling_lstm.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import torch
import torch.nn as nn
import numpy as np
from typing import Any, Tuple, Dict

class SimpleLSTM(nn.Module):
    """LSTM model - exact copy from original rnn/scaling_lstm.py"""
    
    def __init__(self, input_size=128, hidden_size=256, num_layers=2):
        super(SimpleLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
    def forward(self, x):
        # Ensure input is on CPU (from original)
        x = x.cpu()
        
        # Initialize hidden state with zeros on CPU
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).cpu()
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).cpu()
        
        # Forward propagate LSTM only
        out, (hn, cn) = self.lstm(x, (h0, c0))
        
        return out, (hn, cn)

class LSTMScalingAnalyzer(ScalingAnalyzer):
    """LSTM scaling analysis - migrated from original scaling_lstm.py"""
    
    def __init__(self, 
                 input_size: int = 128,
                 hidden_size: int = 256,
                 num_layers: int = 2,
                 batch_size: int = 1,
                 **kwargs):
        super().__init__(algorithm_name=f"LSTM_h{hidden_size}_l{num_layers}", **kwargs)
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.batch_size = batch_size
        
        # Force CPU-only execution (from original)
        torch.set_default_device('cpu')
        
        # Set deterministic behavior for reproducibility (from original)
        torch.manual_seed(42)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        
        # Initialize model on CPU (from original)
        print("Initializing LSTM model on CPU...")
        self.model = SimpleLSTM(input_size, hidden_size, num_layers)
        self.model = self.model.cpu()  # Explicitly move to CPU
        self.model.eval()  # Set to evaluation mode
        
        print("Device confirmation:")
        print(f"- Model device: CPU (forced)")
        print(f"- PyTorch default device: {torch.get_default_device()}")
        print(f"- CUDA available but not used: {torch.cuda.is_available()}")
    
    def prepare_input(self, sequence_length: int) -> torch.Tensor:
        """Generate random input sequence - matches original implementation"""
        
        # Generate random values between -1 and 1, explicitly on CPU (from original)
        sequence = torch.randn(self.batch_size, sequence_length, self.input_size, device='cpu')
        return sequence
    
    def run_algorithm(self, input_data: torch.Tensor) -> Dict[str, Any]:
        """Execute LSTM forward pass"""
        
        # Ensure input sequence is on CPU (from original)
        input_data = input_data.cpu()
        
        # LSTM forward pass
        with torch.no_grad():
            lstm_output, (hidden_state, cell_state) = self.model(input_data)
        
        return {
            'lstm_output': lstm_output,
            'hidden_state': hidden_state,
            'cell_state': cell_state,
            'output_shape': list(lstm_output.shape),
            'sequence_length': input_data.size(1)
        }
    
    def get_theoretical_complexity(self, sequence_length: int) -> Tuple[str, str, float]:
        """Return LSTM theoretical complexity - matches original analysis"""
        
        batch_size = self.batch_size
        input_size = self.input_size
        hidden_size = self.hidden_size
        num_layers = self.num_layers
        
        # Memory usage calculation
        # Input sequence
        input_mb = (batch_size * sequence_length * input_size * 4) / (1024 * 1024)  # float32
        
        # LSTM states (hidden and cell states for each layer)
        states_mb = (2 * num_layers * batch_size * hidden_size * 4) / (1024 * 1024)
        
        # LSTM output
        output_mb = (batch_size * sequence_length * hidden_size * 4) / (1024 * 1024)
        
        # LSTM parameter memory (weights and biases)
        # Each LSTM layer has 4 gates, each gate has input->hidden and hidden->hidden weights
        params_per_layer = 4 * (input_size * hidden_size + hidden_size * hidden_size + hidden_size)
        total_params = params_per_layer + (num_layers - 1) * 4 * (hidden_size * hidden_size + hidden_size * hidden_size + hidden_size)
        params_mb = (total_params * 4) / (1024 * 1024)
        
        # Working memory for computations
        working_mb = (batch_size * sequence_length * hidden_size * 4 * 4) / (1024 * 1024)  # 4 gates
        
        total_theoretical_mb = input_mb + states_mb + output_mb + params_mb + working_mb
        
        return (
            f"O(L×T×H) [L={num_layers}, T={sequence_length}, H={hidden_size}]",  # Time complexity
            f"O(T×H + L×H + params) [T={sequence_length}, H={hidden_size}, L={num_layers}]",  # Space complexity
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, sequence_length: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate LSTM-specific metrics"""
        
        return {
            'sequence_length': sequence_length,
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'batch_size': self.batch_size,
            'output_shape': result['output_shape'],
            'total_parameters': sum(p.numel() for p in self.model.parameters()),
            'memory_complexity': sequence_length * self.hidden_size,  # Dominant term
            'computation_complexity': sequence_length * self.hidden_size * self.num_layers
        }

def main():
    """Run LSTM scaling analysis with original parameters"""
    
    print("=== LSTM Scaling Analysis (Original Parameters) ===")
    
    # Original LSTM parameters from scaling_lstm.py:
    # - Sequence lengths: 1 to 6000 with step 200
    # - Input size: 128, hidden size: 256, num_layers: 2
    # - CPU-only execution with stable measurements
    lstm_analyzer = LSTMScalingAnalyzer(
        input_size=128,
        hidden_size=256,
        num_layers=2,
        batch_size=1,
        output_dir="results/lstm",
        enable_gpu_tracking=False  # CPU-only from original
    )
    
    # Original sequence length range: 1 to 6000 step 200
    # Using smaller range for demo: 200 to 2000 step 200
    sequence_lengths = list(range(200, 2001, 200))  # 200, 400, 600, ..., 2000
    
    print(f"Sequence lengths: {len(sequence_lengths)} points from {min(sequence_lengths)} to {max(sequence_lengths)}")
    print(f"Model config: input_size={lstm_analyzer.input_size}, hidden_size={lstm_analyzer.hidden_size}, layers={lstm_analyzer.num_layers}")
    print(f"Execution: CPU-only (as per original)")
    
    # Run analysis
    lstm_results = lstm_analyzer.run_scaling_analysis(sequence_lengths)
    
    # Scaling analysis
    print("\n=== Scaling Analysis Results ===")
    
    lstm_scaling = lstm_analyzer.analyze_scaling_behavior()
    if lstm_scaling:
        print(f"LSTM scaling factor: {lstm_scaling['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {lstm_scaling['std_scaling_factor']:.2f}")
        print(f"Expected: ~1.0 for O(L×T×H) where L and H are constant, T varies linearly")
    
    # Print sample results
    print(f"\n=== Sample Results ===")
    for i, result in enumerate(lstm_results[:5]):  # First 5 results
        print(f"Seq {result.input_size:4d}: {result.execution_time_ms:6.2f}ms, "
              f"Memory: +{result.memory_increment_mb:6.2f}MB, "
              f"Efficiency: {result.efficiency_ratio:.2f}")
    
    if len(lstm_results) > 5:
        print(f"... and {len(lstm_results) - 5} more results")
    
    print(f"\nLSTM analysis completed! Results saved to: results/lstm/")
    print(f"\nKey improvements from original:")
    print(f"- Unified measurement framework with multiple runs and statistics")
    print(f"- Automatic theoretical complexity comparison")
    print(f"- Standardized output format for cross-algorithm comparison")
    print(f"- Enhanced memory tracking and efficiency analysis")

if __name__ == "__main__":
    main() 