"""
StyleGAN Scaling Analysis using Unified Framework

Migrated from gan/scaling_stylegan2.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import torch
import torch.nn as nn
import numpy as np
from typing import Any, Tuple, Dict
from PIL import Image
import torchvision.transforms as transforms

class MockStyleGAN(nn.Module):
    """
    Simplified StyleGAN-like architecture for demonstration
    From original gan/scaling_stylegan2.py
    """
    
    def __init__(self, latent_dim=512, image_size=256):
        super().__init__()
        self.latent_dim = latent_dim
        self.image_size = image_size
        
        # Simplified StyleGAN-like architecture (from original)
        self.layers = nn.Sequential(
            nn.Linear(latent_dim, 1024),
            nn.ReLU(),
            nn.Linear(1024, 2048),
            nn.<PERSON>LU(),
            nn.Linear(2048, 4096),
            nn.ReLU(),
            nn.Linear(4096, 3 * image_size * image_size),  # RGB images
            nn.Tanh()
        )
    
    def forward(self, z):
        batch_size = z.size(0)
        output = self.layers(z)
        return output.view(batch_size, 3, self.image_size, self.image_size)

class StyleGANScalingAnalyzer(ScalingAnalyzer):
    """StyleGAN scaling analysis - migrated from original scaling_stylegan2.py"""
    
    def __init__(self, 
                 latent_dim: int = 512,
                 image_size: int = 256,
                 device: str = "auto",
                 save_images: bool = False,
                 **kwargs):
        super().__init__(algorithm_name=f"StyleGAN_d{latent_dim}_s{image_size}", **kwargs)
        
        self.latent_dim = latent_dim
        self.image_size = image_size
        self.save_images = save_images
        
        # Determine device (from original)
        if device == "auto":
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Loading StyleGAN model on device: {self.device}")
        
        # Initialize mock StyleGAN model (from original alternative implementation)
        self.model = MockStyleGAN(latent_dim, image_size).to(self.device)
        self.model.eval()
        
        print(f"Model loaded successfully on {self.device}")
    
    def prepare_input(self, batch_size: int) -> torch.Tensor:
        """Generate random latent vectors - matches original implementation"""
        
        # Generate random latent vectors (from original)
        latent_vectors = torch.randn(batch_size, self.latent_dim).to(self.device)
        return latent_vectors
    
    def run_algorithm(self, input_data: torch.Tensor) -> Dict[str, Any]:
        """Execute StyleGAN generation"""
        
        batch_size = input_data.size(0)
        
        # Clear GPU cache before measurement (from original)
        if self.device.type == "cuda":
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # Generate images
        with torch.no_grad():
            generated_images = self.model(input_data)
        
        # Synchronize GPU operations (from original)
        if self.device.type == "cuda":
            torch.cuda.synchronize()
        
        # Save generated images if requested (from original)
        if self.save_images:
            self.save_generated_images(generated_images, batch_size)
        
        return {
            'generated_images': generated_images,
            'batch_size': batch_size,
            'image_shape': list(generated_images.shape),
            'device': str(self.device)
        }
    
    def get_theoretical_complexity(self, batch_size: int) -> Tuple[str, str, float]:
        """Return StyleGAN theoretical complexity - matches original analysis"""
        
        latent_dim = self.latent_dim
        image_size = self.image_size
        
        # Memory usage calculation (from original)
        # Input latent vectors
        latent_mb = (batch_size * latent_dim * 4) / (1024 * 1024)  # float32
        
        # Generated images
        images_mb = (batch_size * 3 * image_size * image_size * 4) / (1024 * 1024)
        
        # Model parameters (rough estimate for the mock model)
        param_count = sum(p.numel() for p in self.model.parameters())
        params_mb = (param_count * 4) / (1024 * 1024)
        
        # Intermediate activations (rough estimate)
        # For the simplified model: batch_size * max_intermediate_size
        max_intermediate = max(1024, 2048, 4096)  # Largest layer
        activations_mb = (batch_size * max_intermediate * 4) / (1024 * 1024)
        
        total_theoretical_mb = latent_mb + images_mb + activations_mb
        
        return (
            f"O(B×operations) [B={batch_size}]",  # Time complexity (linear in batch size)
            f"O(B×latent + B×image + activations) [B={batch_size}, L={latent_dim}, I={image_size}²]",  # Space complexity
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, batch_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate StyleGAN-specific metrics"""
        
        # Calculate images per second (from original)
        images_per_second = batch_size / (self.timer.last_measurement / 1000) if self.timer.last_measurement > 0 else 0
        
        # Calculate memory per image
        memory_per_image = self.memory_tracker.get_gpu_memory_mb() / batch_size if batch_size > 0 else 0
        
        return {
            'batch_size': batch_size,
            'latent_dim': self.latent_dim,
            'image_size': self.image_size,
            'images_per_second': images_per_second,
            'memory_per_image_mb': memory_per_image,
            'device': str(self.device),
            'image_shape': result['image_shape'],
            'total_parameters': sum(p.numel() for p in self.model.parameters()),
            'throughput_metric': images_per_second  # Main performance metric
        }
    
    def save_generated_images(self, generated_images, batch_size, output_dir="generated_images"):
        """Save generated images to disk - from original implementation"""
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Convert from tensor to PIL images and save
        images = generated_images.cpu()
        
        # Normalize to [0, 1] if needed (assuming output is in [-1, 1])
        if images.min() < 0:
            images = (images + 1) / 2
        
        # Clamp to [0, 1]
        images = torch.clamp(images, 0, 1)
        
        # Convert to PIL and save each image (save only first few for demo)
        to_pil = transforms.ToPILImage()
        
        num_to_save = min(batch_size, 3)  # Save max 3 images per batch
        for i in range(num_to_save):
            img = to_pil(images[i])
            filename = f"stylegan_batch{batch_size}_img{i}.png"
            filepath = os.path.join(output_dir, filename)
            img.save(filepath)
        
        print(f"Saved {num_to_save} sample images to {output_dir}/")

def main():
    """Run StyleGAN scaling analysis with original parameters"""
    
    print("=== StyleGAN Scaling Analysis (Original Parameters) ===")
    
    # Original StyleGAN parameters from scaling_stylegan2.py:
    # - Batch sizes: 1 to max_batch_size with step_size
    # - max_batch_size = 64, step_size = 5 (original conservative)
    # - or max_batch_size = 3200, step_size = 50 (original alternative)
    stylegan_analyzer = StyleGANScalingAnalyzer(
        latent_dim=512,
        image_size=256,
        device="auto",
        save_images=True,  # Save sample images
        output_dir="results/stylegan",
        enable_gpu_tracking=True  # GPU tracking enabled
    )
    
    # Original batch size range: 1 to max_batch_size step step_size
    # Using conservative range for demo: 1 to 50 step 5
    max_batch_size = 50
    step_size = 5
    batch_sizes = list(range(1, max_batch_size + 1, step_size))  # 1, 6, 11, ..., 46
    
    print(f"Batch sizes: {len(batch_sizes)} points from {min(batch_sizes)} to {max(batch_sizes)}")
    print(f"Model config: latent_dim={stylegan_analyzer.latent_dim}, image_size={stylegan_analyzer.image_size}")
    print(f"Device: {stylegan_analyzer.device}")
    print(f"GPU tracking: {'enabled' if stylegan_analyzer.enable_gpu_tracking else 'disabled'}")
    
    try:
        # Run analysis
        stylegan_results = stylegan_analyzer.run_scaling_analysis(batch_sizes)
        
        # Scaling analysis
        print("\n=== Scaling Analysis Results ===")
        
        stylegan_scaling = stylegan_analyzer.analyze_scaling_behavior()
        if stylegan_scaling:
            print(f"StyleGAN scaling factor: {stylegan_scaling['mean_scaling_factor']:.2f}")
            print(f"Standard deviation: {stylegan_scaling['std_scaling_factor']:.2f}")
            print(f"Expected: ~1.0 for O(B×operations) where operations are constant per image")
        
        # Print sample results with throughput focus
        print(f"\n=== Sample Performance Results ===")
        for i, result in enumerate(stylegan_results[:5]):  # First 5 results
            custom_metrics = result.custom_metrics
            throughput = custom_metrics.get('images_per_second', 0)
            gpu_mem = result.gpu_memory_mb
            
            print(f"Batch {result.input_size:2d}: {result.execution_time_ms:7.2f}ms, "
                  f"Throughput: {throughput:6.2f} img/s, "
                  f"GPU: {gpu_mem:6.2f}MB")
        
        if len(stylegan_results) > 5:
            print(f"... and {len(stylegan_results) - 5} more results")
        
        print(f"\nStyleGAN analysis completed! Results saved to: results/stylegan/")
        print(f"Sample images saved to: generated_images/")
        
        # Performance summary
        if stylegan_results:
            best_throughput = max(r.custom_metrics.get('images_per_second', 0) for r in stylegan_results)
            best_batch = next(r.input_size for r in stylegan_results if r.custom_metrics.get('images_per_second', 0) == best_throughput)
            
            print(f"\n=== Performance Summary ===")
            print(f"Best throughput: {best_throughput:.2f} images/sec at batch size {best_batch}")
            print(f"Device used: {stylegan_analyzer.device}")
        
    except RuntimeError as e:
        if "out of memory" in str(e):
            print(f"❌ Out of GPU memory. Try reducing max_batch_size or image_size.")
            print(f"Current config: batch_size up to {max_batch_size}, image_size {stylegan_analyzer.image_size}")
        else:
            print(f"❌ Runtime error: {e}")
    except Exception as e:
        print(f"❌ Error during StyleGAN analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 