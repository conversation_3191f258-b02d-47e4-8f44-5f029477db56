"""
Merge Sort Scaling Analysis

This module implements scaling analysis for the Merge Sort algorithm,
measuring both time and space complexity across different input sizes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from core.scaling_framework import <PERSON>aling<PERSON><PERSON><PERSON><PERSON>
import numpy as np
from typing import Any, Di<PERSON>, <PERSON><PERSON>, List
import random
import copy


class MergeSortScalingAnalyzer(ScalingAnalyzer):
    """Merge Sort scaling analysis for sorting operations"""
    
    def __init__(self, data_type: str = 'random', **kwargs):
        """
        Initialize Merge Sort analyzer
        
        Args:
            data_type: Type of input data ('random', 'sorted', 'reverse', 'nearly_sorted')
        """
        super().__init__(algorithm_name=f"MergeSort-{data_type}", **kwargs)
        self.data_type = data_type
    
    def prepare_input(self, input_size: int) -> List[int]:
        """Generate input data for Merge Sort analysis"""
        if self.data_type == 'random':
            return self._generate_random_data(input_size)
        elif self.data_type == 'sorted':
            return self._generate_sorted_data(input_size)
        elif self.data_type == 'reverse':
            return self._generate_reverse_sorted_data(input_size)
        elif self.data_type == 'nearly_sorted':
            return self._generate_nearly_sorted_data(input_size)
        else:
            raise ValueError(f"Unknown data type: {self.data_type}")
    
    def _generate_random_data(self, size: int) -> List[int]:
        """Generate random integer array"""
        random.seed(42)  # For reproducibility
        return [random.randint(1, size * 10) for _ in range(size)]
    
    def _generate_sorted_data(self, size: int) -> List[int]:
        """Generate already sorted array"""
        return list(range(1, size + 1))
    
    def _generate_reverse_sorted_data(self, size: int) -> List[int]:
        """Generate reverse sorted array (worst case for some algorithms)"""
        return list(range(size, 0, -1))
    
    def _generate_nearly_sorted_data(self, size: int) -> List[int]:
        """Generate nearly sorted array with some random swaps"""
        random.seed(42)
        data = list(range(1, size + 1))
        # Perform random swaps on 5% of elements
        num_swaps = max(1, size // 20)
        for _ in range(num_swaps):
            i, j = random.randint(0, size-1), random.randint(0, size-1)
            data[i], data[j] = data[j], data[i]
        return data
    
    def run_algorithm(self, input_data: List[int]) -> Dict[str, Any]:
        """Execute Merge Sort algorithm with detailed tracking"""
        # Make a copy to avoid modifying original data
        data = copy.deepcopy(input_data)
        
        # Track algorithm metrics
        comparisons = [0]  # Use list to allow modification in nested function
        array_accesses = [0]
        max_recursion_depth = [0]
        current_depth = [0]
        
        def merge_sort_recursive(arr: List[int], depth: int = 0) -> List[int]:
            """Recursive merge sort with tracking"""
            current_depth[0] = depth
            max_recursion_depth[0] = max(max_recursion_depth[0], depth)
            
            if len(arr) <= 1:
                return arr
            
            # Divide
            mid = len(arr) // 2
            left_half = arr[:mid]
            right_half = arr[mid:]
            
            array_accesses[0] += len(arr)  # Accessing elements for division
            
            # Conquer
            left_sorted = merge_sort_recursive(left_half, depth + 1)
            right_sorted = merge_sort_recursive(right_half, depth + 1)
            
            # Merge
            return merge(left_sorted, right_sorted, comparisons, array_accesses)
        
        def merge(left: List[int], right: List[int], comp_count: List[int], access_count: List[int]) -> List[int]:
            """Merge two sorted arrays with tracking"""
            result = []
            i = j = 0
            
            while i < len(left) and j < len(right):
                comp_count[0] += 1  # Comparison operation
                access_count[0] += 2  # Accessing left[i] and right[j]
                
                if left[i] <= right[j]:
                    result.append(left[i])
                    i += 1
                else:
                    result.append(right[j])
                    j += 1
            
            # Add remaining elements
            while i < len(left):
                result.append(left[i])
                access_count[0] += 1
                i += 1
            
            while j < len(right):
                result.append(right[j])
                access_count[0] += 1
                j += 1
            
            return result
        
        # Execute merge sort
        sorted_data = merge_sort_recursive(data)
        
        return {
            'sorted_data': sorted_data,
            'comparisons': comparisons[0],
            'array_accesses': array_accesses[0],
            'max_recursion_depth': max_recursion_depth[0],
            'is_sorted': self._verify_sorted(sorted_data)
        }
    
    def _verify_sorted(self, data: List[int]) -> bool:
        """Verify that the array is correctly sorted"""
        for i in range(1, len(data)):
            if data[i] < data[i-1]:
                return False
        return True
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Merge Sort theoretical complexity"""
        n = input_size
        
        # Time complexity: O(n log n) for all cases
        time_complexity = f"O(n log n) [n={n}]"
        
        # Space complexity: O(n) for the temporary arrays
        space_complexity = f"O(n) [n={n}]"
        
        # Memory estimation: original array + temporary arrays during merge
        # In worst case, we need O(n) extra space for merging
        theoretical_memory_mb = (n * 8 * 2) / (1024 * 1024)  # 8 bytes per int, factor of 2 for extra space
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Merge Sort specific metrics"""
        sort_result = result
        
        # Calculate theoretical values
        n = input_size
        theoretical_comparisons = n * np.log2(n) if n > 1 else 0
        theoretical_depth = int(np.log2(n)) + 1 if n > 1 else 1
        
        # Calculate efficiency metrics
        comparison_efficiency = theoretical_comparisons / sort_result['comparisons'] if sort_result['comparisons'] > 0 else 0
        
        # Analyze input characteristics
        original_data = input_data
        inversions = self._count_inversions(original_data)
        is_already_sorted = all(original_data[i] <= original_data[i+1] for i in range(len(original_data)-1))
        
        return {
            'data_type': self.data_type,
            'input_size': input_size,
            'actual_comparisons': sort_result['comparisons'],
            'theoretical_comparisons': int(theoretical_comparisons),
            'comparison_efficiency': round(comparison_efficiency, 3),
            'array_accesses': sort_result['array_accesses'],
            'max_recursion_depth': sort_result['max_recursion_depth'],
            'theoretical_depth': theoretical_depth,
            'correctness_verified': sort_result['is_sorted'],
            'input_inversions': inversions,
            'was_already_sorted': is_already_sorted,
            'algorithm_type': 'divide_and_conquer'
        }
    
    def _count_inversions(self, arr: List[int]) -> int:
        """Count number of inversions in the array (measure of disorder)"""
        inversions = 0
        n = len(arr)
        for i in range(n):
            for j in range(i + 1, n):
                if arr[i] > arr[j]:
                    inversions += 1
        return inversions


def main():
    """Run Merge Sort scaling analysis"""
    print("=== Merge Sort Scaling Analysis ===")
    
    # Test different data types
    data_configs = [
        ('random', 'Random Data'),
        ('sorted', 'Already Sorted'),
        ('reverse', 'Reverse Sorted'),
        ('nearly_sorted', 'Nearly Sorted')
    ]
    
    for data_type, description in data_configs:
        print(f"\n--- Testing {description.upper()} ---")
        
        analyzer = MergeSortScalingAnalyzer(
            data_type=data_type,
            output_dir=f"results/merge_sort_{data_type}",
            enable_gpu_tracking=False
        )
        
        # Array sizes for testing
        array_sizes = [i for i in range(100, 2001, 100)]
        print(f"Array sizes: {array_sizes}")
        
        try:
            results = analyzer.run_scaling_analysis(array_sizes)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0-1.3 for Merge Sort (O(n log n))")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:4d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Comparisons: {custom_metrics.get('actual_comparisons', 0):8d}, "
                      f"Depth: {custom_metrics.get('max_recursion_depth', 0):2d}, "
                      f"Correct: {custom_metrics.get('correctness_verified', False)}")
            
        except Exception as e:
            print(f"Error in {description} Merge Sort analysis: {e}")
    
    print(f"\nMerge Sort scaling analysis completed!")
    print("Results saved in:")
    for data_type, _ in data_configs:
        print(f"- results/merge_sort_{data_type}/")


if __name__ == "__main__":
    main()
