"""
Radix Sort Scaling Analysis

This module implements scaling analysis for the Radix Sort algorithm,
measuring both time and space complexity across different input sizes.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from core.scaling_framework import Scaling<PERSON><PERSON>y<PERSON>
import numpy as np
from typing import Any, Dict, Tu<PERSON>, List
import random
import copy
import math


class RadixSortScalingAnalyzer(ScalingAnalyzer):
    """Radix Sort scaling analysis for sorting operations"""
    
    def __init__(self, data_type: str = 'random', radix: int = 10, **kwargs):
        """
        Initialize Radix Sort analyzer
        
        Args:
            data_type: Type of input data ('random', 'sorted', 'reverse', 'nearly_sorted')
            radix: Base for radix sort (default: 10 for decimal)
        """
        super().__init__(algorithm_name=f"RadixSort-{data_type}-base{radix}", **kwargs)
        self.data_type = data_type
        self.radix = radix
    
    def prepare_input(self, input_size: int) -> List[int]:
        """Generate input data for Radix Sort analysis"""
        if self.data_type == 'random':
            return self._generate_random_data(input_size)
        elif self.data_type == 'sorted':
            return self._generate_sorted_data(input_size)
        elif self.data_type == 'reverse':
            return self._generate_reverse_sorted_data(input_size)
        elif self.data_type == 'nearly_sorted':
            return self._generate_nearly_sorted_data(input_size)
        else:
            raise ValueError(f"Unknown data type: {self.data_type}")
    
    def _generate_random_data(self, size: int) -> List[int]:
        """Generate random positive integer array"""
        random.seed(42)  # For reproducibility
        # Generate numbers with varying digit lengths for better radix sort testing
        max_value = size * 100  # Ensure reasonable range
        return [random.randint(1, max_value) for _ in range(size)]
    
    def _generate_sorted_data(self, size: int) -> List[int]:
        """Generate already sorted array"""
        return list(range(1, size + 1))
    
    def _generate_reverse_sorted_data(self, size: int) -> List[int]:
        """Generate reverse sorted array"""
        return list(range(size, 0, -1))
    
    def _generate_nearly_sorted_data(self, size: int) -> List[int]:
        """Generate nearly sorted array with some random swaps"""
        random.seed(42)
        data = list(range(1, size + 1))
        # Perform random swaps on 5% of elements
        num_swaps = max(1, size // 20)
        for _ in range(num_swaps):
            i, j = random.randint(0, size-1), random.randint(0, size-1)
            data[i], data[j] = data[j], data[i]
        return data
    
    def run_algorithm(self, input_data: List[int]) -> Dict[str, Any]:
        """Execute Radix Sort algorithm with detailed tracking"""
        # Make a copy to avoid modifying original data
        data = copy.deepcopy(input_data)
        
        # Track algorithm metrics
        comparisons = [0]  # Radix sort typically doesn't use comparisons
        array_accesses = [0]
        digit_extractions = [0]
        bucket_operations = [0]
        
        def counting_sort_by_digit(arr: List[int], digit_pos: int) -> List[int]:
            """Counting sort for a specific digit position"""
            n = len(arr)
            output = [0] * n
            count = [0] * self.radix
            
            # Count occurrences of each digit
            for num in arr:
                digit = (num // (self.radix ** digit_pos)) % self.radix
                count[digit] += 1
                digit_extractions[0] += 1
                array_accesses[0] += 1
            
            # Calculate cumulative count
            for i in range(1, self.radix):
                count[i] += count[i - 1]
                bucket_operations[0] += 1
            
            # Build output array
            for i in range(n - 1, -1, -1):
                digit = (arr[i] // (self.radix ** digit_pos)) % self.radix
                output[count[digit] - 1] = arr[i]
                count[digit] -= 1
                array_accesses[0] += 2  # Read from arr, write to output
                digit_extractions[0] += 1
                bucket_operations[0] += 1
            
            return output
        
        def radix_sort(arr: List[int]) -> List[int]:
            """Main radix sort algorithm"""
            if not arr:
                return arr
            
            # Find maximum number to determine number of digits
            max_num = max(arr)
            array_accesses[0] += len(arr)  # Accessing all elements to find max
            
            # Calculate number of digits in the given radix
            if max_num == 0:
                num_digits = 1
            else:
                num_digits = int(math.log(max_num, self.radix)) + 1
            
            # Sort by each digit position
            current_arr = arr[:]
            for digit_pos in range(num_digits):
                current_arr = counting_sort_by_digit(current_arr, digit_pos)
            
            return current_arr
        
        # Execute radix sort
        sorted_data = radix_sort(data)
        
        # Calculate additional metrics
        max_num = max(input_data) if input_data else 0
        num_digits = len(str(max_num)) if max_num > 0 else 1
        
        return {
            'sorted_data': sorted_data,
            'comparisons': comparisons[0],  # Should be 0 for pure radix sort
            'array_accesses': array_accesses[0],
            'digit_extractions': digit_extractions[0],
            'bucket_operations': bucket_operations[0],
            'num_digits': num_digits,
            'max_number': max_num,
            'is_sorted': self._verify_sorted(sorted_data)
        }
    
    def _verify_sorted(self, data: List[int]) -> bool:
        """Verify that the array is correctly sorted"""
        for i in range(1, len(data)):
            if data[i] < data[i-1]:
                return False
        return True
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return Radix Sort theoretical complexity"""
        n = input_size
        
        # For radix sort, complexity depends on number of digits (d) and radix (k)
        # Assuming maximum number is proportional to n, d = log_k(n)
        d = max(1, int(math.log(n * 100, self.radix))) if n > 0 else 1
        k = self.radix
        
        # Time complexity: O(d * (n + k))
        time_complexity = f"O(d*(n+k)) [d={d}, n={n}, k={k}]"
        
        # Space complexity: O(n + k) for the output array and counting array
        space_complexity = f"O(n+k) [n={n}, k={k}]"
        
        # Memory estimation: original array + output array + counting array
        theoretical_memory_mb = ((n * 2 + k) * 8) / (1024 * 1024)  # 8 bytes per int
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate Radix Sort specific metrics"""
        sort_result = result
        
        # Calculate theoretical values
        n = input_size
        max_num = sort_result['max_number']
        d = sort_result['num_digits']
        k = self.radix
        
        # Theoretical operations for radix sort
        theoretical_digit_extractions = n * d * 2  # Extract digit twice per element per pass
        theoretical_array_accesses = n * d * 3  # Multiple accesses per element per pass
        
        # Calculate efficiency metrics
        digit_efficiency = theoretical_digit_extractions / sort_result['digit_extractions'] if sort_result['digit_extractions'] > 0 else 0
        
        # Analyze input characteristics
        original_data = input_data
        unique_elements = len(set(original_data))
        is_already_sorted = all(original_data[i] <= original_data[i+1] for i in range(len(original_data)-1))
        
        # Calculate digit distribution
        digit_distribution = self._analyze_digit_distribution(original_data)
        
        return {
            'data_type': self.data_type,
            'radix_base': self.radix,
            'input_size': input_size,
            'max_number': max_num,
            'num_digits': d,
            'actual_digit_extractions': sort_result['digit_extractions'],
            'theoretical_digit_extractions': theoretical_digit_extractions,
            'digit_efficiency': round(digit_efficiency, 3),
            'array_accesses': sort_result['array_accesses'],
            'bucket_operations': sort_result['bucket_operations'],
            'comparisons_used': sort_result['comparisons'],  # Should be 0
            'correctness_verified': sort_result['is_sorted'],
            'unique_elements': unique_elements,
            'uniqueness_ratio': round(unique_elements / input_size, 3) if input_size > 0 else 0,
            'was_already_sorted': is_already_sorted,
            'digit_distribution_entropy': round(digit_distribution['entropy'], 3),
            'algorithm_type': 'non_comparative'
        }
    
    def _analyze_digit_distribution(self, arr: List[int]) -> Dict[str, float]:
        """Analyze distribution of digits in the input"""
        if not arr:
            return {'entropy': 0.0}
        
        # Count frequency of each digit in least significant position
        digit_counts = [0] * self.radix
        for num in arr:
            digit = num % self.radix
            digit_counts[digit] += 1
        
        # Calculate entropy
        total = len(arr)
        entropy = 0.0
        for count in digit_counts:
            if count > 0:
                p = count / total
                entropy -= p * math.log2(p)
        
        return {'entropy': entropy}


def main():
    """Run Radix Sort scaling analysis"""
    print("=== Radix Sort Scaling Analysis ===")
    
    # Test different configurations
    test_configs = [
        ('random', 10, 'Random Data (Base 10)'),
        ('sorted', 10, 'Already Sorted (Base 10)'),
        ('reverse', 10, 'Reverse Sorted (Base 10)'),
        ('nearly_sorted', 10, 'Nearly Sorted (Base 10)'),
        ('random', 2, 'Random Data (Base 2)'),
        ('random', 16, 'Random Data (Base 16)')
    ]
    
    for data_type, radix, description in test_configs:
        print(f"\n--- Testing {description.upper()} ---")
        
        analyzer = RadixSortScalingAnalyzer(
            data_type=data_type,
            radix=radix,
            output_dir=f"results/radix_sort_{data_type}_base{radix}",
            enable_gpu_tracking=False
        )
        
        # Array sizes for testing
        array_sizes = [i for i in range(100, 1001, 100)]
        print(f"Array sizes: {array_sizes}")
        
        try:
            results = analyzer.run_scaling_analysis(array_sizes)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for Radix Sort (O(d*(n+k)))")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:4d}: "
                      f"Time: {result.execution_time_ms:6.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Digits: {custom_metrics.get('num_digits', 0):2d}, "
                      f"Extractions: {custom_metrics.get('actual_digit_extractions', 0):6d}, "
                      f"Correct: {custom_metrics.get('correctness_verified', False)}")
            
        except Exception as e:
            print(f"Error in {description} Radix Sort analysis: {e}")
    
    print(f"\nRadix Sort scaling analysis completed!")
    print("Results saved in:")
    for data_type, radix, _ in test_configs:
        print(f"- results/radix_sort_{data_type}_base{radix}/")


if __name__ == "__main__":
    main()
