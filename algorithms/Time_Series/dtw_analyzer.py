"""
Dynamic Time Warping (DTW) Scaling Analysis

This module implements scaling analysis for the Dynamic Time Warping algorithm,
measuring both time and space complexity across different sequence lengths.
DTW is used to find optimal alignment between two time series sequences.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from core.scaling_framework import <PERSON>aling<PERSON><PERSON><PERSON><PERSON>
import numpy as np
from typing import Any, Dict, <PERSON><PERSON>, List
import random


class DTWScalingAnalyzer(ScalingAnalyzer):
    """Dynamic Time Warping scaling analysis for time series alignment"""
    
    def __init__(self, sequence_type: str = 'random', similarity: float = 0.7, **kwargs):
        """
        Initialize DTW analyzer
        
        Args:
            sequence_type: Type of sequences ('random', 'similar', 'periodic', 'trend')
            similarity: Similarity level between sequences (0.0 to 1.0)
        """
        super().__init__(algorithm_name=f"DTW-{sequence_type}", **kwargs)
        self.sequence_type = sequence_type
        self.similarity = similarity
    
    def prepare_input(self, input_size: int) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """Generate two time series sequences for DTW analysis"""
        if self.sequence_type == 'random':
            return self._generate_random_sequences(input_size)
        elif self.sequence_type == 'similar':
            return self._generate_similar_sequences(input_size)
        elif self.sequence_type == 'periodic':
            return self._generate_periodic_sequences(input_size)
        elif self.sequence_type == 'trend':
            return self._generate_trend_sequences(input_size)
        else:
            raise ValueError(f"Unknown sequence type: {self.sequence_type}")
    
    def _generate_random_sequences(self, length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate two random sequences"""
        np.random.seed(42)  # For reproducibility
        seq1 = np.random.randn(length)
        seq2 = np.random.randn(length)
        return seq1, seq2
    
    def _generate_similar_sequences(self, length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate two similar sequences with controlled similarity"""
        np.random.seed(42)
        base_seq = np.random.randn(length)
        noise_level = 1.0 - self.similarity
        
        seq1 = base_seq + noise_level * np.random.randn(length) * 0.5
        seq2 = base_seq + noise_level * np.random.randn(length) * 0.5
        return seq1, seq2
    
    def _generate_periodic_sequences(self, length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate periodic sequences with slight phase differences"""
        t = np.linspace(0, 4 * np.pi, length)
        seq1 = np.sin(t) + 0.1 * np.random.randn(length)
        seq2 = np.sin(t + 0.2) + 0.1 * np.random.randn(length)  # Phase shift
        return seq1, seq2
    
    def _generate_trend_sequences(self, length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Generate sequences with trends and noise"""
        np.random.seed(42)
        t = np.linspace(0, 1, length)
        trend1 = 2 * t + np.sin(10 * t)
        trend2 = 1.8 * t + np.sin(9 * t + 0.5)
        
        seq1 = trend1 + 0.2 * np.random.randn(length)
        seq2 = trend2 + 0.2 * np.random.randn(length)
        return seq1, seq2
    
    def run_algorithm(self, input_data: Tuple[np.ndarray, np.ndarray]) -> Dict[str, Any]:
        """Execute DTW algorithm with detailed tracking"""
        seq1, seq2 = input_data
        n, m = len(seq1), len(seq2)
        
        # Track algorithm metrics
        distance_calculations = [0]
        matrix_accesses = [0]
        
        # Initialize DTW matrix
        dtw_matrix = np.full((n + 1, m + 1), np.inf)
        dtw_matrix[0, 0] = 0
        
        # DTW computation with tracking
        for i in range(1, n + 1):
            for j in range(1, m + 1):
                # Calculate distance between points
                distance_calculations[0] += 1
                cost = abs(seq1[i-1] - seq2[j-1])
                
                # Matrix accesses for finding minimum path
                matrix_accesses[0] += 3  # Three previous cells
                
                # DTW recurrence relation
                dtw_matrix[i, j] = cost + min(
                    dtw_matrix[i-1, j],      # insertion
                    dtw_matrix[i, j-1],      # deletion
                    dtw_matrix[i-1, j-1]     # match
                )
        
        # Backtrack to find optimal path
        path = self._backtrack_path(dtw_matrix, seq1, seq2)
        
        # Calculate final DTW distance
        dtw_distance = dtw_matrix[n, m]
        
        return {
            'dtw_distance': dtw_distance,
            'dtw_matrix': dtw_matrix,
            'optimal_path': path,
            'distance_calculations': distance_calculations[0],
            'matrix_accesses': matrix_accesses[0],
            'matrix_size': (n + 1) * (m + 1),
            'path_length': len(path)
        }
    
    def _backtrack_path(self, dtw_matrix: np.ndarray, seq1: np.ndarray, seq2: np.ndarray) -> List[Tuple[int, int]]:
        """Backtrack to find the optimal alignment path"""
        n, m = len(seq1), len(seq2)
        path = []
        i, j = n, m
        
        while i > 0 and j > 0:
            path.append((i-1, j-1))
            
            # Find the direction that led to current cell
            candidates = [
                (dtw_matrix[i-1, j-1], (i-1, j-1)),  # diagonal
                (dtw_matrix[i-1, j], (i-1, j)),      # up
                (dtw_matrix[i, j-1], (i, j-1))       # left
            ]
            
            # Choose the path with minimum cost
            _, (next_i, next_j) = min(candidates, key=lambda x: x[0])
            i, j = next_i, next_j
        
        # Add remaining path
        while i > 0:
            path.append((i-1, j-1))
            i -= 1
        while j > 0:
            path.append((i-1, j-1))
            j -= 1
        
        return list(reversed(path))
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return DTW theoretical complexity"""
        n = input_size
        
        # Time complexity: O(n*m) where n and m are sequence lengths
        # For equal length sequences: O(n^2)
        time_complexity = f"O(n²) [n={n}]"
        
        # Space complexity: O(n*m) for the DTW matrix
        space_complexity = f"O(n²) [n={n}]"
        
        # Memory estimation: DTW matrix of size (n+1) x (n+1) with float64
        theoretical_memory_mb = ((n + 1) * (n + 1) * 8) / (1024 * 1024)
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate DTW specific metrics"""
        seq1, seq2 = input_data
        dtw_result = result
        
        # Calculate theoretical values
        n = input_size
        theoretical_operations = n * n  # O(n²) operations
        theoretical_matrix_size = (n + 1) * (n + 1)
        
        # Calculate efficiency metrics
        operation_efficiency = theoretical_operations / dtw_result['distance_calculations'] if dtw_result['distance_calculations'] > 0 else 0
        
        # Analyze sequence characteristics
        seq1_std = np.std(seq1)
        seq2_std = np.std(seq2)
        cross_correlation = np.corrcoef(seq1, seq2)[0, 1] if len(seq1) > 1 else 0
        
        # Normalize DTW distance by sequence length for comparison
        normalized_distance = dtw_result['dtw_distance'] / input_size
        
        return {
            'sequence_type': self.sequence_type,
            'input_size': input_size,
            'sequence_length': len(seq1),
            'dtw_distance': dtw_result['dtw_distance'],
            'normalized_distance': normalized_distance,
            'distance_calculations': dtw_result['distance_calculations'],
            'theoretical_operations': theoretical_operations,
            'operation_efficiency': round(operation_efficiency, 3),
            'matrix_accesses': dtw_result['matrix_accesses'],
            'matrix_size': dtw_result['matrix_size'],
            'theoretical_matrix_size': theoretical_matrix_size,
            'optimal_path_length': dtw_result['path_length'],
            'seq1_std': round(seq1_std, 3),
            'seq2_std': round(seq2_std, 3),
            'cross_correlation': round(cross_correlation, 3),
            'algorithm_type': 'dynamic_programming'
        }


def main():
    """Run DTW scaling analysis"""
    print("=== Dynamic Time Warping (DTW) Scaling Analysis ===")
    
    # Test different sequence types
    sequence_configs = [
        ('random', 'Random Sequences'),
        ('similar', 'Similar Sequences'),
        ('periodic', 'Periodic Sequences'),
        ('trend', 'Trend Sequences')
    ]
    
    for seq_type, description in sequence_configs:
        print(f"\n--- Testing {description.upper()} ---")
        
        analyzer = DTWScalingAnalyzer(
            sequence_type=seq_type,
            similarity=0.7,
            output_dir=f"results/dtw_{seq_type}",
            enable_gpu_tracking=False
        )
        
        # Sequence lengths for testing (start small due to O(n²) complexity)
        sequence_lengths = [i for i in range(50, 501, 50)]
        print(f"Sequence lengths: {sequence_lengths}")
        
        try:
            results = analyzer.run_scaling_analysis(sequence_lengths)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~2.0 for DTW (O(n²))")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Length {result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"DTW Distance: {custom_metrics.get('dtw_distance', 0):8.2f}, "
                      f"Path Length: {custom_metrics.get('optimal_path_length', 0):3d}, "
                      f"Correlation: {custom_metrics.get('cross_correlation', 0):5.2f}")
            
        except Exception as e:
            print(f"Error in {description} DTW analysis: {e}")
    
    print(f"\nDTW scaling analysis completed!")
    print("Results saved in:")
    for seq_type, _ in sequence_configs:
        print(f"- results/dtw_{seq_type}/")


if __name__ == "__main__":
    main()
