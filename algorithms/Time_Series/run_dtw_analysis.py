#!/usr/bin/env python3
"""
Complete DTW Scaling Analysis Runner

This script runs comprehensive scaling analysis for Dynamic Time Warping (DTW)
across different sequence types and provides detailed performance insights.
"""

import sys
from pathlib import Path
import time

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Time_Series.dtw_analyzer import DTWScalingAnalyzer


def run_comprehensive_dtw_analysis():
    """Run comprehensive DTW scaling analysis"""
    print("=" * 80)
    print("COMPREHENSIVE DYNAMIC TIME WARPING (DTW) SCALING ANALYSIS")
    print("=" * 80)
    
    # Configuration for different sequence types
    sequence_configs = [
        {
            'type': 'random',
            'description': 'Random Sequences',
            'similarity': 0.0,
            'expected_behavior': 'Highest DTW distances, worst-case performance'
        },
        {
            'type': 'similar', 
            'description': 'Similar Sequences',
            'similarity': 0.8,
            'expected_behavior': 'Lower DTW distances, better alignment'
        },
        {
            'type': 'periodic',
            'description': 'Periodic Sequences', 
            'similarity': 0.7,
            'expected_behavior': 'Regular patterns, predictable alignment'
        },
        {
            'type': 'trend',
            'description': 'Trend Sequences',
            'similarity': 0.6,
            'expected_behavior': 'Trend-based alignment, moderate distances'
        }
    ]
    
    # Sequence lengths for analysis (start small due to O(n²) complexity)
    sequence_lengths = [25, 50, 75, 100, 125, 150, 175, 200]
    
    print(f"Sequence lengths to test: {sequence_lengths}")
    print(f"Expected time complexity: O(n²)")
    print(f"Expected space complexity: O(n²)")
    print()
    
    all_results = {}
    
    for config in sequence_configs:
        seq_type = config['type']
        description = config['description']
        
        print(f"\n{'='*60}")
        print(f"ANALYZING {description.upper()}")
        print(f"{'='*60}")
        print(f"Expected behavior: {config['expected_behavior']}")
        
        # Create analyzer
        analyzer = DTWScalingAnalyzer(
            sequence_type=seq_type,
            similarity=config['similarity'],
            output_dir=f"results/dtw_{seq_type}_comprehensive",
            enable_gpu_tracking=False
        )
        
        start_time = time.time()
        
        try:
            # Run scaling analysis
            results = analyzer.run_scaling_analysis(sequence_lengths)
            
            # Analyze scaling behavior
            scaling = analyzer.analyze_scaling_behavior()
            
            analysis_time = time.time() - start_time
            
            # Store results
            all_results[seq_type] = {
                'results': results,
                'scaling': scaling,
                'analysis_time': analysis_time,
                'config': config
            }
            
            # Print summary
            print(f"\n{'-'*40}")
            print(f"SUMMARY FOR {description.upper()}")
            print(f"{'-'*40}")
            
            if scaling:
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Scaling consistency: {'High' if scaling['std_scaling_factor'] < 0.3 else 'Medium' if scaling['std_scaling_factor'] < 0.6 else 'Low'}")
            
            print(f"Analysis completed in: {analysis_time:.2f} seconds")
            
            # Show sample results
            print(f"\nSample Performance Data:")
            print(f"{'Length':>6} | {'Time(ms)':>8} | {'Memory(MB)':>10} | {'DTW Dist':>9} | {'Path Len':>8} | {'Corr':>6}")
            print(f"{'-'*6}-+-{'-'*8}-+-{'-'*10}-+-{'-'*9}-+-{'-'*8}-+-{'-'*6}")
            
            for i, result in enumerate(results[::2]):  # Show every other result
                custom = result.custom_metrics
                print(f"{result.input_size:6d} | {result.execution_time_ms:8.2f} | {result.memory_increment_mb:10.2f} | "
                      f"{custom.get('dtw_distance', 0):9.2f} | {custom.get('optimal_path_length', 0):8d} | "
                      f"{custom.get('cross_correlation', 0):6.3f}")
            
        except Exception as e:
            print(f"❌ Error analyzing {description}: {e}")
            import traceback
            traceback.print_exc()
    
    # Generate comparative analysis
    print(f"\n{'='*80}")
    print("COMPARATIVE ANALYSIS ACROSS SEQUENCE TYPES")
    print(f"{'='*80}")
    
    if len(all_results) > 1:
        print(f"\n{'Sequence Type':<15} | {'Scaling Factor':<14} | {'Consistency':<12} | {'Avg DTW Dist':<12} | {'Analysis Time':<13}")
        print(f"{'-'*15}-+-{'-'*14}-+-{'-'*12}-+-{'-'*12}-+-{'-'*13}")
        
        for seq_type, data in all_results.items():
            scaling = data['scaling']
            results = data['results']
            
            if scaling:
                scaling_factor = scaling['mean_scaling_factor']
                consistency = 'High' if scaling['std_scaling_factor'] < 0.3 else 'Medium' if scaling['std_scaling_factor'] < 0.6 else 'Low'
            else:
                scaling_factor = 0.0
                consistency = 'N/A'
            
            avg_distance = sum(r.custom_metrics.get('dtw_distance', 0) for r in results) / len(results)
            analysis_time = data['analysis_time']
            
            print(f"{seq_type:<15} | {scaling_factor:14.2f} | {consistency:<12} | {avg_distance:12.2f} | {analysis_time:13.2f}s")
    
    # Performance insights
    print(f"\n{'='*80}")
    print("PERFORMANCE INSIGHTS AND RECOMMENDATIONS")
    print(f"{'='*80}")
    
    print("\n🔍 Key Findings:")
    for seq_type, data in all_results.items():
        config = data['config']
        scaling = data['scaling']
        results = data['results']
        
        if scaling:
            factor = scaling['mean_scaling_factor']
            print(f"\n• {config['description']}:")
            print(f"  - Scaling factor: {factor:.2f} ({'Close to theoretical O(n²)' if 1.8 <= factor <= 2.2 else 'Deviates from theoretical'})")
            print(f"  - Performance: {config['expected_behavior']}")
            
            # Memory efficiency
            last_result = results[-1]
            theoretical_memory = last_result.theoretical_memory_mb
            actual_memory = last_result.memory_increment_mb
            efficiency = theoretical_memory / actual_memory if actual_memory > 0 else 0
            print(f"  - Memory efficiency: {efficiency:.2f}x theoretical")
    
    print(f"\n💡 Recommendations:")
    print(f"• For sequences > 500 elements, consider FastDTW or constrained DTW")
    print(f"• Similar sequences show better performance - preprocess for similarity when possible")
    print(f"• Memory usage grows quadratically - monitor for large sequences")
    print(f"• Consider parallel processing for multiple sequence comparisons")
    
    print(f"\n📊 Results saved in:")
    for seq_type in all_results.keys():
        print(f"• results/dtw_{seq_type}_comprehensive/")
    
    print(f"\n{'='*80}")
    print("DTW COMPREHENSIVE ANALYSIS COMPLETED")
    print(f"{'='*80}")


def run_quick_dtw_demo():
    """Run a quick DTW demonstration"""
    print("=" * 60)
    print("QUICK DTW DEMONSTRATION")
    print("=" * 60)
    
    analyzer = DTWScalingAnalyzer(sequence_type='similar', similarity=0.8)
    
    # Generate and analyze small sequences
    seq1, seq2 = analyzer.prepare_input(30)
    result = analyzer.run_algorithm((seq1, seq2))
    metrics = analyzer.calculate_custom_metrics(30, (seq1, seq2), result)
    
    print(f"Sequence length: {len(seq1)}")
    print(f"DTW distance: {result['dtw_distance']:.4f}")
    print(f"Optimal path length: {result['path_length']}")
    print(f"Cross-correlation: {metrics['cross_correlation']:.3f}")
    print(f"Operation efficiency: {metrics['operation_efficiency']:.3f}")
    print(f"Matrix operations: {result['distance_calculations']}")
    
    print("\n✅ DTW algorithm working correctly!")


def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='DTW Scaling Analysis Runner')
    parser.add_argument('--mode', choices=['quick', 'comprehensive'], default='quick',
                       help='Analysis mode: quick demo or comprehensive analysis')
    parser.add_argument('--max-length', type=int, default=200,
                       help='Maximum sequence length for comprehensive analysis')
    
    args = parser.parse_args()
    
    if args.mode == 'quick':
        run_quick_dtw_demo()
    else:
        # Update sequence lengths based on max-length parameter
        global sequence_lengths
        sequence_lengths = list(range(25, args.max_length + 1, 25))
        run_comprehensive_dtw_analysis()


if __name__ == "__main__":
    main()
