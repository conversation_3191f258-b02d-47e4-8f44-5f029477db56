"""
Dynamic Time Warping (DTW) Demo

This module demonstrates the DTW algorithm with visualization and
practical examples of time series alignment.
"""

import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Time_Series.dtw_analyzer import DTWScalingAnalyzer


def visualize_dtw_alignment(seq1, seq2, path, title="DTW Alignment"):
    """Visualize DTW alignment between two sequences"""
    try:
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
        
        # Plot original sequences
        ax1.plot(seq1, 'b-o', label='Sequence 1', markersize=4)
        ax1.plot(seq2, 'r-s', label='Sequence 2', markersize=4)
        ax1.set_title(f'{title} - Original Sequences')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot alignment connections
        ax2.plot(seq1, 'b-o', label='Sequence 1', markersize=4)
        ax2.plot(seq2, 'r-s', label='Sequence 2', markersize=4)
        
        # Draw alignment lines
        for i, j in path[::max(1, len(path)//20)]:  # Show every nth connection to avoid clutter
            ax2.plot([i, j], [seq1[i], seq2[j]], 'g--', alpha=0.6, linewidth=0.8)
        
        ax2.set_title(f'{title} - Optimal Alignment Path')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot warping path in matrix form
        path_array = np.array(path)
        ax3.plot(path_array[:, 0], path_array[:, 1], 'g-o', markersize=3)
        ax3.set_xlabel('Sequence 1 Index')
        ax3.set_ylabel('Sequence 2 Index')
        ax3.set_title(f'{title} - Warping Path')
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'dtw_demo_{title.lower().replace(" ", "_")}.png', dpi=150, bbox_inches='tight')
        plt.show()
        
    except ImportError:
        print("Matplotlib not available. Skipping visualization.")
        print(f"Path length: {len(path)}")
        print(f"Path sample: {path[:min(10, len(path))]}")


def demo_basic_dtw():
    """Demonstrate basic DTW functionality"""
    print("=== Basic DTW Demo ===")
    
    analyzer = DTWScalingAnalyzer(sequence_type='similar', similarity=0.8)
    
    # Generate test sequences
    seq1, seq2 = analyzer.prepare_input(20)
    
    print(f"Sequence 1: {seq1[:10]}... (length: {len(seq1)})")
    print(f"Sequence 2: {seq2[:10]}... (length: {len(seq2)})")
    
    # Run DTW
    result = analyzer.run_algorithm((seq1, seq2))
    
    print(f"\nDTW Results:")
    print(f"- DTW Distance: {result['dtw_distance']:.4f}")
    print(f"- Matrix Size: {result['matrix_size']}")
    print(f"- Path Length: {result['path_length']}")
    print(f"- Distance Calculations: {result['distance_calculations']}")
    
    # Calculate custom metrics
    metrics = analyzer.calculate_custom_metrics(20, (seq1, seq2), result)
    print(f"- Cross-correlation: {metrics['cross_correlation']:.3f}")
    print(f"- Operation Efficiency: {metrics['operation_efficiency']:.3f}")
    
    # Visualize if possible
    visualize_dtw_alignment(seq1, seq2, result['optimal_path'], "Basic DTW")


def demo_sequence_types():
    """Demonstrate DTW with different sequence types"""
    print("\n=== DTW with Different Sequence Types ===")
    
    sequence_types = ['random', 'similar', 'periodic', 'trend']
    length = 30
    
    for seq_type in sequence_types:
        print(f"\n--- {seq_type.upper()} Sequences ---")
        
        analyzer = DTWScalingAnalyzer(sequence_type=seq_type, similarity=0.7)
        seq1, seq2 = analyzer.prepare_input(length)
        result = analyzer.run_algorithm((seq1, seq2))
        
        print(f"DTW Distance: {result['dtw_distance']:.4f}")
        print(f"Path Length: {result['path_length']}")
        
        # Calculate correlation
        correlation = np.corrcoef(seq1, seq2)[0, 1] if length > 1 else 0
        print(f"Cross-correlation: {correlation:.3f}")
        
        # Visualize each type
        visualize_dtw_alignment(seq1, seq2, result['optimal_path'], f"{seq_type.title()} Sequences")


def demo_scaling_behavior():
    """Demonstrate DTW scaling behavior"""
    print("\n=== DTW Scaling Behavior Demo ===")
    
    analyzer = DTWScalingAnalyzer(sequence_type='random', enable_gpu_tracking=False)
    
    # Test with increasing sequence lengths
    lengths = [10, 20, 30, 40, 50]
    results = []
    
    print("Length | Time (ms) | Memory (MB) | DTW Distance | Path Length")
    print("-" * 65)
    
    for length in lengths:
        seq1, seq2 = analyzer.prepare_input(length)
        
        # Measure execution time
        import time
        start_time = time.perf_counter()
        result = analyzer.run_algorithm((seq1, seq2))
        end_time = time.perf_counter()
        
        execution_time = (end_time - start_time) * 1000  # Convert to ms
        
        # Estimate memory usage (DTW matrix)
        memory_mb = ((length + 1) ** 2 * 8) / (1024 * 1024)  # 8 bytes per float64
        
        results.append({
            'length': length,
            'time': execution_time,
            'memory': memory_mb,
            'distance': result['dtw_distance'],
            'path_length': result['path_length']
        })
        
        print(f"{length:6d} | {execution_time:8.2f} | {memory_mb:10.2f} | {result['dtw_distance']:11.4f} | {result['path_length']:10d}")
    
    # Analyze scaling
    print(f"\nScaling Analysis:")
    if len(results) > 1:
        time_ratios = [results[i]['time'] / results[i-1]['time'] for i in range(1, len(results))]
        size_ratios = [results[i]['length'] / results[i-1]['length'] for i in range(1, len(results))]
        
        scaling_factors = [np.log(tr) / np.log(sr) for tr, sr in zip(time_ratios, size_ratios) if sr > 1 and tr > 0]
        
        if scaling_factors:
            avg_scaling = np.mean(scaling_factors)
            print(f"Average scaling factor: {avg_scaling:.2f} (expected ~2.0 for O(n²))")


def demo_real_world_example():
    """Demonstrate DTW with a real-world-like example"""
    print("\n=== Real-World DTW Example ===")
    
    # Simulate two similar but shifted sine waves (like sensor readings)
    t = np.linspace(0, 4*np.pi, 50)
    
    # Original signal
    signal1 = np.sin(t) + 0.1 * np.random.randn(len(t))
    
    # Shifted and slightly modified signal
    signal2 = np.sin(t - 0.5) * 1.1 + 0.1 * np.random.randn(len(t))
    
    print("Simulating two sensor readings with time shift and scaling...")
    
    analyzer = DTWScalingAnalyzer(sequence_type='random')  # We'll provide our own data
    result = analyzer.run_algorithm((signal1, signal2))
    
    print(f"DTW Distance: {result['dtw_distance']:.4f}")
    print(f"Optimal Path Length: {result['path_length']}")
    print(f"Matrix Operations: {result['distance_calculations']}")
    
    # Calculate similarity metrics
    correlation = np.corrcoef(signal1, signal2)[0, 1]
    print(f"Raw Cross-correlation: {correlation:.3f}")
    
    # Visualize the real-world example
    visualize_dtw_alignment(signal1, signal2, result['optimal_path'], "Sensor Readings")


def main():
    """Run all DTW demonstrations"""
    print("Dynamic Time Warping (DTW) Algorithm Demonstration")
    print("=" * 60)
    
    try:
        # Basic functionality demo
        demo_basic_dtw()
        
        # Different sequence types
        demo_sequence_types()
        
        # Scaling behavior
        demo_scaling_behavior()
        
        # Real-world example
        demo_real_world_example()
        
        print("\n" + "=" * 60)
        print("DTW demonstration completed successfully!")
        print("\nKey Takeaways:")
        print("- DTW finds optimal alignment between time series")
        print("- Time complexity: O(n²) where n is sequence length")
        print("- Space complexity: O(n²) for the DTW matrix")
        print("- Useful for comparing sequences with different timing")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
