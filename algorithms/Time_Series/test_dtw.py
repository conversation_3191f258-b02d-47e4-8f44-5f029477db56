"""
Test suite for Dynamic Time Warping (DTW) implementation

This module contains unit tests to verify the correctness of the DTW algorithm
implementation and its scaling analysis functionality.
"""

import sys
from pathlib import Path
import unittest
import numpy as np

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(workspace_root))

from algorithms.Time_Series.dtw_analyzer import DTWScalingAnalyzer


class TestDTWAnalyzer(unittest.TestCase):
    """Test cases for DTW scaling analyzer"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = DTWScalingAnalyzer(sequence_type='random', enable_gpu_tracking=False)
    
    def test_simple_dtw_case(self):
        """Test DTW with simple known sequences"""
        # Simple test case: identical sequences should have DTW distance of 0
        seq1 = np.array([1, 2, 3, 4])
        seq2 = np.array([1, 2, 3, 4])
        
        result = self.analyzer.run_algorithm((seq1, seq2))
        
        # DTW distance should be 0 for identical sequences
        self.assertAlmostEqual(result['dtw_distance'], 0.0, places=6)
        self.assertEqual(result['matrix_size'], 25)  # (4+1) x (4+1)
        self.assertGreater(result['path_length'], 0)
    
    def test_dtw_with_shifted_sequence(self):
        """Test DTW with time-shifted sequences"""
        seq1 = np.array([0, 1, 2, 3, 4])
        seq2 = np.array([1, 2, 3, 4, 5])  # Shifted by +1
        
        result = self.analyzer.run_algorithm((seq1, seq2))
        
        # DTW should find optimal alignment despite shift
        self.assertGreater(result['dtw_distance'], 0)
        self.assertEqual(result['matrix_size'], 36)  # (5+1) x (5+1)
        self.assertGreater(result['distance_calculations'], 0)
    
    def test_sequence_generation(self):
        """Test different sequence generation methods"""
        length = 10
        
        # Test random sequences
        seq1, seq2 = self.analyzer._generate_random_sequences(length)
        self.assertEqual(len(seq1), length)
        self.assertEqual(len(seq2), length)
        
        # Test similar sequences
        analyzer_similar = DTWScalingAnalyzer(sequence_type='similar', similarity=0.8)
        seq1, seq2 = analyzer_similar._generate_similar_sequences(length)
        self.assertEqual(len(seq1), length)
        self.assertEqual(len(seq2), length)
        
        # Test periodic sequences
        seq1, seq2 = self.analyzer._generate_periodic_sequences(length)
        self.assertEqual(len(seq1), length)
        self.assertEqual(len(seq2), length)
        
        # Test trend sequences
        seq1, seq2 = self.analyzer._generate_trend_sequences(length)
        self.assertEqual(len(seq1), length)
        self.assertEqual(len(seq2), length)
    
    def test_prepare_input(self):
        """Test input preparation for different sequence types"""
        input_size = 20
        
        for seq_type in ['random', 'similar', 'periodic', 'trend']:
            analyzer = DTWScalingAnalyzer(sequence_type=seq_type)
            seq1, seq2 = analyzer.prepare_input(input_size)
            
            self.assertEqual(len(seq1), input_size)
            self.assertEqual(len(seq2), input_size)
            self.assertIsInstance(seq1, np.ndarray)
            self.assertIsInstance(seq2, np.ndarray)
    
    def test_theoretical_complexity(self):
        """Test theoretical complexity calculations"""
        input_size = 100
        
        time_comp, space_comp, memory_mb = self.analyzer.get_theoretical_complexity(input_size)
        
        self.assertIn("O(n²)", time_comp)
        self.assertIn("O(n²)", space_comp)
        self.assertGreater(memory_mb, 0)
        
        # Memory should scale quadratically
        memory_200 = self.analyzer.get_theoretical_complexity(200)[2]
        self.assertAlmostEqual(memory_200 / memory_mb, 4.0, delta=0.1)  # 2² = 4
    
    def test_custom_metrics_calculation(self):
        """Test custom metrics calculation"""
        input_size = 50
        seq1, seq2 = self.analyzer.prepare_input(input_size)
        result = self.analyzer.run_algorithm((seq1, seq2))
        
        metrics = self.analyzer.calculate_custom_metrics(input_size, (seq1, seq2), result)
        
        # Check required metrics are present
        required_metrics = [
            'sequence_type', 'input_size', 'dtw_distance', 'normalized_distance',
            'distance_calculations', 'theoretical_operations', 'operation_efficiency',
            'matrix_accesses', 'optimal_path_length', 'cross_correlation'
        ]
        
        for metric in required_metrics:
            self.assertIn(metric, metrics)
        
        # Check metric values are reasonable
        self.assertEqual(metrics['input_size'], input_size)
        self.assertEqual(metrics['sequence_length'], input_size)
        self.assertGreater(metrics['dtw_distance'], 0)
        self.assertGreater(metrics['distance_calculations'], 0)
        self.assertEqual(metrics['theoretical_operations'], input_size * input_size)
    
    def test_dtw_properties(self):
        """Test mathematical properties of DTW"""
        # Test symmetry: DTW(A,B) should equal DTW(B,A)
        seq1 = np.array([1, 3, 2, 4])
        seq2 = np.array([2, 1, 4, 3])
        
        result1 = self.analyzer.run_algorithm((seq1, seq2))
        result2 = self.analyzer.run_algorithm((seq2, seq1))
        
        self.assertAlmostEqual(result1['dtw_distance'], result2['dtw_distance'], places=6)
    
    def test_scaling_analysis_small(self):
        """Test scaling analysis with small input sizes"""
        # Use very small sizes for quick testing
        input_sizes = [5, 10, 15]
        
        try:
            results = self.analyzer.run_scaling_analysis(input_sizes, save_results=False)
            
            self.assertEqual(len(results), len(input_sizes))
            
            # Check that execution time generally increases with input size
            times = [r.execution_time_ms for r in results]
            self.assertGreater(times[-1], times[0])  # Last should be greater than first
            
            # Check that memory usage increases
            memories = [r.memory_increment_mb for r in results]
            self.assertGreaterEqual(memories[-1], memories[0])
            
        except Exception as e:
            self.fail(f"Scaling analysis failed: {e}")
    
    def test_path_backtracking(self):
        """Test optimal path backtracking"""
        # Simple case where we can verify the path
        seq1 = np.array([1, 2])
        seq2 = np.array([1, 2])
        
        result = self.analyzer.run_algorithm((seq1, seq2))
        path = result['optimal_path']
        
        # Path should exist and be reasonable
        self.assertGreater(len(path), 0)
        self.assertLessEqual(len(path), len(seq1) + len(seq2))
        
        # Path should start at (0,0) and end at (len(seq1)-1, len(seq2)-1)
        self.assertEqual(path[0], (0, 0))
        self.assertEqual(path[-1], (len(seq1)-1, len(seq2)-1))
    
    def test_different_sequence_types_analysis(self):
        """Test analysis with different sequence types"""
        input_size = 20
        
        for seq_type in ['random', 'similar', 'periodic', 'trend']:
            with self.subTest(sequence_type=seq_type):
                analyzer = DTWScalingAnalyzer(sequence_type=seq_type, enable_gpu_tracking=False)
                
                # Test single run
                seq1, seq2 = analyzer.prepare_input(input_size)
                result = analyzer.run_algorithm((seq1, seq2))
                
                self.assertGreater(result['dtw_distance'], 0)
                self.assertEqual(result['matrix_size'], (input_size + 1) ** 2)
                
                # Test metrics calculation
                metrics = analyzer.calculate_custom_metrics(input_size, (seq1, seq2), result)
                self.assertEqual(metrics['sequence_type'], seq_type)


def run_dtw_correctness_tests():
    """Run comprehensive correctness tests for DTW implementation"""
    print("=== DTW Correctness Tests ===")
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDTWAnalyzer)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    if result.wasSuccessful():
        print(f"\n✅ All {result.testsRun} tests passed!")
        print("DTW implementation is correct and ready for scaling analysis.")
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for test, traceback in result.failures + result.errors:
            print(f"Failed: {test}")
            print(f"Error: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_dtw_correctness_tests()
    
    if success:
        print("\n=== Running Quick DTW Demo ===")
        
        # Quick demonstration
        analyzer = DTWScalingAnalyzer(sequence_type='similar', enable_gpu_tracking=False)
        
        # Test with small sequences
        seq1, seq2 = analyzer.prepare_input(10)
        result = analyzer.run_algorithm((seq1, seq2))
        metrics = analyzer.calculate_custom_metrics(10, (seq1, seq2), result)
        
        print(f"Demo Results:")
        print(f"- Sequence length: {len(seq1)}")
        print(f"- DTW distance: {result['dtw_distance']:.4f}")
        print(f"- Path length: {result['path_length']}")
        print(f"- Cross-correlation: {metrics['cross_correlation']:.3f}")
        print(f"- Operation efficiency: {metrics['operation_efficiency']:.3f}")
    
    sys.exit(0 if success else 1)
