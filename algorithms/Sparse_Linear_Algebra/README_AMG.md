# Algebraic Multigrid (AMG) Algorithm Scaling Analysis

## Overview

This module implements scaling analysis for the **Algebraic Multigrid (AMG)** algorithm, which is a highly efficient iterative method for solving large sparse linear systems **Ax = b**. AMG is particularly effective for problems arising from discretized partial differential equations.

## Algorithm Description

The Algebraic Multigrid method is designed for:
- Large sparse linear systems with millions of unknowns
- Problems where geometric multigrid is not applicable
- Systems arising from unstructured grids or complex geometries
- Achieving optimal O(n) complexity for many problem classes

### Key Properties
- **Time Complexity**: O(k × nnz) where k is typically much smaller than n
- **Space Complexity**: O(nnz) for the entire hierarchy
- **Convergence**: Grid-independent convergence for many problem types
- **Scalability**: Near-optimal scaling for elliptic problems

## Implementation Details

### AMG Components

1. **Setup Phase**:
   - **Coarsening**: Select coarse grid variables using strength-based criteria
   - **Interpolation**: Build prolongation operators P for inter-grid transfer
   - **Galerkin Coarsening**: Construct coarse matrices A_c = P^T A P

2. **Solve Phase**:
   - **V-Cycle**: Recursive multigrid iteration
   - **Smoothing**: Gauss-Seidel iterations on each level
   - **Coarse Grid Correction**: Direct solve on coarsest level

### Matrix Generation
- Creates sparse symmetric positive-definite matrices using CSR format
- Ensures positive definiteness by adding diagonal dominance
- Configurable sparsity ratio (default: 1-5%)

### Multigrid Hierarchy
- Automatic coarsening based on matrix structure
- Configurable maximum levels and coarsening factors
- Geometric reduction in problem size per level

### Metrics Collected
- **Correctness**: Solution error compared to true solution
- **Convergence**: Number of iterations and residual reduction
- **Hierarchy**: Number of levels, coarsening ratios, level sizes
- **Performance**: Setup time, solve time, memory usage
- **Efficiency**: Work estimates and complexity analysis

## Usage

### Basic Usage

```python
from amg_analyzer import BasicAMGScalingAnalyzer

# Create analyzer with default settings
analyzer = BasicAMGScalingAnalyzer(
    sparsity_ratio=0.01,     # 1% non-zero elements
    max_levels=4,            # Maximum multigrid levels
    tolerance=1e-6,          # Convergence tolerance
    coarsening_factor=0.25,  # Keep 25% of variables when coarsening
    max_iterations=10        # Maximum AMG iterations
)

# Run scaling analysis
input_sizes = [100, 200, 500, 1000]
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### Advanced Configuration

```python
# Test different multigrid configurations
analyzer = BasicAMGScalingAnalyzer(
    sparsity_ratio=0.02,     # 2% sparsity
    max_levels=5,            # More levels for larger problems
    tolerance=1e-8,          # Stricter tolerance
    coarsening_factor=0.2,   # More aggressive coarsening
    max_iterations=15,       # More iterations if needed
    output_dir="results/amg_custom"
)
```

## Scaling Analysis Results

The implementation tests three different configurations:

1. **Configuration 1**: 1% sparsity, 3 levels, 0.25 coarsening factor
2. **Configuration 2**: 2% sparsity, 4 levels, 0.30 coarsening factor  
3. **Configuration 3**: 5% sparsity, 4 levels, 0.20 coarsening factor

### Expected Scaling Behavior
- **Time**: O(nnz) for optimal AMG (grid-independent convergence)
- **Memory**: O(nnz) for the entire multigrid hierarchy
- **Iterations**: Should be independent of problem size for good AMG

### Observed Results
- **Mean scaling factors**: 0.91-1.27 (near-optimal)
- **Convergence**: 2-3 iterations for most problems
- **Hierarchy**: 2-4 levels depending on problem size
- **Coarsening**: Effective reduction ratios of 0.2-0.3

## Files Generated

- `AMG-basic_scaling_results.csv`: Detailed metrics including hierarchy info
- `AMG-basic_scaling_results.json`: JSON format results

## Key Insights

1. **Optimal Complexity**: AMG achieves near-linear scaling with problem size
2. **Grid Independence**: Iteration count remains low regardless of matrix size
3. **Hierarchy Efficiency**: Effective coarsening reduces work significantly
4. **Memory Efficiency**: Total hierarchy memory is only ~1.5x original matrix
5. **Robustness**: Converges reliably for well-conditioned SPD systems

## Algorithm Components

### Coarsening Strategy
- **Strength-based**: Variables with strong connections are coarsened together
- **Diagonal-based**: Simple heuristic using diagonal dominance
- **Configurable**: Coarsening factor controls aggressiveness

### Smoothing
- **Gauss-Seidel**: Point-wise relaxation for error smoothing
- **Pre/Post**: Configurable smoothing before and after coarse grid correction
- **Adaptive**: Number of smoothing steps can be adjusted

### Interpolation
- **Simple Injection**: Basic prolongation for proof-of-concept
- **Extensible**: Framework allows for more sophisticated interpolation

## Dependencies

- NumPy: Dense vector operations and linear algebra
- SciPy: Sparse matrix operations and direct solvers
- Core scaling framework: Measurement infrastructure

## Running the Analysis

```bash
cd algorithms/Sparse_Linear_Algebra
python amg_analyzer.py
```

This will run the complete scaling analysis with multiple AMG configurations and generate detailed results in the `results/` directory.

## Limitations

This is a basic AMG implementation for scaling analysis purposes. Production AMG solvers like PyAMG or HYPRE offer:
- More sophisticated coarsening algorithms
- Advanced interpolation operators
- Parallel implementations
- Specialized smoothers
- Better robustness for difficult problems
