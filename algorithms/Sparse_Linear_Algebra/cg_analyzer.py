"""
Conjugate Gradient (CG) scaling analysis
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import scipy.sparse as sp
from typing import Tu<PERSON>, Dict, Any
import time
import gc

from core.scaling_framework import ScalingAnalyzer


class ConjugateGradientScalingAnalyzer(ScalingAnalyzer):
    """Conjugate Gradient scaling analysis for solving sparse linear systems Ax=b"""
    
    def __init__(self, sparsity_ratio: float = 0.01, max_iterations: int = None, tolerance: float = 1e-6, **kwargs):
        """
        Initialize CG analyzer with scipy sparse implementation
        
        Args:
            sparsity_ratio: Ratio of non-zero elements (default: 0.01 = 1%)
            max_iterations: Maximum CG iterations (default: matrix_size)
            tolerance: Convergence tolerance (default: 1e-6)
        """
        super().__init__(algorithm_name="ConjugateGradient-scipy", **kwargs)
        self.sparsity_ratio = sparsity_ratio
        self.max_iterations = max_iterations
        self.tolerance = tolerance
    
    def prepare_input(self, input_size: int) -> Tuple[sp.csr_matrix, np.ndarray, np.ndarray]:
        """Generate a random sparse symmetric positive-definite matrix and RHS vector for CG"""
        np.random.seed(42)  # For reproducibility
        
        # Generate sparse matrix in CSR format
        density = self.sparsity_ratio
        # Create a random sparse matrix
        A_base = sp.random(input_size, input_size, density=density, 
                          format='csr', dtype=np.float64)
        
        # Make it symmetric: A = A + A^T
        A_symmetric = A_base + A_base.T
        
        # Make it positive definite by adding diagonal dominance
        # Add a diagonal matrix with values larger than the sum of off-diagonal elements
        diagonal_values = np.array(A_symmetric.sum(axis=1)).flatten() + input_size
        A_spd = A_symmetric + sp.diags(diagonal_values, format='csr')
        
        # Generate true solution
        x_true = np.random.randn(input_size).astype(np.float64)
        
        # Generate RHS: b = A * x_true
        b = A_spd.dot(x_true)
        
        return A_spd, b, x_true
    
    def conjugate_gradient_solve(self, A: sp.csr_matrix, b: np.ndarray, 
                               x0: np.ndarray = None, max_iter: int = None, 
                               tol: float = 1e-6) -> Tuple[np.ndarray, int, float, list]:
        """
        Conjugate Gradient solver implementation
        
        Returns:
            x: Solution vector
            iterations: Number of iterations performed
            residual_norm: Final residual norm
            residual_history: History of residual norms
        """
        n = len(b)
        
        # Initialize
        if x0 is None:
            x = np.zeros(n, dtype=np.float64)
        else:
            x = x0.copy()
            
        if max_iter is None:
            max_iter = n
            
        # Initial residual: r = b - Ax
        r = b - A.dot(x)
        p = r.copy()
        
        residual_history = []
        rsold = np.dot(r, r)
        residual_history.append(np.sqrt(rsold))
        
        for iteration in range(max_iter):
            # Check convergence
            if np.sqrt(rsold) < tol:
                break
                
            # Ap = A * p
            Ap = A.dot(p)
            
            # Alpha = r^T * r / (p^T * A * p)
            pAp = np.dot(p, Ap)
            if abs(pAp) < 1e-14:  # Avoid division by zero
                break
            alpha = rsold / pAp
            
            # Update solution: x = x + alpha * p
            x = x + alpha * p
            
            # Update residual: r = r - alpha * Ap
            r = r - alpha * Ap
            
            # Calculate new r^T * r
            rsnew = np.dot(r, r)
            residual_history.append(np.sqrt(rsnew))
            
            # Check convergence
            if np.sqrt(rsnew) < tol:
                iteration += 1
                break
                
            # Beta = rsnew / rsold
            beta = rsnew / rsold
            
            # Update search direction: p = r + beta * p
            p = r + beta * p
            
            # Update rsold
            rsold = rsnew
        
        return x, iteration, np.sqrt(rsold), residual_history
    
    def run_algorithm(self, input_data: Tuple[sp.csr_matrix, np.ndarray, np.ndarray]) -> Tuple[np.ndarray, int, float, list]:
        """Execute CG solver"""
        A, b, x_true = input_data
        max_iter = self.max_iterations if self.max_iterations else A.shape[0]
        
        x_solution, iterations, final_residual, residual_history = self.conjugate_gradient_solve(
            A, b, max_iter=max_iter, tol=self.tolerance
        )
        
        return x_solution, iterations, final_residual, residual_history
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return CG theoretical complexity"""
        n = input_size
        nnz = int(n * n * self.sparsity_ratio)  # Number of non-zeros
        max_iter = self.max_iterations if self.max_iterations else n
        
        # Time complexity: O(iterations * nnz) where iterations <= n
        # Space complexity: O(nnz + n) for matrix storage + vectors
        
        # Memory: sparse matrix (nnz values + indices) + vectors (x, r, p, b, Ap)
        sparse_mb = (nnz * 8 + nnz * 4 + (n+1) * 4) / (1024 * 1024)  # CSR format
        vectors_mb = (5 * n * 8) / (1024 * 1024)  # 5 vectors of size n
        total_theoretical_mb = sparse_mb + vectors_mb
        
        return (
            f"O(k*nnz) [N={n}, nnz≈{nnz:,}, k≤{max_iter}]",
            f"O(nnz+N) [N={n}, memory≈{nnz + 5*n:,} elements]",
            total_theoretical_mb
        )

    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate CG-specific metrics"""
        A, b, x_true = input_data
        x_solution, iterations, final_residual, residual_history = result

        # Matrix properties
        nnz = A.nnz
        actual_sparsity = nnz / (input_size * input_size)
        condition_number = None  # Computing condition number is expensive for large matrices

        # Solution accuracy
        solution_error = np.linalg.norm(x_solution - x_true)
        relative_solution_error = solution_error / np.linalg.norm(x_true) if np.linalg.norm(x_true) > 0 else float('inf')

        # Residual verification: ||b - Ax||
        computed_residual = np.linalg.norm(b - A.dot(x_solution))
        relative_residual = computed_residual / np.linalg.norm(b) if np.linalg.norm(b) > 0 else float('inf')

        # Convergence analysis
        converged = final_residual < self.tolerance
        convergence_rate = None
        if len(residual_history) > 1:
            # Estimate convergence rate from residual history
            log_residuals = np.log(np.array(residual_history) + 1e-16)  # Avoid log(0)
            if len(log_residuals) > 2:
                # Linear fit to estimate exponential convergence rate
                x_vals = np.arange(len(log_residuals))
                convergence_rate = -np.polyfit(x_vals, log_residuals, 1)[0]

        # Calculate norms
        b_norm = np.linalg.norm(b)
        x_norm = np.linalg.norm(x_solution)
        x_true_norm = np.linalg.norm(x_true)

        # Theoretical operations count: iterations * (2*nnz + 3*n) per iteration
        # Each iteration: 1 SpMV (2*nnz ops) + 3 dot products (3*n ops) + vector updates
        theoretical_flops = iterations * (2 * nnz + 6 * input_size)  # Approximate

        # Efficiency metrics
        max_possible_iterations = self.max_iterations if self.max_iterations else input_size
        iteration_efficiency = iterations / max_possible_iterations if max_possible_iterations > 0 else 0

        return {
            'correctness_verified': converged and relative_solution_error < 1e-3,
            'solution_error': float(solution_error),
            'relative_solution_error': float(relative_solution_error),
            'final_residual_norm': float(final_residual),
            'computed_residual_norm': float(computed_residual),
            'relative_residual': float(relative_residual),
            'converged': bool(converged),
            'iterations_performed': int(iterations),
            'max_iterations': int(max_possible_iterations),
            'iteration_efficiency': float(iteration_efficiency),
            'convergence_rate': float(convergence_rate) if convergence_rate is not None else None,
            'operations_count': theoretical_flops,
            'algorithm_type': 'conjugate_gradient',
            'matrix_size': f'{input_size}×{input_size}',
            'implementation': 'custom_cg',
            'nnz': int(nnz),
            'actual_sparsity_ratio': float(actual_sparsity),
            'target_sparsity_ratio': float(self.sparsity_ratio),
            'b_norm': float(b_norm),
            'solution_norm': float(x_norm),
            'true_solution_norm': float(x_true_norm),
            'theoretical_flops': theoretical_flops,
            'storage_format': 'CSR',
            'tolerance': float(self.tolerance),
            'residual_history_length': len(residual_history),
            'initial_residual': float(residual_history[0]) if residual_history else 0.0,
            'final_residual': float(residual_history[-1]) if residual_history else 0.0
        }


def main():
    """Run Conjugate Gradient scaling analysis"""
    print("=== Conjugate Gradient (CG) Scaling Analysis ===")
    print("Testing CG solver performance scaling with sparse matrices")

    # Test different sparsity levels and tolerances
    test_configurations = [
        {'sparsity': 0.001, 'tolerance': 1e-6, 'max_iter': None},
        {'sparsity': 0.01, 'tolerance': 1e-6, 'max_iter': None},
        {'sparsity': 0.05, 'tolerance': 1e-6, 'max_iter': None},
    ]

    for config in test_configurations:
        sparsity = config['sparsity']
        tolerance = config['tolerance']
        max_iter = config['max_iter']

        print(f"\n--- Configuration: Sparsity={sparsity*100:.1f}%, Tolerance={tolerance:.0e} ---")

        analyzer = ConjugateGradientScalingAnalyzer(
            sparsity_ratio=sparsity,
            tolerance=tolerance,
            max_iterations=max_iter,
            output_dir=f"results/cg_scipy_{sparsity*100:.1f}pct_tol{tolerance:.0e}",
            enable_gpu_tracking=False
        )

        # Matrix sizes for testing (start smaller for CG as it's more computationally intensive)
        matrix_sizes = [i for i in range(50, 2050, 100)]
        print(f"Matrix sizes: {matrix_sizes}")

        try:
            results = analyzer.run_scaling_analysis(matrix_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0-2.0 for CG (depends on condition number and convergence)")

            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                      f"Iter: {custom_metrics.get('iterations_performed', 0):4d}, "
                      f"Converged: {custom_metrics.get('converged', False)}, "
                      f"RelErr: {custom_metrics.get('relative_solution_error', float('inf')):.2e}")

        except Exception as e:
            print(f"Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            continue


if __name__ == "__main__":
    main()
