# Conjugate Gradient (CG) Algorithm Scaling Analysis

## Overview

This module implements scaling analysis for the **Conjugate Gradient (CG)** algorithm, which is an iterative method for solving systems of linear equations **Ax = b** where **A** is a symmetric positive-definite matrix.

## Algorithm Description

The Conjugate Gradient method is particularly effective for:
- Large sparse symmetric positive-definite systems
- Systems where direct methods (like LU decomposition) are too expensive
- Problems where an approximate solution is acceptable

### Key Properties
- **Time Complexity**: O(k × nnz) where k is the number of iterations and nnz is the number of non-zeros
- **Space Complexity**: O(nnz + n) for matrix storage plus working vectors
- **Convergence**: Theoretically converges in at most n iterations, but often much faster in practice
- **Numerical Stability**: Generally stable for well-conditioned matrices

## Implementation Details

### Matrix Generation
- Creates sparse symmetric positive-definite matrices using CSR format
- Ensures positive definiteness by adding diagonal dominance
- Configurable sparsity ratio (default: 1%)

### CG Algorithm Steps
1. Initialize: x₀ = 0, r₀ = b - Ax₀, p₀ = r₀
2. For each iteration k:
   - αₖ = (rₖᵀrₖ) / (pₖᵀApₖ)
   - xₖ₊₁ = xₖ + αₖpₖ
   - rₖ₊₁ = rₖ - αₖApₖ
   - βₖ = (rₖ₊₁ᵀrₖ₊₁) / (rₖᵀrₖ)
   - pₖ₊₁ = rₖ₊₁ + βₖpₖ
3. Stop when ||rₖ|| < tolerance

### Metrics Collected
- **Correctness**: Solution error compared to true solution
- **Convergence**: Number of iterations and final residual
- **Performance**: Execution time and memory usage
- **Matrix Properties**: Sparsity ratio, condition number estimates
- **Efficiency**: Iteration efficiency and convergence rate

## Usage

### Basic Usage

```python
from cg_analyzer import ConjugateGradientScalingAnalyzer

# Create analyzer with default settings
analyzer = ConjugateGradientScalingAnalyzer(
    sparsity_ratio=0.01,  # 1% non-zero elements
    tolerance=1e-6,       # Convergence tolerance
    max_iterations=None   # Use matrix size as max iterations
)

# Run scaling analysis
input_sizes = [100, 200, 500, 1000]
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### Advanced Configuration

```python
# Test different sparsity levels
analyzer = ConjugateGradientScalingAnalyzer(
    sparsity_ratio=0.05,     # 5% sparsity
    tolerance=1e-8,          # Stricter tolerance
    max_iterations=100,      # Limit iterations
    output_dir="results/cg_custom"
)
```

## Scaling Analysis Results

The implementation tests three different configurations:

1. **Low Sparsity (0.1%)**: Very sparse matrices, fast convergence
2. **Medium Sparsity (1.0%)**: Balanced sparsity, moderate convergence
3. **High Sparsity (5.0%)**: Denser matrices, slower convergence

### Expected Scaling Behavior
- **Time**: O(k × nnz) where k depends on condition number
- **Memory**: O(nnz + n) linear in matrix size and sparsity
- **Iterations**: Depends on matrix condition number and tolerance

## Files Generated

- `ConjugateGradient-scipy_scaling_results.csv`: Detailed metrics
- `ConjugateGradient-scipy_scaling_results.json`: JSON format results

## Key Insights

1. **Sparsity Impact**: Higher sparsity leads to more iterations but higher per-iteration cost
2. **Convergence**: Well-conditioned matrices converge quickly (< 10 iterations)
3. **Memory Efficiency**: Sparse storage provides significant memory savings
4. **Scaling**: Near-linear scaling with matrix size for fixed sparsity

## Dependencies

- NumPy: Dense vector operations
- SciPy: Sparse matrix operations
- Core scaling framework: Measurement infrastructure

## Running the Analysis

```bash
cd algorithms/Sparse_Linear_Algebra
python cg_analyzer.py
```

This will run the complete scaling analysis with multiple sparsity configurations and generate detailed results in the `results/` directory.
