"""
Sparse Linear Algebra algorithms package

This package contains scaling analyzers for sparse linear algebra operations:
- SpMV (Sparse Matrix-Vector Multiplication)
- CG (Conjugate Gradient solver for linear systems)
- AMG (Algebraic Multigrid solver for linear systems)
"""

from .spmv_analyzer import SpMVScalingAnalyzer
from .cg_analyzer import ConjugateGradientScalingAnalyzer
from .amg_analyzer import BasicAMGScalingAnalyzer

__all__ = [
    'SpMVScalingAnalyzer',
    'ConjugateGradientScalingAnalyzer',
    'BasicAMGScalingAnalyzer'
]