"""
Algebraic Multigrid (AMG) scaling analysis
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import scipy.sparse as sp
from scipy.sparse.linalg import spsolve
from typing import Tuple, Dict, Any, List
import time
import gc

from core.scaling_framework import ScalingAnalyzer


class BasicAMGScalingAnalyzer(ScalingAnalyzer):
    """Basic Algebraic Multigrid scaling analysis for solving sparse linear systems Ax=b"""
    
    def __init__(self, sparsity_ratio: float = 0.01, max_levels: int = 4, 
                 max_iterations: int = 10, tolerance: float = 1e-6, 
                 coarsening_factor: float = 0.25, **kwargs):
        """
        Initialize AMG analyzer with basic implementation
        
        Args:
            sparsity_ratio: Ratio of non-zero elements (default: 0.01 = 1%)
            max_levels: Maximum number of multigrid levels (default: 4)
            max_iterations: Maximum AMG iterations (default: 10)
            tolerance: Convergence tolerance (default: 1e-6)
            coarsening_factor: Fraction of points to keep when coarsening (default: 0.25)
        """
        super().__init__(algorithm_name="AMG-basic", **kwargs)
        self.sparsity_ratio = sparsity_ratio
        self.max_levels = max_levels
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.coarsening_factor = coarsening_factor
    
    def prepare_input(self, input_size: int) -> Tuple[sp.csr_matrix, np.ndarray, np.ndarray]:
        """Generate a random sparse symmetric positive-definite matrix and RHS vector for AMG"""
        np.random.seed(42)  # For reproducibility
        
        # Generate sparse matrix in CSR format
        density = self.sparsity_ratio
        # Create a random sparse matrix
        A_base = sp.random(input_size, input_size, density=density, 
                          format='csr', dtype=np.float64)
        
        # Make it symmetric: A = A + A^T
        A_symmetric = A_base + A_base.T
        
        # Make it positive definite by adding diagonal dominance
        # Add a diagonal matrix with values larger than the sum of off-diagonal elements
        diagonal_values = np.array(A_symmetric.sum(axis=1)).flatten() + input_size
        A_spd = A_symmetric + sp.diags(diagonal_values, format='csr')
        
        # Generate true solution
        x_true = np.random.randn(input_size).astype(np.float64)
        
        # Generate RHS: b = A * x_true
        b = A_spd.dot(x_true)
        
        return A_spd, b, x_true
    
    def coarsen_matrix(self, A: sp.csr_matrix) -> Tuple[sp.csr_matrix, sp.csr_matrix]:
        """
        Simple coarsening: select subset of variables using strength-based approach
        
        Returns:
            A_coarse: Coarsened matrix
            P: Prolongation (interpolation) matrix
        """
        n = A.shape[0]
        target_size = max(int(n * self.coarsening_factor), 1)
        
        # Simple coarsening: select variables with largest diagonal entries
        diag = A.diagonal()
        coarse_indices = np.argsort(diag)[-target_size:]
        coarse_indices = np.sort(coarse_indices)
        
        # Create prolongation matrix P (simple injection)
        P = sp.lil_matrix((n, target_size))
        for i, coarse_idx in enumerate(coarse_indices):
            P[coarse_idx, i] = 1.0
        P = P.tocsr()
        
        # Galerkin coarsening: A_coarse = P^T * A * P
        A_coarse = P.T @ A @ P
        
        return A_coarse.tocsr(), P
    
    def create_multigrid_hierarchy(self, A: sp.csr_matrix) -> List[Dict]:
        """
        Create multigrid hierarchy with matrices and prolongation operators
        
        Returns:
            List of level dictionaries containing matrices and operators
        """
        levels = []
        current_A = A.copy()
        
        for level in range(self.max_levels):
            level_dict = {'A': current_A, 'size': current_A.shape[0]}
            
            # Stop if matrix is too small
            if current_A.shape[0] <= 10:
                levels.append(level_dict)
                break
                
            # Create coarse level
            try:
                A_coarse, P = self.coarsen_matrix(current_A)
                level_dict['P'] = P
                level_dict['R'] = P.T  # Restriction operator (transpose of prolongation)
                levels.append(level_dict)
                current_A = A_coarse
            except:
                # If coarsening fails, stop here
                levels.append(level_dict)
                break
        
        # Add coarsest level
        if len(levels) == 0 or levels[-1]['A'].shape[0] > 10:
            levels.append({'A': current_A, 'size': current_A.shape[0]})
            
        return levels
    
    def v_cycle(self, levels: List[Dict], b: np.ndarray, x: np.ndarray, 
                level: int = 0, pre_smooth: int = 2, post_smooth: int = 2) -> np.ndarray:
        """
        V-cycle multigrid iteration
        
        Args:
            levels: Multigrid hierarchy
            b: Right-hand side
            x: Initial guess
            level: Current level
            pre_smooth: Number of pre-smoothing iterations
            post_smooth: Number of post-smoothing iterations
        """
        if level >= len(levels) - 1:
            # Coarsest level: solve directly
            try:
                return spsolve(levels[level]['A'], b)
            except:
                # If direct solve fails, return current solution
                return x
        
        A = levels[level]['A']
        
        # Pre-smoothing (Gauss-Seidel iterations)
        x = self.gauss_seidel_smooth(A, b, x, pre_smooth)
        
        # Compute residual
        residual = b - A.dot(x)
        
        # Restrict residual to coarse grid
        R = levels[level]['R']
        coarse_residual = R.dot(residual)
        
        # Solve coarse grid problem recursively
        coarse_correction = np.zeros(coarse_residual.shape[0])
        coarse_correction = self.v_cycle(levels, coarse_residual, coarse_correction, level + 1)
        
        # Prolongate correction to fine grid
        P = levels[level]['P']
        fine_correction = P.dot(coarse_correction)
        
        # Apply correction
        x = x + fine_correction
        
        # Post-smoothing
        x = self.gauss_seidel_smooth(A, b, x, post_smooth)
        
        return x
    
    def gauss_seidel_smooth(self, A: sp.csr_matrix, b: np.ndarray, 
                           x: np.ndarray, iterations: int) -> np.ndarray:
        """
        Gauss-Seidel smoothing iterations
        """
        n = A.shape[0]
        x = x.copy()
        
        for _ in range(iterations):
            for i in range(n):
                # Extract row i
                row_start = A.indptr[i]
                row_end = A.indptr[i + 1]
                indices = A.indices[row_start:row_end]
                values = A.data[row_start:row_end]
                
                # Find diagonal element
                diag_idx = np.where(indices == i)[0]
                if len(diag_idx) == 0:
                    continue
                    
                diag_val = values[diag_idx[0]]
                if abs(diag_val) < 1e-14:
                    continue
                
                # Compute new value
                sum_val = np.sum(values * x[indices]) - diag_val * x[i]
                x[i] = (b[i] - sum_val) / diag_val
        
        return x
    
    def amg_solve(self, A: sp.csr_matrix, b: np.ndarray, 
                  x0: np.ndarray = None) -> Tuple[np.ndarray, int, float, list]:
        """
        AMG solver implementation
        
        Returns:
            x: Solution vector
            iterations: Number of iterations performed
            residual_norm: Final residual norm
            residual_history: History of residual norms
        """
        n = len(b)
        
        # Initialize
        if x0 is None:
            x = np.zeros(n, dtype=np.float64)
        else:
            x = x0.copy()
        
        # Create multigrid hierarchy
        levels = self.create_multigrid_hierarchy(A)
        
        residual_history = []
        
        for iteration in range(self.max_iterations):
            # Compute residual
            residual = b - A.dot(x)
            residual_norm = np.linalg.norm(residual)
            residual_history.append(residual_norm)
            
            # Check convergence
            if residual_norm < self.tolerance:
                break
            
            # Perform V-cycle
            x = self.v_cycle(levels, b, x)
        
        final_residual = np.linalg.norm(b - A.dot(x))
        return x, iteration + 1, final_residual, residual_history
    
    def run_algorithm(self, input_data: Tuple[sp.csr_matrix, np.ndarray, np.ndarray]) -> Tuple[np.ndarray, int, float, list, List[Dict]]:
        """Execute AMG solver"""
        A, b, x_true = input_data
        
        x_solution, iterations, final_residual, residual_history = self.amg_solve(A, b)
        
        # Create hierarchy for analysis
        levels = self.create_multigrid_hierarchy(A)
        
        return x_solution, iterations, final_residual, residual_history, levels

    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return AMG theoretical complexity"""
        n = input_size
        nnz = int(n * n * self.sparsity_ratio)  # Number of non-zeros

        # AMG complexity: O(k * nnz) where k is iterations (typically much smaller than n)
        # Setup cost: O(nnz) for hierarchy construction
        # Memory: O(nnz) for all levels combined (geometric decrease)

        # Memory estimation: original matrix + hierarchy (roughly 1.5x original)
        sparse_mb = (nnz * 8 + nnz * 4 + (n+1) * 4) / (1024 * 1024)  # CSR format
        hierarchy_mb = sparse_mb * 0.5  # Additional memory for coarse levels
        vectors_mb = (5 * n * 8) / (1024 * 1024)  # Working vectors
        total_theoretical_mb = sparse_mb + hierarchy_mb + vectors_mb

        return (
            f"O(k*nnz) [N={n}, nnz≈{nnz:,}, k≤{self.max_iterations}]",
            f"O(nnz) [N={n}, memory≈{int(1.5*nnz + 5*n):,} elements]",
            total_theoretical_mb
        )

    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate AMG-specific metrics"""
        A, b, x_true = input_data
        x_solution, iterations, final_residual, residual_history, levels = result

        # Matrix properties
        nnz = A.nnz
        actual_sparsity = nnz / (input_size * input_size)

        # Solution accuracy
        solution_error = np.linalg.norm(x_solution - x_true)
        relative_solution_error = solution_error / np.linalg.norm(x_true) if np.linalg.norm(x_true) > 0 else float('inf')

        # Residual verification: ||b - Ax||
        computed_residual = np.linalg.norm(b - A.dot(x_solution))
        relative_residual = computed_residual / np.linalg.norm(b) if np.linalg.norm(b) > 0 else float('inf')

        # Convergence analysis
        converged = final_residual < self.tolerance
        convergence_rate = None
        if len(residual_history) > 1:
            # Estimate convergence rate from residual history
            log_residuals = np.log(np.array(residual_history) + 1e-16)  # Avoid log(0)
            if len(log_residuals) > 2:
                # Linear fit to estimate exponential convergence rate
                x_vals = np.arange(len(log_residuals))
                convergence_rate = -np.polyfit(x_vals, log_residuals, 1)[0]

        # Multigrid hierarchy analysis
        num_levels = len(levels)
        level_sizes = [level['size'] for level in levels]
        coarsening_ratios = []
        for i in range(len(level_sizes) - 1):
            if level_sizes[i] > 0:
                coarsening_ratios.append(level_sizes[i+1] / level_sizes[i])

        avg_coarsening_ratio = np.mean(coarsening_ratios) if coarsening_ratios else 0.0

        # Calculate total work (approximate)
        total_nnz_all_levels = sum([level['A'].nnz for level in levels])
        setup_work = total_nnz_all_levels  # Hierarchy construction
        solve_work = iterations * total_nnz_all_levels  # V-cycles

        # Calculate norms
        b_norm = np.linalg.norm(b)
        x_norm = np.linalg.norm(x_solution)
        x_true_norm = np.linalg.norm(x_true)

        # Efficiency metrics
        iteration_efficiency = iterations / self.max_iterations if self.max_iterations > 0 else 0

        return {
            'correctness_verified': converged and relative_solution_error < 1e-3,
            'solution_error': float(solution_error),
            'relative_solution_error': float(relative_solution_error),
            'final_residual_norm': float(final_residual),
            'computed_residual_norm': float(computed_residual),
            'relative_residual': float(relative_residual),
            'converged': bool(converged),
            'iterations_performed': int(iterations),
            'max_iterations': int(self.max_iterations),
            'iteration_efficiency': float(iteration_efficiency),
            'convergence_rate': float(convergence_rate) if convergence_rate is not None else None,
            'operations_count': int(solve_work),
            'algorithm_type': 'algebraic_multigrid',
            'matrix_size': f'{input_size}×{input_size}',
            'implementation': 'basic_amg',
            'nnz': int(nnz),
            'actual_sparsity_ratio': float(actual_sparsity),
            'target_sparsity_ratio': float(self.sparsity_ratio),
            'b_norm': float(b_norm),
            'solution_norm': float(x_norm),
            'true_solution_norm': float(x_true_norm),
            'theoretical_flops': int(solve_work),
            'storage_format': 'CSR',
            'tolerance': float(self.tolerance),
            'residual_history_length': len(residual_history),
            'initial_residual': float(residual_history[0]) if residual_history else 0.0,
            'final_residual': float(residual_history[-1]) if residual_history else 0.0,
            # AMG-specific metrics
            'num_levels': int(num_levels),
            'level_sizes': level_sizes,
            'avg_coarsening_ratio': float(avg_coarsening_ratio),
            'coarsening_ratios': coarsening_ratios,
            'setup_work': int(setup_work),
            'solve_work': int(solve_work),
            'total_nnz_all_levels': int(total_nnz_all_levels),
            'hierarchy_efficiency': float(total_nnz_all_levels / nnz) if nnz > 0 else 0.0,
            'max_levels': int(self.max_levels),
            'coarsening_factor': float(self.coarsening_factor)
        }


def main():
    """Run Algebraic Multigrid scaling analysis"""
    print("=== Algebraic Multigrid (AMG) Scaling Analysis ===")
    print("Testing AMG solver performance scaling with sparse matrices")

    # Test different configurations
    test_configurations = [
        {
            'sparsity': 0.01,
            'max_levels': 3,
            'tolerance': 1e-6,
            'coarsening_factor': 0.25,
            'max_iterations': 10
        },
        {
            'sparsity': 0.02,
            'max_levels': 4,
            'tolerance': 1e-6,
            'coarsening_factor': 0.3,
            'max_iterations': 15
        },
        {
            'sparsity': 0.05,
            'max_levels': 4,
            'tolerance': 1e-6,
            'coarsening_factor': 0.2,
            'max_iterations': 20
        },
    ]

    for i, config in enumerate(test_configurations):
        sparsity = config['sparsity']
        max_levels = config['max_levels']
        tolerance = config['tolerance']
        coarsening_factor = config['coarsening_factor']
        max_iterations = config['max_iterations']

        print(f"\n--- Configuration {i+1}: Sparsity={sparsity*100:.1f}%, "
              f"Levels={max_levels}, Coarsening={coarsening_factor:.2f} ---")

        analyzer = BasicAMGScalingAnalyzer(
            sparsity_ratio=sparsity,
            max_levels=max_levels,
            tolerance=tolerance,
            coarsening_factor=coarsening_factor,
            max_iterations=max_iterations,
            output_dir=f"results/amg_basic_{sparsity*100:.1f}pct_L{max_levels}_C{int(coarsening_factor*100)}",
            enable_gpu_tracking=False
        )

        # Matrix sizes for testing (AMG is more expensive, so use smaller sizes)
        matrix_sizes = [i for i in range(50, 1050, 50)]
        print(f"Matrix sizes: {matrix_sizes}")

        try:
            results = analyzer.run_scaling_analysis(matrix_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0-1.5 for AMG (optimal complexity)")

            print(f"\n=== Sample Results ===")
            for j, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                      f"Iter: {custom_metrics.get('iterations_performed', 0):2d}, "
                      f"Levels: {custom_metrics.get('num_levels', 0):2d}, "
                      f"Converged: {custom_metrics.get('converged', False)}, "
                      f"RelErr: {custom_metrics.get('relative_solution_error', float('inf')):.2e}")

        except Exception as e:
            print(f"Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            continue


if __name__ == "__main__":
    main()
