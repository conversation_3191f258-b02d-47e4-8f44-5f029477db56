"""
BFS (Breadth-First Search) Scaling Analysis

This module implements scaling analysis for Breadth-First Search algorithm,
measuring both time and space complexity across different graph sizes.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from collections import deque
from typing import Any, Dict, Tuple, List, Set
import random


class BFSScalingAnalyzer(ScalingAnalyzer):
    """BFS scaling analysis for graph traversal operations"""
    
    def __init__(self, graph_type: str = 'random', edge_probability: float = 0.3, **kwargs):
        """
        Initialize BFS analyzer
        
        Args:
            graph_type: Type of graph ('random', 'grid', 'complete')
            edge_probability: Probability of edge existence for random graphs
        """
        super().__init__(algorithm_name=f"BFS-{graph_type}", **kwargs)
        self.graph_type = graph_type
        self.edge_probability = edge_probability
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Generate graph for BFS analysis"""
        if self.graph_type == 'random':
            graph = self._generate_random_graph(input_size)
        elif self.graph_type == 'grid':
            graph = self._generate_grid_graph(input_size)
        elif self.graph_type == 'complete':
            graph = self._generate_complete_graph(input_size)
        else:
            raise ValueError(f"Unknown graph type: {self.graph_type}")
        
        return {
            'graph': graph,
            'start_node': 0,  # Always start from node 0
            'num_nodes': input_size
        }
    
    def _generate_random_graph(self, num_nodes: int) -> Dict[int, List[int]]:
        """Generate random graph with given number of nodes"""
        random.seed(42)  # For reproducibility
        graph = {i: [] for i in range(num_nodes)}
        
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                if random.random() < self.edge_probability:
                    graph[i].append(j)
                    graph[j].append(i)
        
        return graph
    
    def _generate_grid_graph(self, num_nodes: int) -> Dict[int, List[int]]:
        """Generate 2D grid graph (approximate square grid)"""
        side_length = int(np.sqrt(num_nodes))
        actual_nodes = side_length * side_length
        
        graph = {i: [] for i in range(actual_nodes)}
        
        for i in range(side_length):
            for j in range(side_length):
                node = i * side_length + j
                
                # Connect to right neighbor
                if j < side_length - 1:
                    right = i * side_length + (j + 1)
                    graph[node].append(right)
                    graph[right].append(node)
                
                # Connect to bottom neighbor
                if i < side_length - 1:
                    bottom = (i + 1) * side_length + j
                    graph[node].append(bottom)
                    graph[bottom].append(node)
        
        return graph
    
    def _generate_complete_graph(self, num_nodes: int) -> Dict[int, List[int]]:
        """Generate complete graph (every node connected to every other node)"""
        graph = {}
        for i in range(num_nodes):
            graph[i] = [j for j in range(num_nodes) if j != i]
        return graph
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute BFS algorithm"""
        graph = input_data['graph']
        start_node = input_data['start_node']
        
        # BFS implementation with detailed tracking
        visited = set()
        queue = deque([start_node])
        visited.add(start_node)
        
        traversal_order = []
        distances = {start_node: 0}
        parent = {start_node: None}
        max_queue_size = 1
        
        while queue:
            max_queue_size = max(max_queue_size, len(queue))
            current_node = queue.popleft()
            traversal_order.append(current_node)
            
            for neighbor in graph.get(current_node, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)
                    distances[neighbor] = distances[current_node] + 1
                    parent[neighbor] = current_node
        
        return {
            'visited_nodes': visited,
            'traversal_order': traversal_order,
            'distances': distances,
            'parent': parent,
            'max_queue_size': max_queue_size,
            'nodes_visited': len(visited)
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return BFS theoretical complexity"""
        n = input_size
        
        if self.graph_type == 'complete':
            edges = n * (n - 1) // 2
            time_complexity = f"O(V+E) [V={n}, E≈{edges}]"
        elif self.graph_type == 'grid':
            side = int(np.sqrt(n))
            edges = 2 * side * (side - 1)  # Approximate edges in grid
            time_complexity = f"O(V+E) [V={side}², E≈{edges}]"
        else:  # random
            expected_edges = int(n * (n - 1) * self.edge_probability / 2)
            time_complexity = f"O(V+E) [V={n}, E≈{expected_edges}]"
        
        space_complexity = f"O(V) [V={n}]"
        
        # Memory estimation: visited set + queue + distances + parent
        theoretical_memory_mb = (n * 4 * 8) / (1024 * 1024)  # 4 data structures, 8 bytes per element
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate BFS-specific metrics"""
        graph = input_data['graph']
        bfs_result = result
        
        # Calculate graph statistics
        total_edges = sum(len(neighbors) for neighbors in graph.values()) // 2
        avg_degree = (2 * total_edges) / len(graph) if len(graph) > 0 else 0
        
        # Calculate connectivity
        connected_components = self._count_connected_components(graph)
        is_connected = connected_components == 1
        
        # Calculate BFS efficiency
        nodes_reachable = len(bfs_result['visited_nodes'])
        reachability_ratio = nodes_reachable / input_size if input_size > 0 else 0
        
        # Calculate theoretical operations (each node and edge visited once)
        theoretical_ops = nodes_reachable + sum(
            len(graph.get(node, [])) for node in bfs_result['visited_nodes']
        )
        
        return {
            'graph_type': self.graph_type,
            'num_nodes': input_size,
            'num_edges': total_edges,
            'average_degree': round(avg_degree, 2),
            'connected_components': connected_components,
            'is_connected': is_connected,
            'nodes_reachable': nodes_reachable,
            'reachability_ratio': round(reachability_ratio, 3),
            'max_queue_size': bfs_result['max_queue_size'],
            'traversal_length': len(bfs_result['traversal_order']),
            'theoretical_operations': theoretical_ops,
            'max_distance': max(bfs_result['distances'].values()) if bfs_result['distances'] else 0,
            'avg_distance': round(np.mean(list(bfs_result['distances'].values())), 2) if bfs_result['distances'] else 0
        }
    
    def _count_connected_components(self, graph: Dict[int, List[int]]) -> int:
        """Count number of connected components in the graph"""
        visited = set()
        components = 0
        
        for node in graph:
            if node not in visited:
                components += 1
                # DFS to mark all nodes in this component
                stack = [node]
                while stack:
                    current = stack.pop()
                    if current not in visited:
                        visited.add(current)
                        stack.extend(neighbor for neighbor in graph.get(current, []) 
                                   if neighbor not in visited)
        
        return components


def main():
    """Run BFS scaling analysis"""
    print("=== BFS (Breadth-First Search) Scaling Analysis ===")
    
    # Test different graph types
    graph_configs = [
        ('random', 0.1, 'sparse random'),
        ('random', 0.3, 'medium random'),
        ('grid', None, '2D grid'),
        ('complete', None, 'complete')
    ]
    
    for graph_type, edge_prob, description in graph_configs:
        print(f"\n--- Testing {description.upper()} graphs ---")
        
        kwargs = {'graph_type': graph_type}
        if edge_prob is not None:
            kwargs['edge_probability'] = edge_prob
        
        analyzer = BFSScalingAnalyzer(
            output_dir=f"results/bfs_{graph_type}_{edge_prob or 'default'}",
            enable_gpu_tracking=False,
            **kwargs
        )
        
        # Node sizes for testing
        if graph_type == 'complete':
            # Complete graphs grow very quickly, use smaller sizes
            node_sizes = [i for i in range(10, 101, 10)]
        else:
            node_sizes = [i for i in range(50, 1001, 50)]
        
        print(f"Node sizes: {node_sizes}")
        
        try:
            results = analyzer.run_scaling_analysis(node_sizes)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== {description.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                
                if graph_type == 'complete':
                    print(f"Expected: ~2.0 for Complete Graph BFS (O(V²))")
                else:
                    print(f"Expected: ~1.0-2.0 for BFS (O(V+E))")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Nodes {result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Edges: {custom_metrics.get('num_edges', 0):5d}, "
                      f"Reachable: {custom_metrics.get('nodes_reachable', 0):3d}, "
                      f"MaxQueue: {custom_metrics.get('max_queue_size', 0):3d}")
            
        except Exception as e:
            print(f"Error in {description} BFS analysis: {e}")
    
    print(f"\nBFS scaling analysis completed!")
    print("Results saved in:")
    for graph_type, edge_prob, _ in graph_configs:
        print(f"- results/bfs_{graph_type}_{edge_prob or 'default'}/")


if __name__ == "__main__":
    main()