"""
K-means Clustering Scaling Analysis using Unified Framework

K-means聚类算法的scaling分析器，测量时间和空间复杂度
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
from sklearn.cluster import KMeans
from sklearn.datasets import make_blobs, make_classification
from sklearn.preprocessing import StandardScaler
import numpy as np
from typing import Any, Tuple, Dict

class KMeansScalingAnalyzer(ScalingAnalyzer):
    """K-means clustering scaling analysis - 测量时间和空间复杂度"""
    
    def __init__(self, 
                 n_features: int = 10,
                 n_clusters: int = 8,
                 max_iter: int = 300,
                 n_init: int = 10,
                 random_state: int = 42,
                 **kwargs):
        super().__init__(algorithm_name=f"KMeans_k{n_clusters}_f{n_features}", **kwargs)
        
        self.n_features = n_features
        self.n_clusters = n_clusters
        self.max_iter = max_iter
        self.n_init = n_init
        self.random_state = random_state
        
        # 验证参数设置
        if n_clusters <= 0:
            raise ValueError("n_clusters must be positive")
        if n_features <= 0:
            raise ValueError("n_features must be positive")
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """生成聚类数据集"""
        
        # 使用make_blobs生成具有明确聚类结构的数据
        X, y_true = make_blobs(
            n_samples=input_size,
            centers=self.n_clusters,
            n_features=self.n_features,
            cluster_std=1.0,
            center_box=(-10.0, 10.0),
            random_state=self.random_state + input_size  # 改变随机状态以获得不同数据
        )
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        return {
            'X': X_scaled,
            'y_true': y_true,
            'scaler': scaler,
            'n_samples': input_size,
            'n_features': self.n_features,
            'n_clusters': self.n_clusters
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行K-means聚类"""
        
        X = input_data['X']
        
        # 初始化K-means模型
        kmeans_model = KMeans(
            n_clusters=self.n_clusters,
            init='k-means++',
            n_init=self.n_init,
            max_iter=self.max_iter,
            random_state=self.random_state
        )
        
        # 拟合模型并预测聚类
        kmeans_model.fit(X)
        cluster_labels = kmeans_model.predict(X)
        
        # 计算聚类质量指标
        inertia = kmeans_model.inertia_  # 簇内平方和
        n_iter = kmeans_model.n_iter_   # 实际迭代次数
        
        # 计算silhouette score (如果样本数不太大的话)
        silhouette_score = None
        if len(X) <= 10000:  # 避免在大数据集上计算昂贵的silhouette score
            from sklearn.metrics import silhouette_score as sk_silhouette_score
            try:
                silhouette_score = sk_silhouette_score(X, cluster_labels)
            except Exception:
                silhouette_score = None
        
        return {
            'kmeans_model': kmeans_model,
            'cluster_labels': cluster_labels,
            'cluster_centers': kmeans_model.cluster_centers_,
            'inertia': inertia,
            'n_iter': n_iter,
            'silhouette_score': silhouette_score
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """返回K-means理论复杂度"""
        
        n_samples = input_size
        n_features = self.n_features
        n_clusters = self.n_clusters
        avg_iterations = min(self.max_iter, 20)  # 估计平均迭代次数
        
        # 内存使用计算 (MB)
        # 输入数据
        input_data_mb = (n_samples * n_features * 8) / (1024 * 1024)
        
        # 聚类中心
        centers_mb = (n_clusters * n_features * 8) / (1024 * 1024)
        
        # 聚类标签
        labels_mb = (n_samples * 4) / (1024 * 1024)  # int32
        
        # 距离计算临时存储 (每个样本到所有中心的距离)
        distances_mb = (n_samples * n_clusters * 8) / (1024 * 1024)
        
        # sklearn开销
        sklearn_overhead_mb = min(20, input_data_mb * 0.1)
        
        # 多次初始化的额外开销
        init_overhead_mb = (self.n_init * centers_mb)
        
        total_theoretical_mb = (input_data_mb + centers_mb + labels_mb + 
                              distances_mb + sklearn_overhead_mb + init_overhead_mb)
        
        return (
            f"O(k×n×d×i) [k={n_clusters}, n={n_samples}, d={n_features}, i≈{avg_iterations}]",  # 时间复杂度
            f"O(k×d + n×d + k×n) [k={n_clusters}, n={n_samples}, d={n_features}]",  # 空间复杂度
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """计算K-means特定指标"""
        
        return {
            'inertia': result['inertia'],
            'n_iter': result['n_iter'],
            'silhouette_score': result['silhouette_score'],
            'n_clusters': self.n_clusters,
            'n_features': self.n_features,
            'n_samples': input_size,
            'avg_cluster_size': input_size / self.n_clusters,
            'convergence_ratio': result['n_iter'] / self.max_iter,
            'clusters_to_samples_ratio': self.n_clusters / input_size,
            'features_to_clusters_ratio': self.n_features / self.n_clusters
        }

class KMeansComplexityAnalyzer(ScalingAnalyzer):
    """K-means复杂度分析 - 变化聚类数量"""
    
    def __init__(self, 
                 fixed_samples: int = 10000,
                 n_features: int = 10,
                 max_iter: int = 300,
                 random_state: int = 42,
                 **kwargs):
        super().__init__(algorithm_name=f"KMeans_complexity_s{fixed_samples}_f{n_features}", **kwargs)
        
        self.fixed_samples = fixed_samples
        self.n_features = n_features
        self.max_iter = max_iter
        self.random_state = random_state
    
    def prepare_input(self, n_clusters: int) -> Dict[str, Any]:
        """生成固定样本数但变化聚类数的数据集"""
        
        # 确保聚类数不超过样本数
        actual_clusters = min(n_clusters, self.fixed_samples // 10)  # 至少每个聚类10个样本
        
        # 生成数据
        X, y_true = make_blobs(
            n_samples=self.fixed_samples,
            centers=actual_clusters,
            n_features=self.n_features,
            cluster_std=1.0,
            center_box=(-10.0, 10.0),
            random_state=self.random_state + n_clusters
        )
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        return {
            'X': X_scaled,
            'y_true': y_true,
            'n_clusters': actual_clusters,
            'n_samples': self.fixed_samples,
            'n_features': self.n_features
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行变化聚类数的K-means"""
        
        X = input_data['X']
        n_clusters = input_data['n_clusters']
        
        # 初始化和拟合K-means
        kmeans_model = KMeans(
            n_clusters=n_clusters,
            init='k-means++',
            n_init=10,
            max_iter=self.max_iter,
            random_state=self.random_state
        )
        
        kmeans_model.fit(X)
        cluster_labels = kmeans_model.predict(X)
        
        # 计算指标
        inertia = kmeans_model.inertia_
        n_iter = kmeans_model.n_iter_
        
        return {
            'kmeans_model': kmeans_model,
            'cluster_labels': cluster_labels,
            'inertia': inertia,
            'n_iter': n_iter,
            'n_clusters_used': n_clusters
        }
    
    def get_theoretical_complexity(self, n_clusters: int) -> Tuple[str, str, float]:
        """理论复杂度 - 变化聚类数"""
        
        actual_clusters = min(n_clusters, self.fixed_samples // 10)
        n_samples = self.fixed_samples
        n_features = self.n_features
        
        # 内存计算
        input_data_mb = (n_samples * n_features * 8) / (1024 * 1024)
        centers_mb = (actual_clusters * n_features * 8) / (1024 * 1024)
        labels_mb = (n_samples * 4) / (1024 * 1024)
        distances_mb = (n_samples * actual_clusters * 8) / (1024 * 1024)
        overhead_mb = min(20, input_data_mb * 0.1)
        
        total_theoretical_mb = input_data_mb + centers_mb + labels_mb + distances_mb + overhead_mb
        
        return (
            f"O(k×n×d×i) [k={actual_clusters}, n={n_samples}, d={n_features}]",
            f"O(k×d + n×d + k×n) [k={actual_clusters}, n={n_samples}, d={n_features}]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, n_clusters: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """计算变化聚类数的指标"""
        actual_clusters = input_data['n_clusters']
        
        return {
            'inertia': result['inertia'],
            'n_iter': result['n_iter'],
            'n_clusters_used': actual_clusters,
            'n_samples': self.fixed_samples,
            'n_features': self.n_features,
            'avg_cluster_size': self.fixed_samples / actual_clusters,
            'convergence_ratio': result['n_iter'] / self.max_iter
        }

def main():
    """运行K-means scaling分析"""
    
    print("K-means Clustering Scaling Analysis")
    print("=" * 50)
    
    # 测试1: 固定聚类数，变化样本数
    print("\n1. 样本数scaling分析 (固定8个聚类)")
    analyzer1 = KMeansScalingAnalyzer(
        n_clusters=8,
        n_features=10,
        output_dir="results/kmeans_samples",
        enable_gpu_tracking=False  # K-means CPU版本
    )
    
    # 样本数范围
    sample_sizes = list(range(100, 10001, 100))  # 100到10, 000，步长100
    
    print(f"分析样本数范围: {sample_sizes[0]} 到 {sample_sizes[-1]}")
    results1 = analyzer1.run_scaling_analysis(sample_sizes)
    
    # 分析scaling行为
    scaling_analysis1 = analyzer1.analyze_scaling_behavior()
    if scaling_analysis1:
        print(f"平均scaling因子: {scaling_analysis1['mean_scaling_factor']:.2f}")
        print(f"期望值: ~2.0 对于K-means (接近O(n²))")
    
    # 测试2: 固定样本数，变化聚类数
    print("\n2. 聚类数scaling分析 (固定10,000样本)")
    analyzer2 = KMeansComplexityAnalyzer(
        fixed_samples=10000,
        n_features=10,
        output_dir="results/kmeans_clusters",
        enable_gpu_tracking=False
    )
    
    # 聚类数范围
    cluster_sizes = list(range(2, 101, 2))  # 2到100，步长2
    
    print(f"分析聚类数范围: {cluster_sizes[0]} 到 {cluster_sizes[-1]}")
    results2 = analyzer2.run_scaling_analysis(cluster_sizes)
    
    # 分析scaling行为
    scaling_analysis2 = analyzer2.analyze_scaling_behavior()
    if scaling_analysis2:
        print(f"平均scaling因子: {scaling_analysis2['mean_scaling_factor']:.2f}")
        print(f"期望值: ~1.0 对于聚类数scaling (接近O(k))")
    
    print(f"\nK-means scaling分析完成!")
    print("结果保存在:")
    print("- results/kmeans_samples/ (样本数scaling)")
    print("- results/kmeans_clusters/ (聚类数scaling)")

if __name__ == "__main__":
    main() 