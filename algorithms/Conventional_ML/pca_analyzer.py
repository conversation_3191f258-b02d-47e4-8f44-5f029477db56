"""
PCA Scaling Analysis using Unified Framework

Migrated from conventional_algo/scaling_pca.py
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
from sklearn.decomposition import PCA
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler
import numpy as np
from typing import Any, Tuple, Dict

class PCAScalingAnalyzer(ScalingAnalyzer):
    """PCA scaling analysis - migrated from original scaling_pca.py"""
    
    def __init__(self, 
                 n_features: int = 150,
                 n_components: int = 50,
                 random_state: int = 42,
                 **kwargs):
        super().__init__(algorithm_name=f"PCA_f{n_features}_c{n_components}", **kwargs)
        
        self.n_features = n_features
        self.n_components = n_components
        self.random_state = random_state
        
        # Validate components setting (from original code)
        if n_components > n_features:
            self.n_components = min(n_features, 50)
            print(f"Warning: n_components adjusted to {self.n_components} (max of features or 50)")
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Generate synthetic dataset - matches original implementation"""
        
        # Generate classification dataset (from original code)
        X, y = make_classification(
            n_samples=input_size,
            n_features=self.n_features,
            n_informative=int(self.n_features * 0.7),  # 70% informative features
            n_redundant=int(self.n_features * 0.2),    # 20% redundant features
            n_classes=2,
            n_clusters_per_class=1,
            random_state=self.random_state + input_size  # Vary random state
        )
        
        # Standardize features (from original code)
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        return {
            'X': X_scaled,
            'y': y,
            'scaler': scaler,
            'n_samples': input_size,
            'n_features': self.n_features
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute PCA fitting and transformation"""
        
        X = input_data['X']
        
        # Initialize PCA (from original code)
        pca_model = PCA(
            n_components=self.n_components,
            random_state=self.random_state
        )
        
        # Fit PCA model
        pca_model.fit(X)
        
        # Transform data
        X_transformed = pca_model.transform(X)
        
        # Calculate explained variance ratio
        explained_variance = np.sum(pca_model.explained_variance_ratio_)
        
        return {
            'pca_model': pca_model,
            'X_transformed': X_transformed,
            'explained_variance_ratio': explained_variance,
            'n_components_used': pca_model.n_components_,
            'singular_values': pca_model.singular_values_
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return PCA theoretical complexity - matches original analysis"""
        
        n_samples = input_size
        n_features = self.n_features
        n_components = self.n_components
        
        # Memory usage calculation (from original code)
        # Input data
        input_data_mb = (n_samples * n_features * 8) / (1024 * 1024)
        
        # PCA algorithm memory
        # SVD working memory: O(min(n_samples, n_features)² + n_features²)
        svd_memory_mb = (min(n_samples, n_features) ** 2 * 8) / (1024 * 1024)
        components_mb = (n_components * n_features * 8) / (1024 * 1024)
        
        # Transformed data
        transformed_data_mb = (n_samples * n_components * 8) / (1024 * 1024)
        
        # sklearn overhead
        sklearn_overhead_mb = min(20, input_data_mb * 0.1)
        
        total_theoretical_mb = (input_data_mb + svd_memory_mb + components_mb + 
                              transformed_data_mb + sklearn_overhead_mb)
        
        return (
            f"O(min(n,d)² × max(n,d)) [n={n_samples}, d={n_features}]",  # Time complexity
            f"O(n×d + d² + n×k) [n={n_samples}, d={n_features}, k={n_components}]",  # Space complexity
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate PCA-specific metrics"""
        
        return {
            'explained_variance_ratio': result['explained_variance_ratio'],
            'n_components_used': result['n_components_used'],
            'n_features': self.n_features,
            'n_samples': input_size,
            'components_ratio': self.n_components / self.n_features,
            'reconstruction_quality': result['explained_variance_ratio'],  # Higher is better
            'dimensionality_reduction': (self.n_features - self.n_components) / self.n_features
        }

class PCAComplexityAnalyzer(ScalingAnalyzer):
    """PCA complexity analysis with varying dimensions - from original scaling_pca.py"""
    
    def __init__(self, 
                 fixed_samples: int = 10000,
                 random_state: int = 42,
                 **kwargs):
        super().__init__(algorithm_name=f"PCA_complexity_s{fixed_samples}", **kwargs)
        
        self.fixed_samples = fixed_samples
        self.random_state = random_state
    
    def prepare_input(self, feature_size: int) -> Dict[str, Any]:
        """Generate dataset with varying feature dimensions"""
        
        # Calculate n_components as 95% of features or max 50 (from original)
        n_components = min(int(feature_size * 0.95), 50)
        
        # Generate synthetic dataset
        X, y = make_classification(
            n_samples=self.fixed_samples,
            n_features=feature_size,
            n_informative=int(feature_size * 0.7),
            n_redundant=int(feature_size * 0.2),
            n_classes=2,
            n_clusters_per_class=1,
            random_state=self.random_state + feature_size
        )
        
        # Standardize
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        return {
            'X': X_scaled,
            'y': y,
            'n_features': feature_size,
            'n_components': n_components,
            'n_samples': self.fixed_samples
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute PCA with varying feature dimensions"""
        
        X = input_data['X']
        n_components = input_data['n_components']
        
        # Initialize and fit PCA
        pca_model = PCA(n_components=n_components, random_state=self.random_state)
        pca_model.fit(X)
        X_transformed = pca_model.transform(X)
        
        explained_variance = np.sum(pca_model.explained_variance_ratio_)
        
        return {
            'pca_model': pca_model,
            'X_transformed': X_transformed,
            'explained_variance_ratio': explained_variance,
            'n_components_used': n_components
        }
    
    def get_theoretical_complexity(self, feature_size: int) -> Tuple[str, str, float]:
        """Theoretical complexity for varying feature dimensions"""
        
        n_samples = self.fixed_samples
        n_features = feature_size
        n_components = min(int(feature_size * 0.95), 50)
        
        # Memory calculation
        input_data_mb = (n_samples * n_features * 8) / (1024 * 1024)
        pca_algorithm_mb = (min(n_samples, n_features) ** 2 * 8) / (1024 * 1024)
        transformed_data_mb = (n_samples * n_components * 8) / (1024 * 1024)
        
        total_mb = input_data_mb + pca_algorithm_mb + transformed_data_mb
        
        return (
            f"O(n×d² + d³) [n={n_samples}, d={n_features}]",
            f"O(n×d + d²) [n={n_samples}, d={n_features}]",
            total_mb
        )
    
    def calculate_custom_metrics(self, feature_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate complexity-specific metrics"""
        
        return {
            'n_features': feature_size,
            'n_components': input_data['n_components'],
            'n_samples': self.fixed_samples,
            'explained_variance_ratio': result['explained_variance_ratio'],
            'feature_complexity': feature_size ** 2,  # Quadratic in features
            'component_efficiency': result['explained_variance_ratio'] / input_data['n_components']
        }

def main():
    """Run PCA scaling analysis with original parameters"""
    
    print("=== PCA Incremental Scaling Analysis (Original Parameters) ===")
    
    # Original PCA parameters from scaling_pca.py:
    # - Sample sizes: 1,000 to 20,000 (step: 1,000) -> using 250 to 5000 step 250 for demo
    # - Fixed features: 150, components: 50
    pca_analyzer = PCAScalingAnalyzer(
        n_features=150,
        n_components=50,
        output_dir="results/pca_incremental",
        enable_gpu_tracking=False
    )
    
    # Original sample size range: step_size to max_samples
    # Using smaller range for demo but keeping the pattern
    sample_sizes = list(range(250, 5001, 250))  # 250, 500, 750, ..., 5000
    
    print(f"Sample sizes: {len(sample_sizes)} points from {min(sample_sizes)} to {max(sample_sizes)}")
    print(f"Fixed: {pca_analyzer.n_features} features, {pca_analyzer.n_components} components")
    
    pca_results = pca_analyzer.run_scaling_analysis(sample_sizes)
    
    print("\n=== PCA Complexity Analysis (Varying Feature Dimensions) ===")
    
    # Original complexity analysis: varying feature dimensions
    # From original: features 50 to 500 (step: 50), fixed samples
    complexity_analyzer = PCAComplexityAnalyzer(
        fixed_samples=2000,  # Fixed sample size
        output_dir="results/pca_complexity",
        enable_gpu_tracking=False
    )
    
    # Original feature dimension range: 50 to 500 step 50
    # Using smaller range: 50 to 250 step 25
    feature_sizes = list(range(50, 251, 25))  # 50, 75, 100, ..., 250
    
    print(f"Feature dimensions: {len(feature_sizes)} points from {min(feature_sizes)} to {max(feature_sizes)}")
    print(f"Fixed: {complexity_analyzer.fixed_samples} samples")
    
    complexity_results = complexity_analyzer.run_scaling_analysis(feature_sizes)
    
    # Scaling analysis
    print("\n=== Scaling Analysis Results ===")
    
    # Incremental scaling (varying samples)
    pca_scaling = pca_analyzer.analyze_scaling_behavior()
    if pca_scaling:
        print(f"PCA (varying samples) scaling factor: {pca_scaling['mean_scaling_factor']:.2f}")
        print(f"Expected: ~1.0-2.0 for O(min(n,d)² × max(n,d)) where n > d")
    
    # Complexity scaling (varying features)
    complexity_scaling = complexity_analyzer.analyze_scaling_behavior()
    if complexity_scaling:
        print(f"PCA (varying features) scaling factor: {complexity_scaling['mean_scaling_factor']:.2f}")
        print(f"Expected: ~2.0-3.0 for O(n×d² + d³) where d varies")
    
    print(f"\nPCA incremental analysis completed! Results: results/pca_incremental/")
    print(f"PCA complexity analysis completed! Results: results/pca_complexity/")

if __name__ == "__main__":
    main() 