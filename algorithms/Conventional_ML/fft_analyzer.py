"""
FFT Scaling Analysis using Unified Framework

Migrated from conventional_algo/scaling_fft.py
Separated from dft_analyzer.py for better organization
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer
import numpy as np
from typing import Any, Tuple, Dict

class FFTScalingAnalyzer(ScalingAnalyzer):
    """FFT scaling analysis - migrated from original scaling_fft.py"""
    
    def __init__(self, **kwargs):
        super().__init__(algorithm_name="FFT", **kwargs)
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate random complex signal for FFT - matches original"""
        real_part = np.random.randn(input_size)
        imag_part = np.random.randn(input_size)
        return real_part + 1j * imag_part
    
    def run_algorithm(self, input_data: np.ndarray) -> np.ndarray:
        """Execute FFT algorithm using NumPy"""
        return np.fft.fft(input_data)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return FFT theoretical complexity - matches original analysis"""
        
        # Memory usage calculation from original code
        input_signal_mb = (input_size * 16) / (1024 * 1024)  # Complex numbers
        fft_result_mb = (input_size * 16) / (1024 * 1024)
        algorithm_overhead_mb = input_size * 16 / (1024 * 1024)  # NumPy overhead
        total_theoretical_mb = input_signal_mb + fft_result_mb + algorithm_overhead_mb
        
        return (
            f"O(N log N) [N={input_size}]",
            f"O(N) [N={input_size}]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate FFT-specific metrics"""
        
        # Verify correctness with IFFT reconstruction
        reconstructed = np.fft.ifft(result)
        max_error = np.max(np.abs(input_data - reconstructed))
        is_correct = max_error < 1e-10
        
        return {
            'correctness_verified': is_correct,
            'max_reconstruction_error': float(max_error),
            'operations_count': int(input_size * np.log2(input_size)) if input_size > 0 else 0,
            'algorithm_type': 'fft'
        }

def main():
    """Run FFT scaling analysis with original parameters"""
    
    print("=== FFT Scaling Analysis (Original Parameters) ===")
    print("Migrated from conventional_algo/scaling_fft.py")
    
    # Original FFT parameters: powers of 2 for optimal performance
    # From original: min_power=4, max_power=20 (2^4 to 2^20)
    fft_analyzer = FFTScalingAnalyzer(
        output_dir="results/fft",
        enable_gpu_tracking=False
    )
    
    # Original input range: powers of 2 from 2^4 to 2^20
    # Using smaller range for demo: 2^4 to 2^12
    min_power, max_power = 4, 12
    fft_input_sizes = [2**i for i in range(min_power, max_power + 1)]
    
    print(f"FFT Input sizes: powers of 2 from 2^{min_power} to 2^{max_power}")
    print(f"Sizes: {fft_input_sizes}")
    print(f"Original range: 2^4 to 2^20 (demo uses smaller range for speed)")
    
    # Run FFT scaling analysis
    fft_results = fft_analyzer.run_scaling_analysis(fft_input_sizes)
    
    # Analyze scaling behavior
    fft_scaling = fft_analyzer.analyze_scaling_behavior()
    if fft_scaling:
        print(f"\n=== FFT Scaling Analysis ===")
        print(f"Mean scaling factor: {fft_scaling['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {fft_scaling['std_scaling_factor']:.2f}")
        print(f"Expected: ~1.0-1.3 for FFT (O(N log N))")
    
    # Print sample results
    print(f"\n=== Sample Results ===")
    for i, result in enumerate(fft_results[:5]):  # First 5 results
        custom_metrics = result.custom_metrics
        
        print(f"Size 2^{min_power + i:2d} ({result.input_size:4d}): {result.execution_time_ms:7.2f}ms, "
              f"Memory: +{result.memory_increment_mb:6.2f}MB, "
              f"Correct: {custom_metrics.get('correctness_verified', False)}")
    
    if len(fft_results) > 5:
        print(f"... and {len(fft_results) - 5} more results")
    
    print(f"\nFFT analysis completed! Results saved to: results/fft/")
    print(f"\nKey advantages of FFT:")
    print(f"- Time complexity: O(N log N) vs O(N²) for naive DFT")
    print(f"- Optimized for power-of-2 input sizes")
    print(f"- Built-in NumPy implementation for high performance")

if __name__ == "__main__":
    main() 