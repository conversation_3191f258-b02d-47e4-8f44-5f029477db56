"""
SVM Scaling Analysis using Unified Framework
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnalyzer, ScalingMetrics
from sklearn.svm import SVC
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from typing import Any, Tuple, Dict
import numpy as np

class SVMScalingAnalyzer(ScalingAnalyzer):
    """SVM scaling analysis implementation using unified framework"""
    
    def __init__(self, 
                 n_features: int = 20,
                 kernel: str = 'rbf',
                 C: float = 1.0,
                 test_size: float = 0.2,
                 random_state: int = 42,
                 **kwargs):
        super().__init__(algorithm_name=f"SVM_{kernel}", **kwargs)
        
        self.n_features = n_features
        self.kernel = kernel
        self.C = C
        self.test_size = test_size
        self.random_state = random_state
        
        # Generate master dataset once for consistency
        print("Generating master dataset for consistent subsampling...")
        self.master_X, self.master_y = make_classification(
            n_samples=100000,  # Large master dataset
            n_features=n_features,
            n_informative=int(n_features * 0.7),
            n_redundant=int(n_features * 0.2),
            n_classes=2,
            n_clusters_per_class=1,
            random_state=random_state
        )
        
        # Pre-split and pre-scale master dataset
        self.master_X_train, self.master_X_test, self.master_y_train, self.master_y_test = train_test_split(
            self.master_X, self.master_y, 
            test_size=test_size, 
            random_state=random_state, 
            stratify=self.master_y
        )
        
        # Pre-fit scaler on master dataset
        self.scaler = StandardScaler()
        self.master_X_train = self.scaler.fit_transform(self.master_X_train)
        self.master_X_test = self.scaler.transform(self.master_X_test)
        
        print(f"Master dataset prepared: {len(self.master_X_train)} train, {len(self.master_X_test)} test samples")
    
    def prepare_input(self, input_size: int) -> Dict[str, Any]:
        """Prepare SVM input data by subsampling master dataset"""
        train_size = int(input_size * (1 - self.test_size))
        test_size = int(input_size * self.test_size)
        
        # Subsample from master dataset
        X_train = self.master_X_train[:train_size].copy()
        X_test = self.master_X_test[:test_size].copy()
        y_train = self.master_y_train[:train_size].copy()
        y_test = self.master_y_test[:test_size].copy()
        
        return {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'train_size': train_size,
            'test_size': test_size
        }
    
    def run_algorithm(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute SVM training and prediction"""
        
        # Initialize SVM
        svm_model = SVC(
            kernel=self.kernel,
            C=self.C,
            gamma='scale',
            random_state=self.random_state
        )
        
        # Train SVM
        svm_model.fit(input_data['X_train'], input_data['y_train'])
        
        # Make predictions
        train_predictions = svm_model.predict(input_data['X_train'])
        test_predictions = svm_model.predict(input_data['X_test'])
        
        # Calculate accuracies
        train_accuracy = np.mean(train_predictions == input_data['y_train'])
        test_accuracy = np.mean(test_predictions == input_data['y_test'])
        
        return {
            'model': svm_model,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'n_support_vectors': len(svm_model.support_),
            'support_vector_ratio': len(svm_model.support_) / len(input_data['y_train'])
        }
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return SVM theoretical complexity analysis"""
        train_size = int(input_size * (1 - self.test_size))
        test_size = int(input_size * self.test_size)
        
        # Theoretical memory calculation (in MB)
        train_data_mb = (train_size * self.n_features * 8) / (1024 * 1024)
        test_data_mb = (test_size * self.n_features * 8) / (1024 * 1024)
        
        # Estimate support vectors (typically 10-50% of training data)
        estimated_support_vectors = int(train_size * 0.3)  # Conservative estimate
        support_vectors_mb = (estimated_support_vectors * self.n_features * 8) / (1024 * 1024)
        
        # Kernel cache (limited to reasonable size)
        cache_size_mb = min(200, (min(train_size, 2000) ** 2 * 8) / (1024 * 1024))
        
        # Working memory and overhead
        working_memory_mb = min(50, train_size * 8 / (1024 * 1024))
        sklearn_overhead_mb = min(20, train_data_mb * 0.1)
        
        total_theoretical_mb = (train_data_mb + test_data_mb + support_vectors_mb + 
                              cache_size_mb + working_memory_mb + sklearn_overhead_mb)
        
        return (
            f"O(n²) to O(n³) [n={train_size}]",  # Time complexity
            f"O(n×d + s×d + cache) [n={train_size}, d={self.n_features}, s≈{estimated_support_vectors}]",  # Space complexity
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate SVM-specific metrics"""
        return {
            'train_accuracy': result['train_accuracy'],
            'test_accuracy': result['test_accuracy'],
            'n_support_vectors': result['n_support_vectors'],
            'support_vector_ratio': result['support_vector_ratio'],
            'train_size': input_data['train_size'],
            'test_size': input_data['test_size'],
            'n_features': self.n_features,
            'kernel': self.kernel,
            'C': self.C
        }

def main():
    """Run SVM scaling analysis using unified framework"""
    
    # Initialize analyzer
    analyzer = SVMScalingAnalyzer(
        n_features=20,
        kernel='rbf',
        output_dir="results/svm",
        enable_gpu_tracking=False  # SVM is CPU-only
    )
    
    # Original SVM parameters: 250 to 50,000 samples (step size: 250)
    # Using smaller range for demo but keeping original pattern
    input_sizes = list(range(250, 5001, 250))  # 250 to 5,000 in steps of 250 (demo range)
    
    # Run scaling analysis
    results = analyzer.run_scaling_analysis(input_sizes)
    
    # Analyze scaling behavior
    scaling_analysis = analyzer.analyze_scaling_behavior()
    
    if scaling_analysis:
        print(f"\nScaling Analysis:")
        print(f"Mean scaling factor: {scaling_analysis['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {scaling_analysis['std_scaling_factor']:.2f}")
        print(f"Expected: 2.0-3.0 for SVM (O(n²) to O(n³))")
    
    print(f"\nSVM scaling analysis completed!")

if __name__ == "__main__":
    main() 