"""
DFT Scaling Analysis using Unified Framework

Migrated from conventional_algo/scaling_dft.py
FFT has been separated to fft_analyzer.py for better organization
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
import cmath
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.scaling_framework import ScalingAnaly<PERSON>
import numpy as np
from typing import Any, Tuple, Dict

class DFTScalingAnalyzer(ScalingAnalyzer):
    """Naive DFT scaling analysis - migrated from original scaling_dft.py"""
    
    def __init__(self, **kwargs):
        super().__init__(algorithm_name="DFT_naive", **kwargs)
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate random complex signal - matches original implementation"""
        real_part = np.random.randn(input_size)
        imag_part = np.random.randn(input_size)
        return real_part + 1j * imag_part
    
    def naive_dft(self, signal: np.ndarray) -> np.ndarray:
        """
        Naive implementation of Discrete Fourier Transform.
        Time complexity: O(N²) - exact copy from original
        
        DFT formula: X[k] = Σ(n=0 to N-1) x[n] * e^(-2πi*k*n/N)
        """
        N = len(signal)
        dft_result = np.zeros(N, dtype=complex)
        
        for k in range(N):
            for n in range(N):
                # Calculate the complex exponential: e^(-2πi*k*n/N)
                angle = -2 * np.pi * k * n / N
                complex_exp = cmath.exp(1j * angle)
                dft_result[k] += signal[n] * complex_exp
                
        return dft_result
    
    def run_algorithm(self, input_data: np.ndarray) -> np.ndarray:
        """Execute naive DFT algorithm"""
        return self.naive_dft(input_data)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return DFT theoretical complexity - matches original analysis"""
        
        # Memory usage calculation from original code
        input_signal_mb = (input_size * 16) / (1024 * 1024)  # Complex numbers = 16 bytes
        dft_result_mb = (input_size * 16) / (1024 * 1024)
        total_theoretical_mb = input_signal_mb + dft_result_mb
        
        return (
            f"O(N²) [N={input_size}]",
            f"O(N) [N={input_size}]", 
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate DFT-specific metrics - matches original correctness verification"""
        
        # Verify correctness with IDFT reconstruction (from original code)
        reconstructed = self.naive_idft(result)
        max_error = np.max(np.abs(input_data - reconstructed))
        is_correct = max_error < 1e-10
        
        return {
            'correctness_verified': is_correct,
            'max_reconstruction_error': float(max_error),
            'operations_count': input_size ** 2,  # O(N²) operations
            'algorithm_type': 'naive_dft'
        }
    
    def naive_idft(self, dft_signal: np.ndarray) -> np.ndarray:
        """
        Naive implementation of Inverse Discrete Fourier Transform.
        From original code - used for correctness verification
        """
        N = len(dft_signal)
        idft_result = np.zeros(N, dtype=complex)
        
        for n in range(N):
            for k in range(N):
                angle = 2 * np.pi * k * n / N
                complex_exp = cmath.exp(1j * angle)
                idft_result[n] += dft_signal[k] * complex_exp
                
        return idft_result / N



def main():
    """Run DFT scaling analysis with original parameters"""
    
    print("=== DFT Scaling Analysis (Original Parameters) ===")
    print("Migrated from conventional_algo/scaling_dft.py")
    print("Note: FFT analysis is now in fft_analyzer.py")
    
    # Original DFT parameters: step_size to max_length with step increments
    # From original: max_length=2**16 (65,536), step_size=256
    dft_analyzer = DFTScalingAnalyzer(
        output_dir="results/dft_naive",
        enable_gpu_tracking=False
    )
    
    # Original input range: 256 to 65,536 with step size 256
    # Using smaller range for demo, but keeping the step pattern
    dft_input_sizes = list(range(256, 4097, 256))  # 256, 512, 768, ..., 4096
    
    print(f"DFT Input sizes: {len(dft_input_sizes)} points from {min(dft_input_sizes)} to {max(dft_input_sizes)}")
    print(f"Original range: 256-65,536 with step 256 (demo uses smaller range for speed)")
    
    # Run DFT scaling analysis
    dft_results = dft_analyzer.run_scaling_analysis(dft_input_sizes)
    
    # Analyze scaling behavior
    dft_scaling = dft_analyzer.analyze_scaling_behavior()
    if dft_scaling:
        print(f"\n=== DFT Scaling Analysis ===")
        print(f"Mean scaling factor: {dft_scaling['mean_scaling_factor']:.2f}")
        print(f"Standard deviation: {dft_scaling['std_scaling_factor']:.2f}")
        print(f"Expected: ~2.0 for naive DFT (O(N²))")
    
    # Print sample results
    print(f"\n=== Sample Results ===")
    for i, result in enumerate(dft_results[:5]):  # First 5 results
        custom_metrics = result.custom_metrics
        
        print(f"Size {result.input_size:4d}: {result.execution_time_ms:7.2f}ms, "
              f"Memory: +{result.memory_increment_mb:6.2f}MB, "
              f"Correct: {custom_metrics.get('correctness_verified', False)}")
    
    if len(dft_results) > 5:
        print(f"... and {len(dft_results) - 5} more results")
    
    print(f"\nDFT analysis completed! Results saved to: results/dft_naive/")
    print(f"\nKey characteristics of naive DFT:")
    print(f"- Time complexity: O(N²) - double nested loops")
    print(f"- Space complexity: O(N) - input and output arrays")
    print(f"- Uses direct mathematical formula implementation")
    print(f"- Correctness verified with IDFT reconstruction")
    print(f"\n💡 To run FFT analysis, use: python algorithms/Conventional_ML/fft_analyzer.py")

if __name__ == "__main__":
    main() 