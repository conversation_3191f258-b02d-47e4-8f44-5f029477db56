# Wavelet Transform Scaling Analysis

## Overview

This module provides comprehensive scaling analysis for various wavelet transform operations, measuring both time and space complexity across different input sizes. It supports multiple wavelet types and transform methods.

## Supported Transform Types

### 1. Discrete Wavelet Transform (DWT)
- **Algorithm**: 1D multi-level discrete wavelet decomposition
- **Time Complexity**: O(N) - Linear with input size
- **Space Complexity**: O(N) - Proportional to input size
- **Use Case**: Signal compression, denoising, feature extraction

### 2. Continuous Wavelet Transform (CWT)
- **Algorithm**: Continuous wavelet analysis with multiple scales
- **Time Complexity**: O(N × M) - N is signal length, M is number of scales
- **Space Complexity**: O(N × M) - Coefficient matrix storage
- **Use Case**: Time-frequency analysis, pattern recognition

### 3. Stationary Wavelet Transform (SWT)
- **Algorithm**: Translation-invariant wavelet decomposition
- **Time Complexity**: O(N) - Linear with input size
- **Space Complexity**: O(N × L) - L is number of levels
- **Use Case**: Signal analysis where translation invariance is important

### 4. 2D Discrete Wavelet Transform (DWT2D)
- **Algorithm**: 2D wavelet decomposition for images
- **Time Complexity**: O(N²) - For N×N images
- **Space Complexity**: O(N²) - Image coefficient storage
- **Use Case**: Image compression, texture analysis

## Supported Wavelets

### Orthogonal Wavelets
- **Daubechies**: `db1` (Haar), `db4`, `db8`, `db16`, etc.
- **Coiflets**: `coif2`, `coif4`, `coif6`
- **Symlets**: `sym4`, `sym8`, `sym16`

### Biorthogonal Wavelets
- **Biorthogonal**: `bior2.2`, `bior4.4`, `bior6.8`

### CWT-Specific Wavelets
- **Morlet**: `morl` - Good for time-frequency analysis
- **Mexican Hat**: `mexh` - Good for edge detection
- **Complex Gaussian**: `cgau1`, `cgau2`, etc.

## Usage Examples

### Basic DWT Analysis

```python
from algorithms.Spectral_Methods.wavelet_analyzer import WaveletTransformScalingAnalyzer

# Create analyzer for Daubechies-4 DWT
analyzer = WaveletTransformScalingAnalyzer(
    wavelet='db4',
    transform_type='dwt',
    output_dir='results/wavelet_db4_dwt'
)

# Run scaling analysis
input_sizes = [100, 500, 1000, 2000, 5000]
results = analyzer.run_scaling_analysis(input_sizes)

# Analyze scaling behavior
scaling = analyzer.analyze_scaling_behavior()
print(f"Scaling factor: {scaling['mean_scaling_factor']:.2f}")
```

### CWT Analysis

```python
# Create analyzer for Morlet CWT
analyzer = WaveletTransformScalingAnalyzer(
    wavelet='morl',
    transform_type='cwt',
    output_dir='results/wavelet_morl_cwt'
)

# CWT is more expensive, use smaller sizes
input_sizes = [50, 100, 200, 500, 1000]
results = analyzer.run_scaling_analysis(input_sizes)
```

### 2D Image Analysis

```python
# Create analyzer for 2D DWT
analyzer = WaveletTransformScalingAnalyzer(
    wavelet='db4',
    transform_type='dwt2d',
    levels=3,  # Specific number of decomposition levels
    output_dir='results/wavelet_2d_dwt'
)

# Use square numbers for 2D analysis
input_sizes = [100, 400, 900, 1600, 2500]  # 10x10, 20x20, 30x30, etc.
results = analyzer.run_scaling_analysis(input_sizes)
```

## Custom Metrics

The analyzer provides wavelet-specific metrics:

- **Decomposition Levels**: Number of wavelet decomposition levels
- **Total Coefficients**: Number of wavelet coefficients generated
- **Compression Ratio**: Input size / coefficient count
- **Coefficient Efficiency**: Coefficient density relative to input
- **Theoretical Operations**: Estimated computational operations

## Performance Characteristics

### Expected Scaling Factors

| Transform Type | Expected Scaling | Typical Range |
|---------------|------------------|---------------|
| DWT (1D)      | ~1.0            | 0.8 - 1.2     |
| SWT (1D)      | ~1.0            | 0.8 - 1.2     |
| CWT           | ~2.0 - 2.5      | 1.8 - 3.0     |
| DWT2D         | ~2.0            | 1.8 - 2.2     |

### Memory Usage

- **DWT/SWT**: Approximately 2× input size (input + coefficients)
- **CWT**: Approximately N × M × 8 bytes (coefficient matrix)
- **DWT2D**: Approximately 2× input size for square images

## Running the Full Analysis

```bash
# Run comprehensive wavelet analysis
cd /path/to/algorithm_scaling
python -m algorithms.Spectral_Methods.wavelet_analyzer
```

This will test multiple wavelet types and transform methods, generating detailed scaling reports.

## Output Files

Results are saved in CSV and JSON formats:
- `Wavelet-{wavelet}-{transform}_scaling_results.csv`
- `Wavelet-{wavelet}-{transform}_scaling_results.json`

Each result includes timing, memory usage, and wavelet-specific metrics for comprehensive analysis.
