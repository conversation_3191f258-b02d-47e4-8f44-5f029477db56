"""
Wavelet Transform Scaling Analyzer

This module provides scaling analysis for wavelet transform operations,
measuring both time and space complexity across different input sizes.
Supports multiple wavelet types and decomposition levels.
"""

import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import time
from typing import Any, Dict, Tuple
import pywt
from core.scaling_framework import ScalingAnalyzer


class WaveletTransformScalingAnalyzer(ScalingAnalyzer):
    """Wavelet Transform scaling analysis using unified framework"""
    
    def __init__(self, wavelet: str = 'db4', mode: str = 'symmetric', 
                 levels: int = None, transform_type: str = 'dwt', **kwargs):
        """
        Initialize Wavelet Transform analyzer
        
        Args:
            wavelet: Wavelet type (e.g., 'db4', 'haar', 'coif2', 'bior2.2')
            mode: Signal extension mode ('symmetric', 'periodization', 'zero', etc.)
            levels: Number of decomposition levels (None for maximum)
            transform_type: Type of transform ('dwt', 'cwt', 'swt', 'dwt2d')
        """
        super().__init__(algorithm_name=f"Wavelet-{wavelet}-{transform_type}", **kwargs)
        self.wavelet = wavelet
        self.mode = mode
        self.levels = levels
        self.transform_type = transform_type
        
        # Validate wavelet
        if wavelet not in pywt.wavelist():
            raise ValueError(f"Unsupported wavelet: {wavelet}. Available: {pywt.wavelist()}")
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate input signal for wavelet transform"""
        np.random.seed(42)  # For reproducibility
        
        if self.transform_type == 'dwt2d':
            # For 2D transform, create square image
            size = int(np.sqrt(input_size))
            if size * size != input_size:
                size = int(np.sqrt(input_size)) + 1
            signal = np.random.randn(size, size)
        else:
            # For 1D transforms
            signal = np.random.randn(input_size)
            
        return signal.astype(np.float64)
    
    def run_algorithm(self, input_data: np.ndarray) -> Any:
        """Execute wavelet transform algorithm"""
        if self.transform_type == 'dwt':
            return self._run_dwt(input_data)
        elif self.transform_type == 'cwt':
            return self._run_cwt(input_data)
        elif self.transform_type == 'swt':
            return self._run_swt(input_data)
        elif self.transform_type == 'dwt2d':
            return self._run_dwt2d(input_data)
        else:
            raise ValueError(f"Unknown transform type: {self.transform_type}")
    
    def _run_dwt(self, signal: np.ndarray) -> Tuple:
        """Discrete Wavelet Transform (1D)"""
        if self.levels is None:
            # Multi-level decomposition
            return pywt.wavedec(signal, self.wavelet, mode=self.mode)
        else:
            # Single level decomposition
            return pywt.dwt(signal, self.wavelet, mode=self.mode)
    
    def _run_cwt(self, signal: np.ndarray) -> np.ndarray:
        """Continuous Wavelet Transform"""
        # Generate scales for CWT
        scales = np.arange(1, min(32, len(signal)//4))
        if len(scales) == 0:
            scales = np.array([1])

        # Use appropriate wavelet for CWT
        cwt_wavelet = self.wavelet
        if self.wavelet not in ['cmor', 'mexh', 'morl'] and not self.wavelet.startswith('cgau'):
            # Use Morlet wavelet as default for CWT if current wavelet is not CWT-compatible
            cwt_wavelet = 'morl'

        coefficients, frequencies = pywt.cwt(signal, scales, cwt_wavelet)
        return coefficients
    
    def _run_swt(self, signal: np.ndarray) -> list:
        """Stationary Wavelet Transform"""
        # Calculate maximum levels for SWT
        max_levels = pywt.swt_max_level(len(signal))
        levels = min(self.levels or max_levels, max_levels)
        return pywt.swt(signal, self.wavelet, level=levels, norm=True)
    
    def _run_dwt2d(self, image: np.ndarray) -> Tuple:
        """2D Discrete Wavelet Transform"""
        if self.levels is None:
            # Multi-level 2D decomposition
            return pywt.wavedec2(image, self.wavelet, mode=self.mode)
        else:
            # Single level 2D decomposition
            return pywt.dwt2(image, self.wavelet, mode=self.mode)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for wavelet transform"""
        if self.transform_type == 'cwt':
            # CWT: O(N * M) where N is signal length, M is number of scales
            time_complexity = "O(N × M)"
            # Memory: input + coefficients matrix
            theoretical_memory_mb = (input_size * 8 + input_size * 32 * 8) / (1024 * 1024)
        elif self.transform_type == 'dwt2d':
            # 2D DWT: O(N²) for square image
            time_complexity = "O(N²)"
            theoretical_memory_mb = (input_size * 8 * 2) / (1024 * 1024)  # Input + coefficients
        else:
            # DWT/SWT: O(N) for 1D transforms
            time_complexity = "O(N)"
            theoretical_memory_mb = (input_size * 8 * 2) / (1024 * 1024)  # Input + coefficients
        
        space_complexity = "O(N)"
        
        return time_complexity, space_complexity, theoretical_memory_mb

    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate wavelet transform-specific metrics"""

        # Calculate coefficient statistics
        if self.transform_type == 'dwt':
            if isinstance(result, list):
                # Multi-level DWT returns list of coefficients
                total_coeffs = sum(len(coeff) if hasattr(coeff, '__len__') else 1 for coeff in result)
                max_level = len(result) - 1
            elif isinstance(result, tuple) and len(result) == 2:
                # Single level DWT returns (cA, cD)
                total_coeffs = len(result[0]) + len(result[1])
                max_level = 1
            else:
                total_coeffs = 0
                max_level = 0
        elif self.transform_type == 'cwt':
            total_coeffs = result.size
            max_level = result.shape[0]  # Number of scales
        elif self.transform_type == 'swt':
            total_coeffs = sum(len(coeff[0]) + len(coeff[1]) for coeff in result)
            max_level = len(result)
        elif self.transform_type == 'dwt2d':
            if isinstance(result, list):
                # Multi-level 2D DWT
                total_coeffs = result[0].size  # Approximation coefficients
                for detail_coeffs in result[1:]:
                    total_coeffs += sum(coeff.size for coeff in detail_coeffs)
                max_level = len(result) - 1
            else:
                # Single level 2D DWT returns (cA, (cH, cV, cD))
                total_coeffs = result[0].size + sum(coeff.size for coeff in result[1])
                max_level = 1
        else:
            total_coeffs = 0
            max_level = 0

        # Calculate compression ratio and energy metrics
        compression_ratio = input_size / total_coeffs if total_coeffs > 0 else 1.0

        # Calculate theoretical operations
        if self.transform_type == 'cwt':
            # CWT operations depend on number of scales and convolutions
            scales_count = min(32, input_size//4)
            theoretical_ops = input_size * scales_count * 10  # Approximate
        elif self.transform_type == 'dwt2d':
            # 2D DWT operations
            theoretical_ops = input_size * 4  # Approximate for 2D filtering
        else:
            # 1D DWT/SWT operations
            theoretical_ops = input_size * 2  # Approximate for filtering operations

        return {
            'input_size': input_size,
            'wavelet_type': self.wavelet,
            'transform_type': self.transform_type,
            'extension_mode': self.mode,
            'decomposition_levels': max_level,
            'total_coefficients': total_coeffs,
            'compression_ratio': compression_ratio,
            'theoretical_operations': theoretical_ops,
            'operations_per_sample': theoretical_ops / input_size if input_size > 0 else 0,
            'coefficient_efficiency': total_coeffs / input_size if input_size > 0 else 0
        }


def main():
    """Run Wavelet Transform scaling analysis"""
    print("=== Wavelet Transform Scaling Analysis ===")

    # Test different wavelet transforms
    test_configs = [
        {'wavelet': 'db4', 'transform_type': 'dwt', 'levels': None},
        {'wavelet': 'haar', 'transform_type': 'dwt', 'levels': None},
        {'wavelet': 'morl', 'transform_type': 'cwt', 'levels': None},  # Use Morlet for CWT
        {'wavelet': 'db4', 'transform_type': 'swt', 'levels': 3},
        {'wavelet': 'db4', 'transform_type': 'dwt2d', 'levels': None}
    ]

    for config in test_configs:
        print(f"\n--- Testing {config['wavelet'].upper()} {config['transform_type'].upper()} ---")

        try:
            analyzer = WaveletTransformScalingAnalyzer(
                **config,
                output_dir=f"results/wavelet_{config['wavelet']}_{config['transform_type']}",
                enable_gpu_tracking=False
            )

            # Input sizes for testing
            if config['transform_type'] == 'cwt':
                # CWT is more computationally expensive
                input_sizes = [i for i in range(100, 2100, 200)]
            elif config['transform_type'] == 'dwt2d':
                # 2D transform uses square root of input size
                input_sizes = [i*i for i in range(10, 110, 10)]
            else:
                # 1D transforms
                input_sizes = [i for i in range(100, 10100, 500)]

            print(f"Input sizes: {input_sizes[:5]}...{input_sizes[-5:]} ({len(input_sizes)} points)")

            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()

            if scaling:
                print(f"\n=== {config['transform_type'].upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")

                if config['transform_type'] == 'cwt':
                    print(f"Expected: ~2.0-2.5 for CWT (O(N × M))")
                elif config['transform_type'] == 'dwt2d':
                    print(f"Expected: ~2.0 for 2D DWT (O(N²))")
                else:
                    print(f"Expected: ~1.0 for 1D DWT/SWT (O(N))")

            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:5d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Coeffs: {custom_metrics.get('total_coefficients', 0):8,}, "
                      f"Levels: {custom_metrics.get('decomposition_levels', 0)}")

        except Exception as e:
            print(f"Error in {config['transform_type']} analysis: {e}")
            import traceback
            traceback.print_exc()

    print(f"\nWavelet Transform scaling analysis completed!")
    print("Results saved in:")
    for config in test_configs:
        print(f"- results/wavelet_{config['wavelet']}_{config['transform_type']}/")


if __name__ == "__main__":
    main()
