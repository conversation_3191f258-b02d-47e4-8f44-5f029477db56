"""
Convolution Scaling Analyzer

This module provides scaling analysis for convolution operations,
measuring both time and space complexity across different input sizes.
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import time
from typing import Any, Dict, Tuple
from scipy import signal
from core.scaling_framework import ScalingAnalyzer


class ConvolutionScalingAnalyzer(ScalingAnalyzer):
    """Convolution scaling analysis using unified framework"""
    
    def __init__(self, kernel_size: int = 5, conv_type: str = 'scipy', **kwargs):
        """
        Initialize Convolution analyzer
        
        Args:
            kernel_size: Size of convolution kernel (default: 5)
            conv_type: Type of convolution ('scipy', 'numpy', 'fft')
        """
        super().__init__(algorithm_name="Convolution", **kwargs)
        self.kernel_size = kernel_size
        self.conv_type = conv_type
        
        # Generate fixed kernel for consistent testing
        self.kernel = self._generate_kernel()
    
    def _generate_kernel(self) -> np.ndarray:
        """Generate convolution kernel (Gaussian-like)"""
        kernel = np.random.randn(self.kernel_size, self.kernel_size)
        # Normalize kernel
        return kernel / np.sum(np.abs(kernel))
    
    def prepare_input(self, input_size: int) -> np.ndarray:
        """Generate random 2D signal for convolution"""
        return np.random.randn(input_size, input_size)
    
    def run_algorithm(self, input_data: np.ndarray) -> np.ndarray:
        """Execute convolution algorithm"""
        if self.conv_type == 'scipy':
            return signal.convolve2d(input_data, self.kernel, mode='valid')
        elif self.conv_type == 'numpy':
            return self._numpy_convolution(input_data, self.kernel)
        elif self.conv_type == 'fft':
            return self._fft_convolution(input_data, self.kernel)
        else:
            raise ValueError(f"Unknown convolution type: {self.conv_type}")
    
    def _numpy_convolution(self, image: np.ndarray, kernel: np.ndarray) -> np.ndarray:
        """Naive convolution implementation using NumPy"""
        img_h, img_w = image.shape
        ker_h, ker_w = kernel.shape
        
        # Output size for 'valid' convolution
        out_h = img_h - ker_h + 1
        out_w = img_w - ker_w + 1
        
        result = np.zeros((out_h, out_w))
        
        for i in range(out_h):
            for j in range(out_w):
                result[i, j] = np.sum(image[i:i+ker_h, j:j+ker_w] * kernel)
        
        return result
    
    def _fft_convolution(self, image: np.ndarray, kernel: np.ndarray) -> np.ndarray:
        """FFT-based convolution implementation"""
        # Pad kernel to image size
        img_h, img_w = image.shape
        ker_h, ker_w = kernel.shape
        
        # Create padded kernel
        padded_kernel = np.zeros_like(image)
        padded_kernel[:ker_h, :ker_w] = kernel
        
        # FFT convolution
        fft_image = np.fft.fft2(image)
        fft_kernel = np.fft.fft2(padded_kernel)
        fft_result = fft_image * fft_kernel
        conv_result = np.real(np.fft.ifft2(fft_result))
        
        # Extract 'valid' region
        valid_h = img_h - ker_h + 1
        valid_w = img_w - ker_w + 1
        return conv_result[:valid_h, :valid_w]
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return theoretical complexity for convolution"""
        if self.conv_type == 'fft':
            time_complexity = "O(N² log N)"
            theoretical_memory_mb = (input_size * input_size * 8 * 3) / (1024 * 1024)  # Input + 2 FFT arrays
        else:
            time_complexity = "O(N² × K²)"  # N²: output size, K²: kernel size
            theoretical_memory_mb = (input_size * input_size * 8 * 2) / (1024 * 1024)  # Input + output
        
        space_complexity = "O(N²)"
        
        return time_complexity, space_complexity, theoretical_memory_mb
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate convolution-specific metrics"""
        input_elements = input_size * input_size
        kernel_elements = self.kernel_size * self.kernel_size
        output_size = input_size - self.kernel_size + 1
        output_elements = output_size * output_size
        
        # Calculate theoretical operations
        if self.conv_type == 'fft':
            # FFT operations: 2 forward FFTs + 1 inverse FFT + element-wise multiplication
            fft_ops = 3 * input_elements * np.log2(input_elements)
            mult_ops = input_elements
            theoretical_ops = int(fft_ops + mult_ops)
        else:
            # Direct convolution: output_elements × kernel_elements operations
            theoretical_ops = output_elements * kernel_elements
        
        return {
            'input_size': f'{input_size}×{input_size}',
            'kernel_size': f'{self.kernel_size}×{self.kernel_size}',
            'output_size': f'{output_size}×{output_size}',
            'convolution_type': self.conv_type,
            'input_elements': input_elements,
            'kernel_elements': kernel_elements,
            'output_elements': output_elements,
            'theoretical_operations': theoretical_ops,
            'operations_per_output': kernel_elements if self.conv_type != 'fft' else int(fft_ops / output_elements),
            'reduction_ratio': output_elements / input_elements
        }


def main():
    """Run Convolution scaling analysis"""
    print("=== Convolution Scaling Analysis ===")
    
    # Test different convolution methods
    conv_types = ['scipy', 'numpy', 'fft']
    
    for conv_type in conv_types:
        print(f"\n--- Testing {conv_type.upper()} Convolution ---")
        
        analyzer = ConvolutionScalingAnalyzer(
            kernel_size=5,
            conv_type=conv_type,
            output_dir=f"results/convolution_{conv_type}",
            enable_gpu_tracking=False
        )
        
        # Input sizes for testing (square images)
        if conv_type == 'fft':
            # FFT can handle larger sizes efficiently
            input_sizes = [i for i in range(50, 10050, 200)]
        else:
            # Direct convolution is slower, use smaller sizes
            input_sizes = [i for i in range(50, 10050, 200)]
        
        print(f"Input sizes: {input_sizes}")
        
        try:
            results = analyzer.run_scaling_analysis(input_sizes)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== {conv_type.upper()} Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                
                if conv_type == 'fft':
                    print(f"Expected: ~2.0-2.3 for FFT Convolution (O(N² log N))")
                else:
                    print(f"Expected: ~4.0 for Direct Convolution (O(N² × K²))")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:6.2f}MB, "
                      f"Ops: {custom_metrics.get('theoretical_operations', 0):10,}")
            
        except Exception as e:
            print(f"Error in {conv_type} convolution analysis: {e}")
    
    print(f"\nConvolution scaling analysis completed!")
    print("Results saved in:")
    for conv_type in conv_types:
        print(f"- results/convolution_{conv_type}/")


if __name__ == "__main__":
    main()