#!/usr/bin/env python3
"""
Batch Migration Runner - Execute All Migrated Algorithms

This script runs all migrated algorithms with their original input ranges
to verify the successful migration from the old framework to the new unified framework.
"""

import sys
import os
import time
import traceback
from pathlib import Path

# Add algorithm directories to path
sys.path.append('.')

def run_algorithm(module_name, algorithm_name, enable_analysis=True):
    """Run a single algorithm and capture results"""
    
    print(f"\n{'='*80}")
    print(f"🔄 Running {algorithm_name}")
    print(f"📁 Module: {module_name}")
    print(f"{'='*80}")
    
    start_time = time.time()
    
    try:
        # Import and run the algorithm
        module = __import__(module_name, fromlist=['main'])
        
        if hasattr(module, 'main'):
            module.main()
        else:
            print(f"❌ No main() function found in {module_name}")
            return False
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ {algorithm_name} completed successfully!")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error for {algorithm_name}: {e}")
        print("   This might be due to missing dependencies.")
        return False
        
    except Exception as e:
        print(f"❌ Error running {algorithm_name}: {e}")
        print("   Stack trace:")
        traceback.print_exc()
        return False

def run_unified_analysis():
    """Run unified analysis tools on all results"""
    
    print(f"\n{'='*80}")
    print("🔍 Running Unified Analysis Tools")
    print(f"{'='*80}")
    
    try:
        from analysis.unified_analyzer import ScalingResultsAnalyzer
        
        # Initialize analyzer
        analyzer = ScalingResultsAnalyzer("results")
        
        # List available algorithms
        algorithms = analyzer.list_available_algorithms()
        print(f"📊 Found {len(algorithms)} algorithm result sets:")
        for algo in algorithms:
            print(f"  - {algo}")
        
        if len(algorithms) >= 2:
            print(f"\n📈 Creating comparison plots...")
            
            # Create comparison plot for all algorithms
            fig = analyzer.create_comparison_plot(
                algorithms, 
                save_path="results/all_algorithms_comparison.png"
            )
            print("✅ Comparison plot saved to: results/all_algorithms_comparison.png")
            
            # Generate individual reports
            print(f"\n📝 Generating individual reports...")
            for algo in algorithms:
                try:
                    report = analyzer.generate_algorithm_report(algo)
                    report_file = f"results/{algo}_detailed_report.md"
                    with open(report_file, 'w') as f:
                        f.write(report)
                    print(f"  ✅ {algo} report: {report_file}")
                except Exception as e:
                    print(f"  ❌ Failed to generate report for {algo}: {e}")
            
            # Export summary report
            print(f"\n📋 Generating summary report...")
            analyzer.export_summary_report("results/migration_validation_summary.md")
            print("✅ Summary report: results/migration_validation_summary.md")
        
        else:
            print("⚠️  Need at least 2 algorithms for comparison analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in unified analysis: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all migrated algorithms in sequence"""
    
    print("🚀 DNN Scaling Project - Migration Validation Runner")
    print("=" * 80)
    print("This script runs all algorithms migrated to the new unified framework")
    print("with their original input ranges to validate the migration.")
    print("=" * 80)
    
    # Create results directory
    os.makedirs("results", exist_ok=True)
    
    # Define all migrated algorithms with their new categorized structure
    algorithms = [
        # 🔬 传统机器学习算法
        {
            'module': 'algorithms.Conventional_ML.svm_analyzer',
            'name': 'SVM (Support Vector Machine)',
            'category': 'Conventional_ML',
            'description': 'Original: 250-50,000 samples, step 250 | Demo: 250-10,000 samples',
            'dependencies': ['scikit-learn', 'numpy']
        },
        {
            'module': 'algorithms.Conventional_ML.dft_analyzer',
            'name': 'DFT (Discrete Fourier Transform)',
            'category': 'Conventional_ML',
            'description': 'Original: DFT 256-65,536 step 256 | Demo: 256-4,096 step 256',
            'dependencies': ['numpy']
        },
        {
            'module': 'algorithms.Conventional_ML.fft_analyzer',
            'name': 'FFT (Fast Fourier Transform)',
            'category': 'Conventional_ML',
            'description': 'Original: FFT 2^4-2^20 powers of 2 | Demo: 2^4-2^12',
            'dependencies': ['numpy']
        },
        {
            'module': 'algorithms.Conventional_ML.pca_analyzer',
            'name': 'PCA (Principal Component Analysis)',
            'category': 'Conventional_ML',
            'description': 'Original: samples 1,000-20,000, features 50-500 | Demo: smaller ranges',
            'dependencies': ['scikit-learn', 'numpy']
        },
        
        # 🧠 深度神经网络算法
        {
            'module': 'algorithms.DNN.lstm_analyzer',
            'name': 'LSTM (Long Short-Term Memory)',
            'category': 'DNN',
            'description': 'Original: sequence 1-6,000 step 200 | Demo: 200-2,000 step 200',
            'dependencies': ['torch']
        },
        {
            'module': 'algorithms.DNN.stylegan_analyzer',
            'name': 'StyleGAN (Generative Adversarial Network)',
            'category': 'DNN',
            'description': 'Original: batch 1-3200 step 50 | Demo: batch 1-50 step 5',
            'dependencies': ['torch', 'PIL', 'torchvision']
        },
        
        # 🤖 大语言模型算法
        {
            'module': 'algorithms.LLM.falcon_analyzer',
            'name': 'Falcon-7B (Large Language Model)',
            'category': 'LLM',
            'description': 'Original: sequence 1-2048 step 50 | Demo: smaller range',
            'dependencies': ['transformers', 'torch']
        },
        {
            'module': 'algorithms.LLM.gemma_analyzer',
            'name': 'Gemma-7B (Large Language Model)',
            'category': 'LLM',
            'description': 'Original: sequence 1-8192 step 50 | Demo: smaller range',
            'dependencies': ['transformers', 'torch']
        },
        {
            'module': 'algorithms.LLM.llama_analyzer',
            'name': 'LLaMA-7B (Large Language Model)',
            'category': 'LLM',
            'description': 'Original: sequence 1-4096 step 50 | Demo: smaller range',
            'dependencies': ['transformers', 'torch']
        },
        
        # 📊 稠密线性代数算法
        {
            'module': 'algorithms.Dense_Linear_Algebra.gemm_analyzer',
            'name': 'GeMM (General Matrix Multiplication)',
            'category': 'Dense_Linear_Algebra',
            'description': 'Matrix multiplication: 50×50 to 500×500 | Multiple implementations',
            'dependencies': ['numpy']
        }
    ]
    
    # Results tracking
    successful_runs = []
    failed_runs = []
    skipped_runs = []
    
    print(f"\n📋 Migration Plan: {len(algorithms)} algorithms to validate")
    for i, algo in enumerate(algorithms, 1):
        print(f"{i:2d}. {algo['name']}")
        print(f"    📝 {algo['description']}")
        print(f"    📦 Dependencies: {', '.join(algo['dependencies'])}")
    
    # Ask user for confirmation
    print(f"\n⚠️  Note: Some algorithms may take time to run or require specific dependencies.")
    print("🔄 You can interrupt (Ctrl+C) and individual failures won't stop the batch.")
    
    response = input("\n🚀 Start migration validation? [Y/n]: ").strip().lower()
    if response and response not in ['y', 'yes']:
        print("❌ Migration validation cancelled by user.")
        return
    
    start_time_total = time.time()
    
    # Run each algorithm
    for i, algo in enumerate(algorithms, 1):
        print(f"\n{'='*20} Algorithm {i}/{len(algorithms)} {'='*20}")
        
        success = run_algorithm(algo['module'], algo['name'])
        
        if success:
            successful_runs.append(algo['name'])
        else:
            failed_runs.append(algo['name'])
        
        # Brief pause between algorithms
        time.sleep(1)
    
    # Run unified analysis
    print(f"\n{'='*20} Unified Analysis {'='*20}")
    analysis_success = run_unified_analysis()
    
    # Final summary
    end_time_total = time.time()
    total_duration = end_time_total - start_time_total
    
    print(f"\n{'='*80}")
    print("🎯 MIGRATION VALIDATION SUMMARY")
    print(f"{'='*80}")
    print(f"⏱️  Total Duration: {total_duration:.2f} seconds")
    print(f"✅ Successful: {len(successful_runs)}/{len(algorithms)} algorithms")
    print(f"❌ Failed: {len(failed_runs)}/{len(algorithms)} algorithms")
    
    if successful_runs:
        print(f"\n✅ Successfully migrated and validated:")
        for algo in successful_runs:
            print(f"  - {algo}")
    
    if failed_runs:
        print(f"\n❌ Failed migrations (likely due to dependencies):")
        for algo in failed_runs:
            print(f"  - {algo}")
    
    print(f"\n📁 Results Directory Structure:")
    results_dir = Path("results")
    if results_dir.exists():
        for item in sorted(results_dir.iterdir()):
            if item.is_dir():
                csv_files = list(item.glob("*.csv"))
                json_files = list(item.glob("*.json"))
                print(f"  📂 {item.name}/ - {len(csv_files)} CSV, {len(json_files)} JSON files")
        
        # List summary files
        summary_files = list(results_dir.glob("*.md")) + list(results_dir.glob("*.png"))
        if summary_files:
            print(f"  📋 Summary files:")
            for file in sorted(summary_files):
                print(f"    - {file.name}")
    
    print(f"\n🔍 Next Steps:")
    print(f"1. 📊 Review results in the 'results/' directory")
    print(f"2. 📈 Open 'results/all_algorithms_comparison.png' for visual comparison")
    print(f"3. 📖 Read 'results/migration_validation_summary.md' for detailed analysis")
    
    if analysis_success:
        print(f"4. 🎛️  Use: python analysis/unified_analyzer.py --compare [algorithms] for custom analysis")
    
    if len(successful_runs) >= len(algorithms) * 0.8:  # 80% success rate
        print(f"\n🎉 Migration validation completed successfully!")
        print(f"   The unified framework is working correctly with {len(successful_runs)} algorithms.")
    else:
        print(f"\n⚠️  Migration validation completed with some issues.")
        print(f"   Check dependencies for failed algorithms and try running them individually.")
    
    print(f"\n🔧 Individual Algorithm Commands:")
    for algo in algorithms:
        print(f"   python -m {algo['module']}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Migration validation interrupted by user.")
        print(f"   You can run individual algorithms using:")
        print(f"   python -m algorithms.[algorithm_name]")
    except Exception as e:
        print(f"\n\n❌ Unexpected error in migration validation: {e}")
        traceback.print_exc() 