[{"input_size": 256, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704734.6931665, "execution_time_ms": 0.03319075331091881, "setup_time_ms": 0.02157408744096756, "cleanup_time_ms": 87.29943400248885, "total_time_ms": 87.35419884324074, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 4, "actual_blocks": 4, "compression_ratio": 8.0, "throughput_mbps": 31.2, "bytes_per_second": 32711877, "blocks_per_second": 511123, "bits_per_second": 261695020, "hash_rate_per_second": 127780.77, "hashing_time_ms": 0.007825903594493866, "block_processing_time_us": 1.9564758986234665, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000518798828125, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704735.3374305, "execution_time_ms": 0.03462089225649834, "setup_time_ms": 0.022274907678365707, "cleanup_time_ms": 85.3439960628748, "total_time_ms": 85.40089186280966, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 8, "actual_blocks": 8, "compression_ratio": 16.0, "throughput_mbps": 53.67, "bytes_per_second": 56278426, "blocks_per_second": 879350, "bits_per_second": 450227415, "hash_rate_per_second": 109918.8, "hashing_time_ms": 0.009097624570131302, "block_processing_time_us": 1.1372030712664127, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704735.937714, "execution_time_ms": 0.03893524408340454, "setup_time_ms": 0.024150125682353973, "cleanup_time_ms": 85.1816893555224, "total_time_ms": 85.24477472528815, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 16, "actual_blocks": 16, "compression_ratio": 32.0, "throughput_mbps": 81.12, "bytes_per_second": 85065307, "blocks_per_second": 1329145, "bits_per_second": 680522457, "hash_rate_per_second": 83071.59, "hashing_time_ms": 0.012037809938192368, "block_processing_time_us": 0.752363121137023, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.001251220703125, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704736.5383856, "execution_time_ms": 0.04678051918745041, "setup_time_ms": 0.026249326765537262, "cleanup_time_ms": 86.23502776026726, "total_time_ms": 86.30805760622025, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 32, "actual_blocks": 32, "compression_ratio": 64.0, "throughput_mbps": 105.03, "bytes_per_second": 110130124, "blocks_per_second": 1720783, "bits_per_second": 881040993, "hash_rate_per_second": 53774.47, "hashing_time_ms": 0.018596183508634567, "block_processing_time_us": 0.5811307346448302, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.002227783203125, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704737.1365156, "execution_time_ms": 0.05688238888978958, "setup_time_ms": 0.031785108149051666, "cleanup_time_ms": 84.75895505398512, "total_time_ms": 84.84762255102396, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 64, "actual_blocks": 64, "compression_ratio": 128.0, "throughput_mbps": 179.33, "bytes_per_second": 188039100, "blocks_per_second": 2938110, "bits_per_second": 1504312800, "hash_rate_per_second": 45907.98, "hashing_time_ms": 0.021782703697681427, "block_processing_time_us": 0.3403547452762723, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.004180908203125, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704737.7377374, "execution_time_ms": 0.060097500681877136, "setup_time_ms": 0.045765191316604614, "cleanup_time_ms": 97.30813978239894, "total_time_ms": 97.41400247439742, "baseline_memory_mb": 654.6953125, "peak_memory_mb": 654.6953125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 128, "actual_blocks": 128, "compression_ratio": 256.0, "throughput_mbps": 235.65, "bytes_per_second": 247102087, "blocks_per_second": 3860970, "bits_per_second": 1976816703, "hash_rate_per_second": 30163.83, "hashing_time_ms": 0.03315228968858719, "block_processing_time_us": 0.2590022631920874, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.008087158203125, "efficiency_ratio": 0.0}]