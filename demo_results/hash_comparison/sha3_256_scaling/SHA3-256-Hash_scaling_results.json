[{"input_size": 256, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704738.3585854, "execution_time_ms": 0.03788638859987259, "setup_time_ms": 0.029446091502904892, "cleanup_time_ms": 84.60375899448991, "total_time_ms": 84.67109147459269, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 2, "actual_rounds": 2, "compression_ratio": 8.0, "throughput_mbps": 36.45, "bytes_per_second": 38225268, "rounds_per_second": 298634, "bits_per_second": 305802149, "hash_rate_per_second": 149317.46, "hashing_time_ms": 0.006697140634059906, "round_processing_time_us": 3.348570317029953, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00046539306640625, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704738.9940674, "execution_time_ms": 0.037231482565402985, "setup_time_ms": 0.022227410227060318, "cleanup_time_ms": 91.87504788860679, "total_time_ms": 91.93450678139925, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 4, "actual_rounds": 4, "compression_ratio": 16.0, "throughput_mbps": 65.02, "bytes_per_second": 68182539, "rounds_per_second": 532676, "bits_per_second": 545460313, "hash_rate_per_second": 133169.02, "hashing_time_ms": 0.007509253919124603, "round_processing_time_us": 1.8773134797811508, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00070953369140625, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704739.6008594, "execution_time_ms": 0.03873370587825775, "setup_time_ms": 0.026384368538856506, "cleanup_time_ms": 0.0, "total_time_ms": 0.06511807441711426, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 8, "actual_rounds": 8, "compression_ratio": 32.0, "throughput_mbps": 99.22, "bytes_per_second": 104041599, "rounds_per_second": 812824, "bits_per_second": 832332799, "hash_rate_per_second": 101603.12, "hashing_time_ms": 0.009842216968536377, "round_processing_time_us": 1.2302771210670471, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00119781494140625, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704740.2472084, "execution_time_ms": 0.04593236371874809, "setup_time_ms": 0.029827933758497238, "cleanup_time_ms": 97.82408596947789, "total_time_ms": 97.89984626695514, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 16, "actual_rounds": 16, "compression_ratio": 64.0, "throughput_mbps": 124.95, "bytes_per_second": 131015118, "rounds_per_second": 1023555, "bits_per_second": 1048120947, "hash_rate_per_second": 63972.23, "hashing_time_ms": 0.0156317837536335, "round_processing_time_us": 0.9769864846020937, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00217437744140625, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704740.8835022, "execution_time_ms": 0.04934035241603851, "setup_time_ms": 0.03261305391788483, "cleanup_time_ms": 90.04172356799245, "total_time_ms": 90.12367697432637, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 31, "actual_rounds": 31, "compression_ratio": 128.0, "throughput_mbps": 190.08, "bytes_per_second": 199308749, "rounds_per_second": 1508440, "bits_per_second": 1594469992, "hash_rate_per_second": 48659.36, "hashing_time_ms": 0.020551029592752457, "round_processing_time_us": 0.6629364384758858, "rate_utilization": 0.972, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=31]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00412750244140625, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704741.5816286, "execution_time_ms": 0.06109047681093216, "setup_time_ms": 0.04132930189371109, "cleanup_time_ms": 101.78117686882615, "total_time_ms": 101.8835966475308, "baseline_memory_mb": 654.7578125, "peak_memory_mb": 654.7578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 61, "actual_rounds": 61, "compression_ratio": 256.0, "throughput_mbps": 256.65, "bytes_per_second": 269121235, "rounds_per_second": 2003954, "bits_per_second": 2152969884, "hash_rate_per_second": 32851.71, "hashing_time_ms": 0.030439812690019608, "round_processing_time_us": 0.4990133227872066, "rate_utilization": 0.987, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=61]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00803375244140625, "efficiency_ratio": 0.0}]