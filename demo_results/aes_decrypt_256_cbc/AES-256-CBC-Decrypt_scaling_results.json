[{"input_size": 64, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703775.3298283, "execution_time_ms": 0.11524753645062447, "setup_time_ms": 5.545523017644882, "cleanup_time_ms": 90.47954203560948, "total_time_ms": 96.14031258970499, "baseline_memory_mb": 656.2578125, "peak_memory_mb": 657.60546875, "memory_increment_mb": 1.34765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 64, "ciphertext_size_bytes": 80, "plaintext_size_bytes": 64, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 5, "actual_blocks": 5, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.25, "throughput_mbps": 7.47, "bytes_per_second": 7832886, "blocks_per_second": 489555, "decryption_time_ms": 0.010213349014520645, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.00016983695652173913}, {"input_size": 128, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703775.9950159, "execution_time_ms": 0.12994492426514626, "setup_time_ms": 0.11515896767377853, "cleanup_time_ms": 94.67174205929041, "total_time_ms": 94.91684595122933, "baseline_memory_mb": 657.60546875, "peak_memory_mb": 658.046875, "memory_increment_mb": 0.44140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 128, "ciphertext_size_bytes": 144, "plaintext_size_bytes": 128, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 9, "actual_blocks": 9, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.125, "throughput_mbps": 14.28, "bytes_per_second": 14973013, "blocks_per_second": 935813, "decryption_time_ms": 0.009617302566766739, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0009333517699115045}, {"input_size": 256, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703776.6803904, "execution_time_ms": 0.12129787355661392, "setup_time_ms": 0.12349989265203476, "cleanup_time_ms": 96.68028820306063, "total_time_ms": 96.92508596926928, "baseline_memory_mb": 658.046875, "peak_memory_mb": 658.0546875, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 256, "ciphertext_size_bytes": 272, "plaintext_size_bytes": 256, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 17, "actual_blocks": 17, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0625, "throughput_mbps": 26.49, "bytes_per_second": 27774026, "blocks_per_second": 1735876, "decryption_time_ms": 0.009793322533369064, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.099609375}, {"input_size": 512, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703777.4550655, "execution_time_ms": 0.11569121852517128, "setup_time_ms": 0.1206379383802414, "cleanup_time_ms": 85.90658521279693, "total_time_ms": 86.14291436970234, "baseline_memory_mb": 658.0546875, "peak_memory_mb": 658.0546875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 512, "ciphertext_size_bytes": 528, "plaintext_size_bytes": 512, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 33, "actual_blocks": 33, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0312, "throughput_mbps": 49.59, "bytes_per_second": 51998136, "blocks_per_second": 3249883, "decryption_time_ms": 0.010154210031032562, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703778.0657375, "execution_time_ms": 0.12586573138833046, "setup_time_ms": 0.11721672490239143, "cleanup_time_ms": 85.22118581458926, "total_time_ms": 85.46426827087998, "baseline_memory_mb": 658.0546875, "peak_memory_mb": 658.078125, "memory_increment_mb": 0.0234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 1024, "ciphertext_size_bytes": 1040, "plaintext_size_bytes": 1024, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 65, "actual_blocks": 65, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0156, "throughput_mbps": 98.23, "bytes_per_second": 103001567, "blocks_per_second": 6437597, "decryption_time_ms": 0.01009693369269371, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.126953125}, {"input_size": 2048, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703778.7075403, "execution_time_ms": 0.11830711737275124, "setup_time_ms": 0.14200573787093163, "cleanup_time_ms": 85.27068793773651, "total_time_ms": 85.5310007929802, "baseline_memory_mb": 658.078125, "peak_memory_mb": 658.078125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 2048, "ciphertext_size_bytes": 2064, "plaintext_size_bytes": 2048, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 129, "actual_blocks": 129, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0078, "throughput_mbps": 165.66, "bytes_per_second": 173710857, "blocks_per_second": 10856928, "decryption_time_ms": 0.011881813406944275, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0059051513671875, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703779.3101506, "execution_time_ms": 0.12418506667017937, "setup_time_ms": 0.13472232967615128, "cleanup_time_ms": 86.30455983802676, "total_time_ms": 86.56346723437309, "baseline_memory_mb": 658.078125, "peak_memory_mb": 658.09375, "memory_increment_mb": 0.015625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 4096, "ciphertext_size_bytes": 4112, "plaintext_size_bytes": 4096, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 257, "actual_blocks": 257, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0039, "throughput_mbps": 288.65, "bytes_per_second": 302671902, "blocks_per_second": 18916993, "decryption_time_ms": 0.013585668057203293, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0117645263671875, "efficiency_ratio": 0.7529296875}]