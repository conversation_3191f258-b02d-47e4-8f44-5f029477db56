[{"input_size": 136, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704550.347477, "execution_time_ms": 0.04009762778878212, "setup_time_ms": 0.030131079256534576, "cleanup_time_ms": 85.31465101987123, "total_time_ms": 85.38487972691655, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 136, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 1, "actual_rounds": 1, "compression_ratio": 4.25, "throughput_mbps": 14.46, "bytes_per_second": 15163167, "rounds_per_second": 111493, "bits_per_second": 121305342, "hash_rate_per_second": 111493.88, "hashing_time_ms": 0.008969102054834366, "round_processing_time_us": 8.969102054834366, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0003509521484375, "efficiency_ratio": 0.0}, {"input_size": 272, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704550.9566991, "execution_time_ms": 0.037163496017456055, "setup_time_ms": 0.021934043616056442, "cleanup_time_ms": 85.0846590474248, "total_time_ms": 85.1437565870583, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 272, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 2, "actual_rounds": 2, "compression_ratio": 8.5, "throughput_mbps": 36.92, "bytes_per_second": 38716481, "rounds_per_second": 284680, "bits_per_second": 309731849, "hash_rate_per_second": 142340.0, "hashing_time_ms": 0.007025431841611862, "round_processing_time_us": 3.512715920805931, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00048065185546875, "efficiency_ratio": 0.0}, {"input_size": 544, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704551.5516644, "execution_time_ms": 0.04691528156399727, "setup_time_ms": 0.023175030946731567, "cleanup_time_ms": 201.30726508796215, "total_time_ms": 201.37735540047288, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 544, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 4, "actual_rounds": 4, "compression_ratio": 17.0, "throughput_mbps": 44.21, "bytes_per_second": 46362056, "rounds_per_second": 340897, "bits_per_second": 370896453, "hash_rate_per_second": 85224.37, "hashing_time_ms": 0.011733733117580414, "round_processing_time_us": 2.9334332793951035, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00074005126953125, "efficiency_ratio": 0.0}, {"input_size": 1088, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704552.3879468, "execution_time_ms": 0.049553532153367996, "setup_time_ms": 0.03664195537567139, "cleanup_time_ms": 125.67763961851597, "total_time_ms": 125.76383510604501, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1088, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 8, "actual_rounds": 8, "compression_ratio": 34.0, "throughput_mbps": 101.22, "bytes_per_second": 106135287, "rounds_per_second": 780406, "bits_per_second": 849082296, "hash_rate_per_second": 97550.82, "hashing_time_ms": 0.010251067578792572, "round_processing_time_us": 1.2813834473490715, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00125885009765625, "efficiency_ratio": 0.0}, {"input_size": 2176, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704553.619149, "execution_time_ms": 0.04256404936313629, "setup_time_ms": 0.03096694126725197, "cleanup_time_ms": 100.97749019041657, "total_time_ms": 101.05102118104696, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2176, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 16, "actual_rounds": 16, "compression_ratio": 68.0, "throughput_mbps": 129.56, "bytes_per_second": 135856623, "rounds_per_second": 998945, "bits_per_second": 1086852987, "hash_rate_per_second": 62434.11, "hashing_time_ms": 0.016016885638237, "round_processing_time_us": 1.0010553523898125, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00229644775390625, "efficiency_ratio": 0.0}, {"input_size": 4352, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704554.3398123, "execution_time_ms": 0.04988284781575203, "setup_time_ms": 0.037847086787223816, "cleanup_time_ms": 99.78742199018598, "total_time_ms": 99.87515192478895, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.19921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4352, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 32, "actual_rounds": 32, "compression_ratio": 136.0, "throughput_mbps": 207.29, "bytes_per_second": 217360486, "rounds_per_second": 1598238, "bits_per_second": 1738883891, "hash_rate_per_second": 49944.96, "hashing_time_ms": 0.020022038370370865, "round_processing_time_us": 0.6256886990740895, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00437164306640625, "efficiency_ratio": 0.0}, {"input_size": 8704, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704555.0795898, "execution_time_ms": 0.061264634132385254, "setup_time_ms": 0.061040278524160385, "cleanup_time_ms": 99.52861024066806, "total_time_ms": 99.6509151533246, "baseline_memory_mb": 656.19921875, "peak_memory_mb": 656.203125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8704, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 64, "actual_rounds": 64, "compression_ratio": 272.0, "throughput_mbps": 271.83, "bytes_per_second": 285038698, "rounds_per_second": 2095872, "bits_per_second": 2280309585, "hash_rate_per_second": 32748.01, "hashing_time_ms": 0.03053620457649231, "round_processing_time_us": 0.47712819650769234, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00852203369140625, "efficiency_ratio": 2.181640625}, {"input_size": 17408, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704555.8076355, "execution_time_ms": 0.08246824145317078, "setup_time_ms": 0.06854301318526268, "cleanup_time_ms": 100.09193141013384, "total_time_ms": 100.24294266477227, "baseline_memory_mb": 656.203125, "peak_memory_mb": 656.203125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 17408, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 128, "actual_rounds": 128, "compression_ratio": 544.0, "throughput_mbps": 316.15, "bytes_per_second": 331507123, "rounds_per_second": 2437552, "bits_per_second": 2652056990, "hash_rate_per_second": 19043.38, "hashing_time_ms": 0.05251169204711914, "round_processing_time_us": 0.4102475941181183, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.01682281494140625, "efficiency_ratio": 0.0}]