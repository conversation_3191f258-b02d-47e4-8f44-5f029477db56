[{"input_size": 64, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704513.8439498, "execution_time_ms": 0.037219561636447906, "setup_time_ms": 0.020857900381088257, "cleanup_time_ms": 96.5599617920816, "total_time_ms": 96.61803925409913, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 1, "actual_blocks": 1, "compression_ratio": 2.0, "throughput_mbps": 7.72, "bytes_per_second": 8095120, "blocks_per_second": 126486, "bits_per_second": 64760962, "hash_rate_per_second": 126486.26, "hashing_time_ms": 0.007905997335910797, "block_processing_time_us": 7.905997335910797, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000335693359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704514.5618126, "execution_time_ms": 0.03449879586696625, "setup_time_ms": 0.02138270065188408, "cleanup_time_ms": 105.713055934757, "total_time_ms": 105.76893743127584, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 2, "actual_blocks": 2, "compression_ratio": 4.0, "throughput_mbps": 15.5, "bytes_per_second": 16256307, "blocks_per_second": 254004, "bits_per_second": 130050461, "hash_rate_per_second": 127002.4, "hashing_time_ms": 0.007873866707086563, "block_processing_time_us": 3.9369333535432816, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000396728515625, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704515.2656624, "execution_time_ms": 0.033490825444459915, "setup_time_ms": 0.021548941731452942, "cleanup_time_ms": 97.12884109467268, "total_time_ms": 97.18388086184859, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 4, "actual_blocks": 4, "compression_ratio": 8.0, "throughput_mbps": 28.76, "bytes_per_second": 30159963, "blocks_per_second": 471249, "bits_per_second": 241279707, "hash_rate_per_second": 117812.36, "hashing_time_ms": 0.008488073945045471, "block_processing_time_us": 2.122018486261368, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000518798828125, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704515.9517748, "execution_time_ms": 0.03451388329267502, "setup_time_ms": 0.02252170816063881, "cleanup_time_ms": 96.82513168081641, "total_time_ms": 96.88216727226973, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 8, "actual_blocks": 8, "compression_ratio": 16.0, "throughput_mbps": 50.08, "bytes_per_second": 52507718, "blocks_per_second": 820433, "bits_per_second": 420061748, "hash_rate_per_second": 102554.14, "hashing_time_ms": 0.00975094735622406, "block_processing_time_us": 1.2188684195280075, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704516.6301887, "execution_time_ms": 0.036168936640024185, "setup_time_ms": 0.025146175175905228, "cleanup_time_ms": 96.3533753529191, "total_time_ms": 96.41469046473503, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 16, "actual_blocks": 16, "compression_ratio": 32.0, "throughput_mbps": 76.02, "bytes_per_second": 79709411, "blocks_per_second": 1245459, "bits_per_second": 637675295, "hash_rate_per_second": 77841.22, "hashing_time_ms": 0.01284666359424591, "block_processing_time_us": 0.8029164746403694, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.001251220703125, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704517.3102603, "execution_time_ms": 0.04477398470044136, "setup_time_ms": 0.02623116597533226, "cleanup_time_ms": 98.33363443613052, "total_time_ms": 98.4046395868063, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 32, "actual_blocks": 32, "compression_ratio": 64.0, "throughput_mbps": 98.84, "bytes_per_second": 103644400, "blocks_per_second": 1619443, "bits_per_second": 829155207, "hash_rate_per_second": 50607.62, "hashing_time_ms": 0.019759871065616608, "block_processing_time_us": 0.617495970800519, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.002227783203125, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704517.9963355, "execution_time_ms": 0.05118260160088539, "setup_time_ms": 0.05814572796225548, "cleanup_time_ms": 96.93074272945523, "total_time_ms": 97.04007105901837, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.69921875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 4096, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 64, "actual_blocks": 64, "compression_ratio": 128.0, "throughput_mbps": 128.59, "bytes_per_second": 134834953, "blocks_per_second": 2106796, "bits_per_second": 1078679627, "hash_rate_per_second": 32918.69, "hashing_time_ms": 0.030377879738807678, "block_processing_time_us": 0.47465437091886997, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.004180908203125, "efficiency_ratio": 0.0}, {"input_size": 8192, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704518.6746662, "execution_time_ms": 0.05983589217066765, "setup_time_ms": 0.04722271114587784, "cleanup_time_ms": 96.95693990215659, "total_time_ms": 97.06399850547314, "baseline_memory_mb": 655.69921875, "peak_memory_mb": 655.703125, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 8192, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 128, "actual_blocks": 128, "compression_ratio": 256.0, "throughput_mbps": 234.07, "bytes_per_second": 245436974, "blocks_per_second": 3834952, "bits_per_second": 1963495798, "hash_rate_per_second": 29960.57, "hashing_time_ms": 0.03337720409035683, "block_processing_time_us": 0.2607594069559127, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.008087158203125, "efficiency_ratio": 2.0703125}, {"input_size": 16384, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704519.359355, "execution_time_ms": 0.07647229358553886, "setup_time_ms": 0.07612677291035652, "cleanup_time_ms": 96.91498894244432, "total_time_ms": 97.06758800894022, "baseline_memory_mb": 655.703125, "peak_memory_mb": 655.703125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 16384, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 256, "actual_blocks": 256, "compression_ratio": 512.0, "throughput_mbps": 306.95, "bytes_per_second": 321859307, "blocks_per_second": 5029051, "bits_per_second": 2574874462, "hash_rate_per_second": 19644.73, "hashing_time_ms": 0.050904229283332825, "block_processing_time_us": 0.19884464563801885, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.015899658203125, "efficiency_ratio": 0.0}, {"input_size": 32768, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704520.0579073, "execution_time_ms": 0.109135452657938, "setup_time_ms": 0.10304804891347885, "cleanup_time_ms": 98.20266906172037, "total_time_ms": 98.41485256329179, "baseline_memory_mb": 655.703125, "peak_memory_mb": 655.703125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32768, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 512, "actual_blocks": 512, "compression_ratio": 1024.0, "throughput_mbps": 374.66, "bytes_per_second": 392864687, "blocks_per_second": 6138510, "bits_per_second": 3142917497, "hash_rate_per_second": 11989.28, "hashing_time_ms": 0.08340785279870033, "block_processing_time_us": 0.1629059624974616, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=512]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.031524658203125, "efficiency_ratio": 0.0}]