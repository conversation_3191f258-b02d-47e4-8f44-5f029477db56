[{"input_size": 64, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703888.0123386, "execution_time_ms": 0.11439481750130653, "setup_time_ms": 0.10898802429437637, "cleanup_time_ms": 85.59101168066263, "total_time_ms": 85.81439452245831, "baseline_memory_mb": 657.359375, "peak_memory_mb": 657.359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 64, "ciphertext_size_bytes": 80, "plaintext_size_bytes": 64, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 5, "actual_blocks": 5, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.25, "throughput_mbps": 8.1, "bytes_per_second": 8496893, "blocks_per_second": 531055, "decryption_time_ms": 0.00941520556807518, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703888.619497, "execution_time_ms": 0.1310058869421482, "setup_time_ms": 0.10935124009847641, "cleanup_time_ms": 88.61253969371319, "total_time_ms": 88.85289682075381, "baseline_memory_mb": 657.359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.07421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 128, "ciphertext_size_bytes": 144, "plaintext_size_bytes": 128, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 9, "actual_blocks": 9, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.125, "throughput_mbps": 13.71, "bytes_per_second": 14375791, "blocks_per_second": 898486, "decryption_time_ms": 0.01001683995127678, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.005550986842105263}, {"input_size": 256, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703889.2572846, "execution_time_ms": 0.11720238253474236, "setup_time_ms": 0.11459970846772194, "cleanup_time_ms": 87.79244683682919, "total_time_ms": 88.02424892783165, "baseline_memory_mb": 657.43359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 256, "ciphertext_size_bytes": 272, "plaintext_size_bytes": 256, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 17, "actual_blocks": 17, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0625, "throughput_mbps": 22.92, "bytes_per_second": 24034709, "blocks_per_second": 1502169, "decryption_time_ms": 0.011316966265439987, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703889.9333065, "execution_time_ms": 0.11549443006515503, "setup_time_ms": 0.1207888126373291, "cleanup_time_ms": 91.25598892569542, "total_time_ms": 91.4922721683979, "baseline_memory_mb": 657.43359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 512, "ciphertext_size_bytes": 528, "plaintext_size_bytes": 512, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 33, "actual_blocks": 33, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0312, "throughput_mbps": 48.27, "bytes_per_second": 50619257, "blocks_per_second": 3163703, "decryption_time_ms": 0.01043081283569336, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703890.590602, "execution_time_ms": 0.116786640137434, "setup_time_ms": 0.13291509822010994, "cleanup_time_ms": 86.0558608546853, "total_time_ms": 86.30556259304285, "baseline_memory_mb": 657.43359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 1024, "ciphertext_size_bytes": 1040, "plaintext_size_bytes": 1024, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 65, "actual_blocks": 65, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0156, "throughput_mbps": 84.06, "bytes_per_second": 88143618, "blocks_per_second": 5508976, "decryption_time_ms": 0.011798925697803497, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703891.2049973, "execution_time_ms": 0.11899657547473907, "setup_time_ms": 0.12443959712982178, "cleanup_time_ms": 86.04335691779852, "total_time_ms": 86.28679309040308, "baseline_memory_mb": 657.43359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 2048, "ciphertext_size_bytes": 2064, "plaintext_size_bytes": 2048, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 129, "actual_blocks": 129, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0078, "throughput_mbps": 154.08, "bytes_per_second": 161560278, "blocks_per_second": 10097517, "decryption_time_ms": 0.012775417417287827, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=128]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0059051513671875, "efficiency_ratio": 0.0}, {"input_size": 4096, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703891.832423, "execution_time_ms": 0.12127729132771492, "setup_time_ms": 0.1322273164987564, "cleanup_time_ms": 85.32158844172955, "total_time_ms": 85.57509304955602, "baseline_memory_mb": 657.43359375, "peak_memory_mb": 657.43359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 4096, "ciphertext_size_bytes": 4112, "plaintext_size_bytes": 4096, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 257, "actual_blocks": 257, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0039, "throughput_mbps": 289.9, "bytes_per_second": 303984741, "blocks_per_second": 18999046, "decryption_time_ms": 0.013526994735002518, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=256]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0117645263671875, "efficiency_ratio": 0.0}]