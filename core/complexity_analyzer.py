import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from sklearn.metrics import r2_score
from typing import Dict, Tuple, List, Callable
import os

class ComplexityAnalyzer:
    """复杂度分析器 - 自动拟合时间和空间复杂度"""
    
    def __init__(self, csv_file_path: str, output_dir: str = "complexity_analysis"):
        self.csv_file = csv_file_path
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 预定义复杂度函数
        self.complexity_functions = {
            'O(1)': lambda n, a: a * np.ones_like(n),
            'O(log n)': lambda n, a, b: a * np.log(n) + b,
            'O(n)': lambda n, a, b: a * n + b,
            'O(n log n)': lambda n, a, b: a * n * np.log(n) + b,
            'O(n²)': lambda n, a, b: a * n**2 + b,
            'O(n³)': lambda n, a, b: a * n**3 + b,
            'O(2^n)': lambda n, a, b: a * (2**n) + b
        }
    
    def load_data(self) -> pd.DataFrame:
        """加载CSV数据"""
        return pd.read_csv(self.csv_file)
    
    def fit_complexity(self, x_data: np.ndarray, y_data: np.ndarray, is_time_complexity: bool = True) -> Dict:
        """拟合最佳复杂度函数，增强对高阶复杂度的拟合能力"""
        best_fit = None
        best_r2 = -np.inf
        
        results = {}
        
        # 对于时间复杂度，优先考虑高阶复杂度
        if is_time_complexity:
            # 为高阶复杂度提供更好的初始参数猜测
            initial_params = {
                'O(n²)': [1e-6, 0],  # 更小的系数初始值
                'O(n³)': [1e-9, 0],  # 更小的系数初始值
            }
        else:
            initial_params = {}
        
        for name, func in self.complexity_functions.items():
            try:
                # 确定参数数量
                param_count = func.__code__.co_argcount - 1  # 减去x参数
                
                # 设置初始参数
                p0 = None
                if name in initial_params:
                    p0 = initial_params[name]
                
                # 拟合参数
                popt, _ = curve_fit(func, x_data, y_data, p0=p0, maxfev=10000)
                y_pred = func(x_data, *popt)
                r2 = r2_score(y_data, y_pred)
                
                results[name] = {
                    'params': popt,
                    'r2_score': r2,
                    'equation': self._format_equation(name, popt)
                }
                
                if r2 > best_r2:
                    best_r2 = r2
                    best_fit = name
                    
            except Exception as e:
                results[name] = {'error': str(e)}
        
        # 对于时间复杂度，如果O(n³)和O(n²)的R²非常接近，优先选择O(n³)
        if is_time_complexity and 'O(n³)' in results and 'O(n²)' in results:
            r2_n3 = results['O(n³)'].get('r2_score', -np.inf)
            r2_n2 = results['O(n²)'].get('r2_score', -np.inf)
            
            # 如果O(n³)的R²接近O(n²)的R²（差距小于0.05），优先选择O(n³)
            if r2_n3 > 0.9 and best_fit == 'O(n²)' and (r2_n2 - r2_n3) < 0.05:
                best_fit = 'O(n³)'
                best_r2 = r2_n3
        
        return {
            'best_fit': best_fit,
            'best_r2': best_r2,
            'all_results': results
        }
    
    def _format_equation(self, complexity_name: str, params: np.ndarray) -> str:
        """格式化方程字符串"""
        if complexity_name == 'O(1)':
            return f"y = {params[0]:.3e}"
        elif len(params) == 2:
            return f"y = {params[0]:.3e} * {complexity_name[2:-1]} + {params[1]:.3e}"
        return f"y = {params[0]:.3e} * {complexity_name[2:-1]}"
    
    def plot_analysis(self, x_data: np.ndarray, y_data: np.ndarray, 
                     fit_results: Dict, title: str, xlabel: str, ylabel: str):
        """绘制拟合结果图"""
        plt.figure(figsize=(12, 8))
        
        # 设置中文字体支持 - 如果失败则使用英文
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
        except:
            pass
        
        # 统一使用英文标签避免字体问题
        if '时间复杂度' in title:
            title = 'Time Complexity Analysis'
            xlabel = 'Input Size'
            ylabel = 'Execution Time (ms)'
        elif '空间复杂度' in title:
            title = 'Space Complexity Analysis'
            xlabel = 'Input Size'
            ylabel = 'Memory Usage (MB)'
        
        # 原始数据点
        plt.scatter(x_data, y_data, color='blue', alpha=0.7, label='Data Points')
        
        # 最佳拟合曲线
        best_fit = fit_results['best_fit']
        if best_fit:
            best_params = fit_results['all_results'][best_fit]['params']
            best_func = self.complexity_functions[best_fit]
            equation = fit_results['all_results'][best_fit]['equation']
            
            x_smooth = np.linspace(x_data.min(), x_data.max(), 100)
            y_smooth = best_func(x_smooth, *best_params)
            
            plt.plot(x_smooth, y_smooth, 'r-', linewidth=2, 
                    label=f'{best_fit} (R² = {fit_results["best_r2"]:.4f})')
            
            # 添加方程文本到图表
            equation_text = f"Best fit: {equation}"
            plt.annotate(equation_text, xy=(0.02, 0.95), xycoords='axes fraction', 
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.5", fc="yellow", alpha=0.3))
        
        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 保存图片
        filename = title.lower().replace(' ', '_').replace('(', '').replace(')', '') + '.png'
        plt.savefig(os.path.join(self.output_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存
        
        print(f"图片已保存: {os.path.join(self.output_dir, filename)}")
    
    def analyze_csv(self):
        """分析CSV文件中的复杂度"""
        df = self.load_data()
        
        # 假设列名为: input_size, execution_time_ms, memory_increment_mb
        input_sizes = df.iloc[:, 0].values
        execution_times = df.iloc[:, 1].values
        memory_usage = df.iloc[:, 2].values
        
        print("=== 时间复杂度分析 ===")
        time_results = self.fit_complexity(input_sizes, execution_times, is_time_complexity=True)
        print(f"最佳拟合: {time_results['best_fit']} (R² = {time_results['best_r2']:.4f})")
        
        print("\n=== 空间复杂度分析 ===")
        space_results = self.fit_complexity(input_sizes, memory_usage, is_time_complexity=False)
        print(f"最佳拟合: {space_results['best_fit']} (R² = {space_results['best_r2']:.4f})")
        
        # 打印所有拟合结果，帮助分析
        print("\n时间复杂度所有拟合结果:")
        for name, result in time_results['all_results'].items():
            if 'r2_score' in result:
                print(f"{name}: R² = {result['r2_score']:.4f}, {result['equation']}")
        
        print("\n空间复杂度所有拟合结果:")
        for name, result in space_results['all_results'].items():
            if 'r2_score' in result:
                print(f"{name}: R² = {result['r2_score']:.4f}, {result['equation']}")
        
        # 绘图并保存
        self.plot_analysis(input_sizes, execution_times, time_results,
                          "时间复杂度分析", "输入规模", "执行时间 (ms)")
        
        self.plot_analysis(input_sizes, memory_usage, space_results,
                          "空间复杂度分析", "输入规模", "内存使用 (MB)")
        
        print(f"\n所有图片已保存到目录: {self.output_dir}")
        
        return time_results, space_results

# 使用示例
def analyze_lu_results():
    analyzer = ComplexityAnalyzer(
        csv_file_path="results/lu_numpy/scaling_results.csv",
        output_dir="complexity_analysis/lu_decomposition"
    )
    return analyzer.analyze_csv()
