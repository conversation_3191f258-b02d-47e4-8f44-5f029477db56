"""
Core Scaling Framework

This package contains the unified scaling analysis framework that provides
common infrastructure for measuring time and space complexity across different
algorithms and neural network models.

Key Components:
- ScalingAnalyzer: Abstract base class for all scaling analyses
- ScalingMetrics: Data structure for scaling measurement results
- MemoryTracker: Thread-safe memory usage tracking
- Timer: High-precision timing with warmup and multiple runs

Usage:
    from core.scaling_framework import ScalingAnalyzer, ScalingMetrics
"""

__version__ = "1.0.0"

from .scaling_framework import (
    ScalingAnalyzer,
    ScalingMetrics, 
    MemoryTracker,
    Timer
)

__all__ = [
    'ScalingAnalyzer',
    'ScalingMetrics',
    'MemoryTracker', 
    'Timer'
] 