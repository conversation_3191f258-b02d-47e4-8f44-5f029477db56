#!/usr/bin/env python3
"""
Test script for Sorting Algorithms Scaling Analysis

This script demonstrates how to use both MergeSortScalingAnalyzer and 
RadixSortScalingAnalyzer and compares their performance.
"""

import sys
import os
sys.path.append('.')

# Import directly from the files to avoid dependency issues
from algorithms.Sorting.merge_sort_analyzer import MergeSortScalingAnalyzer
from algorithms.Sorting.radix_sort_analyzer import RadixSortScalingAnalyzer

def test_basic_functionality():
    """Test basic functionality of both sorting algorithms"""
    print("=== Testing Basic Sorting Functionality ===")
    
    test_sizes = [50, 100, 200]
    
    for size in test_sizes:
        print(f"\nTesting with size {size}:")
        
        # Test Merge Sort
        merge_analyzer = MergeSortScalingAnalyzer(
            data_type='random',
            output_dir='test_results/merge_sort_basic',
            enable_gpu_tracking=False
        )
        
        merge_result = merge_analyzer.measure_single_run(size)
        merge_custom = merge_result.custom_metrics
        
        print(f"  Merge Sort: {merge_result.execution_time_ms:6.2f}ms, "
              f"Comparisons: {merge_custom['actual_comparisons']:5d}, "
              f"Correct: {merge_custom['correctness_verified']}")
        
        # Test Radix Sort (Base 10)
        radix_analyzer = RadixSortScalingAnalyzer(
            data_type='random',
            radix=10,
            output_dir='test_results/radix_sort_basic',
            enable_gpu_tracking=False
        )
        
        radix_result = radix_analyzer.measure_single_run(size)
        radix_custom = radix_result.custom_metrics
        
        print(f"  Radix Sort: {radix_result.execution_time_ms:6.2f}ms, "
              f"Digits: {radix_custom['num_digits']:2d}, "
              f"Extractions: {radix_custom['actual_digit_extractions']:5d}, "
              f"Correct: {radix_custom['correctness_verified']}")

def compare_algorithms():
    """Compare performance of Merge Sort vs Radix Sort"""
    print("\n=== Algorithm Performance Comparison ===")
    
    test_size = 500
    data_types = ['random', 'sorted', 'reverse']
    
    for data_type in data_types:
        print(f"\nTesting {data_type} data (size {test_size}):")
        
        # Merge Sort
        merge_analyzer = MergeSortScalingAnalyzer(
            data_type=data_type,
            enable_gpu_tracking=False
        )
        merge_result = merge_analyzer.measure_single_run(test_size)
        merge_custom = merge_result.custom_metrics
        
        # Radix Sort
        radix_analyzer = RadixSortScalingAnalyzer(
            data_type=data_type,
            radix=10,
            enable_gpu_tracking=False
        )
        radix_result = radix_analyzer.measure_single_run(test_size)
        radix_custom = radix_result.custom_metrics
        
        print(f"  Merge Sort: {merge_result.execution_time_ms:6.2f}ms "
              f"(O(n log n), comparisons: {merge_custom['actual_comparisons']})")
        print(f"  Radix Sort: {radix_result.execution_time_ms:6.2f}ms "
              f"(O(d*(n+k)), digits: {radix_custom['num_digits']})")
        
        # Speed comparison
        if radix_result.execution_time_ms > 0:
            speedup = merge_result.execution_time_ms / radix_result.execution_time_ms
            faster = "Radix" if speedup > 1 else "Merge"
            ratio = max(speedup, 1/speedup)
            print(f"  → {faster} Sort is {ratio:.2f}x faster")

def test_radix_bases():
    """Test Radix Sort with different bases"""
    print("\n=== Testing Radix Sort with Different Bases ===")
    
    test_size = 300
    bases = [2, 8, 10, 16]
    
    for base in bases:
        analyzer = RadixSortScalingAnalyzer(
            data_type='random',
            radix=base,
            enable_gpu_tracking=False
        )
        
        result = analyzer.measure_single_run(test_size)
        custom = result.custom_metrics
        
        print(f"  Base {base:2d}: {result.execution_time_ms:6.2f}ms, "
              f"Digits: {custom['num_digits']:2d}, "
              f"Extractions: {custom['actual_digit_extractions']:5d}, "
              f"Correct: {custom['correctness_verified']}")

def test_correctness():
    """Test correctness with known data"""
    print("\n=== Testing Correctness with Known Data ===")
    
    # Create test data
    test_data = [64, 34, 25, 12, 22, 11, 90, 88, 76, 50, 42]
    expected = sorted(test_data)
    
    print(f"Original: {test_data}")
    print(f"Expected: {expected}")
    
    # Test Merge Sort
    merge_analyzer = MergeSortScalingAnalyzer(data_type='random')
    merge_input = merge_analyzer.prepare_input(len(test_data))
    # Override with our test data
    merge_result = merge_analyzer.run_algorithm(test_data)
    merge_sorted = merge_result['sorted_data']
    
    print(f"Merge Sort: {merge_sorted}, Correct: {merge_sorted == expected}")
    
    # Test Radix Sort
    radix_analyzer = RadixSortScalingAnalyzer(data_type='random', radix=10)
    radix_result = radix_analyzer.run_algorithm(test_data)
    radix_sorted = radix_result['sorted_data']
    
    print(f"Radix Sort: {radix_sorted}, Correct: {radix_sorted == expected}")

if __name__ == "__main__":
    print("Sorting Algorithms Scaling Analysis - Test Suite")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        compare_algorithms()
        test_radix_bases()
        test_correctness()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        print("Check the 'test_results/' directory for detailed results.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
