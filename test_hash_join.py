#!/usr/bin/env python3
"""
Test script for Hash Join Algorithm Scaling Analysis

This script demonstrates how to use HashJoinScalingAnalyzer and 
tests its functionality with different configurations.
"""

import sys
import os
sys.path.append('.')

# Import directly from the file to avoid dependency issues
from algorithms.Database.hash_join_analyzer import HashJoinScalingAnalyzer

def test_basic_functionality():
    """Test basic functionality of Hash Join algorithm"""
    print("=== Testing Basic Hash Join Functionality ===")
    
    test_sizes = [50, 100, 200]
    selectivities = [0.1, 0.5, 0.9]
    
    for selectivity in selectivities:
        print(f"\nTesting selectivity {selectivity}:")
        
        for size in test_sizes:
            analyzer = HashJoinScalingAnalyzer(
                join_selectivity=selectivity,
                data_distribution='uniform',
                output_dir='test_results/hash_join_basic',
                enable_gpu_tracking=False
            )
            
            result = analyzer.measure_single_run(size)
            custom = result.custom_metrics
            
            print(f"  Size {size:3d}: {result.execution_time_ms:6.2f}ms, "
                  f"Left: {custom['left_table_size']:3d}, "
                  f"Right: {custom['right_table_size']:3d}, "
                  f"Result: {custom['result_size']:4d}, "
                  f"Actual Sel: {custom['actual_selectivity']:.3f}")

def test_data_distributions():
    """Test different data distributions"""
    print("\n=== Testing Different Data Distributions ===")
    
    test_size = 300
    distributions = ['uniform', 'skewed', 'zipf']
    
    for distribution in distributions:
        print(f"\nTesting {distribution} distribution:")
        
        analyzer = HashJoinScalingAnalyzer(
            join_selectivity=0.5,
            data_distribution=distribution,
            enable_gpu_tracking=False
        )
        
        result = analyzer.measure_single_run(test_size)
        custom = result.custom_metrics
        
        print(f"  Time: {result.execution_time_ms:6.2f}ms")
        print(f"  Tables: Left={custom['left_table_size']}, Right={custom['right_table_size']}")
        print(f"  Result size: {custom['result_size']}")
        print(f"  Hash collisions: {custom['hash_collisions']}")
        print(f"  Collision rate: {custom['collision_rate']:.4f}")
        print(f"  Actual selectivity: {custom['actual_selectivity']:.4f}")

def test_table_size_ratios():
    """Test different table size ratios"""
    print("\n=== Testing Different Table Size Ratios ===")
    
    test_size = 200
    ratio_configs = [
        (1.0, 1.0, "Equal sizes"),
        (2.0, 1.0, "Left table 2x larger"),
        (1.0, 2.0, "Right table 2x larger"),
        (1.5, 0.5, "Left larger, Right smaller")
    ]
    
    for left_ratio, right_ratio, description in ratio_configs:
        print(f"\n{description} (ratios: {left_ratio}, {right_ratio}):")
        
        analyzer = HashJoinScalingAnalyzer(
            join_selectivity=0.4,
            left_table_ratio=left_ratio,
            right_table_ratio=right_ratio,
            data_distribution='uniform',
            enable_gpu_tracking=False
        )
        
        result = analyzer.measure_single_run(test_size)
        custom = result.custom_metrics
        
        print(f"  Time: {result.execution_time_ms:6.2f}ms")
        print(f"  Left table: {custom['left_table_size']} rows")
        print(f"  Right table: {custom['right_table_size']} rows")
        print(f"  Join result: {custom['result_size']} rows")
        print(f"  Build time: {custom['build_time_ms']:.3f}ms ({custom['build_time_ratio']:.1%})")
        print(f"  Probe time: {custom['probe_time_ms']:.3f}ms ({custom['probe_time_ratio']:.1%})")

def test_correctness():
    """Test correctness with small known data"""
    print("\n=== Testing Correctness with Known Data ===")
    
    # Create a simple test case
    analyzer = HashJoinScalingAnalyzer(
        join_selectivity=0.5,
        data_distribution='uniform'
    )
    
    # Generate small tables for inspection
    left_table, right_table = analyzer.prepare_input(10)
    
    print("Left table (first 5 rows):")
    for i, row in enumerate(left_table[:5]):
        print(f"  {row}")
    
    print("\nRight table (first 5 rows):")
    for i, row in enumerate(right_table[:5]):
        print(f"  {row}")
    
    # Execute join
    result = analyzer.run_algorithm((left_table, right_table))
    join_result = result['join_result']
    
    print(f"\nJoin result (first 5 rows):")
    for i, row in enumerate(join_result[:5]):
        print(f"  {row}")
    
    print(f"\nJoin Statistics:")
    print(f"  Left table size: {result['left_table_size']}")
    print(f"  Right table size: {result['right_table_size']}")
    print(f"  Join result size: {result['result_size']}")
    print(f"  Hash operations: {result['hash_operations']}")
    print(f"  Hash collisions: {result['hash_collisions']}")
    print(f"  Comparisons: {result['comparisons']}")
    print(f"  Actual selectivity: {result['actual_selectivity']:.4f}")

def performance_comparison():
    """Compare performance across different configurations"""
    print("\n=== Performance Comparison ===")
    
    test_size = 500
    configs = [
        (0.1, 'uniform', 'Low selectivity, uniform'),
        (0.5, 'uniform', 'Medium selectivity, uniform'),
        (0.9, 'uniform', 'High selectivity, uniform'),
        (0.5, 'skewed', 'Medium selectivity, skewed'),
        (0.5, 'zipf', 'Medium selectivity, zipf')
    ]
    
    results = []
    
    for selectivity, distribution, description in configs:
        analyzer = HashJoinScalingAnalyzer(
            join_selectivity=selectivity,
            data_distribution=distribution,
            enable_gpu_tracking=False
        )
        
        result = analyzer.measure_single_run(test_size)
        custom = result.custom_metrics
        
        results.append((description, result.execution_time_ms, custom))
        
    # Sort by execution time
    results.sort(key=lambda x: x[1])
    
    print(f"\nPerformance ranking (size {test_size}):")
    for i, (desc, time_ms, custom) in enumerate(results, 1):
        print(f"{i}. {desc}")
        print(f"   Time: {time_ms:6.2f}ms, Result: {custom['result_size']:4d} rows, "
              f"Collisions: {custom['hash_collisions']:3d}")

if __name__ == "__main__":
    print("Hash Join Algorithm Scaling Analysis - Test Suite")
    print("=" * 60)
    
    try:
        test_basic_functionality()
        test_data_distributions()
        test_table_size_ratios()
        test_correctness()
        performance_comparison()
        
        print("\n" + "=" * 60)
        print("All Hash Join tests completed successfully!")
        print("Check the 'test_results/' directory for detailed results.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
