"""
Unified Analysis and Visualization Tool for Scaling Results

This module provides tools to analyze and visualize results from the unified scaling framework.
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse

class ScalingResultsAnalyzer:
    """Analyzer for scaling framework results"""
    
    def __init__(self, results_dir: str = "results"):
        self.results_dir = Path(results_dir)
        self.data_cache = {}
        
    def load_algorithm_results(self, algorithm_name: str) -> pd.DataFrame:
        """Load results for a specific algorithm"""
        
        if algorithm_name in self.data_cache:
            return self.data_cache[algorithm_name]
        
        csv_file = self.results_dir / f"{algorithm_name}_scaling_results.csv"
        
        if not csv_file.exists():
            raise FileNotFoundError(f"Results file not found: {csv_file}")
        
        df = pd.read_csv(csv_file)
        
        # Cache the data
        self.data_cache[algorithm_name] = df
        
        return df
    
    def list_available_algorithms(self) -> List[str]:
        """List all available algorithm results"""
        
        csv_files = list(self.results_dir.glob("*_scaling_results.csv"))
        algorithms = [f.stem.replace("_scaling_results", "") for f in csv_files]
        
        return sorted(algorithms)
    
    def create_comparison_plot(self, 
                             algorithms: List[str], 
                             metric: str = "execution_time_ms",
                             save_path: Optional[str] = None) -> plt.Figure:
        """Create comparison plot for multiple algorithms"""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Colors for different algorithms
        colors = plt.cm.Set1(np.linspace(0, 1, len(algorithms)))
        
        for i, algorithm in enumerate(algorithms):
            try:
                df = self.load_algorithm_results(algorithm)
                
                # Plot 1: Time complexity
                ax1.loglog(df['input_size'], df[metric], 
                          'o-', label=algorithm, color=colors[i], 
                          linewidth=2, markersize=6)
                
                # Plot 2: Memory usage
                ax2.loglog(df['input_size'], df['memory_increment_mb'], 
                          's-', label=algorithm, color=colors[i],
                          linewidth=2, markersize=6)
                
                # Plot 3: Efficiency ratio
                ax3.semilogx(df['input_size'], df['efficiency_ratio'], 
                           '^-', label=algorithm, color=colors[i],
                           linewidth=2, markersize=6)
                
                # Plot 4: Scaling factor (if more than one data point)
                if len(df) > 1:
                    scaling_factors = self.calculate_scaling_factors(df, metric)
                    if scaling_factors:
                        sizes, factors = zip(*scaling_factors)
                        ax4.semilogx(sizes, factors, 
                                   'v-', label=algorithm, color=colors[i],
                                   linewidth=2, markersize=6)
                
            except FileNotFoundError:
                print(f"Warning: Results not found for {algorithm}")
                continue
        
        # Customize plots
        ax1.set_xlabel('Input Size')
        ax1.set_ylabel(f'{metric.replace("_", " ").title()} (ms)')
        ax1.set_title('Time Complexity Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        ax2.set_xlabel('Input Size')
        ax2.set_ylabel('Memory Usage (MB)')
        ax2.set_title('Space Complexity Comparison')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        ax3.set_xlabel('Input Size')
        ax3.set_ylabel('Efficiency Ratio (Theory/Actual)')
        ax3.set_title('Memory Efficiency Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        ax4.set_xlabel('Input Size')
        ax4.set_ylabel('Empirical Scaling Factor')
        ax4.set_title('Scaling Factor Analysis')
        ax4.axhline(y=1.0, color='green', linestyle='--', alpha=0.7, label='O(N) linear')
        ax4.axhline(y=2.0, color='orange', linestyle='--', alpha=0.7, label='O(N²) quadratic')
        ax4.axhline(y=3.0, color='red', linestyle='--', alpha=0.7, label='O(N³) cubic')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(0, 4)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def calculate_scaling_factors(self, df: pd.DataFrame, metric: str) -> List[Tuple[float, float]]:
        """Calculate empirical scaling factors"""
        
        scaling_factors = []
        
        for i in range(1, len(df)):
            size_ratio = df.iloc[i]['input_size'] / df.iloc[i-1]['input_size']
            metric_ratio = df.iloc[i][metric] / df.iloc[i-1][metric]
            
            if size_ratio > 1 and metric_ratio > 0:
                scaling_factor = np.log(metric_ratio) / np.log(size_ratio)
                scaling_factors.append((df.iloc[i]['input_size'], scaling_factor))
        
        return scaling_factors
    
    def generate_algorithm_report(self, algorithm: str) -> str:
        """Generate detailed report for a specific algorithm"""
        
        df = self.load_algorithm_results(algorithm)
        
        report = f"""
# Scaling Analysis Report: {algorithm}

## Summary
- **Algorithm**: {algorithm}
- **Data Points**: {len(df)}
- **Input Size Range**: {df['input_size'].min():,} to {df['input_size'].max():,}
- **Time Range**: {df['execution_time_ms'].min():.4f} to {df['execution_time_ms'].max():.4f} ms
- **Memory Range**: {df['memory_increment_mb'].min():.2f} to {df['memory_increment_mb'].max():.2f} MB

## Theoretical Complexity
- **Time Complexity**: {df['theoretical_time_complexity'].iloc[0] if not df['theoretical_time_complexity'].isna().all() else 'N/A'}
- **Space Complexity**: {df['theoretical_space_complexity'].iloc[0] if not df['theoretical_space_complexity'].isna().all() else 'N/A'}

## Performance Metrics
- **Average Execution Time**: {df['execution_time_ms'].mean():.4f} ms
- **Average Memory Usage**: {df['memory_increment_mb'].mean():.2f} MB
- **Average Efficiency Ratio**: {df['efficiency_ratio'].mean():.2f}

## Scaling Analysis
"""
        
        # Calculate scaling factors
        scaling_factors = self.calculate_scaling_factors(df, 'execution_time_ms')
        
        if scaling_factors:
            factors = [f[1] for f in scaling_factors]
            mean_factor = np.mean(factors)
            std_factor = np.std(factors)
            
            report += f"""
- **Mean Scaling Factor**: {mean_factor:.2f}
- **Standard Deviation**: {std_factor:.2f}
- **Scaling Consistency**: {'High' if std_factor < 0.5 else 'Medium' if std_factor < 1.0 else 'Low'}

### Complexity Classification
"""
            if mean_factor < 1.5:
                report += "- **Classification**: Sub-linear to Linear (O(N) or better)\n"
            elif mean_factor < 2.5:
                report += "- **Classification**: Quadratic-like (O(N²))\n"
            elif mean_factor < 3.5:
                report += "- **Classification**: Cubic-like (O(N³))\n"
            else:
                report += "- **Classification**: Super-cubic (O(N^k), k>3)\n"
        
        # Add custom metrics if available
        custom_columns = [col for col in df.columns if col.startswith('custom_')]
        
        if custom_columns:
            report += "\n## Algorithm-Specific Metrics\n"
            for col in custom_columns:
                metric_name = col.replace('custom_', '').replace('_', ' ').title()
                if df[col].dtype in ['int64', 'float64']:
                    report += f"- **{metric_name}**: {df[col].mean():.4f} (mean)\n"
                else:
                    report += f"- **{metric_name}**: {df[col].iloc[0]} (first value)\n"
        
        return report
    
    def create_detailed_analysis(self, 
                               algorithm: str, 
                               save_path: Optional[str] = None) -> plt.Figure:
        """Create detailed analysis plots for a single algorithm"""
        
        df = self.load_algorithm_results(algorithm)
        
        fig = plt.figure(figsize=(16, 12))
        
        # Create grid layout
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # Plot 1: Time vs Input Size (log-log)
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.loglog(df['input_size'], df['execution_time_ms'], 'bo-', linewidth=2, markersize=6)
        ax1.set_xlabel('Input Size')
        ax1.set_ylabel('Execution Time (ms)')
        ax1.set_title(f'{algorithm} - Time Complexity')
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Memory vs Input Size
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.loglog(df['input_size'], df['memory_increment_mb'], 'ro-', linewidth=2, markersize=6)
        ax2.set_xlabel('Input Size')
        ax2.set_ylabel('Memory Usage (MB)')
        ax2.set_title(f'{algorithm} - Space Complexity')
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Efficiency Ratio
        ax3 = fig.add_subplot(gs[0, 2])
        ax3.semilogx(df['input_size'], df['efficiency_ratio'], 'go-', linewidth=2, markersize=6)
        ax3.set_xlabel('Input Size')
        ax3.set_ylabel('Efficiency Ratio')
        ax3.set_title(f'{algorithm} - Memory Efficiency')
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Scaling factors
        ax4 = fig.add_subplot(gs[1, 0])
        scaling_factors = self.calculate_scaling_factors(df, 'execution_time_ms')
        if scaling_factors:
            sizes, factors = zip(*scaling_factors)
            ax4.semilogx(sizes, factors, 'mo-', linewidth=2, markersize=6)
            ax4.axhline(y=1.0, color='green', linestyle='--', alpha=0.7, label='Linear')
            ax4.axhline(y=2.0, color='orange', linestyle='--', alpha=0.7, label='Quadratic')
            ax4.axhline(y=3.0, color='red', linestyle='--', alpha=0.7, label='Cubic')
            ax4.legend()
        ax4.set_xlabel('Input Size')
        ax4.set_ylabel('Scaling Factor')
        ax4.set_title(f'{algorithm} - Empirical Scaling')
        ax4.grid(True, alpha=0.3)
        
        # Plot 5: Time breakdown (if available)
        ax5 = fig.add_subplot(gs[1, 1])
        if 'setup_time_ms' in df.columns and 'cleanup_time_ms' in df.columns:
            ax5.loglog(df['input_size'], df['setup_time_ms'], 's-', label='Setup', markersize=4)
            ax5.loglog(df['input_size'], df['execution_time_ms'], 'o-', label='Execution', markersize=4)
            ax5.loglog(df['input_size'], df['cleanup_time_ms'], '^-', label='Cleanup', markersize=4)
            ax5.legend()
        else:
            ax5.loglog(df['input_size'], df['execution_time_ms'], 'bo-', linewidth=2, markersize=6)
        ax5.set_xlabel('Input Size')
        ax5.set_ylabel('Time (ms)')
        ax5.set_title(f'{algorithm} - Time Breakdown')
        ax5.grid(True, alpha=0.3)
        
        # Plot 6: Custom metrics (if available)
        ax6 = fig.add_subplot(gs[1, 2])
        custom_columns = [col for col in df.columns if col.startswith('custom_') and df[col].dtype in ['int64', 'float64']]
        if custom_columns:
            for i, col in enumerate(custom_columns[:3]):  # Show max 3 metrics
                ax6.semilogx(df['input_size'], df[col], 'o-', label=col.replace('custom_', ''), markersize=4)
            ax6.legend()
            ax6.set_xlabel('Input Size')
            ax6.set_ylabel('Value')
            ax6.set_title(f'{algorithm} - Custom Metrics')
        else:
            ax6.text(0.5, 0.5, 'No Custom Metrics', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('Custom Metrics (None Available)')
        ax6.grid(True, alpha=0.3)
        
        # Plot 7-9: Statistical analysis
        ax7 = fig.add_subplot(gs[2, :])
        
        # Summary statistics table
        stats_data = {
            'Metric': ['Input Size', 'Execution Time (ms)', 'Memory (MB)', 'Efficiency Ratio'],
            'Min': [
                f"{df['input_size'].min():,}",
                f"{df['execution_time_ms'].min():.4f}",
                f"{df['memory_increment_mb'].min():.2f}",
                f"{df['efficiency_ratio'].min():.2f}"
            ],
            'Max': [
                f"{df['input_size'].max():,}",
                f"{df['execution_time_ms'].max():.4f}",
                f"{df['memory_increment_mb'].max():.2f}",
                f"{df['efficiency_ratio'].max():.2f}"
            ],
            'Mean': [
                f"{df['input_size'].mean():,.0f}",
                f"{df['execution_time_ms'].mean():.4f}",
                f"{df['memory_increment_mb'].mean():.2f}",
                f"{df['efficiency_ratio'].mean():.2f}"
            ],
            'Std': [
                f"{df['input_size'].std():,.0f}",
                f"{df['execution_time_ms'].std():.4f}",
                f"{df['memory_increment_mb'].std():.2f}",
                f"{df['efficiency_ratio'].std():.2f}"
            ]
        }
        
        stats_df = pd.DataFrame(stats_data)
        
        # Create table
        ax7.axis('tight')
        ax7.axis('off')
        table = ax7.table(cellText=stats_df.values, colLabels=stats_df.columns,
                         cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        ax7.set_title(f'{algorithm} - Statistical Summary', pad=20)
        
        plt.suptitle(f'Detailed Scaling Analysis: {algorithm}', fontsize=16, fontweight='bold')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def export_summary_report(self, output_file: str = "scaling_analysis_summary.md"):
        """Export comprehensive summary report"""
        
        algorithms = self.list_available_algorithms()
        
        report = f"""# Scaling Analysis Summary Report

Generated using Unified Scaling Analysis Framework

## Overview
- **Total Algorithms Analyzed**: {len(algorithms)}
- **Available Algorithms**: {', '.join(algorithms)}

"""
        
        for algorithm in algorithms:
            try:
                algo_report = self.generate_algorithm_report(algorithm)
                report += algo_report + "\n" + "="*80 + "\n\n"
            except Exception as e:
                report += f"## {algorithm}\n**Error**: Could not generate report - {str(e)}\n\n"
        
        with open(output_file, 'w') as f:
            f.write(report)
        
        print(f"Summary report exported to: {output_file}")

def main():
    """Command-line interface for the analyzer"""
    
    parser = argparse.ArgumentParser(description="Analyze scaling framework results")
    parser.add_argument("--results-dir", default="results", help="Directory containing results")
    parser.add_argument("--algorithm", help="Specific algorithm to analyze")
    parser.add_argument("--compare", nargs="+", help="Algorithms to compare")
    parser.add_argument("--export", action="store_true", help="Export summary report")
    parser.add_argument("--output-dir", default="analysis_output", help="Output directory for plots and reports")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize analyzer
    analyzer = ScalingResultsAnalyzer(args.results_dir)
    
    # List available algorithms
    algorithms = analyzer.list_available_algorithms()
    print(f"Available algorithms: {algorithms}")
    
    if args.algorithm:
        # Analyze specific algorithm
        print(f"Analyzing {args.algorithm}...")
        
        try:
            # Generate detailed plot
            fig = analyzer.create_detailed_analysis(
                args.algorithm, 
                save_path=os.path.join(args.output_dir, f"{args.algorithm}_detailed_analysis.png")
            )
            plt.show()
            
            # Generate report
            report = analyzer.generate_algorithm_report(args.algorithm)
            report_file = os.path.join(args.output_dir, f"{args.algorithm}_report.md")
            with open(report_file, 'w') as f:
                f.write(report)
            print(f"Report saved to: {report_file}")
            
        except FileNotFoundError:
            print(f"Results not found for {args.algorithm}")
    
    elif args.compare:
        # Compare multiple algorithms
        print(f"Comparing algorithms: {args.compare}")
        
        fig = analyzer.create_comparison_plot(
            args.compare,
            save_path=os.path.join(args.output_dir, "algorithm_comparison.png")
        )
        plt.show()
    
    if args.export:
        # Export summary report
        summary_file = os.path.join(args.output_dir, "scaling_analysis_summary.md")
        analyzer.export_summary_report(summary_file)

if __name__ == "__main__":
    main() 