"""
Unified Analysis and Visualization Tools

This package provides tools for analyzing and visualizing results from the
unified scaling framework, including cross-algorithm comparisons and detailed
performance analysis.

Key Components:
- ScalingResultsAnalyzer: Main analysis tool for scaling results
- Comparison plotting capabilities
- Automatic report generation
- Command-line interface for batch analysis

Usage:
    from analysis.unified_analyzer import ScalingResultsAnalyzer
"""

__version__ = "1.0.0"

from .unified_analyzer import ScalingResultsAnalyzer

__all__ = ['ScalingResultsAnalyzer'] 