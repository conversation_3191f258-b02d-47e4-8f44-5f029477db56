input_size,algorithm_name,timestamp,execution_time_ms,setup_time_ms,cleanup_time_ms,total_time_ms,baseline_memory_mb,peak_memory_mb,memory_increment_mb,gpu_memory_mb,operations_count,accuracy,throughput,theoretical_time_complexity,theoretical_space_complexity,theoretical_memory_mb,efficiency_ratio,custom_original_input_size_bytes,custom_ciphertext_size_bytes,custom_plaintext_size_bytes,custom_key_size_bits,custom_decryption_mode,custom_padding_algorithm,custom_theoretical_blocks,custom_actual_blocks,custom_block_size_bytes,custom_padding_removed_bytes,custom_compression_ratio,custom_throughput_mbps,custom_bytes_per_second,custom_blocks_per_second,custom_decryption_time_ms,custom_correctness_verified,custom_has_error,custom_algorithm_family
64,AES-256-CBC-Decrypt,1753703757.4417968,0.1172,0.1093,91.9407,92.1672,656.48,656.55,0.07,0.00,,,,O(n) [blocks=4],O(1),0.00,0.0033,64,80,64,256,CBC,PKCS7,5,5,16,16,1.25,8.7,9125607,570350,0.0087665393948555,True,False,symmetric_decryption
128,AES-256-CBC-Decrypt,1753703758.1534853,0.1222,0.1204,103.2331,103.4757,656.93,656.93,0.00,0.00,,,,O(n) [blocks=8],O(1),0.00,0.0000,128,144,128,256,CBC,PKCS7,9,9,16,16,1.125,11.07,11609327,725582,0.01240381971001625,True,False,symmetric_decryption
256,AES-256-CBC-Decrypt,1753703758.816576,0.1141,0.1153,90.6773,90.9068,656.93,656.93,0.00,0.00,,,,O(n) [blocks=16],O(1),0.00,0.0000,256,272,256,256,CBC,PKCS7,17,17,16,16,1.0625,22.38,23466937,1466683,0.011590775102376938,True,False,symmetric_decryption
512,AES-256-CBC-Decrypt,1753703759.4782062,0.1232,0.1154,92.7321,92.9706,656.93,656.93,0.00,0.00,,,,O(n) [blocks=32],O(1),0.00,0.3867,512,528,512,256,CBC,PKCS7,33,33,16,16,1.0312,48.11,50443605,3152725,0.010467134416103363,True,False,symmetric_decryption
1024,AES-256-CBC-Decrypt,1753703760.1649091,0.1148,0.1177,97.2782,97.5108,656.93,656.93,0.00,0.00,,,,O(n) [blocks=64],O(1),0.00,0.0000,1024,1040,1024,256,CBC,PKCS7,65,65,16,16,1.0156,94.73,99332102,6208256,0.01046992838382721,True,False,symmetric_decryption
