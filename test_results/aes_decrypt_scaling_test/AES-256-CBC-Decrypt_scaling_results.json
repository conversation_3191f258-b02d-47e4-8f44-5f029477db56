[{"input_size": 64, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703757.4417968, "execution_time_ms": 0.11721588671207428, "setup_time_ms": 0.10933214798569679, "cleanup_time_ms": 91.9406870380044, "total_time_ms": 92.16723507270217, "baseline_memory_mb": 656.484375, "peak_memory_mb": 656.5546875, "memory_increment_mb": 0.0703125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 64, "ciphertext_size_bytes": 80, "plaintext_size_bytes": 64, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 5, "actual_blocks": 5, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.25, "throughput_mbps": 8.7, "bytes_per_second": 9125607, "blocks_per_second": 570350, "decryption_time_ms": 0.0087665393948555, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.0032552083333333335}, {"input_size": 128, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703758.1534853, "execution_time_ms": 0.12218579649925232, "setup_time_ms": 0.12040510773658752, "cleanup_time_ms": 103.23310783132911, "total_time_ms": 103.47569873556495, "baseline_memory_mb": 656.92578125, "peak_memory_mb": 656.92578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 128, "ciphertext_size_bytes": 144, "plaintext_size_bytes": 128, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 9, "actual_blocks": 9, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.125, "throughput_mbps": 11.07, "bytes_per_second": 11609327, "blocks_per_second": 725582, "decryption_time_ms": 0.01240381971001625, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703758.816576, "execution_time_ms": 0.11413805186748505, "setup_time_ms": 0.11533685028553009, "cleanup_time_ms": 90.67728463560343, "total_time_ms": 90.90675953775644, "baseline_memory_mb": 656.92578125, "peak_memory_mb": 656.92578125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 256, "ciphertext_size_bytes": 272, "plaintext_size_bytes": 256, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 17, "actual_blocks": 17, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0625, "throughput_mbps": 22.38, "bytes_per_second": 23466937, "blocks_per_second": 1466683, "decryption_time_ms": 0.011590775102376938, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703759.4782062, "execution_time_ms": 0.12315977364778519, "setup_time_ms": 0.11541787534952164, "cleanup_time_ms": 92.73205371573567, "total_time_ms": 92.97063136473298, "baseline_memory_mb": 656.92578125, "peak_memory_mb": 656.9296875, "memory_increment_mb": 0.00390625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 512, "ciphertext_size_bytes": 528, "plaintext_size_bytes": 512, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 33, "actual_blocks": 33, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0312, "throughput_mbps": 48.11, "bytes_per_second": 50443605, "blocks_per_second": 3152725, "decryption_time_ms": 0.010467134416103363, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.38671875}, {"input_size": 1024, "algorithm_name": "AES-256-CBC-Decrypt", "timestamp": 1753703760.1649091, "execution_time_ms": 0.11482033878564835, "setup_time_ms": 0.11773919686675072, "cleanup_time_ms": 97.27820474654436, "total_time_ms": 97.51076428219676, "baseline_memory_mb": 656.9296875, "peak_memory_mb": 656.9296875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"original_input_size_bytes": 1024, "ciphertext_size_bytes": 1040, "plaintext_size_bytes": 1024, "key_size_bits": 256, "decryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 65, "actual_blocks": 65, "block_size_bytes": 16, "padding_removed_bytes": 16, "compression_ratio": 1.0156, "throughput_mbps": 94.73, "bytes_per_second": 99332102, "blocks_per_second": 6208256, "decryption_time_ms": 0.01046992838382721, "correctness_verified": true, "has_error": false, "algorithm_family": "symmetric_decryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}]