[{"input_size": 100, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657632.7226682, "execution_time_ms": 0.10287752375006676, "setup_time_ms": 0.07561780512332916, "cleanup_time_ms": 84.70437675714493, "total_time_ms": 84.88287208601832, "baseline_memory_mb": 650.3515625, "peak_memory_mb": 650.3515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 0, "total_coefficients": 100, "compression_ratio": 1.0, "theoretical_operations": 400, "operations_per_sample": 4.0, "coefficient_efficiency": 1.0}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00152587890625, "efficiency_ratio": 0.0}, {"input_size": 400, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657633.3554049, "execution_time_ms": 0.24754730984568596, "setup_time_ms": 0.06974907591938972, "cleanup_time_ms": 84.18230898678303, "total_time_ms": 84.4996053725481, "baseline_memory_mb": 650.3515625, "peak_memory_mb": 650.3515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 400, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 1, "total_coefficients": 676, "compression_ratio": 0.591715976331361, "theoretical_operations": 1600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.69}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 900, "algorithm_name": "Wavelet-db4-dwt2d", "timestamp": 1753657633.9574418, "execution_time_ms": 0.29837917536497116, "setup_time_ms": 0.0942540355026722, "cleanup_time_ms": 84.70854023471475, "total_time_ms": 85.10117344558239, "baseline_memory_mb": 650.3515625, "peak_memory_mb": 650.3515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 900, "wavelet_type": "db4", "transform_type": "dwt2d", "extension_mode": "symmetric", "decomposition_levels": 2, "total_coefficients": 1548, "compression_ratio": 0.5813953488372093, "theoretical_operations": 3600, "operations_per_sample": 4.0, "coefficient_efficiency": 1.72}, "theoretical_time_complexity": "O(N²)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.01373291015625, "efficiency_ratio": 0.0}]