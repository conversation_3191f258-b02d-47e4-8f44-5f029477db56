[{"input_size": 64, "algorithm_name": "AES-256-CBC", "timestamp": 1753702936.453456, "execution_time_ms": 0.154249370098114, "setup_time_ms": 0.023828353732824326, "cleanup_time_ms": 129.25424799323082, "total_time_ms": 129.43232571706176, "baseline_memory_mb": 656.83984375, "peak_memory_mb": 656.97265625, "memory_increment_mb": 0.1328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 4, "actual_blocks": 5, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.25, "output_size_bytes": 80, "expansion_ratio": 1.25, "throughput_mbps": 5.96, "bytes_per_second": 6252625, "blocks_per_second": 488486, "encryption_time_ms": 0.010235700756311417, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0002288818359375, "efficiency_ratio": 0.001723345588235294}, {"input_size": 128, "algorithm_name": "AES-256-CBC", "timestamp": 1753702937.386227, "execution_time_ms": 0.12848488986492157, "setup_time_ms": 0.02675410360097885, "cleanup_time_ms": 158.89610489830375, "total_time_ms": 159.05134389176965, "baseline_memory_mb": 657.28125, "peak_memory_mb": 657.28125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 8, "actual_blocks": 9, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.125, "output_size_bytes": 144, "expansion_ratio": 1.125, "throughput_mbps": 11.22, "bytes_per_second": 11770056, "blocks_per_second": 827582, "encryption_time_ms": 0.010875053703784943, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0004119873046875, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "AES-256-CBC", "timestamp": 1753702938.2286274, "execution_time_ms": 0.1532435417175293, "setup_time_ms": 0.05100574344396591, "cleanup_time_ms": 158.99654710665345, "total_time_ms": 159.20079639181495, "baseline_memory_mb": 657.28125, "peak_memory_mb": 657.28125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 16, "actual_blocks": 17, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0625, "output_size_bytes": 272, "expansion_ratio": 1.0625, "throughput_mbps": 27.39, "bytes_per_second": 28719873, "blocks_per_second": 1907179, "encryption_time_ms": 0.008913688361644745, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0007781982421875, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "AES-256-CBC", "timestamp": 1753702939.354676, "execution_time_ms": 0.12291464954614639, "setup_time_ms": 0.058471690863370895, "cleanup_time_ms": 85.9398297034204, "total_time_ms": 86.12121604382992, "baseline_memory_mb": 657.28515625, "peak_memory_mb": 657.28515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 32, "actual_blocks": 33, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0312, "output_size_bytes": 528, "expansion_ratio": 1.0312, "throughput_mbps": 66.9, "bytes_per_second": 70153233, "blocks_per_second": 4521595, "encryption_time_ms": 0.007298309355974197, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=32]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0015106201171875, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "AES-256-CBC", "timestamp": 1753702940.1848311, "execution_time_ms": 0.12548016384243965, "setup_time_ms": 0.025673769414424896, "cleanup_time_ms": 84.65896779671311, "total_time_ms": 84.81012172996998, "baseline_memory_mb": 657.28515625, "peak_memory_mb": 657.28515625, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "key_size_bits": 256, "encryption_mode": "CBC", "padding_algorithm": "PKCS7", "theoretical_blocks": 64, "actual_blocks": 65, "block_size_bytes": 16, "padding_overhead_bytes": 16, "padding_ratio": 0.0156, "output_size_bytes": 1040, "expansion_ratio": 1.0156, "throughput_mbps": 112.85, "bytes_per_second": 118335212, "blocks_per_second": 7511512, "encryption_time_ms": 0.008653383702039719, "has_error": false, "algorithm_family": "symmetric_encryption"}, "theoretical_time_complexity": "O(n) [blocks=64]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0029754638671875, "efficiency_ratio": 0.0}]