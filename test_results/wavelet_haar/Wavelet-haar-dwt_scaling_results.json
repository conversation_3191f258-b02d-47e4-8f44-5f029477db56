[{"input_size": 500, "algorithm_name": "Wavelet-haar-dwt", "timestamp": 1753657634.5991735, "execution_time_ms": 0.15505468472838402, "setup_time_ms": 0.05479622632265091, "cleanup_time_ms": 99.17944157496095, "total_time_ms": 99.38929248601198, "baseline_memory_mb": 650.35546875, "peak_memory_mb": 650.36328125, "memory_increment_mb": 0.0078125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 500, "wavelet_type": "haar", "transform_type": "dwt", "extension_mode": "symmetric", "decomposition_levels": 8, "total_coefficients": 502, "compression_ratio": 0.9960159362549801, "theoretical_operations": 1000, "operations_per_sample": 2.0, "coefficient_efficiency": 1.004}, "theoretical_time_complexity": "O(N)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.00762939453125, "efficiency_ratio": 0.9765625}]