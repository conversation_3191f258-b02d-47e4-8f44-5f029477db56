[{"input_size": 64, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704467.1991246, "execution_time_ms": 0.028800033032894135, "setup_time_ms": 0.01903017982840538, "cleanup_time_ms": 86.93549642339349, "total_time_ms": 86.98332663625479, "baseline_memory_mb": 655.828125, "peak_memory_mb": 655.828125, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 1, "actual_rounds": 1, "compression_ratio": 2.0, "throughput_mbps": 10.94, "bytes_per_second": 11473324, "rounds_per_second": 179270, "bits_per_second": 91786595, "hash_rate_per_second": 179270.69, "hashing_time_ms": 0.005578156560659409, "round_processing_time_us": 5.578156560659409, "rate_utilization": 0.471, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00028228759765625, "efficiency_ratio": 0.0}, {"input_size": 136, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704467.8467882, "execution_time_ms": 0.030431989580392838, "setup_time_ms": 0.020116101950407028, "cleanup_time_ms": 99.29259214550257, "total_time_ms": 99.34314023703337, "baseline_memory_mb": 655.828125, "peak_memory_mb": 656.26171875, "memory_increment_mb": 0.43359375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 136, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 1, "actual_rounds": 1, "compression_ratio": 4.25, "throughput_mbps": 14.12, "bytes_per_second": 14810232, "rounds_per_second": 108898, "bits_per_second": 118481856, "hash_rate_per_second": 108898.77, "hashing_time_ms": 0.009182840585708618, "round_processing_time_us": 9.182840585708618, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.0003509521484375, "efficiency_ratio": 0.0008094031531531531}, {"input_size": 272, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704468.4647741, "execution_time_ms": 0.030567683279514313, "setup_time_ms": 0.02372823655605316, "cleanup_time_ms": 85.98827570676804, "total_time_ms": 86.0425716266036, "baseline_memory_mb": 656.26171875, "peak_memory_mb": 656.26171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 272, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 2, "actual_rounds": 2, "compression_ratio": 8.5, "throughput_mbps": 37.05, "bytes_per_second": 38852970, "rounds_per_second": 285683, "bits_per_second": 310823760, "hash_rate_per_second": 142841.8, "hashing_time_ms": 0.007000751793384552, "round_processing_time_us": 3.500375896692276, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00048065185546875, "efficiency_ratio": 0.0}, {"input_size": 544, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704469.0766428, "execution_time_ms": 0.03167204558849335, "setup_time_ms": 0.023441854864358902, "cleanup_time_ms": 86.38421772047877, "total_time_ms": 86.43933162093163, "baseline_memory_mb": 656.26171875, "peak_memory_mb": 656.26171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 544, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 4, "actual_rounds": 4, "compression_ratio": 17.0, "throughput_mbps": 58.28, "bytes_per_second": 61115935, "rounds_per_second": 449381, "bits_per_second": 488927482, "hash_rate_per_second": 112345.47, "hashing_time_ms": 0.008901115506887436, "round_processing_time_us": 2.225278876721859, "rate_utilization": 1.0, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00074005126953125, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704469.7378616, "execution_time_ms": 0.031246617436408997, "setup_time_ms": 0.029645860195159912, "cleanup_time_ms": 86.2245261669159, "total_time_ms": 86.28541864454746, "baseline_memory_mb": 656.26171875, "peak_memory_mb": 656.26171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 8, "actual_rounds": 8, "compression_ratio": 32.0, "throughput_mbps": 117.34, "bytes_per_second": 123036046, "rounds_per_second": 961219, "bits_per_second": 984288370, "hash_rate_per_second": 120152.39, "hashing_time_ms": 0.008322764188051224, "round_processing_time_us": 1.040345523506403, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00119781494140625, "efficiency_ratio": 0.0}, {"input_size": 2048, "algorithm_name": "SHA3-256-<PERSON>h", "timestamp": 1753704470.341369, "execution_time_ms": 0.039690081030130386, "setup_time_ms": 0.02718064934015274, "cleanup_time_ms": 85.36893920972943, "total_time_ms": 85.43580994009972, "baseline_memory_mb": 656.26171875, "peak_memory_mb": 656.26171875, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 2048, "output_size_bytes": 32, "hash_algorithm": "SHA3-256", "hash_size_bits": 256, "rate_bits": 1088, "rate_bytes": 136, "capacity_bits": 512, "state_size_bits": 1600, "theoretical_rounds": 16, "actual_rounds": 16, "compression_ratio": 64.0, "throughput_mbps": 143.21, "bytes_per_second": 150170605, "rounds_per_second": 1173207, "bits_per_second": 1201364840, "hash_rate_per_second": 73325.49, "hashing_time_ms": 0.01363782212138176, "round_processing_time_us": 0.85236388258636, "rate_utilization": 0.941, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-3", "construction": "sponge"}, "theoretical_time_complexity": "O(n) [rounds=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00217437744140625, "efficiency_ratio": 0.0}]