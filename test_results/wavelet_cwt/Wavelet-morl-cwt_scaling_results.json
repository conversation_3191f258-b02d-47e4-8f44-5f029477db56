[{"input_size": 50, "algorithm_name": "Wavelet-morl-cwt", "timestamp": 1753657630.8366568, "execution_time_ms": 0.7032386027276516, "setup_time_ms": 0.05106395110487938, "cleanup_time_ms": 94.20217899605632, "total_time_ms": 94.95648154988885, "baseline_memory_mb": 649.69921875, "peak_memory_mb": 650.3359375, "memory_increment_mb": 0.63671875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 50, "wavelet_type": "morl", "transform_type": "cwt", "extension_mode": "symmetric", "decomposition_levels": 11, "total_coefficients": 550, "compression_ratio": 0.09090909090909091, "theoretical_operations": 6000, "operations_per_sample": 120.0, "coefficient_efficiency": 11.0}, "theoretical_time_complexity": "O(N × M)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.0125885009765625, "efficiency_ratio": 0.019770897239263802}, {"input_size": 100, "algorithm_name": "Wavelet-morl-cwt", "timestamp": 1753657631.4986613, "execution_time_ms": 1.0185398161411285, "setup_time_ms": 0.04029739648103714, "cleanup_time_ms": 85.41337493807077, "total_time_ms": 86.47221215069294, "baseline_memory_mb": 650.3359375, "peak_memory_mb": 650.3359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 100, "wavelet_type": "morl", "transform_type": "cwt", "extension_mode": "symmetric", "decomposition_levels": 24, "total_coefficients": 2400, "compression_ratio": 0.041666666666666664, "theoretical_operations": 25000, "operations_per_sample": 250.0, "coefficient_efficiency": 24.0}, "theoretical_time_complexity": "O(N × M)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.025177001953125, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "Wavelet-morl-cwt", "timestamp": 1753657632.1147814, "execution_time_ms": 1.3349478133022785, "setup_time_ms": 0.05663512274622917, "cleanup_time_ms": 84.29163321852684, "total_time_ms": 85.68321615457535, "baseline_memory_mb": 650.3359375, "peak_memory_mb": 650.3359375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": 200, "wavelet_type": "morl", "transform_type": "cwt", "extension_mode": "symmetric", "decomposition_levels": 31, "total_coefficients": 6200, "compression_ratio": 0.03225806451612903, "theoretical_operations": 64000, "operations_per_sample": 320.0, "coefficient_efficiency": 31.0}, "theoretical_time_complexity": "O(N × M)", "theoretical_space_complexity": "O(N)", "theoretical_memory_mb": 0.05035400390625, "efficiency_ratio": 0.0}]