[{"input_size": 32, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704442.8817463, "execution_time_ms": 0.026465486735105515, "setup_time_ms": 0.01948326826095581, "cleanup_time_ms": 88.36473897099495, "total_time_ms": 88.41068772599101, "baseline_memory_mb": 655.16015625, "peak_memory_mb": 655.37109375, "memory_increment_mb": 0.2109375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 32, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 1, "actual_blocks": 1, "compression_ratio": 1.0, "throughput_mbps": 5.79, "bytes_per_second": 6072234, "blocks_per_second": 189757, "bits_per_second": 48577875, "hash_rate_per_second": 189757.33, "hashing_time_ms": 0.005269888788461685, "block_processing_time_us": 5.269888788461685, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.00030517578125, "efficiency_ratio": 0.0014467592592592592}, {"input_size": 64, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704443.5277877, "execution_time_ms": 0.03188168630003929, "setup_time_ms": 0.02158619463443756, "cleanup_time_ms": 87.59394101798534, "total_time_ms": 87.64740889891982, "baseline_memory_mb": 655.75, "peak_memory_mb": 655.75, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 64, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 1, "actual_blocks": 1, "compression_ratio": 2.0, "throughput_mbps": 5.64, "bytes_per_second": 5910081, "blocks_per_second": 92345, "bits_per_second": 47280654, "hash_rate_per_second": 92345.03, "hashing_time_ms": 0.010828953236341476, "block_processing_time_us": 10.828953236341476, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=1]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000335693359375, "efficiency_ratio": 0.0}, {"input_size": 128, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704444.1554852, "execution_time_ms": 0.028484128415584564, "setup_time_ms": 0.05469704046845436, "cleanup_time_ms": 85.65469179302454, "total_time_ms": 85.73787296190858, "baseline_memory_mb": 655.75, "peak_memory_mb": 655.75, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 128, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 2, "actual_blocks": 2, "compression_ratio": 4.0, "throughput_mbps": 17.16, "bytes_per_second": 17990569, "blocks_per_second": 281102, "bits_per_second": 143924553, "hash_rate_per_second": 140551.32, "hashing_time_ms": 0.007114838808774948, "block_processing_time_us": 3.557419404387474, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=2]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000396728515625, "efficiency_ratio": 0.0}, {"input_size": 256, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704444.7673185, "execution_time_ms": 0.031009595841169357, "setup_time_ms": 0.02174917608499527, "cleanup_time_ms": 85.72816895321012, "total_time_ms": 85.78092772513628, "baseline_memory_mb": 655.75, "peak_memory_mb": 655.75, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 256, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 4, "actual_blocks": 4, "compression_ratio": 8.0, "throughput_mbps": 30.48, "bytes_per_second": 31962547, "blocks_per_second": 499414, "bits_per_second": 255700378, "hash_rate_per_second": 124853.7, "hashing_time_ms": 0.008009374141693115, "block_processing_time_us": 2.002343535423279, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=4]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000518798828125, "efficiency_ratio": 0.0}, {"input_size": 512, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704445.402019, "execution_time_ms": 0.03199158236384392, "setup_time_ms": 0.022158026695251465, "cleanup_time_ms": 86.65661979466677, "total_time_ms": 86.71076940372586, "baseline_memory_mb": 655.75, "peak_memory_mb": 655.75, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 512, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 8, "actual_blocks": 8, "compression_ratio": 16.0, "throughput_mbps": 51.36, "bytes_per_second": 53850114, "blocks_per_second": 841408, "bits_per_second": 430800912, "hash_rate_per_second": 105176.0, "hashing_time_ms": 0.00950787216424942, "block_processing_time_us": 1.1884840205311775, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=8]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.000762939453125, "efficiency_ratio": 0.0}, {"input_size": 1024, "algorithm_name": "SHA-256-Hash", "timestamp": 1753704446.0286636, "execution_time_ms": 0.03285501152276993, "setup_time_ms": 0.02851337194442749, "cleanup_time_ms": 87.2843018732965, "total_time_ms": 87.3456702567637, "baseline_memory_mb": 655.75, "peak_memory_mb": 655.75, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size_bytes": 1024, "output_size_bytes": 32, "hash_algorithm": "SHA-256", "hash_size_bits": 256, "block_size_bits": 512, "block_size_bytes": 64, "theoretical_blocks": 16, "actual_blocks": 16, "compression_ratio": 32.0, "throughput_mbps": 85.91, "bytes_per_second": 90079602, "blocks_per_second": 1407493, "bits_per_second": 720636819, "hash_rate_per_second": 87968.36, "hashing_time_ms": 0.01136772334575653, "block_processing_time_us": 0.7104827091097832, "hash_hex_length": 64, "has_error": false, "algorithm_family": "cryptographic_hash", "hash_family": "SHA-2"}, "theoretical_time_complexity": "O(n) [blocks=16]", "theoretical_space_complexity": "O(1)", "theoretical_memory_mb": 0.001251220703125, "efficiency_ratio": 0.0}]