#!/usr/bin/env python3
"""
Simple test script for Merge Sort Scaling Analysis

This script demonstrates how to use the MergeSortScalingAnalyzer
and provides a quick verification of the implementation.
"""

from algorithms.Sorting import MergeSortScalingAnalyzer

def test_basic_functionality():
    """Test basic functionality of Merge Sort analyzer"""
    print("=== Testing Basic Merge Sort Functionality ===")
    
    # Create analyzer for random data
    analyzer = MergeSortScalingAnalyzer(
        data_type='random',
        output_dir='test_results/merge_sort_basic',
        enable_gpu_tracking=False
    )
    
    # Test with small sizes
    test_sizes = [10, 50, 100, 200]
    print(f"Testing with sizes: {test_sizes}")
    
    results = analyzer.run_scaling_analysis(test_sizes)
    
    # Verify results
    print(f"\n=== Results Summary ===")
    for result in results:
        custom = result.custom_metrics
        print(f"Size {result.input_size:3d}: "
              f"Time: {result.execution_time_ms:6.2f}ms, "
              f"Comparisons: {custom['actual_comparisons']:5d}, "
              f"Correct: {custom['correctness_verified']}")
    
    # Analyze scaling behavior
    scaling = analyzer.analyze_scaling_behavior()
    if scaling:
        print(f"\nScaling factor: {scaling['mean_scaling_factor']:.2f} ± {scaling['std_scaling_factor']:.2f}")
        print(f"Expected for O(n log n): ~1.0-1.3")
    
    return results

def test_different_data_types():
    """Test different input data types"""
    print("\n=== Testing Different Data Types ===")
    
    data_types = ['random', 'sorted', 'reverse', 'nearly_sorted']
    test_size = 500
    
    for data_type in data_types:
        print(f"\nTesting {data_type} data:")
        
        analyzer = MergeSortScalingAnalyzer(
            data_type=data_type,
            output_dir=f'test_results/merge_sort_{data_type}',
            enable_gpu_tracking=False
        )
        
        # Single measurement
        result = analyzer.measure_single_run(test_size)
        custom = result.custom_metrics
        
        print(f"  Size: {result.input_size}")
        print(f"  Time: {result.execution_time_ms:.2f}ms")
        print(f"  Comparisons: {custom['actual_comparisons']}")
        print(f"  Theoretical: {custom['theoretical_comparisons']}")
        print(f"  Efficiency: {custom['comparison_efficiency']:.3f}")
        print(f"  Inversions: {custom['input_inversions']}")
        print(f"  Already sorted: {custom['was_already_sorted']}")
        print(f"  Correct: {custom['correctness_verified']}")

def test_correctness():
    """Test correctness of sorting"""
    print("\n=== Testing Sorting Correctness ===")
    
    analyzer = MergeSortScalingAnalyzer(data_type='random')
    
    # Test with different sizes
    for size in [10, 100, 1000]:
        input_data = analyzer.prepare_input(size)
        result = analyzer.run_algorithm(input_data)
        
        # Verify sorting
        sorted_data = result['sorted_data']
        is_sorted = all(sorted_data[i] <= sorted_data[i+1] for i in range(len(sorted_data)-1))
        
        print(f"Size {size:4d}: Sorted correctly = {is_sorted}")
        
        # Verify all elements are present
        original_sorted = sorted(input_data)
        elements_match = sorted(sorted_data) == original_sorted
        print(f"Size {size:4d}: All elements present = {elements_match}")

if __name__ == "__main__":
    print("Merge Sort Scaling Analysis - Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        test_basic_functionality()
        test_different_data_types()
        test_correctness()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        print("Check the 'test_results/' directory for detailed results.")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
