#!/usr/bin/env python3
"""
Test script to verify Radix Sort fix for different bases
"""

import math

def counting_sort_by_digit(arr, digit_pos, radix):
    """Counting sort for a specific digit position"""
    n = len(arr)
    output = [0] * n
    count = [0] * radix
    
    # Count occurrences of each digit
    for num in arr:
        digit = (num // (radix ** digit_pos)) % radix
        count[digit] += 1
    
    # Calculate cumulative count
    for i in range(1, radix):
        count[i] += count[i - 1]
    
    # Build output array
    for i in range(n - 1, -1, -1):
        digit = (arr[i] // (radix ** digit_pos)) % radix
        output[count[digit] - 1] = arr[i]
        count[digit] -= 1
    
    return output

def radix_sort(arr, radix=10):
    """Radix sort implementation"""
    if not arr:
        return arr
    
    # Find maximum number to determine number of digits
    max_num = max(arr)
    
    # Calculate number of digits in the given radix
    if max_num == 0:
        num_digits = 1
    else:
        num_digits = int(math.log(max_num, radix)) + 1
    
    # Sort by each digit position
    current_arr = arr[:]
    for digit_pos in range(num_digits):
        current_arr = counting_sort_by_digit(current_arr, digit_pos, radix)
    
    return current_arr

def test_radix_sort():
    """Test radix sort with different bases"""
    test_data = [64, 34, 25, 12, 22, 11, 90, 88, 76, 50, 42]
    
    print("Original data:", test_data)
    print("Expected sorted:", sorted(test_data))
    print()
    
    for base in [2, 8, 10, 16]:
        result = radix_sort(test_data[:], base)
        is_sorted = all(result[i] <= result[i+1] for i in range(len(result)-1))
        matches_expected = result == sorted(test_data)
        
        print(f"Base {base:2d}: {result}")
        print(f"         Sorted: {is_sorted}, Matches expected: {matches_expected}")
        
        if not matches_expected:
            print(f"         ERROR: Expected {sorted(test_data)}")
        print()

if __name__ == "__main__":
    test_radix_sort()
